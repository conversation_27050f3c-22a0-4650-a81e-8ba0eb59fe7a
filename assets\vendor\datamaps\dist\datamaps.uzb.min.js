!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo={type:"Topology",objects:{uzb:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Bukhoro"},id:"UZ.BU",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Khorezm"},id:"UZ.KH",arcs:[[-3,5,6]]},{type:"Polygon",properties:{name:"Karakalpakstan"},id:"UZ.QR",arcs:[[7,-4,-7,8]]},{type:"Polygon",properties:{name:"Navoi"},id:"UZ.NW",arcs:[[9,10,11,-5,-8,12]]},{type:"Polygon",properties:{name:"Samarkand"},id:"UZ.SA",arcs:[[13,14,15,-11]]},{type:"Polygon",properties:{name:"Kashkadarya"},id:"UZ.QA",arcs:[[16,17,18,-1,-12,-16]]},{type:"Polygon",properties:{name:"Surkhandarya"},id:"UZ.SU",arcs:[[-18,19]]},{type:"Polygon",properties:{name:"Andijon"},id:"UZ.AN",arcs:[[20,21,22]]},{type:"MultiPolygon",properties:{name:"Ferghana"},id:"UZ.FA",arcs:[[[23]],[[24]],[[-21,25,26]]]},{type:"Polygon",properties:{name:"Namangan"},id:"UZ.NG",arcs:[[-22,-27,27,28,29],[30]]},{type:"Polygon",properties:{name:"Jizzakh"},id:"UZ.JI",arcs:[[31,32,33,34,-14,-10,35]]},{type:"MultiPolygon",properties:{name:"Sirdaryo"},id:"UZ.SI",arcs:[[[-34,36]],[[37,-32,38,39]]]},{type:"Polygon",properties:{name:"Tashkent"},id:"UZ.TA",arcs:[[-29,40,-40,41],[42]]},{type:"Polygon",properties:{name:"Tashkent"},id:"UZ.TK",arcs:[[-43]]}]}},arcs:[[[5335,2722],[-28,-33],[-58,-93],[-8,-9],[-6,-4],[-6,0],[-7,-1],[-4,-3],[-4,-4],[-44,-77],[-6,-13],[-3,-10],[-1,-6],[0,-6],[1,-6],[1,-6],[3,-10],[4,-9],[18,-37],[2,-5],[2,-5],[0,-7],[-1,-10],[-3,-5],[-3,-3],[-97,-56],[-31,-19],[-80,-51],[-28,-30],[-53,-93],[-9,-15]],[[4886,2096],[-17,36],[-14,14],[-18,-3],[-45,-36],[-15,-5],[-17,5],[-18,14],[-93,107],[-82,93],[-64,74],[-36,58],[-62,139],[-37,56],[-81,92],[-192,217],[-95,106],[-79,90],[-68,77],[-34,54],[-6,5],[-42,83],[-6,32],[-10,67],[-2,28],[3,25],[4,22],[4,22],[-3,25],[-18,61],[-2,24],[0,32],[-2,24],[-5,21],[-15,48],[-4,22],[-2,50],[-5,11],[-48,43],[-28,13],[-13,15],[-2,8]],[[3617,3965],[121,235],[30,103],[-7,40],[-19,45],[-95,166]],[[3647,4554],[132,98],[-1,16],[-165,356]],[[3613,5024],[206,120],[49,-1],[19,-66],[14,-39],[98,-179],[78,-215],[14,-12],[52,-18],[76,-46],[90,-79],[175,-167],[13,-36],[22,-34],[-1,-24],[69,38],[42,97],[34,112],[11,24],[11,14],[12,6],[11,-4],[11,-15],[12,-28],[3,-40],[1,-79],[8,-26],[25,-13],[158,-30],[13,19],[18,107],[11,9],[19,12],[77,5],[26,8],[18,13],[19,41],[7,-1],[5,-29],[12,-120],[6,-32],[8,-35],[20,-26],[20,-18],[58,-22],[176,-19],[28,13],[-14,-74],[-2,-31],[-2,-31],[1,-30],[1,-24],[2,-14],[0,-14],[-8,1],[-31,10],[-5,0],[-6,-2],[-4,-6],[-7,-16],[-1,-15],[-16,-76],[-12,-23],[-11,-16],[-13,-65],[-7,-20],[-68,1],[-13,7],[-13,13],[-12,16],[-11,5],[-13,0],[-20,-6],[-10,-8],[-5,-10],[-2,-12],[-1,-11],[0,-10],[3,-23],[15,-23],[-10,-46],[-5,-7],[-3,-2],[0,-3],[0,-4],[7,-17],[17,-28],[9,-17],[3,-8],[-1,-4],[-18,-4],[-3,-1],[-4,-4],[-4,-5],[-6,-1],[-9,-11],[-27,-89],[-8,-17],[-11,-16],[-10,-12],[-36,-25],[-4,-16],[-5,-8],[-2,-5],[-19,-37],[-9,-33],[-8,-31],[9,-29],[42,-93],[16,-17],[28,-24],[0,-7],[3,-11],[2,-10],[9,-6],[11,-1],[25,18],[9,-3],[5,-14],[70,-51],[10,-28],[5,-27],[0,-19],[2,-26],[5,-25],[11,-11],[14,-8],[9,-7],[10,-17],[9,-20],[3,-7],[3,-13],[6,-32],[1,-8],[2,-33]],[[3617,3965],[-1,3],[-5,27],[-4,11],[-21,45],[-8,25],[-12,74],[-7,17],[-19,32],[-6,16],[-5,26],[-7,54],[-18,100],[-6,24],[4,9],[-3,17],[-1,85],[-2,24],[-5,20],[-43,111],[-11,20],[-131,148],[-50,34],[-57,23],[-21,4],[-13,-10],[-8,-25],[-6,-42],[-14,-13],[-11,-37],[-17,-28],[-20,-16],[-22,-1],[-24,15],[-60,66],[-10,0],[-34,-9],[-4,11],[-1,11],[-3,3],[-3,-10],[-8,-20],[-4,-6],[-3,-5],[-28,44],[-41,15],[-117,-11],[-71,-36],[-35,-1],[-20,9],[-17,18],[-31,49],[-35,40],[-56,45],[-64,51],[-16,25],[-4,28],[7,96],[4,20],[8,17],[10,13],[24,19],[8,15],[-5,22],[-39,84],[-18,53],[-5,42],[15,33],[16,17],[18,2],[22,-10],[20,-20],[11,-8],[9,2],[7,16],[-1,17],[-6,16],[-7,14],[-8,11],[-27,25],[-9,14],[-7,15]],[[2425,5599],[7,12],[7,27],[-1,13],[-7,20],[-1,6],[-1,5],[2,5],[4,5],[9,3],[16,1],[12,8],[29,32],[12,15],[3,3],[9,2],[13,-15],[6,-5],[29,-11],[29,-5],[8,-10],[2,-25],[0,-54],[5,-19],[15,-37],[7,-30],[16,-45],[8,-16],[10,-11],[13,-4],[28,-2],[9,-8],[6,-19],[10,-47],[15,-41],[19,-33],[138,-192],[15,-5],[7,-4],[7,-7],[21,-37],[18,-25],[13,-8],[34,-10],[11,-10],[31,-53],[7,-17],[7,-35],[9,-7],[3,-3],[14,-9],[15,2],[10,12],[23,69],[10,17],[9,11],[10,9],[12,4],[31,0],[112,-42],[28,-18],[26,-25],[34,-41],[99,-169],[70,-172],[2,-4],[9,-7],[8,-4],[31,15]],[[3524,7518],[62,-159],[30,-41],[157,-65],[9,-8],[0,-12],[-3,-13],[-159,-449],[-149,-434],[-32,-114],[-13,-247],[0,-15],[1,-12],[67,-186],[11,-37],[-2,-7],[-7,-11],[-125,-108],[-13,-16],[0,-16],[5,-17],[250,-527]],[[2425,5599],[-13,32],[-17,22],[-61,36],[-31,-2],[-8,6],[-6,17],[1,15],[6,12],[10,5],[1,0],[34,20],[-1,47],[-12,59],[6,55],[9,11],[12,7],[10,11],[2,20],[-7,16],[-14,12],[-26,15],[-20,29],[-16,38],[-19,31],[-26,4],[-26,-5],[-51,7],[-123,-28],[-30,1],[-14,8],[-24,39],[-13,7],[-30,5],[-17,13],[-7,22],[-7,91],[-6,23],[-8,20],[-37,58],[-14,14],[-15,-2],[-17,-13],[-17,-7],[-17,-1],[-26,7],[-41,28],[-39,55],[-125,223],[-14,12],[-11,-27],[-8,-101],[-12,-35],[-24,-11],[-30,3],[-30,11],[-63,38],[-14,1],[-11,-5],[-31,-29],[-22,-15],[-10,-12],[-6,-17],[4,-23],[10,-15],[73,-52],[21,-24],[18,-42],[15,-56],[44,-106],[23,-43],[7,-9],[2,-10],[-2,-10],[-7,-7],[-37,-8],[-10,3],[-7,16],[0,19],[4,20],[2,22],[-6,43],[-15,30],[-20,22],[-58,42],[-13,3],[-10,-3],[-21,-13],[-10,-2],[-9,9],[-13,30],[-9,12],[-11,6],[-13,1],[-13,-4],[-11,-7],[-6,-12],[-5,-14],[-15,-26],[-16,-11],[-10,-13],[4,-31],[12,-41],[1,-19],[-5,-27],[-24,-83],[-8,-14],[-22,-11],[-5,-16],[4,-19],[8,-13],[2,-11],[-13,-14],[-11,-4],[-40,3],[-12,-4],[-32,-24],[-27,-9],[-82,22],[-56,-3],[-55,-41],[-47,-70],[-32,-92],[-21,-50],[-24,-28],[-56,-40],[-17,-34],[0,-46],[13,-95],[-1,-45],[-5,-38],[-1,-38],[11,-42],[13,-33],[6,-32],[3,-34],[-1,-42],[3,-33],[8,-33],[11,-30],[13,-22],[8,-9],[9,-7],[29,-11],[4,-5],[-1,-8],[-7,-12],[-12,-14],[-27,-21],[-12,-21],[-17,-50],[-12,-10],[-71,23],[-55,18],[-130,11],[-21,1],[-121,11],[-108,9],[-94,8],[-1,274],[0,274],[0,274],[0,274],[0,274],[0,274],[0,274],[0,274],[0,274],[0,274],[-1,274],[0,274],[0,274],[0,274],[0,274],[0,274],[0,1],[0,1],[1,0],[0,1],[1,0],[54,26],[55,25],[54,26],[55,25],[54,25],[55,26],[55,25],[42,20],[12,6],[55,25],[54,26],[55,25],[54,25],[55,26],[55,25],[54,26],[55,25],[73,32],[72,33],[73,32],[73,32],[34,15],[33,14],[34,14],[34,15],[35,15],[35,14],[35,15],[35,15],[47,19],[14,-2],[16,-10],[44,-43],[13,-13],[36,-35],[57,-55],[74,-73],[51,-49],[38,-37],[100,-98],[109,-106],[114,-111],[117,-114],[117,-114],[113,-110],[106,-104],[98,-95],[85,-82],[69,-68],[51,-50],[37,-35],[15,-25],[10,-32],[4,-29],[4,-62],[5,-27],[34,-82],[67,-106],[32,-51],[32,-51],[41,-70],[33,-57],[34,-57],[33,-57],[34,-58],[32,-56],[33,-56],[33,-56],[33,-56],[43,-74],[12,-8],[12,-7],[1,0]],[[6278,4738],[0,-4],[-72,-45],[-4,-18],[-8,-15],[10,-167],[1,-6],[2,-6],[3,-10],[5,-9],[3,-4],[8,-7],[53,-22],[3,-4],[2,-5],[1,-5],[1,-9],[0,-12],[-2,-21],[0,-20],[1,-6],[5,-21],[13,-35],[-16,-64],[-89,-271]],[[6198,3952],[-12,18],[-6,14],[-2,5],[-2,5],[0,5],[0,5],[3,19],[1,11],[-1,5],[-2,5],[-19,36],[-17,23],[-6,5],[-6,2],[-5,-4],[-42,-45],[-14,-9],[-11,-3],[-10,2],[-10,7],[-11,9],[-12,14],[-8,7],[-9,1],[-7,-4],[-7,-8],[-7,-12],[-5,-14],[-5,-15],[-8,-32],[-1,-19],[-1,-20],[5,-21],[5,-10],[2,-10],[0,-9],[-5,-14],[-4,-6],[-11,-8],[-3,-7],[0,-9],[6,-29],[2,-42],[-4,-26],[-8,-27],[-1,-8],[2,-9],[2,-6],[9,-12],[-1,-11],[-3,-8],[-14,-33],[-5,-15],[-4,-16],[-5,-27],[-15,-56],[-1,-9],[1,-6],[3,-2],[4,-1],[11,3],[5,0],[6,-1],[4,-3],[3,-7],[2,-8],[0,-9],[-3,-10],[-5,-10],[-38,-55],[-6,-6],[-126,-42],[-25,8],[-25,25],[-12,-6],[-11,9],[-12,14],[-23,13],[-9,16],[-15,33],[-6,1],[-2,2],[-12,3],[-53,38],[-8,1],[-4,-3],[-3,-6],[-4,-11],[-2,-8],[0,-6],[0,-15],[-1,-24],[-1,-6],[-1,-6],[-1,-4],[-2,-6],[-15,-35],[0,-7],[2,-5],[4,-1],[4,-1],[3,-3],[2,-5],[1,-6],[0,-6],[-3,-10],[-9,-31],[-1,-7],[-10,-62],[-4,-14],[-5,-6],[-8,-4],[-16,0],[-17,4],[-17,12],[-20,18],[-4,0],[-4,-2],[-3,-9],[-1,-7],[-2,-21],[-5,-7],[-5,-5],[-35,-14],[-28,-7],[-6,-2],[-3,-3],[-3,-4],[-1,-6],[5,-15],[17,-31],[11,-17],[5,-7],[5,-11],[17,-101],[1,-9],[0,-18],[-2,-11],[-1,-9],[-17,-62],[-1,-7],[7,-35],[31,-99]],[[5403,2754],[-11,-11],[-57,-21]],[[3524,7518],[48,13],[21,6],[20,5],[21,6],[20,6],[33,9],[33,9],[33,9],[33,9],[53,13],[53,13],[53,12],[53,13],[54,13],[53,13],[53,13],[53,13],[89,-12],[88,-12],[89,-12],[89,-12],[80,-11],[81,-11],[81,-11],[81,-11],[32,-4],[10,4],[10,5],[58,48],[56,46],[58,48],[57,47],[10,6],[11,6],[9,-2],[10,-3],[9,-13],[10,-13],[63,-121],[62,-122],[30,-46],[28,-45],[60,-57],[60,-56],[12,-15],[11,-15],[50,-154],[31,-97],[44,-135],[37,-116],[6,-1],[5,-1],[84,69],[84,68],[-13,-297],[-12,-296],[-2,-32],[-1,-31],[-3,-10],[-3,-11],[-7,-4],[-7,-4],[-2,-8],[-1,-8],[-1,-36],[-1,-197],[-3,-232],[1,-7],[1,-6],[2,-3],[3,-3],[70,-1],[64,0],[63,-1],[71,0],[2,0],[2,1],[2,0],[2,0],[2,-1],[2,-1],[2,-1],[1,-1],[10,-151],[10,-151],[19,-147],[19,-148],[25,-176],[24,-176],[13,-24],[13,-25],[15,-6]],[[6198,3952],[4,-5],[74,-4],[24,-11],[9,-4],[10,-2],[13,1],[15,5],[4,0],[3,-3],[3,-8],[4,-15],[3,-11],[1,-12],[-1,-16],[-3,-18],[-5,-18],[-1,-7],[-1,-7],[0,-8],[1,-57],[1,-20],[9,-62],[1,-6],[2,-6],[4,-5],[12,-9],[3,-4],[2,-5],[0,-9],[-2,-7],[0,-8],[2,-10],[16,-31],[0,-27],[-3,-11],[-2,-4],[-3,-4],[-11,-6],[-3,-2],[-1,-6],[-1,-6],[3,-54],[2,-6],[3,-5],[6,-3],[5,0],[5,1],[3,2],[12,9],[2,1],[4,0],[5,-1],[4,-5],[3,-7],[0,-16],[-2,-8],[0,-10],[1,-6],[21,-36],[7,5],[3,1],[3,1],[7,-18],[46,-20],[6,-2],[10,1],[4,2],[4,1],[9,-10],[4,-3],[5,-1],[6,3],[14,-3],[101,-30],[9,-24],[16,-112],[-2,-9],[-9,-4],[-5,0],[-15,-6],[-13,-7],[-18,-15],[-9,-11],[-5,-7],[-1,-5],[-1,-9],[-1,-17],[1,-11],[0,-8],[1,-6],[2,-12],[0,-7],[0,-14],[-2,-8],[-2,-5],[-5,-3],[-5,-2],[-18,0],[-8,-3],[-3,-12],[-2,-9],[-1,-83],[6,-19],[8,-16],[24,-34],[44,-62]],[[6673,2747],[-5,-11],[-8,-46],[-3,-48],[-1,-44],[-7,-40],[-5,-40],[-4,-2],[-5,3],[-6,-3],[-1,-2]],[[6628,2514],[-139,56],[-31,2],[-1,-6],[1,-12],[0,-6],[0,-6],[-1,-6],[-2,-5],[-3,-4],[-3,-3],[-7,-2],[-65,-4],[-8,3],[-4,2],[-4,4],[-3,4],[-3,7],[0,9],[0,19],[4,33],[-2,17],[-12,35],[-12,3],[-50,15],[-45,12],[-8,0],[-11,-5],[-9,-8],[-4,-6],[-4,-6],[-19,-46],[-6,-9],[-43,-28],[-5,0],[-7,3],[-10,7],[-34,44],[-15,15],[-11,-1],[-84,-31],[-3,0],[-55,-44],[-13,-12],[-9,-5],[-21,0],[-39,16],[-3,5],[-3,7],[-1,14],[-1,9],[0,8],[1,19],[0,18],[-2,7],[-2,6],[-14,18],[-18,19],[-30,47],[-6,14],[-4,9],[-1,7],[-6,38],[-1,6],[-2,4],[-3,4],[-3,3],[-4,3],[-5,1],[-19,1],[-6,-2],[-5,-5],[-1,-6],[0,-6],[0,-19],[0,-6],[-1,-6],[-2,-5],[-3,-4],[-12,-15],[-12,-10],[-7,-4],[-249,10]],[[6628,2514],[-5,-6],[-2,-6],[-2,-8],[-1,-17],[5,-47],[16,-25],[70,-40],[48,-2],[23,-15],[2,-5],[5,-13],[3,-6],[4,-1],[10,3],[4,-2],[5,-14],[1,-16],[-7,-93],[3,-29],[1,-1]],[[6811,2171],[-9,-11],[-10,-7],[-42,-3],[-8,-3],[-7,-4],[-3,-3],[-4,-5],[-4,-7],[-3,-9],[-2,-9],[0,-10],[5,-27],[3,-43],[14,-41],[1,-5],[1,-7],[0,-6],[-1,-8],[-1,-7],[-1,-7],[0,-6],[0,-7],[0,-6],[1,-6],[1,-5],[2,-13],[1,-6],[0,-7],[-2,-9],[-3,-10],[-6,-14],[-3,-10],[-1,-9],[0,-21],[-1,-6],[-2,-4],[-4,1],[-3,3],[-11,18],[-2,4],[-4,3],[-3,2],[-9,4],[-9,2],[-9,-1],[-7,-3],[-8,-6],[-6,-7],[-3,-5],[-4,-6],[-34,-109],[-6,-13],[-11,-17],[-5,-7],[-9,-8],[-23,-9],[-5,-17],[-6,-17],[-11,-18],[-21,-29],[-4,-8],[-3,-15],[-4,-21],[-6,-61],[-4,-28],[-2,-7],[-7,-13],[-65,-83],[-33,-30],[-7,-3],[-21,-6],[-5,-6],[-5,-10],[-9,-24],[-6,-24],[-3,-14],[-3,-14],[-3,-15],[-5,-12],[-71,-108],[-5,-10],[-3,-20],[-3,-46],[-3,-17],[-9,-29],[-18,-18]],[[6207,933],[-7,33],[-20,23],[-21,16],[-22,10],[-44,0],[-19,9],[-59,45],[-27,55],[-17,27],[-19,14],[-38,17],[-36,47],[-16,14],[-38,10],[-59,46],[-20,3],[-16,-11],[-29,-37],[-15,-13],[-40,-6],[-39,22],[-96,104],[-58,63],[-78,86],[-55,81],[-78,113],[-39,40],[-85,48],[-75,70],[-123,165],[-33,69]],[[6811,2171],[10,-16],[74,-20],[16,1],[44,24],[8,6],[8,4],[9,-1],[10,-5],[4,-4],[9,-14],[10,-3],[9,6],[10,11],[5,2],[4,0],[5,0],[5,-2],[9,-18],[20,-69],[10,-26],[6,-14],[6,-24],[0,-27],[-6,-19],[-7,-17],[-9,-12],[-10,-10],[-13,-4],[-10,0],[-7,-6],[-2,-26],[2,-12],[3,-11],[3,-11],[-1,-13],[-3,-7],[-14,-17],[-5,-18],[2,-22],[7,-45],[2,-24],[-2,-68],[1,-27],[5,-15],[14,-35],[3,-18],[1,-38],[2,-18],[6,-16],[23,-36],[21,-63],[10,-17],[30,-27],[46,-67],[15,-36],[2,-46],[-9,-70],[-15,-66],[-22,-56],[-7,-23],[-1,0],[-4,-21],[-3,-18],[-4,-15],[-6,-7],[-3,-4],[-9,-4],[-27,-6],[-18,-10],[-11,-17],[-24,-122],[-18,-55],[-42,-96],[-31,-49],[-30,-62],[-25,-71],[-17,-73],[-2,-50],[12,-95],[2,-50],[-3,-43],[-7,-40],[-11,-36],[-3,-18],[-2,23],[-7,16],[-10,10],[-12,5],[-21,-2],[-8,2],[-6,5],[-13,16],[-6,3],[-7,-1],[-11,-6],[-6,-1],[-4,-5],[-3,-12],[-5,-12],[-6,-3],[-10,13],[-11,50],[-13,11],[-12,-6],[-11,-13],[-19,-32],[-12,-14],[-11,-10],[-13,-6],[-14,-2],[-15,-5],[-22,-24],[-13,-4],[-13,9],[-7,18],[-4,22],[-6,21],[-12,17],[-26,17],[-11,12],[-6,18],[-9,52],[-8,10],[-12,6],[-23,28],[-11,8],[-28,1],[-53,-21],[-76,-5],[-7,-4],[-11,-16],[-6,-4],[-16,1],[-7,2],[-39,27],[-14,3],[-15,-2],[-11,-6],[-14,18],[1,22],[9,20],[11,19],[9,24],[-2,19],[-10,17],[-11,16],[-8,33],[2,43],[13,81],[2,47],[-4,94],[2,47],[20,58],[32,69],[22,67],[-6,25]],[[9463,3910],[0,1],[-1,5],[-5,27],[-1,17],[2,21],[2,23],[0,14],[0,6],[-1,6],[-8,12],[-30,14],[-7,1],[-4,6],[-11,25],[-9,10],[-5,-8],[-7,-13],[-10,-1],[-35,11],[-27,15],[-16,-2],[-12,-3],[-14,3],[-5,3],[-5,4],[-23,50],[-6,14],[-5,9],[-6,5],[-30,17],[-10,1],[-6,-2],[-3,-3],[-6,1],[-8,4],[-15,13],[-6,0],[-4,-3],[-1,-21],[-2,2],[-6,13],[-13,38],[-17,41]],[[9087,4286],[16,26],[-9,30],[-7,14],[0,8],[2,7],[19,27],[8,8],[7,18],[60,25],[20,-15],[24,18],[7,3],[12,0],[14,-2],[16,3],[77,-29],[10,-2],[9,3],[35,2],[9,3],[4,2],[2,3],[5,6],[14,100],[3,19]],[[9444,4563],[34,16],[21,4],[4,6],[7,31],[3,9],[11,13],[7,-8],[17,-40],[12,-12],[17,-8],[31,-5],[3,-3],[1,-5],[1,-6],[-1,-7],[0,-28],[10,-8],[15,-2],[11,-7],[25,-60],[18,-31],[22,-15],[25,-5],[76,11],[22,-10],[3,-35],[-2,-20],[7,2],[27,29],[43,31],[10,-10],[8,-17],[12,-13],[34,3],[13,-4],[5,-2],[3,-24],[-18,-37],[-27,-24],[-55,-32],[-50,-49],[-42,-16],[-20,-14],[-14,-33],[-7,-80],[-17,-12],[-21,15],[-11,0],[-5,-20],[0,-17],[-3,-17],[-6,-15],[-9,-11],[-23,-2],[-41,44],[-22,5],[-17,13],[-19,35],[-20,26],[-17,-12],[-2,-20],[13,-33],[0,-21],[-4,-20],[0,-13],[5,-11],[25,-28],[3,-10],[3,-14],[0,-28],[-6,-30],[-12,-26],[-14,-4],[-16,9],[-34,32],[-9,5],[-26,-2],[-8,3],[1,1],[2,11],[7,3],[10,1],[8,5],[3,12],[-6,7],[-10,1],[-5,0]],[[9189,3246],[-9,-3],[-15,7],[-11,8],[-13,15],[-6,17],[10,18],[15,1],[10,8],[21,25],[14,7],[3,-12],[-13,-77],[-6,-14]],[[8847,3412],[4,-1],[3,2],[6,8],[3,3],[15,6],[8,-2],[4,-11],[-4,-19],[-27,-29],[-9,-15],[1,-31],[26,-43],[-1,-37],[-7,-15],[-8,-4],[-19,-1],[-28,-12],[-8,2],[-7,10],[-2,13],[-2,10],[-12,1],[-6,-8],[-8,-12],[-8,-8],[-8,6],[-1,30],[25,62],[-2,35],[-7,8],[-16,5],[-7,7],[-7,15],[-14,50],[-3,19],[0,20],[4,21],[10,37],[18,16],[13,-13],[10,-29],[8,-33],[10,-20],[16,-16],[32,-22],[2,-2],[3,-3]],[[9463,3910],[-26,-2],[-10,-4],[-39,-34],[-5,-12],[-4,-22],[-8,-34],[-16,-24],[-50,-40],[-3,-17],[4,-18],[11,-12],[17,-12],[4,-5],[4,-4],[3,-9],[-4,-13],[-8,-3],[-9,2],[-7,5],[-5,1],[-3,-1],[-7,-3],[-23,3],[-23,9],[-21,-2],[-18,-29],[-11,-38],[-6,-16],[-10,-14],[-30,-28],[-8,-4],[-12,8],[-4,18],[0,49],[-4,24],[-8,27],[-10,14],[-11,-15],[-3,-22],[-2,-21],[-5,-14],[-14,-2],[-28,9],[-13,8],[-12,12],[-11,16],[-4,17],[-6,15],[-26,13],[-18,26],[-12,2],[-18,-3],[-10,1],[-8,6],[-6,11],[-5,12],[-6,8],[-9,-4],[-9,-22],[-5,-26],[-8,-21],[-18,-3],[-68,16],[-34,-9],[-21,-34],[-12,2],[-52,-8],[-83,-40],[-25,-3],[-24,3],[-24,26],[-10,51],[-8,55],[-15,40],[-13,8],[-11,-2],[-11,-5],[-13,-2],[-12,6],[-29,20],[8,42],[-2,5],[-5,9],[-1,2],[0,-1],[-7,18],[-1,3],[-1,6],[-1,25],[9,23],[17,18],[40,14],[6,4],[6,12],[14,37],[8,13],[8,5],[8,4],[9,6],[8,10],[20,40],[35,45],[9,13],[8,10],[5,4],[10,3],[2,5],[1,6],[2,4],[9,9],[0,1]],[[8606,4192],[15,15],[6,10],[6,13],[6,11],[10,7],[8,1],[16,-3],[8,2],[27,38],[12,0],[9,-6],[5,-5],[3,-5],[19,-4],[66,6],[-37,-79],[3,-5],[23,-4],[4,-4],[17,-26],[4,-4],[6,-3],[11,-4],[6,-3],[12,-15],[26,-23],[9,-4],[13,4],[38,56],[18,44],[12,15],[52,44],[48,25]],[[8606,4192],[1,10],[5,40],[-22,20],[-53,19],[-9,19],[-2,20],[6,15],[11,2],[21,-27],[11,-7],[1,22],[-5,11],[-106,187],[-18,25],[-2,3],[-1,4],[-12,19]],[[8432,4574],[5,28],[-2,88],[-2,27],[-12,31],[-12,24],[-3,10],[-2,8],[0,8],[1,7],[11,36],[2,8],[3,16],[-2,10],[-16,4],[-2,4],[-2,6],[-1,19],[-1,13],[1,9],[1,7],[29,58],[5,16],[7,28]],[[8440,5039],[4,0],[19,11],[72,64],[20,4],[11,-10],[9,-21],[33,-87],[6,-24],[1,-25],[-9,-74],[1,-21],[7,-16],[10,2],[11,9],[13,2],[11,-7],[11,-12],[9,-15],[8,-17],[11,-16],[12,-2],[25,6],[27,-8],[13,-9],[12,-15],[11,-9],[22,-5],[9,-11],[15,-39],[9,-9],[6,23],[-2,25],[-2,21],[1,17],[12,10],[14,-2],[7,-14],[7,-45],[5,-17],[8,-13],[7,1],[7,23],[14,29],[40,-53],[13,17],[3,42],[-5,206],[4,15],[8,-3],[28,-41],[25,-16],[20,6],[16,26],[19,64],[9,41],[2,23],[-2,22],[-16,33],[-6,20],[11,46],[7,13],[13,8],[13,-3],[7,-16],[3,-23],[0,-25],[-10,-67],[0,-15],[6,-3],[3,10],[3,13],[2,7],[7,-4],[3,-20],[5,-3],[5,6],[0,25],[4,5],[5,-3],[4,-7],[55,-125],[9,-36],[4,-39],[-2,-51],[3,-50],[16,-12],[46,12],[14,-18],[9,-19],[10,-9],[17,17],[13,17],[13,14],[15,3],[14,-13],[7,-23],[3,-33],[1,-34],[5,-62],[0,-24],[-4,-21],[-8,-28],[18,8]],[[8541,4478],[-1,20],[-7,29],[-8,21],[-14,19],[-16,10],[-9,-3],[7,-25],[0,-1],[10,-15],[17,-36],[10,-15],[11,-4]],[[7156,4153],[-1,-49],[-8,-12],[-32,-9],[-7,-3],[-4,-2],[-2,-3],[-2,-7],[0,-28],[-4,-44],[1,-7],[3,-4],[10,2],[10,5],[6,1],[1,-15],[-68,-247],[-3,-17],[-1,-19],[2,-32],[2,-10],[4,-9],[2,-5],[16,-4],[132,-4],[175,68],[2,-1],[1,-2],[-3,-9],[-3,-5],[-2,-7],[-3,-8],[-8,-46],[-2,-8],[-2,-11],[7,-21]],[[7375,3581],[-23,-10],[-12,-14],[15,-26],[26,-26],[20,-12],[6,2]],[[7407,3495],[-25,-52],[-9,-26],[-3,-13],[0,-9],[0,-8],[1,-7],[2,-11],[2,-6],[0,-2],[2,-4],[4,-21],[2,-12],[1,-6],[0,-11],[-1,-40],[-2,-6],[-2,-1],[-2,5],[-3,5],[-3,3],[-3,0],[-5,-4],[-1,-5],[2,-5],[2,-4],[11,-10],[3,-4],[1,-5],[0,-6],[-2,-9],[-3,-10],[-1,-19],[-1,-12]],[[7374,3180],[-1,1],[-7,1],[-6,-26],[-6,-193],[-3,-28],[-8,-24],[-14,-23],[-25,-25],[-8,-18],[2,-7],[4,-16],[-11,-8],[-36,-15],[-29,-5],[-28,4],[-48,27],[-87,4],[-199,82],[-27,1],[-22,-12],[-54,-61],[-47,-12],[-11,-17],[4,-53],[-2,-5],[-3,-3],[-2,-2],[-3,-1],[-15,3],[-9,-2]],[[6278,4738],[9,-4],[24,-9],[68,6],[57,5],[85,8],[62,6],[74,7],[48,4],[75,7],[75,7],[6,-3],[7,-3],[12,-9],[12,-10],[7,-4],[6,-3],[6,1],[5,1],[17,18],[17,17],[7,7],[8,8],[3,-17],[4,-17],[1,-1],[0,-1],[2,-7],[2,-6],[13,-26],[13,-27],[3,-10],[3,-11],[2,-23],[2,-23],[4,-10],[4,-10],[4,5],[3,4],[4,3],[4,3],[3,-1],[4,-1],[3,-6],[3,-7],[4,14],[4,13],[6,1],[7,1],[1,-13],[1,-12],[0,-2],[0,-1],[-1,-5],[-1,-6],[3,-1],[3,-2],[1,-1],[1,-1],[-7,-8],[-7,-8],[-2,-1],[-3,-2],[3,-2],[2,-2],[1,-3],[2,-3],[2,-7],[3,-7],[-1,-17],[0,-17],[-2,-20],[-3,-20],[-5,-20],[-4,-19],[-4,-16],[-5,-15],[-3,-8],[-3,-8],[-5,-8],[-4,-7],[-5,-6],[-5,-6],[-10,-7],[-10,-7],[-3,-4],[-2,-4],[-1,-5],[-1,-4],[2,-2],[2,-2],[11,-4],[12,-4],[9,-8],[8,-8],[27,-41],[27,-41],[19,-24],[20,-24],[23,-19]],[[7407,3495],[136,42],[27,-31],[4,-17],[2,-18],[-1,-18],[-5,-14],[-13,-4],[-42,11],[-12,-6],[-21,-29],[-7,-3],[-14,37],[-9,12],[-7,-22],[1,-11],[10,-13],[2,-9],[-1,-25],[1,-11],[3,-12],[51,-111],[2,-27],[-25,-16],[-13,32],[-9,52],[-12,41],[-12,13],[-4,-9],[2,-23],[6,-25],[13,-44],[0,-17],[-7,-23],[-3,-2],[-6,-2],[-2,-2],[-4,-18],[0,-2],[0,-5],[-1,-12],[-4,-8],[-8,6],[-15,40],[-10,12],[-20,-28],[-6,4]],[[7626,3620],[-18,15],[-13,3],[-95,-23],[-12,-7],[-20,-18],[-14,-3],[-33,9],[-13,-2],[-33,-13]],[[7156,4153],[45,-37],[69,-57],[1,3],[1,3],[3,6],[4,6],[5,-1],[4,0],[2,4],[2,4],[2,6],[1,5],[3,3],[3,3],[3,-1],[4,-1],[11,-9],[12,-9],[8,-2],[7,-2],[7,6],[6,6],[2,7],[1,8],[-5,5],[-6,5],[7,18],[7,18],[1,4],[0,4],[-2,7],[-3,6],[-1,8],[-2,7],[-1,7],[-2,8],[1,8],[0,7],[-7,3],[-8,2],[-2,3],[-3,2],[-3,5],[-2,5],[-2,11],[-2,11],[-3,50],[-3,50],[1,10],[1,9],[2,3],[2,3],[1,2],[2,2],[2,7],[2,7],[1,5],[0,6],[-1,14],[-1,13],[0,7],[1,6],[1,-1],[1,-1],[3,-2],[3,-2],[1,-1],[1,-1],[3,14],[4,14],[8,8],[8,9],[12,9]],[[7379,4498],[30,-43],[4,-10],[2,-12],[1,-28],[2,-9],[3,-3],[5,6],[2,0],[2,-8],[2,-21],[9,6],[13,-3],[13,-9],[9,-10],[5,-11],[7,-20],[4,-9],[4,-5],[16,-12],[6,-8],[3,-6],[6,-19],[1,-6],[-1,-14],[0,-5],[4,-6],[4,0],[4,1],[4,-2],[8,-12],[7,-14],[1,-16],[-8,-16],[9,-8],[8,4],[9,8],[8,5],[6,-5],[-2,-12],[-10,-25],[25,8],[11,-2],[7,-29],[10,-14],[3,-12],[-2,-10],[-10,-14],[0,-9],[5,-4],[5,6],[7,10],[5,5],[5,-4],[4,-8],[0,-9],[-14,-10],[-6,-16],[-6,-43],[6,1],[6,-1],[6,-3],[5,-6],[-11,-10],[2,-9],[6,-10],[3,-7],[2,-10],[2,-10],[1,-11],[-3,-10],[-8,-17],[-3,-9],[-4,-12],[-1,-8],[-12,-110],[8,-33],[4,-10],[1,-17],[-9,-48],[-3,-15],[0,-1]],[[8432,4574],[-7,12],[-26,17],[-22,-9],[-16,-126],[-32,-59],[-42,-39],[-61,-38],[-55,-66],[-107,-52],[-13,-10],[-11,-15],[-25,-49],[-14,-19],[-15,-7],[-18,10],[-8,10],[-6,13],[-10,28],[-7,12],[-14,15],[-7,10],[-23,55],[-13,19],[-19,3],[-9,9],[-7,12],[-8,10],[-10,2],[-19,-10],[-10,-8],[-8,-11],[-15,-30],[-9,-31],[-4,-36],[0,-44],[4,-63],[-2,-16],[-9,-8],[-35,-5],[-21,-22],[0,-25],[23,-62],[13,-67],[5,-21],[15,-43],[7,-23],[3,-26],[-4,-59],[-19,-5],[-22,14],[-15,-4],[2,-35],[43,-60],[0,-28],[-22,-16],[-29,2],[-53,24],[-20,16]],[[7379,4498],[6,3],[18,13],[5,11],[6,11],[11,48],[11,48],[8,14],[8,14],[20,13],[20,14],[7,19],[7,20],[4,7],[4,8],[6,3],[6,3],[12,3],[13,2],[10,8],[11,8],[10,11],[10,10],[5,8],[5,8],[2,6],[2,7],[5,57],[4,57],[0,10],[0,10],[-1,7],[-1,7],[-1,2],[0,2],[-2,2],[-1,1],[-3,5],[-3,6],[0,5],[-1,5],[1,4],[1,4],[3,3],[2,3],[14,10],[13,11],[6,2],[5,3],[6,-3],[5,-4],[3,13],[3,12],[7,11],[7,11],[16,15],[16,16],[5,1],[4,2],[9,-5],[9,-5],[4,1],[4,1],[11,11],[12,11],[4,2],[4,2],[18,-5],[17,-5],[3,3],[4,3],[-1,7],[0,6],[-5,14],[-5,13],[-1,7],[-1,6],[4,16],[4,15],[7,9],[8,9],[17,8],[16,8],[30,36],[30,36],[8,15],[8,14],[5,6],[5,6],[39,17],[40,17],[32,2],[32,2],[13,8],[12,8],[9,16],[8,16],[4,9],[4,9],[5,6],[4,5],[17,10],[16,9],[24,27],[24,28],[8,13],[7,13],[20,53],[20,52],[4,7],[4,6],[5,5],[5,4],[5,3],[6,3],[4,3],[5,4],[4,7],[3,6],[3,11],[2,10],[7,24],[7,25],[13,16],[12,17],[29,19],[28,18],[4,-1],[4,-1],[6,-6],[5,-6],[5,-8],[4,-8],[3,-7],[2,-8],[2,-12],[2,-11],[4,-7],[5,-7],[6,-3],[6,-4],[7,-2],[7,-1],[9,7],[9,6],[6,18],[6,17],[6,22],[5,22],[7,19],[7,20],[8,15],[8,15],[3,8],[3,8],[3,11],[2,11],[4,9],[4,8],[7,1],[7,0],[14,-6],[13,-6],[11,3],[11,2],[11,9],[11,9],[20,24],[20,25],[12,18],[26,29],[19,4],[18,-12],[82,-89],[18,-10],[2,-1],[-8,-44],[-22,-23],[-48,-22],[-82,-94],[-24,-8],[-29,1],[-24,-9],[-10,-44],[-2,-69],[-6,-20],[-21,-11],[-58,-11],[-17,-16],[-58,-76],[-25,-46],[-17,-56],[-14,-31],[-18,-18],[-19,-15],[-34,-42],[-95,-85],[-12,-31],[11,-38],[21,-18],[82,-14],[1,0],[21,-21],[10,-13],[9,-17],[14,-42],[9,-12],[10,-1]],[[7664,4891],[10,9],[1,-15],[4,-14],[15,-25],[9,15],[1,-5],[-4,-12],[8,-20],[17,-7],[19,36],[29,42],[22,17],[-6,9],[4,12],[-6,13],[-8,8],[9,24],[-2,19],[-21,22],[-18,3],[-14,15],[-20,0],[-31,-48],[-4,-38],[-15,-58],[1,-2]]],
transform:{scale:[.0017174519372937325,.0008374409014901444],translate:[55.975838663000104,37.18514740000006]}},m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();