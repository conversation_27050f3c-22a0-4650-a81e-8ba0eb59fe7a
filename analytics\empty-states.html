<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Analytics: Empty States | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Analytics: Empty States | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Analytics: Empty States | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>

  <link rel="stylesheet" href="../assets/vendor/apexcharts/dist/apexcharts.css">
  <style type="text/css">
    .apexcharts-tooltip.apexcharts-theme-light
    {
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  </style>
</head>

<body class="bg-gray-50 dark:bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="lg:ms-65 lg:hidden fixed top-0 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 bg-white border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
    <div class="flex justify-between xl:grid xl:grid-cols-3 basis-full items-center w-full py-2.5 px-2 sm:px-5">
      <!-- Logo & Sidebar Toggle -->
      <div class="flex items-center gap-x-3">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
          <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->

        <div class="lg:hidden">
          <!-- Sidebar Toggle -->
          <button type="button" class="w-7 h-9.5 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13" />
            </svg>
          </button>
          <!-- End Sidebar Toggle -->
        </div>

        <div class="lg:hidden">
          <!-- Templates Dropdown -->
          <div class="hs-dropdown  relative  [--scope:window] [--auto-close:inside] inline-flex">
            <button id="hs-dropdown-preview-navbar" type="button" class="hs-dropdown-toggle  group relative flex justify-center items-center size-8 text-xs rounded-full text-gray-800 hover:bg-gray-100 focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <span class="">
                <svg class=" size-4 shrink-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </span>

              <span class="absolute -top-0.5 -end-0.5">
                <span class="relative flex">
                  <span class="animate-ping absolute inline-flex size-full rounded-full bg-red-400 dark:bg-red-600 opacity-75"></span>
                  <span class="relative inline-flex size-2 bg-red-500 rounded-full"></span>
                  <span class="sr-only">Notification</span>
                </span>
              </span>
            </button>

            <!-- Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-full min-w-90 md:w-125 transition-[opacity,margin] duration opacity-0 hidden z-61 overflow-hidden border border-gray-200 bg-white rounded-xl shadow-xl dark:bg-neutral-800 dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-preview-navbar">
              <!-- Tab -->
              <div class="p-3 pb-0 flex flex-wrap justify-between items-center gap-3 border-b border-gray-200 dark:border-neutral-700">
                <!-- Nav Tab -->
                <nav class="flex gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pmn-item-pro" aria-selected="true" data-hs-tab="#hs-pmn-pro" aria-controls="hs-pmn-pro" role="tab">
                    Pro
                  </button>
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pmn-item-free" aria-selected="false" data-hs-tab="#hs-pmn-free" aria-controls="hs-pmn-free" role="tab">
                    Free
                  </button>
                </nav>
                <!-- End Nav Tab -->

                <!-- Switch/Toggle -->
                <div class="mb-2 flex items-center gap-x-0.5">
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:hidden flex shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="dark">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                    </svg>
                    Switch to Dark
                  </button>
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:flex hidden shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="light">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="4"></circle>
                      <path d="M12 2v2"></path>
                      <path d="M12 20v2"></path>
                      <path d="m4.93 4.93 1.41 1.41"></path>
                      <path d="m17.66 17.66 1.41 1.41"></path>
                      <path d="M2 12h2"></path>
                      <path d="M20 12h2"></path>
                      <path d="m6.34 17.66-1.41 1.41"></path>
                      <path d="m19.07 4.93-1.41 1.41"></path>
                    </svg>
                    Switch to Light
                  </button>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <!-- End Tab -->

              <!-- Tab Content -->
              <div id="hs-pmn-pro" class="" role="tabpanel" aria-labelledby="hs-pmn-item-pro">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (21)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../pro/pricing.html">
                      Purchase
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/dashboard/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Dashboard
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img55.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img55.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img58.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img58.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/video-call/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img61.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img61.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Video Call
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/startup/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img32.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img32.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Startup
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/payment/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img8.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img8.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Payment
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img16.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img16.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Chat
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img21.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img21.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ecommerce/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        E-Commerce
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img46.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img46.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/crm/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img11.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img11.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CRM
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/workspace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img18.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img18.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Workspace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 bg-gray-100 dark:bg-neutral-700 transition" href="../../pro/analytics/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img9.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img9.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Analytics
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/calendars/day.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img14.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img14.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Calendars
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/smart-home/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img35.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img35.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Smart Home
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img52.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img52.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/inbox/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img26.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img26.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Inbox
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/project/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img10.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img10.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Project
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop-marketplace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img29.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img29.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop Marketplace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img49.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img49.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/files/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img12.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img12.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Files
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                      Examples (780<!-- (780) -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                      Templates (21)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->

              <!-- Tab Content -->
              <div id="hs-pmn-free" class="hidden" role="tabpanel" aria-labelledby="hs-pmn-item-free">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (5)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../templates.html">
                      Free download
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img5.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img5.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img2.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img2.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img3.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img3.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../examples.html">
                      Examples (220+<!-- 222 -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../templates.html">
                      Templates (5)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->
            </div>
            <!-- End Dropdown -->
          </div>
          <!-- End Templates Dropdown -->
        </div>
      </div>
      <!-- End Logo & Sidebar Toggle -->

      <div class="lg:hidden h-9.5 flex justify-end items-center gap-x-2">
        <!-- Account Dropdown -->
        <div class="hs-dropdown inline-flex   [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
          <button id="hs-pro-dnad" type="button" class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
            <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
          </button>

          <!-- Account Dropdown -->
          <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnad">
            <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
              <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                <div class="grow">
                  <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                    James Collison
                  </span>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Preline@HS
                  </p>
                </div>
              </a>
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
                Billing
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                Settings
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                  <circle cx="12" cy="7" r="4" />
                </svg>
                My account
              </a>
            </div>
            <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
              <!-- Switch/Toggle -->
              <div class="flex flex-wrap justify-between items-center gap-2">
                <label for="hs-pro-dnaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                <label for="hs-pro-dnaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                  <input data-hs-theme-switch type="checkbox" id="hs-pro-dnaddm" class="peer sr-only">
                  <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                  <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                </label>
              </div>
              <!-- End Switch/Toggle -->
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Customization
                <div class="ms-auto">
                  <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                    New
                  </span>
                </div>
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Manage team
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Sign out
              </a>
            </div>
            <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
              <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M8 12h8" />
                  <path d="M12 8v8" />
                </svg>
                Add team account
              </button>
            </div>
          </div>
          <!-- End Account Dropdown -->
        </div>
        <!-- End Account Dropdown -->
      </div>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN SIDEBAR ========== -->
  <aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
    hs-overlay-open:translate-x-0
    -translate-x-full transition-all duration-300 transform
    w-82 h-full
    hidden
    fixed inset-y-0 start-0 z-60
    bg-white
    lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
    dark:bg-neutral-800" tabindex="-1" aria-label="Sidebar">
    <div class="h-full flex">
      <!-- Nav Sidebar -->
      <div class="w-16 flex flex-col h-full max-h-full">
        <div class="p-4 flex flex-col items-center">
          <!-- Logo -->
          <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
            <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            </svg>
          </a>
          <!-- End Logo -->
        </div>

        <!-- Content -->
        <div class="h-full px-4 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <!-- Nav -->
          <ul class="flex flex-col items-center space-y-1">
            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                  <polyline points="9 22 9 12 15 12 15 22" />
                </svg>
                <span class="sr-only">Dashboard</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Dashboard
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
                <span class="sr-only">Users</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Users
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 9 5 12 1.8-5.2L21 14Z" />
                  <path d="M7.2 2.2 8 5.1" />
                  <path d="m5.1 8-2.9-.8" />
                  <path d="M14 4.1 12 6" />
                  <path d="m6 12-1.9 2" />
                </svg>
                <span class="sr-only">Events</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Events
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2" />
                  <rect width="18" height="18" x="3" y="4" rx="2" />
                  <circle cx="12" cy="10" r="2" />
                  <line x1="8" x2="8" y1="2" y2="4" />
                  <line x1="16" x2="16" y1="2" y2="4" />
                </svg>
                <span class="sr-only">Attributes</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Attributes
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4 text-indigo-600 dark:text-indigo-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="m16 12-4-4-4 4" />
                  <path d="M12 16V8" />
                </svg>
                <span class="sr-only">Upgrade</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Upgrade
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 22v-5" />
                  <path d="M9 8V2" />
                  <path d="M15 8V2" />
                  <path d="M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z" />
                </svg>
                <span class="sr-only">Integrations</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Integrations
              </span>
            </li>
            <!-- End Item -->
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Content -->

        <footer class="hidden lg:block text-center border-t border-gray-200 dark:border-neutral-700">
          <!-- Account Dropdown -->
          <div class="hs-dropdown  inline-flex  [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
            <button id="hs-pro-dsad" type="button" class="w-full flex items-center gap-x-3 text-start py-4 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
            </button>

            <!-- Account Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dsad">
              <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
                <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                  <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                  <div class="grow">
                    <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                      James Collison
                    </span>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      Preline@HS
                    </p>
                  </div>
                </a>
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <line x1="2" x2="22" y1="10" y2="10" />
                  </svg>
                  Billing
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  Settings
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                  My account
                </a>
              </div>
              <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
                <!-- Switch/Toggle -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                  <label for="hs-pro-dsaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                  <label for="hs-pro-dsaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                    <input data-hs-theme-switch type="checkbox" id="hs-pro-dsaddm" class="peer sr-only">
                    <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                    <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                  </label>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Customization
                  <div class="ms-auto">
                    <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                      New
                    </span>
                  </div>
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Manage team
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Sign out
                </a>
              </div>
              <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
                <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                  <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M8 12h8" />
                    <path d="M12 8v8" />
                  </svg>
                  Add team account
                </button>
              </div>
            </div>
            <!-- End Account Dropdown -->
          </div>
          <!-- End Account Dropdown -->
        </footer>

        <!-- Sidebar Close -->
        <div class="lg:hidden absolute top-4 -end-6 z-10">
          <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-e-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="7 8 3 12 7 16" />
              <line x1="21" x2="11" y1="12" y2="12" />
              <line x1="21" x2="11" y1="6" y2="6" />
              <line x1="21" x2="11" y1="18" y2="18" />
            </svg>
          </button>
        </div>
        <!-- End Sidebar Close -->
      </div>
      <!-- End Nav Sidebar -->

      <!-- Secondary Sidebar -->
      <div class="w-72 bg-white border-x border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
        <div class="flex flex-col h-full max-h-full">
          <!-- Quick Actions -->
          <div class="py-3 px-2 border-b border-gray-200 dark:border-neutral-700">
            <button type="button" class="py-1.5 ps-3 pe-1.5 w-full inline-flex items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700/50">
              Quick actions
              <span class="ms-auto flex items-center gap-x-1 py-px px-1.5 border border-gray-200 rounded-md dark:border-neutral-700">
                <svg class="shrink-0 size-2.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
                </svg>
                <span class="text-[11px] uppercase">k</span>
              </span>
            </button>
          </div>
          <!-- End Quick Actions -->

          <!-- Content -->
          <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <!-- Nav -->
            <nav class="hs-accordion-group size-full flex flex-col justify-between" data-hs-accordion-always-open>
              <!-- Body -->
              <div>
                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/index.html">
                      Overview
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/visitors.html">
                      Visitors
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/survey.html">
                      Survey
                      <div class="ms-auto">
                        <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-md text-[10px] leading-4 font-medium bg-white border border-gray-200 text-gray-800 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                          Beta
                        </span>
                      </div>
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/incidents.html">
                      Incidents
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 bg-gray-100 focus:bg-gray-200 dark:bg-neutral-700 dark:focus:bg-neutral-600" href="../../pro/analytics/empty-states.html">
                      Empty Contents
                    </a>
                  </li>
                  <!-- End Link -->
                </ul>

                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li class="hs-accordion active" id="projects-accordion">
                    <button type="button" class="hs-accordion-toggle py-1 px-3 flex justify-center items-center gap-x-1 text-xs text-gray-500 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-500 dark:focus:bg-neutral-700" aria-expanded="true" aria-controls="projects-accordion-sub">
                      <svg class="hs-accordion-active:rotate-90 shrink-0 size-3 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                      Channels
                    </button>

                    <div id="projects-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="projects-accordion">
                      <ul class="hs-accordion-group mt-1 flex flex-col" data-hs-accordion-always-open>
                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/index.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />
                                <path d="m3.3 7 8.7 5 8.7-5" />
                                <path d="M12 22V12" />
                              </svg>
                            </span>
                            PRO
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="18" height="7" x="3" y="3" rx="1" />
                                <rect width="9" height="7" x="3" y="14" rx="1" />
                                <rect width="5" height="7" x="16" y="14" rx="1" />
                              </svg>
                            </span>
                            Examples
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="4" width="20" height="16" rx="2" />
                                <path d="M10 4v4" />
                                <path d="M2 8h20" />
                                <path d="M6 4v4" />
                              </svg>
                            </span>
                            Templates
                          </a>
                        </li>
                        <!-- End Link -->
                      </ul>
                    </div>
                  </li>
                  <!-- End Link -->
                </ul>
              </div>
              <!-- End Body -->

              <!-- Footer -->
              <footer class="p-2 pb-4 flex flex-col gap-y-3">
                <!-- Credits Card -->
                <a class="p-3 group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="flex justify-between items-center gap-x-1">
                    <div class="grow">
                      <p class="font-medium text-sm text-gray-800 dark:text-neutral-200">
                        65/100 credits used
                      </p>
                      <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                        Invite team members
                      </p>
                      <p class="mt-2 flex items-center gap-1 text-xs text-indigo-600 dark:text-indigo-500">
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
                        </svg>
                        Upgrade now
                        <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                          <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                        </svg>
                      </p>
                    </div>

                    <!-- Circular Progress -->
                    <div class="relative size-14">
                      <svg class="size-full -rotate-90" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-100 dark:text-indigo-500/40" stroke-width="4"></circle>
                        <!-- Progress Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-600 dark:text-indigo-500" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="35" stroke-linecap="round"></circle>
                      </svg>
                    </div>
                    <!-- End Circular Progress -->
                  </div>
                </a>
                <!-- End Credits Card -->

                <!-- News Card -->
                <a class="group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 transition duration-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="p-2 w-full h-32 rounded-md">
                    <img class="size-full object-cover rounded-md dark:hidden" src="../assets/img/demo-previews/img32.webp" alt="Card Image">
                    <img class="size-full object-cover rounded-md dark:block hidden" src="../assets/img/demo-previews-dark/img32.webp" alt="Card Image">
                  </div>

                  <div class="p-3">
                    <div class="flex items-center gap-2">
                      <div class="grow">
                        <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                          New
                        </p>
                        <p class="text-wrap font-medium text-sm text-gray-800 group-hover:text-indigo-600 group-focus:text-indigo-600 dark:text-neutral-200 dark:group-hover:text-white dark:group-focus:text-white">
                          Preline Startup Demo
                        </p>
                      </div>

                      <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </a>
                <!-- End News Card -->
              </footer>
              <!-- End Footer -->
            </nav>
            <!-- End Nav -->
          </div>
          <!-- End Content -->
        </div>
      </div>
      <!-- End Secondary Sidebar -->
    </div>
  </aside>
  <!-- ========== END MAIN SIDEBAR ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" class="lg:ps-82 pt-15 lg:pt-0">
    <div class="pt-3.5 pb-10 px-2 sm:px-5 flex flex-col gap-y-5">
      <!-- Header -->
      <div class="flex flex-wrap justify-between items-center gap-2">
        <h1 class="inline-block text-lg font-semibold text-gray-800 dark:text-neutral-200">
          Overview
        </h1>

        <!-- Button Group -->
        <div class="flex items-center gap-x-2">
          <!-- Calendar Dropdown -->
          <div class="hs-dropdown [--auto-close:inside] inline-flex">
            <button id="hs-pro-dnic" type="button" class="py-1.5 px-2.5 inline-flex items-center text-[13px] rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <svg class="shrink-0 me-2 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                <line x1="16" x2="16" y1="2" y2="6" />
                <line x1="8" x2="8" y1="2" y2="6" />
                <line x1="3" x2="21" y1="10" y2="10" />
                <path d="M8 14h.01" />
                <path d="M12 14h.01" />
                <path d="M16 14h.01" />
                <path d="M8 18h.01" />
                <path d="M12 18h.01" />
                <path d="M16 18h.01" />
              </svg>
              Today
              <svg class="shrink-0 ms-1.5 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>

            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-79.5 transition-[opacity,margin] duration opacity-0 hidden z-50 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnic">

              <!-- Calendar -->
              <div class="p-3 space-y-0.5">
                <!-- Months -->
                <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                  <!-- Prev Button -->
                  <div class="col-span-1">
                    <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                      <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6" />
                      </svg>
                    </button>
                  </div>
                  <!-- End Prev Button -->

                  <!-- Month / Year -->
                  <div class="col-span-3 flex justify-center items-center gap-x-1">
                    <div class="relative">
                      <select data-hs-select='{
                            "placeholder": "Select month",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                        <option value="0">January</option>
                        <option value="1">February</option>
                        <option value="2">March</option>
                        <option value="3">April</option>
                        <option value="4">May</option>
                        <option value="5">June</option>
                        <option value="6" selected>July</option>
                        <option value="7">August</option>
                        <option value="8">September</option>
                        <option value="9">October</option>
                        <option value="10">November</option>
                        <option value="11">December</option>
                      </select>
                    </div>

                    <span class="text-gray-800 dark:text-neutral-200">/</span>

                    <div class="relative">
                      <select data-hs-select='{
                            "placeholder": "Select year",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                        <option selected>2023</option>
                        <option>2024</option>
                        <option>2025</option>
                        <option>2026</option>
                        <option>2027</option>
                      </select>
                    </div>
                  </div>
                  <!-- End Month / Year -->

                  <!-- Next Button -->
                  <div class="col-span-1 flex justify-end">
                    <button type="button" class=" size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                      <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </button>
                  </div>
                  <!-- End Next Button -->
                </div>
                <!-- Months -->

                <!-- Weeks -->
                <div class="flex pb-1.5">
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Mo
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Tu
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    We
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Th
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Fr
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Sa
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Su
                  </span>
                </div>
                <!-- Weeks -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      26
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      27
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      28
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      29
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      30
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      1
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      2
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      3
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      4
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      5
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      6
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      7
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      8
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      9
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      10
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      11
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      12
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      13
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      14
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      15
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      16
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      17
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      18
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      19
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                      20
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      21
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      22
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      23
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      24
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      25
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      26
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      27
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      28
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      29
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      30
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      31
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      1
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      2
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      3
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      4
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      5
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      6
                    </button>
                  </div>
                </div>
                <!-- Days -->
              </div>
              <!-- End Calendar -->
            </div>
          </div>
          <!-- End Calendar Dropdown -->

          <!-- Add Activity Dropdown -->
          <div class="hs-dropdown [--auto-close:inside] [--placement:top-right] relative inline-flex">
            <!-- Button -->
            <button id="hs-pro-daaad" type="button" class="py-1.5 px-2.5 inline-flex items-center gap-x-2 text-[13px] rounded-lg border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-indigo-600 dark:focus:ring-indigo-500" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
              Add activity
            </button>
            <!-- End Button -->

            <!-- Add Activity Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-48 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-daaad">
              <div class="p-1">
                <div class="flex justify-between items-center py-1.5 px-2 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                  <label for="hs-pro-dachdds1" class="flex flex-1 items-center gap-x-3 cursor-pointer text-[13px] text-gray-800 dark:text-neutral-300">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                    Customers
                  </label>
                  <input type="checkbox" class="shrink-0 size-3.5 border-gray-300 rounded-sm text-indigo-500 checked:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800" id="hs-pro-dachdds1" checked>
                </div>

                <div class="flex justify-between items-center py-1.5 px-2 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                  <label for="hs-pro-dachdds2" class="flex flex-1 items-center gap-x-3 cursor-pointer text-[13px] text-gray-800 dark:text-neutral-300">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6" />
                      <path d="m12 12 4 10 1.7-4.3L22 16Z" />
                    </svg>
                    Avg. click rate
                  </label>
                  <input type="checkbox" class="shrink-0 size-3.5 border-gray-300 rounded-sm text-indigo-500 checked:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800" id="hs-pro-dachdds2" checked>
                </div>

                <div class="flex justify-between items-center py-1.5 px-2 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                  <label for="hs-pro-dachdds3" class="flex flex-1 items-center gap-x-3 cursor-pointer text-[13px] text-gray-800 dark:text-neutral-300">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="18" x2="18" y1="20" y2="10" />
                      <line x1="12" x2="12" y1="20" y2="4" />
                      <line x1="6" x2="6" y1="20" y2="14" />
                    </svg>
                    Sessions
                  </label>
                  <input type="checkbox" class="shrink-0 size-3.5 border-gray-300 rounded-sm text-indigo-500 checked:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800" id="hs-pro-dachdds3" checked>
                </div>

                <div class="flex justify-between items-center py-1.5 px-2 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                  <label for="hs-pro-dachdds4" class="flex flex-1 items-center gap-x-3 cursor-pointer text-[13px] text-gray-800 dark:text-neutral-300">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
                      <circle cx="12" cy="12" r="3" />
                    </svg>
                    Pageviews
                  </label>
                  <input type="checkbox" class="shrink-0 size-3.5 border-gray-300 rounded-sm text-indigo-500 checked:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800" id="hs-pro-dachdds4" checked>
                </div>

                <div class="flex justify-between items-center py-1.5 px-2 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                  <label for="hs-pro-dachdds5" class="flex flex-1 items-center gap-x-3 cursor-pointer text-[13px] text-gray-800 dark:text-neutral-300">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z" />
                      <path d="m2 22 3-3" />
                      <path d="M7.5 13.5 10 11" />
                      <path d="M10.5 16.5 13 14" />
                      <path d="m18 3-4 4h6l-4 4" />
                    </svg>
                    Requests
                  </label>
                  <input type="checkbox" class="shrink-0 size-3.5 border-gray-300 rounded-sm text-indigo-500 checked:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800" id="hs-pro-dachdds5">
                </div>

                <div class="my-1 border-t border-gray-200 dark:border-neutral-700"></div>

                <button type="button" class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                  Delete all
                </button>
              </div>
            </div>
            <!-- End Add Activity Dropdown -->
          </div>
          <!-- End Add Activity Dropdown -->
        </div>
        <!-- End Button Group -->
      </div>
      <!-- End Header -->

      <!-- Audience -->
      <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
        <!-- Tab Nav -->
        <nav class="relative z-0 flex border-b border-gray-200 dark:border-neutral-700" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
          <!-- Nav Item -->
          <button type="button" class="hs-tab-active:border-t-indigo-600 relative flex-1 first:border-s-0 border-s border-t-3 md:border-t-4 border-gray-200 border-t-transparent hover:border-t-gray-300 focus:outline-hidden focus:border-t-gray-300 p-3.5 xl:px-6 text-start focus:z-10 dark:hs-tab-active:border-t-indigo-500 dark:border-t-transparent dark:border-neutral-700 dark:hover:border-t-neutral-600 dark:focus:border-t-neutral-600 active" id="bar-with-underline-item-1" aria-selected="true" data-hs-tab="#bar-with-underline-1" aria-controls="bar-with-underline-1" role="tab">
            <span class="flex gap-x-4">
              <svg class="hidden xl:block shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
              </svg>
              <span class="grow text-center md:text-start">
                <span class="block text-xs md:text-sm text-gray-500 dark:text-neutral-500">
                  Users
                </span>
                <span class="hidden xl:mt-1 md:flex md:justify-between md:items-center md:gap-x-2">
                  <span class="block text-lg lg:text-xl xl:text-2xl text-gray-800 dark:text-neutral-200">
                    0
                  </span>
                </span>
              </span>
            </span>
          </button>
          <!-- End Nav Item -->

          <!-- Nav Item -->
          <button type="button" class="hs-tab-active:border-t-indigo-600 relative flex-1 first:border-s-0 border-s border-t-3 md:border-t-4 border-gray-200 border-t-transparent hover:border-t-gray-300 focus:outline-hidden focus:border-t-gray-300 p-3.5 xl:px-6 text-start focus:z-10 dark:hs-tab-active:border-t-indigo-500 dark:border-t-transparent dark:border-neutral-700 dark:hover:border-t-neutral-600 dark:focus:border-t-neutral-600" id="bar-with-underline-item-2" aria-selected="false" data-hs-tab="#bar-with-underline-2" aria-controls="bar-with-underline-2" role="tab">
            <span class="flex gap-x-4">
              <svg class="hidden xl:block shrink-0 size-5 text-gray-500 dark:text-neutral-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
              <span class="grow text-center md:text-start">
                <span class="block text-xs md:text-sm text-gray-500 dark:text-neutral-500">
                  New users
                </span>
                <span class="hidden xl:mt-1 md:flex md:justify-between md:items-center md:gap-x-2">
                  <span class="block text-lg lg:text-xl xl:text-2xl text-gray-800 dark:text-neutral-200">
                    0
                  </span>
                </span>
              </span>
            </span>
          </button>
          <!-- End Nav Item -->

          <!-- Nav Item -->
          <button type="button" class="hs-tab-active:border-t-indigo-600 relative flex-1 first:border-s-0 border-s border-t-3 md:border-t-4 border-gray-200 border-t-transparent hover:border-t-gray-300 focus:outline-hidden focus:border-t-gray-300 p-3.5 xl:px-6 text-start focus:z-10 dark:hs-tab-active:border-t-indigo-500 dark:border-t-transparent dark:border-neutral-700 dark:hover:border-t-neutral-600 dark:focus:border-t-neutral-600" id="bar-with-underline-item-3" aria-selected="false" data-hs-tab="#bar-with-underline-3" aria-controls="bar-with-underline-3" role="tab">
            <span class="flex gap-x-4">
              <svg class="hidden xl:block shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                <path d="M3 3v5h5" />
              </svg>
              <span class="grow text-center md:text-start">
                <span class="block text-xs md:text-sm text-gray-500 dark:text-neutral-500">
                  Returning users
                </span>
                <span class="hidden xl:mt-1 md:flex md:justify-between md:items-center md:gap-x-2">
                  <span class="block text-lg lg:text-xl xl:text-2xl text-gray-800 dark:text-neutral-200">
                    0
                  </span>
                </span>
              </span>
            </span>
          </button>
          <!-- End Nav Item -->

          <!-- Nav Item -->
          <button type="button" class="hs-tab-active:border-t-indigo-600 relative flex-1 first:border-s-0 border-s border-t-3 md:border-t-4 border-gray-200 border-t-transparent hover:border-t-gray-300 focus:outline-hidden focus:border-t-gray-300 p-3.5 xl:px-6 text-start focus:z-10 dark:hs-tab-active:border-t-indigo-500 dark:border-t-transparent dark:border-neutral-700 dark:hover:border-t-neutral-600 dark:focus:border-t-neutral-600" id="bar-with-underline-item-4" aria-selected="false" data-hs-tab="#bar-with-underline-4" aria-controls="bar-with-underline-4" role="tab">
            <span class="flex gap-x-4">
              <svg class="hidden xl:block shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
              <span class="grow text-center md:text-start">
                <span class="block text-xs md:text-sm text-gray-500 dark:text-neutral-500">
                  Avg. engagement time
                </span>
                <span class="hidden xl:mt-1 md:flex md:justify-between md:items-center md:gap-x-2">
                  <span class="block text-lg lg:text-xl xl:text-2xl text-gray-800 dark:text-neutral-200">
                    0
                  </span>
                </span>
              </span>
            </span>
          </button>
          <!-- End Nav Item -->
        </nav>
        <!-- End Tab Nav -->

        <!-- Tab Content -->
        <div class="p-5">
          <!-- Tab Content Item -->
          <div id="bar-with-underline-1" role="tabpanel" aria-labelledby="bar-with-underline-item-1">
            <!-- Empty State -->
            <div class="p-5 min-h-125 flex flex-col justify-center items-center text-center">
              <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <g filter="url(#filter1)">
                  <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                  <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                  <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                </g>
                <defs>
                  <filter id="filter1" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="6" />
                    <feGaussianBlur stdDeviation="6" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                  </filter>
                </defs>
              </svg>

              <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                  No Users data
                </p>
                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                  In the meantime, you can create new custom insights to monitor your most important metrics
                </p>
              </div>
              <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-indigo-500" data-hs-overlay="#hs-pro-atbetb">
                <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>Create insight
              </button>
            </div>
            <!-- End Empty State -->
          </div>
          <!-- End Tab Content Item -->

          <!-- Tab Content Item -->
          <div id="bar-with-underline-2" class="hidden" role="tabpanel" aria-labelledby="bar-with-underline-item-2">
            <!-- Empty State -->
            <div class="p-5 min-h-125 flex flex-col justify-center items-center text-center">
              <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <g filter="url(#filter2)">
                  <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                  <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                  <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                </g>
                <defs>
                  <filter id="filter2" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="6" />
                    <feGaussianBlur stdDeviation="6" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                  </filter>
                </defs>
              </svg>

              <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                  No New Users data
                </p>
                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                  In the meantime, you can create new custom insights to monitor your most important metrics
                </p>
              </div>
              <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-indigo-500" data-hs-overlay="#hs-pro-atbetb">
                <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>Create insight
              </button>
            </div>
            <!-- End Empty State -->
          </div>
          <!-- End Tab Content Item -->

          <!-- Tab Content Item -->
          <div id="bar-with-underline-3" class="hidden" role="tabpanel" aria-labelledby="bar-with-underline-item-3">
            <!-- Empty State -->
            <div class="p-5 min-h-125 flex flex-col justify-center items-center text-center">
              <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <g filter="url(#filter3)">
                  <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                  <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                  <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                </g>
                <defs>
                  <filter id="filter3" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="6" />
                    <feGaussianBlur stdDeviation="6" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                  </filter>
                </defs>
              </svg>

              <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                  No Returning Users data
                </p>
                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                  In the meantime, you can create new custom insights to monitor your most important metrics
                </p>
              </div>
              <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-indigo-500" data-hs-overlay="#hs-pro-atbetb">
                <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>Create insight
              </button>
            </div>
            <!-- End Empty State -->
          </div>
          <!-- End Tab Content Item -->

          <!-- Tab Content Item -->
          <div id="bar-with-underline-4" class="hidden" role="tabpanel" aria-labelledby="bar-with-underline-item-4">
            <!-- Empty State -->
            <div class="p-5 min-h-125 flex flex-col justify-center items-center text-center">
              <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                <g filter="url(#filter4)">
                  <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                  <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                  <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                  <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                </g>
                <defs>
                  <filter id="filter4" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="6" />
                    <feGaussianBlur stdDeviation="6" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                  </filter>
                </defs>
              </svg>

              <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                  No Avg. Engagement Time data
                </p>
                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                  In the meantime, you can create new custom insights to monitor your most important metrics
                </p>
              </div>
              <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-indigo-500" data-hs-overlay="#hs-pro-atbetb">
                <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>Create insight
              </button>
            </div>
            <!-- End Empty State -->
          </div>
          <!-- End Tab Content Item -->
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Audience -->

      <!-- Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
        <!-- Listbar Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Goal Conversions
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Heading -->
          <div class="pb-2 px-7 flex gap-x-2">
            <div class="w-full">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                Goal
              </h3>
            </div>
            <div class="w-20 text-end">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                Unique
              </h3>
            </div>
            <div class="w-20 text-end">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                Total
              </h3>
            </div>
            <div class="w-20 text-end">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                CR
              </h3>
            </div>
          </div>
          <!-- End Heading -->

          <!-- Body -->
          <div class="p-5 pt-0 space-y-4">
            <!-- List Group -->
            <ul class="space-y-2">
              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->
            </ul>
            <!-- End List Group -->
          </div>
          <!-- End Body -->
        </div>
        <!-- End Listbar Card -->

        <!-- Listbar Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Devices
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Heading -->
          <div class="pb-2 px-7 flex gap-x-2">
            <div class="w-full">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                Visitors
              </h3>
            </div>
            <div class="w-20 text-end">
              <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
                %
              </h3>
            </div>
          </div>
          <!-- End Heading -->

          <!-- Body -->
          <div class="p-5 pt-0 space-y-4">
            <!-- List Group -->
            <ul class="space-y-2">
              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="relative size-full truncate">
                  <span class="relative z-1 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="absolute inset-y-0 start-0 bg-indigo-100 h-full rounded-sm dark:bg-indigo-500/20" style="width: 1%"></div>
                </div>
                <div class="w-20 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    -
                  </span>
                </div>
              </li>
              <!-- End List Item -->
            </ul>
            <!-- End List Group -->
          </div>
          <!-- End Body -->
        </div>
        <!-- End Listbar Card -->
      </div>
      <!-- End Grid -->

      <!-- Charts Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
        <!-- Progress w/ Labels in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Location
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0 space-y-4">
            <!-- List Group -->
            <ul class="space-y-4">
              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->

              <!-- List Item -->
              <li class="flex justify-between items-center gap-x-2">
                <div class="w-full grid grid-cols-2 items-center gap-x-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    -
                  </span>
                  <div class="flex justify-end" role="progressbar" aria-valuenow="7" aria-valuemin="0" aria-valuemax="100">
                    <div class="h-1.5 flex flex-col justify-center overflow-hidden bg-indigo-500 rounded-full text-xs text-white text-center whitespace-nowrap" style="width: 1%"></div>
                  </div>
                </div>
                <div class="min-w-15 text-end">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                </div>
              </li>
              <!-- End List Item -->
            </ul>
            <!-- End List Group -->
          </div>
          <!-- End Body -->
        </div>
        <!-- End Progress w/ Labels in Card -->

        <!-- Doughnut Chart in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Market Share
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0 space-y-4">
            <div class="h-full flex flex-col justify-between space-y-4">
              <div class="size-full flex justify-center">
                <!-- Empty State -->
                <div class="p-5 h-full flex flex-col justify-center items-center text-center">
                  <svg class="w-16 h-auto mx-auto" width="103" height="103" viewBox="0 0 103 103" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M102.055 48.8251C99.9773 38.8501 94.5762 29.8754 86.735 23.3691C78.8937 16.8628 69.0766 13.2102 58.8895 13.0088L58.6755 23.8325C66.4119 23.9854 73.8673 26.7593 79.8221 31.7004C85.777 36.6414 89.8787 43.4571 91.4564 51.0323L102.055 48.8251Z" fill="currentColor" class="fill-gray-300 dark:fill-neutral-600" />
                    <path d="M62.5229 102.772C68.8396 102.134 74.9498 100.166 80.452 96.9988C85.9543 93.8311 90.7239 89.535 94.4476 84.3927C98.1712 79.2504 100.765 73.3784 102.057 67.1625C103.35 60.9466 103.313 54.5275 101.948 48.327L91.3753 50.6541C92.4118 55.3629 92.44 60.2377 91.4583 64.9583C90.4766 69.6788 88.5071 74.1382 85.6793 78.0433C82.8514 81.9485 79.2292 85.2111 75.0507 87.6167C70.8722 90.0223 66.2319 91.5166 61.4348 92.0012L62.5229 102.772Z" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700" />
                    <path d="M13 58C13 64.3064 14.3255 70.5424 16.8906 76.3035C19.4557 82.0647 23.2031 87.2224 27.8897 91.4421C32.5764 95.6618 38.0976 98.8493 44.0954 100.798C50.0932 102.747 56.4336 103.413 62.7054 102.753L61.5734 91.9869C56.8104 92.4876 51.9954 91.9817 47.4405 90.5019C42.8856 89.0221 38.6926 86.6014 35.1335 83.3968C31.5743 80.1922 28.7284 76.2754 26.7804 71.9002C24.8324 67.525 23.8258 62.7892 23.8258 58L13 58Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                    <path d="M0 45.5C0 39.5249 1.16396 33.6082 3.42542 28.0879C5.68688 22.5676 9.00156 17.5517 13.1802 13.3266C17.3588 9.10158 22.3196 5.75007 27.7792 3.46348C33.2389 1.17689 39.0905 -2.61182e-07 45 0L45 25.0547C42.3446 25.0547 39.7152 25.5836 37.2619 26.611C34.8086 27.6385 32.5795 29.1445 30.7019 31.043C28.8242 32.9415 27.3348 35.1954 26.3186 37.6759C25.3024 40.1565 24.7794 42.8151 24.7794 45.5H0Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                  </svg>

                  <div class="max-w-sm mx-auto">
                    <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                      No data
                    </p>
                    <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                      No data here yet. We will notify you when there's an update.
                    </p>
                  </div>
                </div>
                <!-- End Empty State -->

              </div>
            </div>
          </div>
          <!-- End Body -->
        </div>
        <!-- End Doughnut Chart in Card -->

        <!-- Line Charts in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Age
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0 space-y-4">
            <div class="w-full">
              <!-- Apex Lines Chart -->
              <div id="hs-age-lines-chart" class="min-h-[277px] "></div>
            </div>

            <!-- Legend Indicator -->
            <div class="flex justify-center items-center gap-x-4 mt-6">
              <div class="inline-flex items-center">
                <span class="shrink-0 size-2.5 inline-block bg-indigo-600 rounded-xs me-2.5"></span>
                <span class="text-sm text-gray-800 dark:text-neutral-200">
                  18-25
                </span>
              </div>
              <div class="inline-flex items-center">
                <span class="shrink-0 size-2.5 inline-block bg-cyan-500 rounded-xs me-2.5 dark:bg-cyan-500"></span>
                <span class="text-sm text-gray-800 dark:text-neutral-200">
                  25-40
                </span>
              </div>
              <div class="inline-flex items-center">
                <span class="shrink-0 size-2.5 inline-block bg-gray-200 rounded-xs me-2.5 dark:bg-neutral-700"></span>
                <span class="text-sm text-gray-800 dark:text-neutral-200">
                  40+
                </span>
              </div>
            </div>
            <!-- End Legend Indicator -->
          </div>
          <!-- End Body -->
        </div>
        <!-- End Line Charts in Card -->

        <!-- Bubble Chart with Legend Indicators in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4 relative z-10">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Devices
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0 space-y-4">
            <div class="h-full flex flex-col justify-center w-full text-center ">
              <!-- Empty State -->
              <div class="p-5 h-full flex flex-col justify-center items-center text-center">
                <svg class="w-16 h-auto mx-auto" width="103" height="103" viewBox="0 0 103 103" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M102.055 48.8251C99.9773 38.8501 94.5762 29.8754 86.735 23.3691C78.8937 16.8628 69.0766 13.2102 58.8895 13.0088L58.6755 23.8325C66.4119 23.9854 73.8673 26.7593 79.8221 31.7004C85.777 36.6414 89.8787 43.4571 91.4564 51.0323L102.055 48.8251Z" fill="currentColor" class="fill-gray-300 dark:fill-neutral-600" />
                  <path d="M62.5229 102.772C68.8396 102.134 74.9498 100.166 80.452 96.9988C85.9543 93.8311 90.7239 89.535 94.4476 84.3927C98.1712 79.2504 100.765 73.3784 102.057 67.1625C103.35 60.9466 103.313 54.5275 101.948 48.327L91.3753 50.6541C92.4118 55.3629 92.44 60.2377 91.4583 64.9583C90.4766 69.6788 88.5071 74.1382 85.6793 78.0433C82.8514 81.9485 79.2292 85.2111 75.0507 87.6167C70.8722 90.0223 66.2319 91.5166 61.4348 92.0012L62.5229 102.772Z" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700" />
                  <path d="M13 58C13 64.3064 14.3255 70.5424 16.8906 76.3035C19.4557 82.0647 23.2031 87.2224 27.8897 91.4421C32.5764 95.6618 38.0976 98.8493 44.0954 100.798C50.0932 102.747 56.4336 103.413 62.7054 102.753L61.5734 91.9869C56.8104 92.4876 51.9954 91.9817 47.4405 90.5019C42.8856 89.0221 38.6926 86.6014 35.1335 83.3968C31.5743 80.1922 28.7284 76.2754 26.7804 71.9002C24.8324 67.525 23.8258 62.7892 23.8258 58L13 58Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                  <path d="M0 45.5C0 39.5249 1.16396 33.6082 3.42542 28.0879C5.68688 22.5676 9.00156 17.5517 13.1802 13.3266C17.3588 9.10158 22.3196 5.75007 27.7792 3.46348C33.2389 1.17689 39.0905 -2.61182e-07 45 0L45 25.0547C42.3446 25.0547 39.7152 25.5836 37.2619 26.611C34.8086 27.6385 32.5795 29.1445 30.7019 31.043C28.8242 32.9415 27.3348 35.1954 26.3186 37.6759C25.3024 40.1565 24.7794 42.8151 24.7794 45.5H0Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                </svg>

                <div class="max-w-sm mx-auto">
                  <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                    No data
                  </p>
                  <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                    No data here yet. We will notify you when there's an update.
                  </p>
                </div>
              </div>
              <!-- End Empty State -->

            </div>
          </div>
          <!-- End Body -->
        </div>
        <!-- End Bubble Chart with Legend Indicators in Card -->

        <!-- Area Charts with Legend Indicators in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-2 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Total sales
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0">
            <h3 class="text-2xl font-medium text-gray-800 dark:text-neutral-200">
              $0
            </h3>

            <div id="hs-total-sales" class="min-h-[215px] -ms-3"></div>

            <div class="pt-4 px-2 mb-3.5">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Conversion funnel
              </h4>
            </div>

            <div class="relative after:absolute after:bottom-0 after:inset-x-2.5 after:border-b after:border-gray-200 dark:after:border-neutral-700">
              <!-- Nav Tab -->
              <nav class="flex gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pro-tabs-dtsch-item-first-time" aria-selected="true" data-hs-tab="#hs-pro-tabs-dtsch-first-time" aria-controls="hs-pro-tabs-dtsch-first-time" role="tab">
                  <span class="shrink-0 size-2.5 inline-block bg-indigo-600 rounded-xs dark:bg-indigo-500"></span><span class="text-sm text-gray-800 dark:text-neutral-200">First-time</span>
                </button>
                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pro-tabs-dtsch-item-returning" aria-selected="false" data-hs-tab="#hs-pro-tabs-dtsch-returning" aria-controls="hs-pro-tabs-dtsch-returning" role="tab">
                  <span class="shrink-0 size-2.5 inline-block bg-cyan-400 rounded-xs dark:bg-cyan-500"></span><span class="text-sm text-gray-800 dark:text-neutral-200">Returning</span>
                </button>
              </nav>
              <!-- End Nav Tab -->
            </div>

            <div>
              <!-- Tab Content -->
              <div id="hs-pro-tabs-dtsch-first-time" role="tabpanel" aria-labelledby="hs-pro-tabs-dtsch-item-first-time">
                <!-- List Group -->
                <ul class="px-2">
                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Added to cart
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->

                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Reached checkout
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->

                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Sessions converted
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->
                </ul>
                <!-- End List Group -->
              </div>
              <!-- End Tab Content -->

              <!-- Tab Content -->
              <div id="hs-pro-tabs-dtsch-returning" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dtsch-item-returning">
                <!-- List Group -->
                <ul class="px-2">
                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Added to cart
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->

                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Reached checkout
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->

                  <!-- List Item -->
                  <li class="py-3 grid grid-cols-2 justify-between items-center gap-x-4 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-800 dark:text-neutral-200">
                        Sessions converted
                      </span>
                    </div>
                    <div class="flex justify-end items-center gap-x-1.5">
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        0
                      </span>
                    </div>
                  </li>
                  <!-- End List Item -->
                </ul>
                <!-- End List Group -->
              </div>
              <!-- End Tab Content -->
            </div>
          </div>
          <!-- End Body -->
        </div>
        <!-- End Area Charts with Legend Indicators in Card -->

        <!-- Pie Chart with Legend Indicators in Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="p-5 pb-4 grid grid-cols-2 items-center gap-x-4">
            <div>
              <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                Acquisition
              </h2>
            </div>
            <!-- End Col -->
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="h-full p-5 pt-0 space-y-4">
            <div class="h-full flex justify-center w-full">
              <!-- Empty State -->
              <div class="p-5 h-full flex flex-col justify-center items-center text-center">
                <svg class="w-16 h-auto mx-auto" width="103" height="103" viewBox="0 0 103 103" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M102.055 48.8251C99.9773 38.8501 94.5762 29.8754 86.735 23.3691C78.8937 16.8628 69.0766 13.2102 58.8895 13.0088L58.6755 23.8325C66.4119 23.9854 73.8673 26.7593 79.8221 31.7004C85.777 36.6414 89.8787 43.4571 91.4564 51.0323L102.055 48.8251Z" fill="currentColor" class="fill-gray-300 dark:fill-neutral-600" />
                  <path d="M62.5229 102.772C68.8396 102.134 74.9498 100.166 80.452 96.9988C85.9543 93.8311 90.7239 89.535 94.4476 84.3927C98.1712 79.2504 100.765 73.3784 102.057 67.1625C103.35 60.9466 103.313 54.5275 101.948 48.327L91.3753 50.6541C92.4118 55.3629 92.44 60.2377 91.4583 64.9583C90.4766 69.6788 88.5071 74.1382 85.6793 78.0433C82.8514 81.9485 79.2292 85.2111 75.0507 87.6167C70.8722 90.0223 66.2319 91.5166 61.4348 92.0012L62.5229 102.772Z" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700" />
                  <path d="M13 58C13 64.3064 14.3255 70.5424 16.8906 76.3035C19.4557 82.0647 23.2031 87.2224 27.8897 91.4421C32.5764 95.6618 38.0976 98.8493 44.0954 100.798C50.0932 102.747 56.4336 103.413 62.7054 102.753L61.5734 91.9869C56.8104 92.4876 51.9954 91.9817 47.4405 90.5019C42.8856 89.0221 38.6926 86.6014 35.1335 83.3968C31.5743 80.1922 28.7284 76.2754 26.7804 71.9002C24.8324 67.525 23.8258 62.7892 23.8258 58L13 58Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                  <path d="M0 45.5C0 39.5249 1.16396 33.6082 3.42542 28.0879C5.68688 22.5676 9.00156 17.5517 13.1802 13.3266C17.3588 9.10158 22.3196 5.75007 27.7792 3.46348C33.2389 1.17689 39.0905 -2.61182e-07 45 0L45 25.0547C42.3446 25.0547 39.7152 25.5836 37.2619 26.611C34.8086 27.6385 32.5795 29.1445 30.7019 31.043C28.8242 32.9415 27.3348 35.1954 26.3186 37.6759C25.3024 40.1565 24.7794 42.8151 24.7794 45.5H0Z" fill="currentColor" class="fill-gray-200 dark:fill-neutral-600" />
                </svg>

                <div class="max-w-sm mx-auto">
                  <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                    No data
                  </p>
                  <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                    No data here yet. We will notify you when there's an update.
                  </p>
                </div>
              </div>
              <!-- End Empty State -->

            </div>
          </div>
          <!-- End Body -->
        </div>
        <!-- End Pie Chart with Legend Indicators in Card -->
      </div>
      <!-- End Charts Grid -->
    </div>
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <!-- Required -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>
  <!-- Apexcharts -->
  <script src="../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../assets/vendor/apexcharts/dist/apexcharts.min.js"></script>
  <script src="../assets/vendor/preline/dist/helper-apexcharts.js"></script>

  <!-- Datamap -->
  <!-- DataMap -->
  <script src="../assets/vendor/d3/d3.min.js"></script>
  <script src="../assets/vendor/topojson/build/topojson.min.js"></script>
  <script src="../assets/vendor/datamaps/dist/datamaps.world.min.js"></script>

  <!-- JS INITIALIZATIONS -->
  <script>
    (function () {
      const dataSet = {
      };
      const dataMap = new Datamap({
        element: document.querySelector('#hs-users-datamap'),
        projection: 'mercator',
        responsive: true,
        fills: {
          defaultFill: '#d4d4d4',
          MAJOR: '#4f46e5'
        },
        data: dataSet,
        geographyConfig: {
          borderColor: 'rgba(0, 0, 0, .09)',
          highlightFillColor: '#818cf8',
          highlightBorderColor: '#818cf8',
          popupTemplate: function (geo, data) {
            const growUp = `<svg class="size-4 text-teal-500 dark:text-teal-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>
              </svg>`;
            const growDown = `<svg class="shrink-0 size-4 text-red-500 dark:text-red-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 17 13.5 8.5 8.5 13.5 2 7"/><polyline points="16 17 22 17 22 11"/></svg>
              </svg>`;
          
            return `<div class="bg-white rounded-xl shadow-xl w-37.5 p-3">
                <div class="flex mb-1">
                  <div class="me-2">
                    <div class="size-4 rounded-full bg-no-repeat bg-center bg-cover" style="background-image: url('../../assets/vendor/svg-country-flags/svg/${data.short}.svg')"></div>
                  </div>
                  <span class="text-sm font-medium">${data.customName || geo.properties.name}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">Active:</span>
                  &nbsp;<span class="text-sm font-medium ${data.active.value}</span>
                  &nbsp;<span class="text-sm ${data.active.isGrown ? 'text-teal-500 dark:text-teal-400' : 'text-red-500 dark:text-red-400'}">${data.active.percent}</span>
                  &nbsp;${data.active.isGrown ? growUp : growDown}
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 dark:text-neutral-500">New:</span>
                  &nbsp;<span class="text-sm font-medium ${data.new.value}</span>
                  &nbsp;<span class="text-sm ${data.active.isGrown ? 'text-teal-500 dark:text-teal-400' : 'text-red-500 dark:text-red-400'}">${data.new.percent}</span>
                  &nbsp;${data.new.isGrown ? growUp : growDown}
                </div>
              </div>`;
          }
        }
      });
      dataMap.addPlugin('update', function (_, mode) {
        this.options.fills = (mode === 'dark') ? {
          defaultFill: '#404040',
          MAJOR: '#4f46e5',
        } : {
          defaultFill: 'rgba(189, 197, 208, 0.7)',
          MAJOR: '#4f46e5'
        };
      
        this.updateChoropleth(dataSet, { reset: true });
      });
    
      dataMap.update(localStorage.getItem('hs_theme'));
    
      window.addEventListener('on-hs-appearance-change', (evt) => {
        dataMap.update(evt.detail);
      });
    
      window.addEventListener('resize', function () {
        dataMap.resize();
      });
    })();
  </script>

  <script>
  	window.addEventListener('load', () => {
  	  // Apex Donut Chart
  	  (function () {
  	    buildChart('#hs-market-share-donut-chart', (mode) => ({
  	      chart: {
  	        height: 230,
  	        width: 230,
  	        type: 'donut',
  	        zoom: {
  	          enabled: false
  	        }
  	      },
  	      plotOptions: {
  	        pie: {
  	          donut: {
  	            size: '76%'
  	          }
  	        }
  	      },
  	      series: [100],
  	      labels: ['Tailwind CSS', 'Preline UI', 'Others'],
  	      legend: {
  	        show: false
  	      },
  	      stroke: {
  	        width: 0
  	      },
  	      dataLabels: {
  	        enabled: false
  	      },
  	      grid: {
  	        padding: {
  	          top: 0,
  	          bottom: 0,
  	          left: 0,
  	          right: 0
  	        }
  	      },
  	      states: {
  	        hover: {
  	          filter: {
  	            type: 'none'
  	          }
  	        }
  	      },
  	      tooltip: {
  	        enabled: true,
  	        custom: function (props) {
  	          return buildTooltipForDonut(
  	            props,
  	            mode === 'dark' ? ['#fff', '#fff', '#000'] : ['#fff', '#fff', '#000']
  	          );
  	        }
  	      }
  	    }), {
  	      colors: ['#4f46e5', '#67e8f9', '#e5e7eb'],
  	      stroke: {
  	        colors: ['rgb(255, 255, 255)']
  	      }
  	    }, {
  	      colors: ['#4f46e5', '#67e8f9', '#e5e7eb'],
  	      stroke: {
  	        colors: ['rgb(38, 38, 38)']
  	      }
  	    });
  	  })();
  	});
  </script>

  <script>
  	window.addEventListener('load', () => {
  	  // Apex Lines Chart
  	  (function () {
  	    buildChart('#hs-age-lines-chart', (mode) => ({
  	      chart: {
  	        height: 262,
  	        type: 'line',
  	        toolbar: {
  	          show: false
  	        },
  	        zoom: {
  	          enabled: false
  	        }
  	      },
  	      series: [
  	        {
  	          name: '18-15',
  	          data: [0, 0, 0, 0]
  	        },
  	        {
  	          name: '25-40',
  	          data: [0, 0, 0, 0]
  	        },
  	        {
  	          name: '40+',
  	          data: [0, 0, 0, 0]
  	        }
  	      ],
  	      dataLabels: {
  	        enabled: false
  	      },
  	      stroke: {
  	        curve: 'straight',
  	        width: [3, 3, 3],
  	        dashArray: [0, 0, 4]
  	      },
  	      grid: {
  	        strokeDashArray: 0,
  	        padding: {
  	          top: -20,
  	          right: 0
  	        }
  	      },
  	      title: {
  	        show: false
  	      },
  	      legend: {
  	        show: false
  	      },
  	      xaxis: {
  	        type: 'category',
  	        categories: [
  	          '12:00 am',
  	          '6:00 am',
  	          '12:00 pm',
  	          '6:00 pm'
  	        ],
  	        axisBorder: {
  	          show: false
  	        },
  	        axisTicks: {
  	          show: false
  	        },
  	        tooltip: {
  	          enabled: false
  	        },
  	        labels: {
  	          offsetY: 2,
  	          style: {
  	            colors: '#9ca3af',
  	            fontSize: '13px',
  	            fontFamily: 'Inter, ui-sans-serif',
  	            fontWeight: 400
  	          },
  	          formatter: (title) => {
  	            let t = title;
	            
  	            if (t) {
  	              const newT = t.split(' ');
  	              t = `${newT[0]} ${newT[1].slice(0, 3)}`;
  	            }
	            
  	            return t;
  	          }
  	        }
  	      },
  	      yaxis: {
  	        min: 0,
  	        max: 40000,
  	        tickAmount: 4,
  	        labels: {
  	          align: 'left',
  	          minWidth: 0,
  	          maxWidth: 140,
  	          style: {
  	            colors: '#9ca3af',
  	            fontSize: '12px',
  	            fontFamily: 'Inter, ui-sans-serif',
  	            fontWeight: 400
  	          },
  	          formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
  	        }
  	      },
  	      tooltip: {
  	        custom: function (props) {
  	          return buildTooltip(props, {
  	            title: 'Spends by the age',
  	            mode,
  	            hasTextLabel: true,
  	            wrapperExtClasses: 'min-w-48',
  	            labelDivider: ':',
  	            labelExtClasses: 'ms-2'
  	          });
  	        }
  	      }
  	    }), {
  	      colors: ['#4f46e5', '#67e8f9', '#d1d5db'],
  	      grid: {
  	        borderColor: '#e5e7eb'
  	      }
  	    }, {
  	      colors: ['#4f46e5', '#67e8f9', '#737373'],
  	      grid: {
  	        borderColor: '#404040'
  	      }
  	    });
  	  })();
  	});
  </script>

  <script>
  	// Apex Lines Chart
  	(function () {
  	  buildChart(
  	    "#hs-devices-bubble-chart",
  	    () => ({
  	      chart: {
  	        height: "100%",
  	        type: "bubble",
  	        toolbar: {
  	          show: false,
  	        },
  	        zoom: {
  	          enabled: false,
  	        },
  	      },
  	      series: [
  	        { data: [[5, 6, 70]] },
  	        { data: [[8, 4, 45]] },
  	        { data: [[10, 9, 90]] },
  	      ],
  	      dataLabels: {
  	        style: {
  	          fontSize: "20px",
  	          fontFamily: "Inter, ui-sans-serif",
  	          fontWeight: "400",
  	          colors: ["#fff", "#1f2937", "#fff"],
  	        },
  	        formatter: (value) => (value ? `${value}%` : ""),
  	      },
  	      fill: {
  	        opacity: 1,
  	      },
  	      legend: {
  	        show: false,
  	      },
  	      plotOptions: {
  	        bubble: {
  	          zScaling: false,
  	          minBubbleRadius: 40,
  	        },
  	      },
  	      grid: {
  	        show: false,
  	        padding: {
  	          top: 0,
  	          bottom: 0,
  	          left: 0,
  	          right: 0,
  	        },
  	      },
  	      xaxis: {
  	        min: 0,
  	        max: 15,
  	        labels: {
  	          show: false,
  	        },
  	        axisBorder: {
  	          show: false,
  	        },
  	        axisTicks: {
  	          show: false,
  	        },
  	      },
  	      yaxis: {
  	        min: 0,
  	        max: 15,
  	        labels: {
  	          show: false,
  	        },
  	      },
  	      tooltip: {
  	        enabled: false,
  	      },
  	      states: {
  	        hover: {
  	          filter: {
  	            type: "none",
  	          },
  	        },
  	      },
  	    }),
  	    {
  	      colors: ["#67e8f9", "#e5e7eb", "#4f46e5"],
  	      markers: {
  	        strokeColors: "rgb(255, 255, 255)",
  	      },
  	    },
  	    {
  	      colors: ["#67e8f9", "#e5e7eb", "#4f46e5"],
  	      markers: {
  	        strokeColors: "rgb(38, 38, 38)",
  	      },
  	    }
  	  );
  	})();
  </script>

  <script>
  	window.addEventListener('load', () => {
  	  // Apex Area Chart
  	  (function () {
  	    buildChart('#hs-total-sales', (mode) => ({
  	      chart: {
  	        height: 230,
  	        type: 'area',
  	        toolbar: {
  	          show: false
  	        },
  	        zoom: {
  	          enabled: false
  	        }
  	      },
  	      series: [
  	        {
  	          name: 'First-time',
  	          data: [0, 0, 0, 0]
  	        },
  	        {
  	          name: 'Returning',
  	          data: [0, 0, 0, 0]
  	        }
  	      ],
  	      legend: {
  	        show: false
  	      },
  	      dataLabels: {
  	        enabled: false
  	      },
  	      stroke: {
  	        curve: 'straight',
  	        width: 2
  	      },
  	      fill: {
  	        type: 'gradient',
  	        gradient: {
  	          type: 'vertical',
  	          shadeIntensity: 1,
  	          opacityFrom: 0.2,
  	          opacityTo: 0.8
  	        }
  	      },
  	      xaxis: {
  	        type: 'category',
  	        tickPlacement: 'on',
  	        categories: [
  	          'Jan',
  	          'May',
  	          'Sep',
  	          'Dec'
  	        ],
  	        axisBorder: {
  	          show: false
  	        },
  	        axisTicks: {
  	          show: false
  	        },
  	        crosshairs: {
  	          stroke: {
  	            dashArray: 0
  	          },
  	          dropShadow: {
  	            show: false
  	          }
  	        },
  	        tooltip: {
  	          enabled: false
  	        },
  	        labels: {
  	          style: {
  	            colors: '#9ca3af',
  	            fontSize: '13px',
  	            fontFamily: 'Inter, ui-sans-serif',
  	            fontWeight: 400
  	          }
  	        }
  	      },
  	      yaxis: {
  	        labels: {
  	          align: 'left',
  	          minWidth: 0,
  	          maxWidth: 140,
  	          style: {
  	            colors: '#9ca3af',
  	            fontSize: '12px',
  	            fontFamily: 'Inter, ui-sans-serif',
  	            fontWeight: 400
  	          },
  	          formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
  	        }
  	      },
  	      tooltip: {
  	        x: {
  	          format: 'MMMM yyyy'
  	        },
  	        y: {
  	          formatter: (value) => `$${value >= 1000 ? `${value / 1000}k` : value}`
  	        },
  	        custom: function (props) {
  	          return buildTooltipCompareTwoAlt(props, {
  	            title: 'Sales',
  	            mode,
  	            hasCategory: false,
  	            hasTextLabel: true,
  	            wrapperExtClasses: 'min-w-48'
  	          });
  	        }
  	      },
  	      responsive: [{
  	        breakpoint: 568,
  	        options: {
  	          chart: {
  	            height: 300
  	          },
  	          labels: {
  	            style: {
  	              colors: '#9ca3af',
  	              fontSize: '11px',
  	              fontFamily: 'Inter, ui-sans-serif',
  	              fontWeight: 400
  	            },
  	            offsetX: -2,
  	            formatter: (title) => title.slice(0, 3)
  	          },
  	          yaxis: {
  	            labels: {
  	              align: 'left',
  	              minWidth: 0,
  	              maxWidth: 140,
  	              style: {
  	                colors: '#9ca3af',
  	                fontSize: '11px',
  	                fontFamily: 'Inter, ui-sans-serif',
  	                fontWeight: 400
  	              },
  	              formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
  	            }
  	          },
  	        },
  	      }]
  	    }), {
  	      colors: ['#4f46e5', '#22d3ee'],
  	      fill: {
  	        gradient: {
  	          stops: [0, 90, 100]
  	        }
  	      },
  	      grid: {
  	        strokeDashArray: 2,
  	        borderColor: '#e5e7eb'
  	      }
  	    }, {
  	      colors: ['#6366f1', '#22d3ee'],
  	      fill: {
  	        gradient: {
  	          stops: [100, 90, 0]
  	        }
  	      },
  	      grid: {
  	        strokeDashArray: 2,
  	        borderColor: '#404040'
  	      }
  	    });
  	  })();
  	});
  </script>

  <script>
  	window.addEventListener('load', () => {
  	  // Apex Pie Chart
  	  (function () {
  	    buildChart('#hs-acquisition-pie-chart', () => ({
  	      chart: {
  	        height: '100%',
  	        type: 'pie',
  	        zoom: {
  	          enabled: false
  	        }
  	      },
  	      series: [100],
  	      labels: ['Direct', 'Organic search', 'Referral'],
  	      title: {
  	        show: false
  	      },
  	      stroke: {
  	        width: 0
  	      },
  	      dataLabels: {
  	        style: {
  	          fontSize: '20px',
  	          fontFamily: 'Inter, ui-sans-serif',
  	          fontWeight: '400',
  	          colors: ['#fff', '#fff', '#1f2937']
  	        },
  	        dropShadow: {
  	          enabled: false
  	        },
  	        formatter: (value) => `${value.toFixed(1)} %`
  	      },
  	      plotOptions: {
  	        pie: {
  	          dataLabels: {
  	            offset: -15
  	          }
  	        }
  	      },
  	      legend: {
  	        show: false
  	      },
  	      grid: {
  	        padding: {
  	          top: 0,
  	          bottom: 0,
  	          left: 0,
  	          right: 0
  	        }
  	      },
  	      tooltip: {
  	        enabled: false
  	      },
  	      states: {
  	        hover: {
  	          filter: {
  	            type: 'none'
  	          }
  	        }
  	      }
  	    }), {
  	      colors: ['#4f46e5', '#67e8f9', '#e5e7eb'],
  	      stroke: {
  	        colors: ['rgb(255, 255, 255)']
  	      }
  	    }, {
  	      colors: ['#4f46e5', '#67e8f9', '#e5e7eb'],
  	      stroke: {
  	        colors: ['rgb(38, 38, 38)']
  	      }
  	    });
  	  })();
  	});
  </script>

</body>
</html>