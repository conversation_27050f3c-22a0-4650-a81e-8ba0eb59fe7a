!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo={type:"Topology",objects:{mrt:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:null},id:"-99",arcs:[[0]]},{type:"Polygon",properties:{name:"Tiris Zemmour"},id:"MR.TZ",arcs:[[1,2]]},{type:"Polygon",properties:{name:"Brakna"},id:"MR.BR",arcs:[[3,4,5,6,7]]},{type:"MultiPolygon",properties:{name:"Dakhlet Nouadhibou"},id:"MR.DN",arcs:[[[8]],[[9]],[[10]],[[11]],[[12,13,14]]]},{type:"Polygon",properties:{name:"Inchiri"},id:"MR.IN",arcs:[[15,16,17,-14]]},{type:"Polygon",properties:{name:"Nouakchott"},id:"MR.NO",arcs:[[18,19]]},{type:"Polygon",properties:{name:"Trarza"},id:"MR.TR",arcs:[[-7,20,-20,21,-17,22]]},{type:"Polygon",properties:{name:"Assaba"},id:"MR.AS",arcs:[[23,24,25,26,-4,27]]},{type:"Polygon",properties:{name:"Guidimaka"},id:"MR.GD",arcs:[[28,29,-26]]},{type:"Polygon",properties:{name:"Gorgol"},id:"MR.GO",arcs:[[-30,30,-5,-27]]},{type:"Polygon",properties:{name:"Adrar"},id:"MR.AD",arcs:[[31,32,-23,-16,-13,33,-2,34]]},{type:"Polygon",properties:{name:"Hodh ech Chargui"},id:"MR.HC",arcs:[[35,36,-32,37]]},{type:"Polygon",properties:{name:"Hodh el Gharbi"},id:"MR.HG",arcs:[[-36,38,-24,39]]},{type:"Polygon",properties:{name:"Tagant"},id:"MR.TG",arcs:[[-40,-28,-8,-33,-37]]}]}},arcs:[[[442,4140],[16,5],[2,-12],[0,-8],[-18,-3],[2,7],[-2,11]],[[8625,7601],[-1,0],[-3491,-2195],[-1297,-134],[-511,176],[-21,7],[-2,0]],[[3302,5455],[-23,330],[-13,199],[-13,199],[-11,52],[-40,102],[-8,51],[10,54],[27,50],[70,89],[15,18],[82,51],[102,64],[88,55],[51,32],[50,15],[137,18],[30,8],[178,77],[84,20],[10,13],[4,27],[0,65],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,61],[0,60],[0,60],[0,60],[0,60],[0,61],[0,60],[82,0],[82,0],[82,0],[82,0],[82,0],[83,0],[82,0],[82,0],[82,0],[82,0],[82,0],[83,0],[82,0],[82,0],[82,0],[82,0],[82,0],[82,0],[83,0],[82,0],[82,0],[82,0],[82,0],[82,0],[83,0],[82,0],[82,0],[82,0],[82,0],[82,0],[82,0],[83,0],[69,0],[16,4],[5,10],[0,36],[0,11],[0,31],[0,49],[0,62],[0,74],[0,83],[0,88],[0,91],[0,91],[0,89],[0,82],[-1,74],[0,63],[0,48],[0,31],[0,11],[162,-93],[161,-92],[161,-93],[162,-93],[161,-93],[162,-92],[161,-93],[161,-93],[162,-92],[161,-93],[162,-93],[161,-93],[137,-78],[137,-79],[136,-78],[117,-67],[20,-12],[99,-57],[141,-85],[142,-85],[141,-85],[142,-86],[-181,0],[-180,0],[-181,0],[-180,0],[-181,0],[-181,0],[-180,0],[-181,0],[8,-64],[8,-63],[8,-64],[8,-63],[8,-64],[7,-63],[8,-63],[8,-64],[8,-65]],[[3891,2024],[-13,-6],[-9,-9],[-1,-14],[-8,-9],[-32,-5],[-26,-10],[-15,-15],[-25,-3],[-19,-14],[-21,-38],[-22,-9],[-17,-4],[-15,-7],[-9,-6],[-10,-5],[-10,3],[-11,0],[-37,-11],[-7,-7],[-9,-7],[-24,-6],[-59,-63],[-9,-14],[-6,-17],[1,-28],[-5,-28]],[[3473,1692],[-2,-29],[-39,-53],[-15,-31],[-13,-42],[-20,-38],[-68,-25],[-77,-7],[-39,-44],[-5,-64],[-15,-21],[-332,-84],[-35,-15],[-10,-32],[-4,-35],[-16,-19],[-23,-12],[-17,-6]],[[2743,1135],[4,14],[2,10],[-8,1],[-7,-1],[-7,-2],[-7,-3],[-7,-6],[-14,-20],[-6,-6],[-13,-9],[-13,-7],[-7,-3],[-6,0],[-7,1],[-7,5],[-7,6],[-5,7],[-4,7],[-7,22],[-4,6],[-5,6],[-13,9],[-7,4],[-32,8],[-14,8],[-3,12],[4,15],[0,6],[-3,7],[-8,10],[-2,6],[1,6],[6,13],[-1,5],[-5,6],[-7,3],[-16,6],[-8,5],[-14,16],[-5,5],[-28,14],[-48,42],[-28,30],[-19,13],[-6,5],[-14,17],[-6,4],[-10,2],[-19,1],[-9,4],[-4,6],[-1,14],[-3,6],[-8,4],[-31,-3],[-7,4],[-2,6],[4,15],[1,8],[-4,6],[-6,4],[-52,20],[-16,-1],[-7,-3],[-22,-12],[-8,-4],[-8,-2],[-9,-1],[-9,0],[-33,7],[-9,0],[-34,-9],[-9,-1],[-9,2],[-23,10],[-7,2],[-8,0],[-28,-7],[-9,-1],[-139,9],[-25,-5],[-15,2],[-9,10],[-6,11]],[[1739,1542],[13,16],[80,49],[112,58],[63,25],[25,21],[17,22],[5,4],[198,138],[295,411],[183,156],[229,159],[640,444]],[[3599,3045],[155,-225],[-23,-220],[-11,-134],[3,0],[-23,-269],[110,-109],[81,-64]],[[560,3876],[-3,0],[5,14],[27,48],[31,41],[9,1],[7,-6],[-10,-14],[-13,-12],[-10,-11],[-14,-23],[-29,-38]],[[492,3944],[-5,-6],[-6,5],[-4,17],[4,16],[12,5],[6,-1],[0,-8],[-2,-16],[-5,-12]],[[607,4080],[-25,-107],[-20,-50],[-33,-46],[-14,17],[-3,6],[1,6],[2,7],[0,8],[-6,8],[-6,15],[6,15],[23,32],[2,4],[-1,23],[1,7],[7,15],[10,12],[12,4],[11,-10],[16,32],[10,12],[7,-10]],[[516,4653],[-5,-3],[-10,4],[-6,14],[4,12],[8,5],[7,-2],[4,-4],[-2,-26]],[[2272,5257],[100,-150]],[[2372,5107],[-1360,5],[214,-330],[-530,-912],[-45,-77],[-30,-34],[-5,2]],[[616,3761],[2,2],[20,31],[4,15],[7,11],[2,5],[-2,6],[-6,3],[-5,0],[-3,-3],[-4,0],[-45,-46],[-23,-17],[33,66],[-12,-6],[-33,-26],[-11,-12],[-6,-7],[-5,-9],[-1,-10],[4,-9],[0,-5],[-7,-15],[-2,-8],[-2,-2],[-5,-1],[-5,0],[-5,1],[-4,2],[0,1],[0,3],[-1,4],[-2,7],[1,19],[1,7],[7,12],[22,21],[4,14],[3,17],[8,8],[11,6],[12,9],[78,110],[5,4],[5,3],[5,5],[2,7],[1,16],[2,6],[5,2],[2,3],[14,15],[4,5],[0,8],[-2,7],[-4,6],[-5,6],[6,5],[4,6],[2,8],[-1,8],[-13,-7],[-4,-4],[-2,13],[11,19],[-3,12],[-16,8],[-10,-7],[-6,-11],[-2,-7],[-13,-7],[-6,3],[-1,10],[3,11],[5,8],[5,7],[48,45],[13,20],[7,23],[-3,20],[-25,40],[-10,22],[6,0],[6,0],[5,2],[5,4],[18,26],[7,16],[3,15],[-2,15],[-6,12],[-23,33],[-5,2],[-12,1],[-7,4],[-4,7],[-3,8],[-3,3],[-13,7],[-23,53],[-7,7],[-7,5],[-6,5],[-2,7],[2,3],[5,2],[3,2],[-1,4],[-3,4],[-5,9],[-4,15],[-30,75],[-8,11],[-12,4],[-7,10],[-1,20],[-4,14],[-14,-6],[-16,10],[0,6],[37,10],[10,5],[-16,2],[-29,-5],[-13,3],[-11,13],[6,5],[-12,22],[-6,4],[-17,7],[-6,-35],[7,-77],[-12,-30],[-8,12],[-10,9],[-21,12],[-7,7],[-10,18],[-8,10],[-10,7],[-26,12],[-6,6],[0,9],[3,4],[6,1],[2,2],[0,7],[-5,6],[-1,6],[-4,12],[-9,10],[-22,16],[-4,6],[1,15],[-3,7],[-14,13],[-9,38],[-5,9],[-9,10],[-5,11],[-4,13],[-1,12],[-4,-4],[-5,-4],[-2,-3],[-12,11],[-5,12],[-2,13],[-4,13],[-5,6],[-8,6],[-7,7],[-3,12],[-4,7],[-19,25],[-5,11],[-2,11],[-4,13],[-10,23],[-16,17],[-11,-6],[-21,-36],[-35,-44],[-8,-6],[-5,-10],[6,-14],[10,4],[1,-13],[-6,-29],[-1,-26],[-4,-7],[-8,-5],[-18,-5],[-7,-6],[-1,-7],[4,-6],[8,-9],[6,-12],[0,-6],[0,-9],[-8,-34],[-7,-17],[-8,-9],[-20,86],[3,24],[19,72],[6,25],[34,92],[16,76],[14,69],[8,7],[94,0],[34,0],[95,0],[148,0],[192,0],[228,0],[253,0],[272,0],[280,0],[280,0],[271,0],[25,0]],[[2372,5107],[15,-21],[-53,-1346],[-1,-121],[-1,-220]],[[2332,3399],[-33,0],[-1589,-4]],[[710,3395],[-23,52],[-36,55],[-48,52],[-15,14],[-4,1],[-3,6],[-6,1],[-8,1],[-7,3],[-14,9],[-32,9],[-13,7],[-6,9],[-14,34],[-2,14],[-9,15],[-16,13],[-9,12],[11,12],[48,-12],[45,6],[40,22],[27,31]],[[856,2511],[6,59],[-9,144],[-20,199]],[[833,2913],[8,0],[219,2],[1,-182],[-1,-222],[-204,0]],[[1739,1542],[-1,2],[-10,11],[-10,4],[-18,-7],[-8,-5],[-5,-8],[-3,-11],[-2,-5],[-4,-5],[-5,-3],[-6,-3],[-6,0],[-6,2],[-8,7],[-7,9],[-7,8],[-12,3],[-11,-3],[-7,-7],[-2,-11],[5,-10],[13,-13],[3,-5],[-1,-6],[-3,-4],[-5,-3],[-18,-7],[-87,-13],[-13,0],[-39,6],[-26,-1],[-13,-3],[-34,-13],[-12,-3],[-12,0],[-10,4],[-5,7],[-5,9],[-8,5],[-12,2],[-12,-6],[-10,-8],[-20,-27],[-8,-7],[-9,-4],[-12,-1],[-35,6],[-13,-1],[-9,-6],[-19,-16],[-11,-6],[-12,-4],[-12,-1],[-13,3],[-19,9],[-7,2],[-27,1],[-37,12],[-13,0],[-20,-3],[-7,0],[-28,6],[-12,-1],[-32,-14],[-6,-1],[-5,1],[-11,4],[-6,1],[-7,-1],[-5,-3],[-6,-3],[-20,-3],[-18,2],[-17,7],[-13,14],[-10,14],[-6,6],[-8,4],[-10,2],[-10,-1],[-9,-5],[-24,-16],[-9,-3],[-9,-2],[-9,1],[-27,5],[-19,-4],[-17,-10],[-15,-14],[-11,-15],[-6,-15],[-5,-31],[-33,-135],[-3,-10],[-6,-8],[-12,-8],[-30,-6],[-12,-10],[-6,-16],[-4,-54],[-14,-32],[-11,-35],[-5,-9],[-2,-87],[-8,-44],[-19,-30],[13,134],[-2,71],[-8,160],[8,52],[6,15],[23,40],[6,40],[7,12],[9,21],[0,85],[6,35],[66,157],[17,27],[12,54],[14,26],[39,52],[70,143],[5,19],[11,15],[76,225],[25,97],[6,106],[8,69]],[[833,2913],[-8,74],[-9,29],[-42,86],[-19,99],[-14,32],[7,19],[-1,22],[-32,111],[-5,10]],[[2332,3399],[797,0],[470,-354]],[[4895,2381],[-10,-26],[-2,-26],[13,-26],[16,-81],[55,-105],[4,-28],[-6,-29],[11,-29],[-5,-15],[-7,-15],[-13,-68],[7,-16],[14,-12],[4,-21],[-13,-95],[0,-23],[5,-21],[0,-14],[-3,-14],[12,-19],[-7,-12],[-8,-10],[6,-17],[17,-11],[17,-20],[4,-12],[7,-10],[15,-1],[15,-7],[13,-13],[8,-14],[-2,-27],[12,-24],[17,-22],[25,-11],[13,-2],[13,-4],[24,12],[9,-10],[13,4],[12,3],[5,-8],[-1,-10],[-10,-11],[-19,-2],[-1,-19],[-18,-12],[-7,-14],[-43,-22],[-16,0],[-15,-4],[-11,-9],[8,-18],[33,-10],[10,-6],[2,-17],[-7,-17],[-7,-8],[-5,-8],[-4,-23],[-5,-11],[-3,-12],[11,-19],[-7,-11],[3,-13],[27,-9],[23,-17],[13,-24],[2,-27],[-4,-22],[-1,-22],[-10,-14],[0,-22],[-3,-5],[17,-16],[51,-39],[24,-30],[67,-406]],[[5299,553],[-3,-1],[-16,-2],[-34,0],[-49,7],[-13,0],[-9,-9],[-3,-15],[1,-33],[-7,-17],[-6,-6],[-13,-7],[-9,-13],[-7,-1],[-8,0],[-9,-3],[-13,-12],[-6,-13],[-8,-30],[-9,-13],[-22,-20],[-7,-13],[-13,-45],[-7,-13],[-27,39],[-30,28],[-23,41],[-26,25],[-206,144],[-16,16],[-75,104],[-21,17],[-19,8],[-46,9],[-8,-1],[-2,0],[1,-4],[2,-2],[3,-2],[4,-2],[1,-4],[1,-4],[-1,-4],[-1,-4],[-8,-8],[-9,-4],[-22,-7],[-14,-7],[-29,-29],[-23,-12],[-22,2],[-22,6],[-3,1]],[[4388,640],[6,33],[20,37],[5,41],[10,39],[31,33],[8,20],[4,19],[0,11],[5,10],[19,13],[10,20],[-2,7],[-2,9],[6,12],[9,10],[22,11],[13,19],[-87,13],[-57,36],[-12,2],[-13,-2],[-14,2],[-10,9],[-43,-5],[-9,8],[-10,5],[-7,-12],[4,-19],[9,-17],[7,-18],[-5,-13],[-12,-7],[-11,-4],[-5,-13],[2,-13],[-17,0],[-22,2],[-23,-2],[-21,-6],[-15,9],[-16,17],[-25,4]],[[4140,960],[-12,32],[8,58],[-19,-1],[-15,4],[-9,12],[-29,28],[-36,11],[-6,-5],[-4,-5],[-13,4],[-13,8],[-29,-1],[-10,16],[-5,63],[-13,13],[-18,4],[-13,8],[-15,4],[-16,-5],[-15,-8],[-31,-25],[-32,-3],[-4,24],[17,55],[-2,32],[-4,13],[-3,27],[-6,13],[1,25],[-31,9],[-155,25],[-21,-7],[-18,-16],[-11,-3],[-11,5],[-14,1],[-10,-1],[-4,14],[0,14],[16,13],[9,18],[0,16],[-13,10],[-6,18],[-3,19],[-13,24],[-25,13],[-18,12],[-4,19],[1,36],[12,33],[15,30],[-17,29]],[[3891,2024],[79,-70],[131,-46],[117,-34],[128,42],[149,-12],[35,42],[0,52],[7,112],[21,44],[-13,44],[32,13],[-4,30],[-33,8],[25,14],[26,16],[25,6],[5,24],[-12,22],[5,5],[-1,7],[-8,13],[-15,20],[-31,30],[-5,10],[-26,40],[19,-2],[-31,56],[-20,33],[-5,8],[-17,15],[12,24],[26,13],[1,39],[46,-3],[4,25],[24,1],[52,-1],[22,-4],[9,2],[7,-7],[19,-2],[21,13],[47,14],[23,-2],[7,16],[28,36],[-21,19],[23,16],[25,29],[26,17],[60,24],[23,-2],[26,16],[-89,-468]],[[4388,640],[-21,3],[0,-27],[-2,-9],[-21,-22],[-10,-25],[-13,-57],[-27,-56],[-4,-12],[-4,-38],[-16,-43],[-2,-16],[1,-8],[2,-11],[0,-8],[-10,-32],[-2,-24],[-1,-4],[8,-6],[5,2],[4,4],[6,0],[9,-8],[6,-9],[2,-11],[-1,-15],[-6,-12],[-5,-14],[-4,-29],[1,-5],[2,-4],[6,-7],[0,-5],[-5,-3],[-6,-1],[-3,-2],[-3,-3],[-4,-4],[-4,-6],[-3,-7],[-7,-11],[-12,-10],[-15,-7],[-33,-7],[-11,-10],[-20,-25],[-14,-9],[-16,-4],[-16,0],[-15,3],[-6,-4],[-5,-13],[-6,-9],[-9,4],[-35,30],[-12,6],[-17,-3],[-36,-12],[-17,-2],[-18,3],[-14,6],[-45,34],[-52,23],[-14,11],[-28,31],[-11,7],[0,8],[-19,64],[-4,8],[-7,7],[-9,5],[-19,4],[-9,5],[-14,10],[-45,25],[-10,13],[-7,6],[-7,3],[-13,-2],[-26,-8],[-8,-1],[-1,0],[-22,9],[-24,22]],[[3535,316],[34,111],[25,53],[71,109],[45,53],[17,9],[14,13],[6,32],[20,40],[138,119],[18,23],[10,20],[18,15],[36,10],[42,-6],[54,25],[57,18]],[[3535,316],[-6,6],[-21,6],[-1,2],[-2,8],[-1,6],[5,20],[0,7],[-3,6],[-4,5],[-7,5],[-8,2],[-9,0],[-17,-3],[-8,0],[-8,2],[-7,6],[-18,17],[-5,10],[8,6],[28,-2],[13,2],[5,11],[-5,13],[-11,10],[-48,32],[-10,10],[-7,12],[-3,13],[0,27],[-2,6],[-3,6],[-9,10],[-2,5],[-4,22],[-2,7],[-5,6],[-13,6],[-13,-2],[-12,-6],[-11,-8],[-12,-8],[-12,-1],[-25,6],[-4,4],[-3,7],[-2,7],[0,7],[5,30],[-2,11],[-6,9],[-9,8],[-10,5],[-19,5],[-6,2],[-15,11],[-6,2],[-12,1],[-24,-4],[-12,-1],[-8,20],[-1,7],[1,7],[3,6],[12,19],[1,5],[-1,6],[-3,7],[-9,13],[-6,5],[-6,5],[-13,7],[-6,4],[-4,6],[-1,7],[2,6],[3,7],[2,7],[-1,15],[-4,14],[-7,12],[-15,16],[-3,5],[-2,5],[0,7],[3,13],[1,7],[-2,7],[-5,6],[-20,15],[-8,7],[-6,7],[-5,9],[-11,27],[-1,6],[-1,14],[-2,6],[-5,5],[-23,15],[-4,4],[-6,11],[-4,4],[-13,1],[-29,-11],[-9,5],[2,12],[14,25],[-2,11],[-5,1],[-5,0],[-4,-2],[-12,-4],[-42,-9],[-25,-10],[-6,-2],[-3,0],[-16,0],[-4,-1],[-23,-11],[-5,-1],[-6,0],[-1,1],[-6,5],[-2,1],[-5,2],[-2,1],[-1,1],[-1,3],[-1,1],[-4,3],[-8,3],[-4,2],[-3,9],[4,10]],[[8735,6684],[-1,-1],[-5,-12],[-1301,-2672],[-11,-23]],[[7417,3976],[-2468,-11],[-17,0],[-301,-229],[-16,-12],[-19,-135],[-4,-29],[-185,93],[-174,-111],[-368,-224],[-94,-96],[-172,-177]],[[2272,5257],[228,0],[228,0],[192,0],[148,0],[95,0],[34,0],[119,0],[-9,129],[-5,69]],[[8625,7601],[0,-4],[6,-49],[13,-109],[13,-108],[6,-49],[12,-97],[11,-96],[12,-96],[11,-96],[12,-96],[11,-96],[3,-21]],[[6588,606],[1,0],[1,2],[5,5],[29,28],[-8,253],[-46,94],[-34,70],[162,207],[-20,55],[117,112],[52,55],[137,143],[187,193],[-140,145],[-112,116],[-450,-54]],[[6469,2030],[2,4],[946,1942]],[[8735,6684],[9,-75],[11,-97],[12,-96],[11,-96],[12,-96],[11,-96],[12,-96],[11,-96],[12,-97],[11,-96],[12,-96],[12,-96],[11,-96],[12,-96],[11,-96],[12,-96],[11,-97],[12,-96],[5,-46],[12,-103],[13,-103],[5,-46],[11,-86],[11,-100],[12,-99],[12,-100],[12,-100],[11,-99],[12,-100],[12,-99],[11,-100],[13,-110],[5,-46],[5,-47],[5,-46],[6,-46],[8,-74],[8,-74],[9,-74],[8,-74],[8,-74],[8,-74],[9,-74],[8,-74],[8,-73],[9,-74],[8,-74],[8,-74],[8,-72],[8,-72],[9,-72],[8,-72],[8,-71],[8,-72],[8,-72],[8,-72],[8,-72],[8,-72],[8,-72],[8,-72],[8,-71],[8,-72],[8,-72],[9,-72],[3,-32],[7,-13],[7,-13],[32,-21],[30,-19],[37,-24],[32,-21],[33,-22],[33,-21],[4,-4],[4,-4],[1,-5],[0,-5],[-4,-24],[-15,-75],[-15,-75],[-15,-76],[-15,-75],[-15,-78],[-15,-77],[-16,-77],[-15,-78],[-1,-4],[0,-3],[-1,-1],[0,-3],[-1,-4],[0,-1],[0,-1],[-1,0],[-1,0],[-1,0],[-1,0],[-129,0],[-109,0],[-51,0],[-56,0],[-59,0],[-63,1],[-109,0],[-80,0],[-37,0],[-123,0],[-129,0],[-133,0],[-136,0],[-139,0],[-140,0],[-140,0],[-138,0],[-137,0],[-133,0],[-129,0],[-123,0],[-117,0],[-109,0],[-70,0],[-65,0],[-60,0],[-55,0],[-76,0]],[[6588,606],[-12,0],[-100,0],[-28,0],[-142,0],[5,73],[11,55],[-2,25],[-20,9],[-16,-10],[-49,-57],[-12,-21],[0,-23],[13,-50],[9,-44],[-202,-8],[-40,-8],[-16,-7],[-46,-25],[-31,-8],[-95,8],[-95,-13],[-33,1],[-19,5],[-30,18],[-16,8],[-6,0],[-15,-2],[-8,0],[-69,26],[-12,2],[-39,-3],[-22,4],[-84,1],[-12,-2],[-16,3],[-13,-2],[-27,-8]],[[4895,2381],[261,-71],[348,-92],[498,30],[6,-4],[461,-214]]],transform:{scale:[.0012260787846784676,.0012552272072207226],translate:[-17.0811748859999,14.734398906000123]}},m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",
m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();