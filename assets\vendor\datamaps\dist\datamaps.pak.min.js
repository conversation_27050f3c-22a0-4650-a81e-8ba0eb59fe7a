!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo={type:"Topology",objects:{pak:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Baluchistan"},id:"PK.BA",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Azad Kashmir"},id:"PK.JK",arcs:[[4,5,6,7]]},{type:"Polygon",properties:{name:"F.C.T."},id:"PK.IS",arcs:[[8,9]]},{type:"Polygon",properties:{name:"Northern Areas"},id:"PK.NA",arcs:[[-8,10,11]]},{type:"Polygon",properties:{name:"F.A.T.A."},id:"PK.TA",arcs:[[12,-4,13,14]]},{type:"Polygon",properties:{name:"Punjab"},id:"PK.PB",arcs:[[-10,15,-6,16,17,-1,-13,18]]},{type:"MultiPolygon",properties:{name:"Sind"},id:"PK.SD",arcs:[[[19]],[[20,-2,-18]]]},{type:"Polygon",properties:{name:"N.W.F.P."},id:"PK.NW",arcs:[[-11,-7,-16,-9,-19,-15,21]]}]}},arcs:[[[5797,5522],[-8,-148],[7,-22],[1,-10],[-1,-8],[-3,-4],[-4,-2],[-11,-1],[-4,-2],[-51,-72],[-2,-2],[-3,-1],[-15,-1],[-4,-2],[-5,-6],[-4,-10],[-4,-21],[-1,-10],[0,-6],[1,-3],[6,-12],[2,-4],[1,-5],[-2,-7],[-3,-9],[-54,-95],[-6,-20],[-6,-41],[-11,-46],[-5,-10],[-2,-6],[0,-5],[2,-4],[4,-2],[6,-3],[3,-2],[1,-3],[1,-3],[-1,-16],[1,-5],[0,-3],[1,-2],[2,-2],[2,-1],[2,1],[2,2],[6,11],[2,3],[2,2],[2,2],[5,2],[2,1],[2,0],[7,0],[7,-1],[3,-1],[4,-2],[2,-4],[-1,-10],[-3,-14],[-16,-33],[-4,-5],[-7,-7],[-10,-10],[-3,-5],[-3,-9],[-2,-14],[-1,-8],[0,-18],[-8,-28],[-33,-71],[-29,-79],[-6,-12],[-6,-7],[-12,-10],[-15,-10],[-3,-4],[-20,-34],[-13,-12],[-37,-6],[-12,-5],[-11,-6],[-2,-1],[-3,-3],[-3,-4],[-7,-13],[-3,-8],[-2,-10],[0,-22],[1,-24],[-18,-93],[0,-9],[2,-4],[2,-3],[3,-1],[3,-1],[27,-3],[6,-2],[3,-2],[5,-3],[4,-5],[2,-2],[2,-3],[1,-4],[1,-8],[-1,-10],[-2,-19],[0,-9],[1,-5],[13,-21],[2,-2],[2,-2],[3,0],[2,2],[2,2],[4,4],[4,5],[2,1],[3,2],[3,1],[3,0],[3,-1],[2,-3],[1,-8],[-1,-12],[-22,-113],[-7,-15],[-5,-6],[-3,-3],[-9,-4],[-4,-4],[-5,-4],[-8,-9],[-30,-53],[-14,-17],[-8,-8],[-17,-14],[-2,-2],[-4,-5],[-3,-8],[-4,-13],[-3,-19],[-1,-9],[0,-6],[1,-3],[1,-2],[1,-2],[4,-4],[6,-7],[2,-2],[2,-4],[1,-5],[-3,-14],[-34,-85],[-14,-25],[-16,-23],[-5,-4],[-4,-4],[-11,-6],[-23,-9],[-4,-2],[-3,-3],[-3,-5],[-2,-6],[-1,-6],[0,-4],[1,-4],[32,-53]],[[5270,3587],[-82,-3],[-294,-14],[-106,8],[-63,-9],[-18,-6],[-4,-2],[-5,-4],[-8,-8],[-4,-5],[-31,-55],[-2,-2],[-2,-2],[-4,-3],[-4,-3],[-53,-24],[-28,-22],[-11,-13],[-136,-112],[-18,-23],[-28,-45],[-149,-73],[-80,-22],[-69,-10],[-25,-15],[-7,-14],[-30,-75],[-42,-61],[-42,-79],[-8,-19],[-4,-14],[-4,-25],[-23,-109],[-4,-35],[-1,-25],[7,-73],[5,-315],[1,-61],[4,-37],[7,-33],[5,-16],[158,-320],[6,-27],[2,-20],[-10,-67],[0,-14],[4,-39],[0,-20],[-3,-23],[-7,-26],[-31,-92],[-9,-16],[-19,-24],[-22,-29],[-10,-20],[-3,-12],[-5,-6],[-5,-5],[-16,-10],[-5,-7],[-32,-63],[-3,-9],[-2,-7],[1,-5],[2,-15],[1,-8],[-1,-7],[-2,-9],[-16,-45],[-4,-8],[-4,-6],[-11,-10],[-18,-17],[-22,-29],[-6,-9],[-3,-7],[-1,-4],[0,-4],[1,-14],[-1,-6],[-3,-12],[-6,-11],[-12,-20],[-15,-18],[-9,-6],[-8,-5],[-8,-3],[-25,-4],[-3,0],[-4,-2],[-8,-5],[-97,-88]],[[3616,896],[0,5],[-14,9],[0,29],[9,57],[2,45],[3,14],[6,12],[11,17],[4,12],[-1,17],[-10,23],[-2,14],[-4,6],[-23,22],[-9,5],[-7,6],[-32,35],[-22,15],[-8,12],[4,15],[16,-5],[7,15],[-2,20],[-15,10],[-8,13],[-5,26],[-7,20],[-11,-7],[-2,3],[-2,3],[-1,4],[-8,-3],[-3,5],[-3,10],[-3,8],[-21,25],[-4,11],[-15,-5],[-15,1],[-10,9],[2,16],[-10,-8],[-13,-5],[-22,-2],[-7,1],[-13,4],[-7,0],[-3,-2],[-3,-3],[-6,-4],[-22,-5],[-13,-9],[-10,-11],[-7,-13],[-11,-26],[-4,-4],[-6,-3],[-7,-7],[-6,-8],[-3,-8],[16,1],[49,9],[8,3],[6,6],[9,12],[2,2],[3,2],[2,0],[4,1],[2,2],[0,5],[-1,5],[1,4],[13,5],[18,3],[17,-1],[7,-5],[3,-8],[7,-4],[8,-2],[7,-4],[5,-7],[9,-16],[7,-8],[9,8],[12,3],[12,-2],[10,-9],[-21,-20],[24,-18],[9,-12],[-8,-12],[-9,2],[-11,8],[-18,16],[-48,24],[-29,8],[-21,-6],[-16,4],[-20,-6],[-20,-9],[-18,-4],[-55,-1],[-40,-12],[-49,-9],[-27,1],[-14,-1],[-21,-12],[-15,-5],[-21,-13],[-26,-5],[-32,-18],[-12,-2],[-14,2],[-88,27],[-12,2],[-14,-4],[-14,-6],[-14,-4],[-11,3],[3,4],[5,12],[-20,-2],[-16,-4],[-14,-6],[-13,-9],[-20,12],[-17,-8],[-14,-17],[-14,-27],[-5,-6],[-6,-3],[-9,-2],[-7,0],[-16,4],[-17,3],[-19,6],[-11,2],[-38,0],[-37,8],[-19,1],[-17,-9],[-14,7],[-20,2],[-39,-3],[-15,-8],[-14,-12],[-11,-16],[-6,-16],[-4,-19],[0,-10],[4,-7],[7,-4],[10,-3],[8,-5],[0,-9],[-4,0],[-30,-10],[-9,1],[-15,5],[-9,0],[16,28],[0,13],[-10,17],[-11,8],[-17,6],[-17,5],[-12,-1],[-7,-6],[-9,-16],[-7,-3],[-8,-1],[-12,-3],[-6,-1],[-13,4],[-22,23],[-13,9],[-26,7],[-27,3],[-6,2],[-8,9],[-5,4],[-37,2],[-56,12],[1,11],[5,13],[2,14],[-3,4],[-6,3],[-5,6],[-3,10],[3,7],[6,4],[8,1],[7,0],[14,-3],[6,2],[3,12],[-3,3],[-6,1],[-8,1],[-29,-8],[-15,-1],[-7,6],[-34,-19],[-8,-9],[9,3],[3,2],[5,-13],[4,-4],[6,4],[9,11],[8,0],[8,-13],[11,-26],[-8,-14],[-4,-1],[-11,0],[-23,-4],[-11,-1],[-11,5],[-15,-7],[-23,4],[-63,26],[-42,7],[-41,-1],[-41,-7],[-20,-7],[-11,-6],[-10,-9],[-17,-21],[-9,-19],[-6,-5],[-10,4],[3,-13],[4,-9],[19,-25],[3,-3],[1,-2],[0,-5],[-2,-5],[-2,-5],[-3,-4],[-4,-1],[-20,4],[-35,18],[-21,4],[-43,-6],[-10,2],[-29,14],[-41,12],[-21,2],[-19,-4],[-38,-23],[-15,-3],[-52,6],[-106,25],[-103,3],[-47,-1],[-31,-4],[-8,-16],[2,-11],[13,-7],[-12,-5],[-16,5],[-19,-5],[-17,-11],[-12,-10],[-7,-7],[-7,-11],[-4,-11],[1,-12],[8,-8],[13,-4],[9,-5],[-4,-10],[-25,-1],[-31,2],[-21,7],[15,13],[2,3],[3,2],[2,1],[5,3],[5,0],[5,2],[1,8],[-8,19],[-4,14],[-14,11],[-13,9],[-18,1],[-21,5],[-28,-1],[-27,-10],[-14,-6],[-7,-15],[-6,-12],[9,-8],[3,-17],[7,-7],[5,-9],[-13,-5],[-25,2],[-29,4],[-36,1],[-30,-5],[-7,-14],[-2,-6],[-6,-7],[-6,-22],[-8,1],[-12,-9],[-15,-7],[-22,-3],[-16,6],[-6,12],[4,16],[6,8],[-4,10],[2,7],[11,9],[6,8],[2,5],[0,10],[2,8],[8,13],[3,8],[-1,11],[-2,2],[-4,-2],[-4,-2],[-2,-1],[-5,-7],[-3,-1],[-4,1],[-8,7],[-3,1],[-8,2],[-3,3],[-4,4],[-5,2],[-7,1],[-20,-1],[-10,3],[-6,0],[-7,-3],[-15,1],[20,63],[3,63],[5,74],[3,64],[3,44],[2,11],[4,7],[9,10],[4,6],[1,26],[-6,29],[-4,28],[14,19],[13,4],[12,0],[10,2],[10,13],[3,13],[7,52],[7,62],[6,47],[5,40],[6,46],[5,23],[9,20],[15,13],[74,23],[23,16],[11,6],[13,4],[25,5],[6,8],[2,18],[10,18],[24,-3],[27,-11],[23,-3],[14,8],[-1,10],[-6,14],[-3,17],[4,10],[20,20],[6,13],[-1,5],[-2,4],[-2,5],[1,7],[4,5],[5,4],[13,5],[5,3],[8,7],[5,2],[5,1],[13,-1],[7,2],[3,3],[5,10],[5,4],[6,2],[103,12],[13,5],[5,4],[5,6],[5,3],[7,-1],[7,-4],[5,0],[19,5],[6,2],[4,2],[4,4],[7,13],[4,6],[7,3],[42,-2],[48,-1],[56,-1],[24,-4],[25,-1],[18,4],[6,0],[5,-3],[4,-3],[5,-3],[6,0],[14,14],[7,30],[5,109],[4,12],[10,5],[25,5],[10,9],[2,14],[-6,12],[-7,12],[-6,14],[-3,84],[1,9],[3,8],[6,10],[2,7],[7,12],[12,6],[14,0],[10,-4],[-21,50],[-8,14],[-7,8],[-6,4],[-15,6],[-7,4],[-16,16],[-8,3],[-6,1],[-34,-12],[-8,0],[-10,1],[-16,-1],[-33,-11],[-16,-3],[-35,-7],[-17,-1],[-20,4],[-14,9],[-5,2],[-5,-1],[-8,-5],[-5,-1],[-9,6],[-27,26],[10,0],[6,5],[4,9],[3,10],[1,12],[-3,9],[-12,15],[32,79],[4,31],[-4,39],[-5,69],[-4,38],[-5,60],[-3,45],[-17,72],[-11,52],[10,85],[9,77],[-3,26],[-14,9],[-101,-22],[-12,8],[-39,45],[-9,17],[-4,13],[-4,7],[-7,5],[-10,3],[-10,6],[-6,9],[-6,10],[-7,9],[-17,10],[-82,17],[-51,25],[-20,5],[-41,3],[-18,7],[-30,18],[-37,11],[-11,6],[-20,25],[-39,38],[-25,36],[-64,61],[-18,24],[-18,41],[-17,21],[-4,11],[-1,13],[-4,15],[-26,63],[-5,8],[-7,6],[-8,4],[-5,4],[-1,4],[2,5],[4,4],[6,7],[2,7],[-2,6],[-6,5],[-11,5],[-4,11],[-4,13],[-6,10],[-7,4],[-5,1],[-6,2],[-6,8],[-4,8],[-1,19],[-2,8],[-8,13],[-20,23],[-6,16],[0,14],[8,26],[1,15],[-6,19],[-11,8],[-13,6],[-12,10],[-50,64],[-49,61],[-46,59],[-41,51],[-32,41],[-50,62],[20,-9],[44,-15],[51,-18],[51,-17],[52,-18],[51,-17],[51,-18],[52,-17],[51,-18],[52,-17],[51,-18],[51,-17],[52,-18],[51,-17],[51,-18],[52,-17],[51,-18],[52,-17],[58,-20],[64,-13],[77,8],[18,1],[30,3],[30,3],[30,3],[31,4],[30,3],[30,3],[30,3],[31,3],[30,3],[30,3],[30,3],[31,3],[30,3],[30,3],[30,3],[31,3],[94,9],[135,-28],[114,-23],[70,-32],[17,7],[23,47],[14,19],[21,12],[167,53],[13,0],[114,-1],[84,-1],[103,-20],[31,-1],[89,28],[36,11],[36,11],[36,11],[35,11],[36,11],[36,11],[36,11],[36,11],[36,11],[35,11],[36,11],[36,11],[36,12],[36,11],[36,11],[35,11],[53,16],[49,37],[16,23],[13,23],[4,2],[5,2],[2,4],[-5,7],[-19,15],[-25,28],[-22,15],[-3,11],[1,11],[9,29],[40,85],[3,14],[-2,46],[12,99],[-2,15],[-3,15],[-5,10],[-15,20],[-10,30],[2,32],[60,241],[6,10],[10,6],[84,18],[14,6],[58,63],[11,17],[21,84],[15,11],[24,3],[16,13],[14,17],[19,17],[41,21],[23,7],[37,1],[9,-5],[4,-10],[1,-17],[-1,-6],[-4,-5],[-2,-4],[1,-6],[4,-4],[8,-3],[14,-2],[13,-1],[23,7],[13,0],[12,-4],[23,-13],[12,-4],[10,-2],[32,2],[40,-4],[11,2],[43,19],[37,5],[22,11],[33,6],[12,5],[39,26],[8,8],[9,6],[10,3],[25,-1],[9,5],[7,14],[-3,31],[-23,8],[-51,-8],[-12,4],[-12,8],[-9,11],[-4,30],[-8,12],[-9,12],[-5,11],[8,13],[20,1],[40,-10],[19,2],[19,8],[13,10],[20,15],[38,44],[18,9],[8,0],[19,-3],[25,2],[6,3],[7,11],[11,8],[32,19],[4,5],[2,16],[3,7],[20,19],[3,6],[4,7],[13,32],[8,10],[13,1],[15,-6],[14,-8],[16,-13],[6,-10],[8,-8],[13,-2],[25,1],[24,-2],[65,-24],[23,-4],[10,4],[14,8],[8,10],[-6,6],[-12,2],[-37,-1],[-13,2],[-11,5],[-2,8],[12,9],[26,15],[15,5],[14,1],[21,-9],[36,-21],[34,-7],[8,-4],[4,-9],[2,-31],[5,-11],[16,-19],[28,-42],[16,-12],[25,3],[39,20],[21,7],[22,-1],[17,7],[22,16],[20,19],[8,13],[9,7],[9,10],[67,108],[17,19],[30,22],[1,3]],[[5218,6172],[17,-1],[4,-7],[9,0],[12,3],[92,7],[11,-2],[21,6],[11,8],[10,10],[10,11],[16,24],[10,9],[28,11],[6,1],[47,10],[17,-3],[2,-3],[-1,-10],[-5,-16],[-5,-8],[0,-6],[1,-5],[5,-8],[7,-6],[8,-6],[12,-7],[3,-2],[1,-3],[1,-4],[-1,-7],[-2,-6],[-13,-22],[-1,-4],[-1,-3],[1,-4],[1,-4],[1,-5],[5,-7],[3,-4],[4,-3],[3,-2],[3,-1],[3,0],[2,1],[4,1],[3,2],[3,3],[3,3],[6,8],[8,16],[2,2],[2,2],[3,0],[3,-1],[2,-3],[0,-3],[-3,-39],[-4,-14],[-2,-16],[1,-42],[0,-10],[2,-51],[-2,-17],[-3,-48],[-1,-9],[-5,-17],[-1,-8],[-1,-5],[1,-5],[2,-7],[2,-3],[5,-4],[20,-8],[6,-2],[5,0],[6,-3],[9,-18],[11,-81],[0,-8],[2,-4],[2,-4],[5,-3],[2,0],[2,1],[0,3],[-1,13],[1,6],[1,8],[2,5],[3,4],[5,6],[1,2],[2,7],[3,8],[1,4],[1,2],[1,3],[2,0],[4,-1],[5,-6],[3,-1],[3,1],[2,1],[1,3],[3,2],[2,1],[4,0],[11,-2],[2,0],[3,1],[3,3],[3,2],[4,7],[4,7],[3,6],[2,2],[3,1],[3,0],[2,1],[3,2],[7,11],[3,5],[2,3],[2,2],[2,0],[1,0],[3,-4],[2,-4],[2,-8],[0,-7],[-1,-11],[2,-10],[2,-7],[7,-10],[2,-4],[1,-5],[1,-5],[-1,-8],[-7,-32],[-1,-7],[1,-51],[-9,-63],[-1,-30],[1,-18],[5,-25]],[[8873,8191],[-7,5],[-23,0],[-40,-9],[-57,-3],[-218,44],[-156,57],[-39,7],[-39,-4],[-40,-15],[-46,-35],[-16,-8],[-98,-17],[-8,-4],[-18,-15],[-1,-18],[5,-19],[0,-21],[-9,-15],[-30,-21],[-10,-17],[-1,-28],[-3,-9],[-5,-6],[-16,-9],[-6,-5],[-10,-17],[-3,-18],[5,-18],[13,-15],[15,-9],[15,-6],[16,-2],[18,2],[18,-2],[10,-13],[14,-37],[9,-15],[5,-16],[-2,-15],[-12,-11],[-37,-20],[-14,-15],[-6,-23],[6,-22],[14,-20],[18,-14],[19,-4],[42,11],[13,0],[33,-12],[22,1],[23,6],[23,1],[19,-14],[8,-24],[-6,-24],[-15,-22],[-16,-17],[-22,-16],[-22,-10],[-66,-11],[-14,-14],[-22,-38],[-3,-9],[-3,-10],[-2,-9],[-2,-22],[2,-12],[3,-11],[5,-10],[30,-26],[39,-21],[33,-27],[10,-42],[-13,-57],[-19,-54],[-6,-13],[-7,-10],[-8,-9],[-10,-8],[-23,-9],[-10,-7],[-6,-13],[-2,-33],[8,-24],[17,-17],[22,-16],[20,-21],[35,-48],[22,-14],[58,-10],[17,-11],[6,-16],[2,-38],[7,-39],[-1,-15],[-7,-14],[-4,-6]],[[8313,6832],[-206,109],[-4,5],[-10,19],[0,1],[-2,3],[-4,4],[-5,5],[-10,6],[-12,3],[-20,-1],[-9,-1],[-6,-2],[-1,-1],[-11,-6],[-1,0],[-4,-2],[-10,2],[-12,7],[-8,4],[-6,12],[-20,12],[-36,17],[-20,14],[-11,16],[14,14],[-1,8],[4,6],[2,8],[1,9],[-4,7],[-5,5],[-12,8],[-2,4],[-1,5],[-1,4],[0,2],[-3,1],[-5,-2],[-1,0],[-3,-2],[-1,0],[-4,0],[-2,0],[-2,1],[-1,7],[0,1],[2,-1],[4,2],[1,0],[4,2],[2,2],[-1,15],[-2,14],[-1,14],[2,8],[2,5],[2,9],[-3,7],[-7,10],[-7,10],[-1,13],[5,10],[7,9],[6,22],[6,4],[5,1],[1,0],[3,3],[0,7],[-1,6],[-2,6],[-5,24],[0,7],[2,5],[4,2],[4,2],[2,7],[-1,12],[-5,14],[-6,13],[-7,10],[-17,15],[-2,6],[3,10],[-1,5],[1,7],[11,24],[3,10],[-1,14],[-2,14],[-4,12],[-8,11],[-5,20],[9,55],[-2,25],[-37,73]],[[7821,7719],[-1,1],[-6,44],[-11,45],[0,1],[0,8],[0,5],[0,2],[1,24],[-1,12],[-10,53],[-9,10],[-2,8],[-5,10],[-25,37],[-4,15],[2,4],[7,13],[3,9],[3,14],[0,1],[1,4],[0,23],[0,3],[1,0],[0,2],[4,7],[1,2],[1,2],[0,3],[-1,19],[0,4],[1,3],[3,5],[1,3],[1,2],[0,7],[0,3],[0,4],[2,3],[3,3],[6,3],[19,3],[2,0],[4,0],[12,-3],[2,0],[1,0],[3,1],[12,6],[8,4],[5,1],[5,1],[5,0],[2,0],[4,-1],[3,-1],[1,-2],[1,-1],[1,-4],[2,-5],[2,-2],[1,-2],[3,0],[1,1],[3,3],[6,12],[13,36],[0,4],[0,4],[-1,4],[-5,7],[-1,3],[-1,4],[1,3],[1,3],[19,29],[3,6],[5,16],[4,8],[0,1],[5,5],[6,6],[13,10],[7,4],[5,2],[11,-1],[4,1],[4,1],[4,3],[1,0],[11,9],[2,1],[14,2],[26,-2],[2,1],[2,0],[11,9],[2,1],[3,2],[5,4],[1,1],[2,2],[0,1],[1,0],[4,8],[3,3],[3,3],[4,4],[1,0],[4,1],[10,1],[4,1],[3,1],[7,3],[7,3],[2,2],[2,1],[3,4],[1,2],[3,12],[3,7],[0,1],[1,1],[3,5],[7,5],[1,1],[2,1],[3,7],[1,6],[0,12],[0,6],[2,6],[5,7],[6,6],[4,3],[4,2],[2,3],[0,3],[-2,5],[-3,4],[-3,3],[-7,3],[-1,1],[-1,1],[-2,1],[0,1],[-1,0],[-1,2],[-1,2],[0,4],[1,4],[6,12],[3,5],[4,5],[7,6],[16,5]],[[8195,8536],[8,-1],[23,0],[1,0],[56,-9],[1,0],[50,5],[37,14],[32,9],[1,0],[15,-3],[20,-10],[17,-20],[16,-25],[24,-55],[13,-24],[17,-17],[85,-49],[41,-18],[38,-11],[29,-1],[19,3],[14,6],[24,5],[12,6],[22,21],[11,2],[1,0],[12,-6],[29,-30],[15,-20],[10,-25],[4,-25],[-2,-24],[-15,-35],[-1,-1],[-1,-6],[0,-1]],[[7456,7509],[29,6],[14,7],[6,7],[6,6],[9,7],[7,3],[5,1],[23,3],[14,6],[3,3],[2,2],[4,7]],[[7578,7567],[6,-4],[49,-23],[9,-6],[8,-8],[20,-21],[23,-19],[5,-7],[0,-9],[-4,-9],[-16,-18],[-7,-10],[-23,-56],[-4,-6],[-19,-20],[-8,-10],[-1,-2],[-1,-1],[-2,-2],[-11,-3],[-13,3],[-16,9],[-8,5],[-5,7],[2,9],[4,10],[1,11],[-4,14],[-6,13],[-8,10],[-7,7],[-13,4],[-10,-1],[-12,-3],[-23,-8],[-21,-13],[-10,-8],[-13,-7],[-14,-3],[-15,2],[-13,12],[-10,15],[-8,19],[4,20],[13,18],[59,31]],[[8195,8536],[-1,4],[0,2],[-3,3],[-8,7],[-2,4],[-1,3],[1,3],[1,3],[-1,1],[0,1],[-1,0],[-2,1],[-11,-1],[-1,0],[-17,2],[-8,1],[-1,0],[-3,1],[-4,2],[-10,11],[-1,1],[-2,2],[-1,3],[-2,2],[-3,0],[-1,1],[-10,-1],[-5,-2],[-5,-2],[-1,0],[-3,1],[-3,2],[-15,19],[-6,2],[-3,1],[-7,3],[-1,0],[-6,1],[-11,-4],[-3,0],[-1,0],[-2,2],[-12,12],[-12,7],[-5,1],[-2,1],[-1,0],[-4,1],[-15,0],[-2,0],[-2,-1],[-2,-2],[-13,-13],[-3,-2],[-2,-1],[-1,0],[-3,0],[-2,2],[-4,3],[-4,7],[0,4],[0,3],[1,3],[2,12],[0,3],[-1,4],[-18,32],[-1,2],[0,3],[0,3],[2,21],[0,2],[-1,5],[0,2],[1,3],[2,4],[14,23],[4,8],[1,3],[0,2],[2,2],[9,8],[15,8],[6,3],[3,1],[1,2],[1,2],[-1,2],[0,3],[-1,3],[-2,31],[0,3],[-4,12],[-8,4],[-1,0],[-79,12],[-39,-1],[-38,-6],[-1,0],[-23,18],[-1,0],[-13,3],[-21,-11],[0,1],[-8,16],[-1,4],[-1,4],[-3,2],[-3,4],[-1,0],[-4,3],[-1,0],[-4,2],[-1,0],[-7,1],[-3,0],[-12,-1],[-3,0],[-9,3],[-2,1],[-2,1],[-3,8],[-1,3],[-4,5],[-5,3],[-19,13],[-5,5],[-2,2],[-8,7],[-57,38],[-4,4],[-2,4],[-1,4],[0,5],[0,6],[3,38],[1,3],[1,2],[12,11],[2,3],[0,3],[-1,2],[-2,3],[-1,0],[-12,8],[-6,4],[0,1],[-7,5],[-8,9],[-2,2],[-1,3],[-1,1],[-1,0],[-1,0],[-3,-2],[-5,-6],[-2,-3],[-3,-1],[-3,-2],[-4,-1],[-6,0],[-7,1],[-9,3],[-1,0],[-4,2],[-1,1],[-4,3],[-9,4],[-2,1],[-3,1],[-4,1],[-61,-3],[-5,-1],[-5,-2],[-24,-15],[-2,-2],[-3,-5],[-2,-2],[-5,-1],[-4,0],[-26,6],[-58,7],[-1,0],[-60,26],[-25,11],[44,81],[1,3],[0,2],[-5,12],[-1,6],[-4,5],[-19,17],[-2,3],[-2,2],[0,3],[-3,6],[-1,5],[0,4],[1,4],[1,3],[1,3],[16,20],[1,5],[-1,4],[-4,8],[-1,3],[0,3],[1,11],[7,31],[2,5],[3,4],[3,6],[5,6],[5,5],[8,6],[9,3],[7,1],[6,1],[7,-1],[17,-7],[3,0],[4,2],[13,18],[3,3],[12,9],[9,4],[9,2],[4,2],[7,5],[23,27],[5,4],[5,3],[19,4],[5,3],[4,6],[2,4],[0,4],[-1,9],[0,18],[2,10],[1,4],[3,3],[7,3],[12,2],[18,0],[16,2],[2,2],[2,2],[1,6],[0,4],[-3,5],[-2,3],[-1,3],[0,3],[2,2],[4,3],[33,12],[4,3],[4,4],[5,7],[1,5],[2,9],[2,4],[8,12],[6,10],[1,3],[0,2],[-2,6],[-10,25],[-2,9],[-1,17],[0,4],[0,3],[1,3],[3,1],[12,2],[20,5],[7,3],[18,14],[4,2],[3,-1],[6,-4],[10,-10],[3,-2],[3,0],[16,3],[17,2],[10,-1],[5,0],[11,3],[63,23],[9,-1],[15,-3],[37,-12],[3,0],[14,1],[7,0],[3,-1],[4,-2],[4,-1],[3,2],[9,4],[3,0],[2,-2],[6,-8],[3,-5],[2,-2],[1,0],[3,-2],[3,0],[17,1],[4,-1],[7,-4],[3,0],[4,0],[7,4],[12,11],[4,2],[4,0],[5,-2],[12,-6],[4,0],[3,1],[12,8],[3,1],[6,-1],[20,-5],[13,-5],[12,-3],[3,0],[4,2],[2,5],[-1,4],[0,3],[0,3],[0,3],[3,7],[0,3],[0,4],[-1,0],[-1,6],[-1,4],[0,1],[-4,4],[-7,6],[-1,1],[-34,22],[0,1],[-8,8],[-1,1],[-8,4],[-6,3],[-23,4],[-5,2],[-3,2],[-1,1],[-2,1],[-4,4],[-1,3],[0,1],[-2,9],[2,14],[0,1]],[[7940,9879],[38,-2],[37,-6],[19,-8],[51,-31],[18,-5],[19,-7],[17,0],[37,12],[6,7],[3,26],[4,11],[9,6],[10,3],[40,-5],[15,5],[30,24],[52,32],[16,13],[25,7],[14,1],[12,-4],[2,-2],[2,-2],[11,-16],[12,-11],[10,3],[4,24],[3,5],[1,6],[-2,5],[-3,4],[12,6],[40,10],[22,9],[10,-2],[9,-12],[16,-14],[46,-1],[19,-16],[18,-26],[5,-10],[3,-22],[4,-7],[8,3],[4,7],[5,22],[6,9],[9,6],[11,5],[21,5],[24,1],[52,7],[12,-2],[9,-5],[17,-16],[21,-11],[73,-15],[23,-10],[17,-16],[10,-22],[4,-29],[2,-31],[4,-28],[13,-18],[38,-2],[4,5],[1,8],[4,7],[39,15],[49,-10],[48,-23],[38,-26],[37,-33],[15,-19],[63,-117],[0,-1],[3,-12],[4,-18],[-12,-59],[1,-30],[4,-11],[16,-20],[6,-9],[2,-14],[-8,-6],[-12,-5],[-8,-10],[0,-8],[5,-13],[-1,-8],[-3,-6],[-4,-4],[-31,-19],[-8,-8],[-4,-12],[3,-26],[14,-24],[19,-18],[19,-8],[48,-3],[17,-11],[15,-27],[6,-28],[3,-31],[6,-27],[17,-15],[15,-1],[29,8],[14,1],[13,-2],[10,-4],[9,0],[23,16],[11,0],[11,-2],[11,2],[5,5],[7,13],[6,4],[7,2],[7,2],[14,0],[14,5],[23,23],[11,-4],[7,-24],[-9,-61],[12,-24],[21,-12],[44,-16],[20,-15],[15,-21],[8,-8],[11,-6],[8,-1],[84,-201],[84,-200],[-4,-3],[-12,-15],[2,-18],[6,-21],[-5,-23],[-9,-13],[-37,-30],[-12,-13],[-8,-6],[-9,2],[-17,10],[-16,5],[-22,-2],[-22,-7],[-15,-12],[-5,-17],[3,-41],[-6,-14],[-57,-54],[-8,-4],[-53,-12],[-11,0],[-9,4],[-27,20],[-24,4],[-23,-9],[-86,-50],[-66,-17],[-20,0],[-39,16],[-22,-4],[-10,-9],[-12,-22],[-9,-10],[-4,-3],[-17,-5],[-40,-30],[-36,-38],[-12,-10],[-12,-3],[-26,4],[-13,0],[-36,-9],[-27,1],[-163,44],[-25,13],[-24,18],[-10,11],[-9,14],[-8,5]],[[5897,5653],[5,-33],[-3,-9],[-4,-1],[-26,-1],[-4,-1],[-6,-2],[-14,-7],[-11,-3],[-22,-12],[-3,-4],[-1,-4],[0,-3],[1,-12],[4,-15],[1,-6],[0,-6],[-1,-3],[-5,-12],[-3,-2],[-4,0],[-2,2],[-2,3]],[[5218,6172],[2,4],[-1,10],[-20,57],[-12,71],[1,16],[5,32],[0,31],[4,32],[1,17],[-2,15],[-16,42],[-7,32],[3,31],[12,29],[18,24],[13,8],[25,9],[11,10],[33,50],[7,14],[2,10],[-6,24],[-1,13],[-2,6],[-4,3],[-5,3],[-6,3],[-4,5],[-3,6],[-1,15],[6,10],[21,16],[15,22],[16,12],[4,3],[6,22],[-12,81],[6,13],[6,13],[17,21],[20,14],[25,3],[13,0],[20,4],[6,-2],[5,-3],[5,-1],[12,3],[29,21],[24,4],[41,-21],[26,2],[46,29],[6,1],[18,-1],[8,3],[4,7],[21,40],[7,3],[15,-5],[13,-1],[12,6],[105,90],[5,25],[-10,23],[-33,37],[-3,6],[-5,12],[-4,4],[-17,5],[-5,4],[-8,11],[-6,14],[-2,14],[1,14],[12,47],[1,19],[-3,5],[-4,3],[-10,5],[-6,5],[-3,4],[-3,11],[-6,30],[-6,8],[-26,-5],[-33,15],[-10,1],[-14,2],[-10,6],[-6,14],[-5,15],[-5,11],[-15,22],[-5,12],[-8,28],[-6,10],[-18,18],[-4,11],[9,11],[10,12],[2,11],[-2,11],[1,12],[10,10],[10,4],[7,2],[32,5],[21,-1],[133,-48],[36,-3],[21,-11],[11,-3],[50,-2],[51,-11],[19,-1],[82,12],[84,-1],[43,9],[11,22],[3,9],[6,2],[17,-5],[11,-1],[9,3],[7,6],[7,10],[13,5],[30,6],[10,10],[3,14],[-2,11],[-1,13],[7,15],[17,20],[4,10],[1,18],[-2,15],[-6,26],[0,13],[18,53],[-2,18],[-44,25],[-19,18],[-18,22],[-12,19],[-6,14],[-4,17],[1,16],[7,13],[9,5],[2,0],[13,0],[25,-4],[11,6],[7,15],[2,18],[-2,16],[-5,16],[1,12],[6,9],[44,35],[23,13],[9,8],[32,46],[10,26],[11,23],[22,17],[74,30]],[[6541,8415],[2,-2],[9,-16],[21,-26],[1,-2],[12,-6],[14,-3],[6,-2],[5,-1],[1,-1],[1,-2],[1,-7],[0,-1],[1,-2],[2,-3],[0,-1],[1,-2],[0,-1],[1,-4],[0,-1],[0,-1],[2,-3],[2,-4],[1,-7],[0,-3],[0,-4],[0,-2],[1,-2],[11,-10],[8,-4],[5,-2],[4,1],[27,15],[9,3],[4,1],[4,0],[22,-3],[4,-2],[4,-2],[8,-2],[3,-1],[3,1],[4,0],[2,-1],[3,-3],[3,-7],[2,-4],[4,-5],[0,-2],[-3,-2],[-4,-1],[-3,-2],[-6,-9],[-1,-2],[-2,-1],[-4,-1],[-2,-1],[-1,-2],[1,-4],[1,-2],[1,-1],[0,-1],[1,-5],[1,-6],[0,-9],[-4,-23],[-14,-10],[-1,0],[-1,0],[-1,1],[-3,2],[-1,0],[-1,0],[-5,-4],[-2,-1],[-9,-11],[-4,-3],[-1,0],[-1,-1],[-1,0],[-1,-2],[-2,0],[-2,-4],[-2,-6],[-6,-24],[0,-4],[1,-8],[-3,-4],[-12,-13],[9,-30],[-1,-2],[0,-1],[2,-8],[1,-3],[0,-1],[-1,-4],[0,-1],[0,-1],[0,-1],[1,-2],[1,-1],[1,0],[5,-1],[2,0],[1,0],[2,1],[1,0],[2,-4],[6,-16],[-5,-1],[-3,-2],[-2,-2],[-1,0],[-1,-1],[-2,-1],[-1,0],[-1,0],[-4,-5],[-1,-1],[-1,-1],[-1,-2],[-1,-7],[-1,-2],[0,-1],[0,-1],[0,-1],[0,-1],[1,0],[1,-2],[1,-1],[-1,-1],[-2,-2],[-5,-2],[-2,-1],[-2,-1],[-3,0],[-1,0],[-4,-1],[-1,-1],[0,-2],[-1,-7],[0,-1],[-1,0],[0,-1],[1,-6],[0,-2],[0,-1],[0,-1],[-2,0],[-2,0],[-2,1],[-1,1],[-3,3],[-2,2],[-1,1],[-1,0],[-1,0],[-1,0],[-1,0],[-3,2],[-1,-1],[-1,0],[-8,-9],[-4,-4],[-1,-1],[-2,-1],[-1,-1],[0,-1],[-1,-2],[0,-3],[0,-2],[-1,-2],[-2,-2],[-11,-10],[-1,-2],[-1,-1],[0,-1],[1,-3],[1,-1],[1,-2],[0,-1],[-4,-12],[0,-2],[1,-1],[1,-1],[1,0],[1,0],[0,-1],[0,-1],[0,-1],[0,-1],[-2,-7],[-2,-3],[-18,-15],[-5,-10],[-20,-16],[-4,-3],[-1,-1],[-1,-1],[-10,-7],[-3,-3],[-1,-1],[-1,-2],[-1,-2],[-48,-13],[-2,-3],[-1,-2],[-1,0],[0,-1],[0,-1],[1,-4],[0,-1],[0,-1],[0,-1],[-2,-4],[1,-1],[1,-1],[3,-2],[2,-1],[1,0],[2,-1],[3,-1],[2,-1],[5,-4],[3,-2],[1,-2],[0,-3],[1,-7],[0,-4],[-1,-2],[-1,-1],[-2,-1],[-2,-2],[-2,-2],[0,-1],[0,-1],[-1,-3],[1,-2],[8,-10],[0,-2],[3,-16],[1,-7],[-2,-5],[-3,-6],[0,-2],[0,-2],[-1,-7],[0,-7],[0,-4],[2,-5],[4,-10],[4,-5],[5,-4],[5,-5],[5,-6],[5,-10],[3,-17],[0,-4],[-2,-15],[0,-3],[0,-2],[1,-3],[2,-3],[9,-7],[23,-17],[6,-6],[2,-3],[1,-3],[1,-3],[1,-4],[-2,-13],[1,-6],[0,-4],[6,-20],[3,-8],[2,-3],[2,-1],[3,-1],[3,-1],[7,-1],[6,0],[29,6],[34,18],[20,14],[8,9],[12,22],[3,9],[3,5],[2,2],[3,1],[3,-2],[3,-4],[5,-17],[3,-5],[3,-4],[7,-7],[5,-2],[4,-2],[20,-3],[16,-1],[3,-1],[2,-2],[1,-4],[1,-3],[-2,-6],[-2,-5],[-19,-28],[-3,-6],[-4,-5],[-13,-16],[-1,-3],[-1,-2],[0,-2],[1,-2],[2,-2],[21,-15],[7,-5],[1,-2],[1,-2],[-1,-2],[0,-3],[-2,-2],[-2,-2],[-3,-2],[-6,-3],[-21,-7],[-7,-4],[-9,-6],[-35,-22],[-17,-8],[-13,-1],[-25,3],[-15,5],[-6,3],[-4,3],[-4,5],[0,3],[1,2],[2,3],[3,3],[7,3],[21,8],[6,3],[6,3],[2,3],[2,3],[1,2],[0,3],[0,4],[-2,3],[-1,2],[-2,2],[-2,2],[-3,1],[-4,0],[-7,-2],[-6,-2],[-79,5],[-9,2],[-4,1],[-4,-1],[-14,-3],[-26,-2],[-7,-2],[-5,-3],[-4,-5],[-3,-1],[-4,0],[-8,4],[-4,3],[-5,5],[-10,7],[-30,16],[-14,10],[-10,10],[-1,2],[-4,9],[-1,3],[-2,2],[-4,1],[-3,0],[-3,-1],[-3,2],[-10,7],[-4,2],[-11,4],[-3,0],[-3,-1],[-1,-4],[0,-31],[1,-4],[8,-21],[1,-9],[-23,-8],[-7,-5],[-3,-2],[-3,-4],[-1,-2],[-1,-3],[0,-2],[1,-2],[2,-1],[2,-2],[3,-1],[3,-1],[2,-2],[1,-3],[-1,-2],[-3,-2],[-24,-6],[-7,-3],[-5,-2],[-6,-6],[-6,-5],[-13,-6],[-5,0],[-99,9],[-10,4],[-21,14],[-11,-1],[-3,-3],[-3,-5],[-9,-21],[-3,-11],[0,-3],[0,-2],[0,-3],[1,-7],[1,-7],[-1,-3],[-1,-4],[-3,-3],[-5,-1],[-3,0],[-3,0],[-17,-7],[-11,-2],[-14,0],[-2,-1],[-3,-1],[-4,-4],[-18,-20],[-6,-5],[-3,-2],[-3,0],[-4,0],[-9,0],[-7,0],[-7,-2],[-8,-1],[-10,-2],[-4,-2],[-8,-6],[-2,-3],[-1,-2],[-1,-3],[-1,-6],[0,-2],[1,-3],[3,-4],[8,-7],[21,-13],[7,-2],[3,-7],[6,-28],[15,-22],[9,-5],[5,-1],[6,-1],[7,0],[3,-1],[20,-8],[3,-1],[8,0],[4,-1],[3,-1],[1,-2],[-4,-12],[0,-2],[0,-2],[1,-2],[2,-1],[7,-2],[20,-2],[13,0],[3,0],[8,-3],[2,0],[6,0],[3,0],[4,-1],[8,-3],[12,-3],[16,-6],[2,-2],[3,-3],[2,-3],[1,-3],[-1,-5],[-10,-15],[-39,-45],[-15,-4],[-4,-2],[-5,-3],[-10,-13],[-5,-5],[-8,-4],[-5,-2],[-15,-2],[-12,-5],[-9,-2],[-4,-1],[-6,-2],[-3,0],[-3,1],[-3,0],[-13,2],[-5,-1],[-3,0],[-4,0],[-15,6],[-3,-1],[-3,-1],[-10,-7],[-8,-9],[-4,-3],[-6,-2],[-2,-3],[-14,-49],[-4,-8],[-17,-24],[-18,-36],[-6,-9],[-4,-5],[-2,0],[-2,0],[-3,-1],[-2,-5],[-1,-4],[0,-36],[2,-22],[-1,-5],[0,-5],[-2,-11],[0,-2],[2,-1],[5,-1],[3,-2],[10,-15],[105,-137],[5,-14],[2,-6],[0,-2],[-1,-3],[-2,-3],[-7,-4],[-4,-1],[-4,1],[-41,24],[-5,2],[-5,1],[-2,0],[-2,-1],[-2,0],[-2,-2],[-4,-5],[-4,-9],[-6,-16],[-3,-12],[-3,-8],[-3,-8],[-14,-16],[-20,-18],[-3,-3],[-12,-21],[-3,-4],[-17,-15],[-13,-9],[-7,-3],[-2,-1],[-3,-1],[-3,0],[-3,1],[-7,3],[-3,0],[-19,-2],[-2,-1],[-2,-1],[-4,-2],[-3,-3],[-4,-4],[-6,-15],[-8,-12],[-4,-5],[-4,-3],[-23,-16],[-15,-13],[-6,-7],[-8,-11],[-2,-6],[-2,-11],[0,-3],[1,-2],[2,-3],[17,-27],[9,-26],[5,-27],[7,-14],[24,-22],[24,-81],[1,-7],[1,-20],[0,-6],[-2,-12],[-2,-9],[0,-2],[6,-44],[1,-4],[10,-17],[102,-356]],[[7578,7567],[12,4],[30,-1],[14,10],[15,13],[19,7],[11,7],[14,7],[3,2],[2,4],[1,3],[0,3],[2,5],[4,7],[18,26],[1,3],[-1,2],[-1,3],[-2,4],[2,7],[3,4],[4,2],[4,1],[4,1],[3,-1],[7,-5],[2,-1],[4,0],[5,1],[4,3],[3,3],[11,19],[4,4],[3,2],[4,1],[12,5],[5,5],[4,-1],[4,-2],[3,-3],[6,-2]],[[8313,6832],[0,-1],[-6,-9],[6,-14],[8,-11],[10,-7],[14,-2],[11,3],[20,12],[12,1],[11,-5],[18,-19],[10,-6],[12,0],[33,5],[12,4],[13,10],[4,11],[3,13],[6,14],[11,12],[11,2],[9,-7],[2,-17],[-4,-16],[-16,-31],[-4,-17],[0,-16],[4,-31],[-1,-14],[-5,-13],[-6,-13],[-5,-14],[2,-15],[18,-52],[17,-20],[22,-8],[24,1],[22,8],[24,1],[42,-22],[26,3],[26,11],[20,2],[20,-8],[23,-20],[20,-13],[44,-11],[21,-12],[42,-36],[17,-15],[16,-21],[4,-17],[-6,-15],[-20,-23],[-5,-13],[-6,-25],[-2,-8],[-20,1],[-8,-1],[-15,-6],[-19,-10],[-13,-14],[0,-17],[-8,1],[-14,4],[-5,-2],[-7,-7],[-5,-3],[-5,1],[-38,14],[-11,-2],[4,-21],[-6,-1],[-15,2],[-6,-4],[-5,-7],[-2,-6],[-4,-6],[-8,-4],[-15,-2],[-14,2],[-12,-2],[-10,-13],[-27,10],[-11,-16],[-5,-26],[-12,-20],[-13,-3],[-39,9],[-18,-35],[-8,-6],[-10,-4],[-46,-37],[-10,-10],[-6,-12],[-2,-17],[0,-10],[-1,-3],[-3,-3],[-5,-7],[-1,-3],[-2,-9],[-1,-3],[-2,-2],[-4,-2],[-2,-2],[-6,-12],[-2,-2],[6,-9],[34,-65],[18,-71],[19,-30],[1,-14],[-3,-14],[-7,-12],[-10,-13],[-8,-13],[-24,-64],[-4,-18],[0,-33],[-10,-30],[-2,-15],[2,-15],[3,-10],[6,-10],[8,-10],[10,-5],[27,5],[12,0],[10,-6],[11,-11],[5,-7],[-36,-20],[-13,-10],[-9,-14],[0,-15],[-10,-9],[-8,-12],[-10,-10],[-12,-5],[-14,0],[-11,-2],[-11,-6],[-11,-10],[-6,-13],[-3,-10],[-5,-6],[-13,1],[-16,5],[-7,0],[-8,-1],[-6,-5],[-6,-7],[-4,-8],[5,-13],[-7,-13],[-13,-11],[-12,-4],[-2,-3],[6,-14],[0,-6],[-4,-5],[-10,-8],[-5,-5],[-13,-20],[-10,-9],[-4,-6],[-4,-15],[-9,-7],[-2,-7],[-2,-10],[-5,-4],[-14,-3],[-14,-7],[-9,-7],[-9,-8],[-9,-12],[-6,-14],[1,-13],[2,-14],[-3,-18],[-8,-10],[-25,-7],[-5,-6],[-4,-10],[-31,-46],[-6,-5],[-8,-2],[-10,4],[1,-5],[2,-10],[1,-5],[-12,1],[-12,-7],[-10,-11],[-4,-12],[-2,-3],[31,-17],[12,-19],[14,-32],[9,-33],[-1,-10],[-2,-12],[-102,-90],[-24,-15],[-112,-26],[-107,-53],[-9,-11],[-26,-92],[-27,-90],[-32,-111],[-34,-69],[-30,-63],[-48,-98],[-38,-55],[-16,-29],[-20,-52],[-8,-11],[-10,-7],[-80,-45],[-70,-39],[-82,-46],[-89,-49],[-17,-12],[-46,-60],[-14,-31],[-36,-151],[-13,-35],[-17,-33],[-24,-27],[-76,-67],[-49,-69],[-7,-15],[-3,-14],[-8,-91],[-3,-12],[-8,-7],[-99,-32],[-87,-29],[-51,-5],[-49,5],[-53,-5],[-53,-12],[-47,-18],[-75,-40],[-71,-38],[-50,-12],[-44,6],[-31,24],[-24,37],[-19,48],[-2,14],[2,28],[-5,15],[-7,10],[-18,15],[-20,25],[-15,13],[-18,10],[-18,6],[-13,2],[-12,-3],[-24,-10],[-24,-4],[-11,-4],[-11,-8],[-34,-42],[-43,-33],[-6,-7]],[[5766,3141],[-71,36],[-38,25],[-10,9],[-12,13],[-38,61],[-10,21],[-38,123],[-2,14],[-1,5],[-3,7],[-6,7],[-3,6],[-1,6],[0,26],[0,5],[-3,6],[-4,7],[-8,7],[-5,3],[-5,1],[-10,10],[-14,32],[-17,6],[-58,7],[-4,2],[-2,2],[-1,3],[0,6],[0,3],[-1,3],[-2,3],[-3,2],[-4,2],[-15,5],[-17,3],[-16,0],[-8,-1],[-66,-30]],[[5897,5653],[10,-2],[6,4],[1,1],[2,4],[1,5],[2,4],[3,6],[8,10],[4,4],[6,2],[9,-2],[6,0],[9,2],[5,1],[5,4],[14,12],[7,3],[6,0],[12,-3],[20,-10],[7,-2],[8,-2],[6,1],[28,12],[8,3],[5,0],[5,0],[3,-2],[3,-2],[7,-8],[5,-4],[12,-6],[9,19],[14,15],[2,5],[1,4],[-1,3],[-2,7],[5,23],[2,2],[3,4],[10,3],[8,5],[4,4],[2,3],[2,9],[1,19],[0,5],[-6,17],[1,5],[3,3],[5,2],[3,2],[0,3],[-3,4],[-8,10],[-3,5],[-5,12],[30,83],[1,7],[0,5],[-4,12],[0,11],[4,9],[12,17],[26,56],[5,14],[5,31],[6,14],[9,11],[10,10],[8,10],[11,32],[10,13],[23,23],[3,6],[1,7],[0,14],[2,11],[4,10],[5,8],[22,27],[6,4],[18,10],[6,4],[8,12],[5,15],[6,31],[3,9],[12,21],[3,7],[1,7],[1,15],[3,14],[6,12],[23,32],[6,12],[4,13],[1,52],[-3,8],[-3,2],[-1,3],[0,5],[-1,4],[-2,2],[-5,2],[-4,1],[-4,-1],[-2,-2],[-2,-5],[-1,-2],[-1,-2],[-2,-2],[-2,-1],[-4,-1],[-2,-2],[-2,-3],[-2,-5],[-1,-3],[-3,-4],[-3,-1],[-3,0],[-2,2],[-4,4],[-3,8],[-2,10],[9,31],[1,13],[-2,5],[-2,1],[-3,1],[-3,0],[-27,-6],[-5,1],[-5,0],[-7,4],[-4,5],[-2,6],[-3,22],[-7,26],[0,7],[0,14],[-3,15],[-21,47],[-2,7],[2,4],[2,2],[6,2],[7,7],[2,12],[5,54],[3,13],[3,10],[15,25],[6,5],[4,4],[8,3],[9,5],[6,4],[40,17],[2,0],[2,-1],[4,-3],[3,-2],[8,-2],[2,-1],[1,-2],[3,-1],[5,-1],[7,3],[5,2],[4,1],[5,-1],[5,1],[9,5],[5,1],[8,1],[7,2],[5,2],[3,4],[8,12],[3,6],[1,5],[0,3],[-1,4],[-2,4],[-3,4],[-2,1],[-3,0],[-2,-1],[-2,0],[-2,0],[-1,3],[-1,7],[7,33],[-1,3],[-5,8],[-6,10],[0,2],[0,3],[0,4],[0,3],[-1,3],[-1,3],[-2,2],[-3,2],[-3,6],[-1,10],[3,16],[2,6],[5,5],[5,-4],[3,-5],[4,-4],[4,-3],[17,-8],[6,-2],[5,0],[25,9],[4,-1],[16,-11],[7,-6],[5,-6],[2,-5],[1,-5],[0,-23],[1,-5],[2,-4],[4,-9],[5,-9],[11,-14],[6,-7],[6,-4],[30,-13],[7,8],[-1,16],[2,13],[4,13],[6,8],[6,8],[6,10],[3,15],[-2,16],[-6,11],[-9,9],[-8,11],[4,0],[-10,8],[-3,12],[3,12],[8,11],[5,11],[-2,13],[1,14],[11,16],[34,18],[10,3],[18,-1],[6,1],[5,4],[2,4],[3,5],[6,1],[-3,9],[1,7],[3,6],[6,7],[2,5],[3,10],[3,5],[27,21],[5,8],[1,15],[-1,10],[0,8],[11,20],[2,39],[3,11],[12,29],[4,6],[3,4],[7,22],[6,6],[13,5],[7,4],[12,16],[3,2],[16,-8],[24,-2],[12,-3],[9,-5],[7,6],[8,2],[7,-2],[7,-6],[5,29],[-10,72],[9,28],[9,4],[58,13],[11,6],[20,18],[19,14],[23,6],[26,2],[10,-1],[10,-24],[6,-18],[4,-6],[5,-4],[3,-2],[27,-3],[11,-3],[8,-3],[4,-4],[2,-3],[-1,-3],[-2,-2],[-2,-2],[-5,-3],[-4,-3],[-8,-8],[-1,-3],[-1,-3],[0,-5],[1,-3],[3,-4],[13,-11],[5,-3],[5,-1],[8,1],[8,3],[2,1],[3,2],[2,3],[4,6],[5,10],[2,4],[3,3],[7,2],[3,1],[1,2],[0,2],[-1,5],[0,2],[1,2],[1,3],[13,14],[4,2],[4,2],[3,-1],[4,-3],[3,-3],[3,-4],[1,-3],[1,-2],[-4,-9],[-1,-3],[-1,-3],[1,-3],[2,-3],[11,-12],[14,1],[2,-2],[3,-4],[0,-4],[1,-3],[-1,-10],[0,-3],[-1,-3],[-2,-2],[-5,-3],[-3,-5],[1,-2],[2,-2],[3,-2],[2,0],[3,0],[3,0],[3,2],[4,0],[5,-1],[5,-4],[1,-2],[0,-3],[-1,-3],[-1,-2],[-2,-3],[-2,-2],[-5,-2],[-3,-2],[-4,-3],[-2,-5],[-2,-12],[1,-5],[2,-4],[3,-1],[4,0],[14,2],[3,-1],[7,-5],[3,-1],[4,0],[9,1],[4,-1],[1,-2],[-1,-3],[-1,-2],[1,-9]],[[4093,267],[5,-2],[-13,-6],[-2,-10],[-5,-10],[-16,-5],[-11,5],[-10,11],[-5,9],[-2,4],[-4,13],[9,-10],[14,-1],[28,11],[12,-9]],[[5766,3141],[-54,-60],[-7,-13],[-45,-145],[-15,-22],[-52,-55],[-37,-65],[-73,-75],[-40,-30],[-55,-61],[-26,-47],[-16,-57],[-14,-92],[-13,-89],[5,-31],[19,-23],[96,-43],[25,-19],[45,-43],[27,-11],[148,6],[23,-6],[22,-14],[18,-24],[3,-27],[-4,-62],[2,-30],[-2,-13],[-5,-15],[-4,-15],[1,-14],[5,-30],[-3,-28],[-9,-27],[-33,-61],[-3,-12],[-1,-27],[-5,-39],[0,-11],[12,-38],[19,-36],[25,-32],[25,-24],[11,-15],[13,-42],[9,-17],[10,-8],[24,-10],[35,-8],[72,2],[24,6],[24,11],[23,8],[25,-6],[13,-20],[2,-31],[-3,-65],[-4,-86],[5,-26],[10,-16],[25,-30],[5,-18],[3,-18],[7,-15],[21,-25],[38,-38],[11,-15],[7,-18],[21,-103],[13,-41],[17,-39],[37,-64],[21,-66],[12,-21],[5,-8],[-13,-10],[-41,-22],[-8,-17],[-5,-24],[1,-21],[12,-12],[3,-4],[0,-5],[-1,-5],[-2,-5],[-1,-15],[-1,-6],[0,-5],[1,-6],[1,-5],[15,-9],[24,-5],[22,-8],[5,-18],[-6,-7],[-30,-12],[-7,-9],[-4,-8],[-7,-5],[-12,0],[-13,6],[-12,1],[-11,-4],[-38,-28],[-10,-13],[3,-14],[8,-12],[-4,-5],[-11,-3],[-13,-5],[-23,-13],[-12,-4],[-83,7],[-23,13],[-10,11],[-5,11],[-3,29],[-6,27],[1,8],[3,5],[11,7],[3,8],[-7,18],[-26,1],[-65,-18],[-28,-22],[-11,-4],[-33,-2],[-12,-7],[-23,-18],[-12,-3],[-13,-1],[-35,-13],[-22,-2],[-7,-5],[-7,-12],[-15,-46],[-6,-14],[-23,-21],[-27,-7],[-125,-2],[-34,5],[-27,15],[-48,57],[-18,9],[-174,5],[-46,-19],[-25,-4],[-11,3],[-35,19],[-15,5],[-12,-3],[-25,-15],[-16,-7],[-12,1],[-8,10],[-12,24],[-5,6],[-5,5],[-6,3],[-8,-1],[-4,-5],[-1,-6],[-2,-6],[-19,-40],[-6,-5],[-12,10],[-3,43],[-9,16],[-33,1],[-13,-31],[0,-60],[0,-78],[-1,-104],[-48,0],[-61,0],[-71,1],[-29,-5],[-19,-16],[0,6],[-1,7],[-2,6],[-4,3],[-8,-1],[0,-7],[2,-7],[1,-6],[-5,-9],[-8,-9],[-8,-4],[-9,4],[-4,7],[-1,6],[-3,3],[-6,-1],[-5,-5],[-2,-7],[1,-7],[-1,-5],[1,-1],[3,-3],[1,-3],[-2,-2],[-3,0],[-6,1],[-2,0],[-4,-3],[-11,-6],[-5,-3],[-14,-26],[-1,-1],[0,1],[-15,47],[-1,-24],[1,-7],[1,-5],[5,-10],[2,-3],[-3,-15],[-11,-29],[-3,-15],[0,-14],[7,-21],[2,-14],[-6,-1],[-11,10],[-18,19],[-2,-18],[-10,0],[-12,10],[-5,16],[2,5],[8,7],[2,4],[0,14],[0,3],[5,13],[4,3],[8,0],[0,5],[-8,7],[-10,12],[-8,15],[-3,17],[4,20],[2,9],[-10,-1],[-15,10],[-6,3],[17,-65],[8,-17],[-12,-28],[-7,-13],[-6,-5],[-4,5],[-3,8],[-1,9],[-1,6],[-3,4],[-7,-1],[-6,-3],[-1,-3],[-6,2],[-7,4],[-6,5],[-2,8],[-2,5],[-5,0],[-6,0],[-4,2],[-3,8],[0,9],[-3,7],[-5,1],[-6,-4],[-9,-10],[-2,11],[-1,28],[-5,7],[-4,-17],[-6,-50],[-4,-10],[-18,3],[-19,6],[-33,17],[7,-6],[7,-10],[1,-10],[-7,-11],[-10,-2],[-14,1],[-23,6],[-6,1],[-5,-1],[-4,2],[-1,6],[0,9],[3,7],[3,5],[2,8],[5,42],[3,9],[0,5],[-2,0],[-1,1],[0,2],[0,3],[-8,-13],[0,-31],[-5,-14],[-10,-5],[-9,4],[-6,9],[-1,13],[-8,-9],[-5,3],[-5,9],[-7,7],[-9,1],[-2,-6],[4,-8],[7,-7],[-35,9],[-7,6],[5,2],[5,-1],[4,0],[7,4],[4,7],[8,17],[5,7],[-6,-3],[-7,-5],[-7,-4],[-6,2],[-1,7],[2,12],[4,17],[-27,6],[-7,4],[10,26],[6,8],[9,-3],[17,12],[6,8],[2,11],[-14,-13],[-13,1],[-13,8],[-17,4],[-35,-2],[-18,3],[-8,11],[-3,25],[-6,18],[-16,32],[-3,10],[-2,9],[0,38],[-3,6],[-5,8],[2,5],[4,8],[2,3],[-4,20],[12,10],[19,3],[16,-2],[-12,7],[-11,3],[-26,0],[-7,9],[1,19],[8,34],[-4,-6],[-4,-4],[-9,-6],[-1,20],[-6,17],[-10,15],[-13,15],[13,13],[-3,5],[-14,2],[-6,9],[-3,11],[-2,11],[-1,10],[9,-4],[12,-1],[11,1],[10,4],[-18,13],[-9,5],[-11,3],[-11,-1],[-8,-3],[-5,2],[-1,15],[4,21],[12,19],[15,16],[15,13],[17,10],[2,5],[-6,11],[-8,6],[-6,0],[-6,-4],[-23,-5],[-5,2],[-4,15],[-3,8],[-5,6],[-6,3],[-11,-2],[-6,-2],[-7,-1],[-12,5],[-4,-6],[5,-16],[-11,6],[-26,21],[-11,3],[-11,6],[-8,8],[-5,8],[-5,0],[0,-14],[2,-5],[3,-6],[-15,2],[-12,9],[-11,11],[-13,9],[-18,0],[-16,-6],[-16,-3],[-14,9],[-6,-3],[-33,-5],[-15,-6],[-7,-2],[-16,7],[2,14],[12,16],[17,9]],[[6541,8415],[9,3],[6,6],[5,8],[5,17],[5,8],[7,5],[4,6],[1,7],[-3,7],[1,33],[16,20],[42,30],[16,24],[5,14],[-2,10],[-19,16],[-34,39],[-12,19],[2,20],[18,24],[31,26],[6,11],[1,15],[-6,10],[-8,9],[-4,7],[-4,5],[-4,16],[3,15],[4,13],[1,15],[-6,11],[-10,7],[-34,17],[-12,10],[-6,12],[8,15],[11,15],[3,13],[-2,13],[-8,14],[-4,4],[-9,7],[-4,5],[-3,8],[0,7],[1,7],[-1,7],[-4,11],[-21,37],[-8,11],[-28,20],[-7,11],[-3,25],[-9,10],[-18,8],[-17,4],[-17,7],[-15,16],[-9,7],[-22,12],[-7,6],[-1,3],[-2,11],[6,12],[20,19],[3,7],[3,16],[3,6],[24,15],[19,9],[6,4],[4,7],[3,16],[3,5],[39,13],[13,10],[47,52],[10,7],[12,4],[15,3],[12,6],[-3,10],[-7,12],[3,11],[16,15],[5,8],[5,12],[7,23],[6,7],[12,1],[12,-5],[36,-24],[18,-19],[9,-3],[10,0],[9,4],[8,8],[-2,10],[-11,20],[0,24],[12,8],[40,3],[10,3],[8,7],[8,8],[8,7],[29,16],[10,10],[13,7],[26,6],[9,10],[1,6],[-2,7],[-1,6],[5,6],[7,4],[16,5],[36,5],[10,6],[-4,12],[-9,15],[2,9],[10,7],[27,11],[55,13],[27,1],[26,8],[28,-1],[13,3],[38,32],[30,15],[40,9],[41,3],[51,-7],[55,2],[18,5],[15,8],[15,4],[19,-8],[9,0],[7,3],[15,10],[10,4],[92,9],[19,-2],[19,-5],[9,-1],[9,1],[30,11],[30,-2],[41,5],[19,-2],[20,-4],[81,14],[42,-2],[1,0],[1,0]]],
transform:{scale:[.0016206212906290677,.0013361294212421236],translate:[60.844378703000075,23.694525458000058]}},m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();