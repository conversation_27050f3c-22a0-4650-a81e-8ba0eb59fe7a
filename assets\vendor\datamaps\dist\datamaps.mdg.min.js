!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo={type:"Topology",objects:{mdg:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Alaotra-Mangoro"},id:"MG.",arcs:[[0,1,2,3,4,5]]},{type:"Polygon",properties:{name:"Amoron'i Mania"},id:"MG.",arcs:[[6,7,8,9,10,11]]},{type:"Polygon",properties:{name:"Analamanga"},id:"MG.",arcs:[[-4,12,13,14,15]]},{type:"MultiPolygon",properties:{name:"Analanjirofo"},id:"MG.",arcs:[[[16]],[[17,18,19,-1,20]]]},{type:"Polygon",properties:{name:"Androy"},id:"MG.",arcs:[[21,22,23]]},{type:"Polygon",properties:{name:"Anosy"},id:"MG.",arcs:[[24,25,-24,26,27]]},{type:"Polygon",properties:{name:"Atsimo-Andrefana"},id:"MG.",arcs:[[28,29,-27,-23,30,31,-10]]},{type:"Polygon",properties:{name:"Atsimo-Atsinanana"},id:"MG.",arcs:[[32,-25,33,34,35]]},{type:"Polygon",properties:{name:"Atsinanana"},id:"MG.",arcs:[[36,-7,37,-2,-20,38]]},{type:"Polygon",properties:{name:"Betsiboka"},id:"MG.",arcs:[[-5,-16,39,40,41,42]]},{type:"Polygon",properties:{name:"Boeny"},id:"MG.",arcs:[[43,-42,44,45]]},{type:"Polygon",properties:{name:"Bongolava"},id:"MG.",arcs:[[-15,46,47,48,49,-40]]},{type:"MultiPolygon",properties:{name:"Diana"},id:"MG.",arcs:[[[50]],[[51,52,53]]]},{type:"Polygon",properties:{name:"Haute Matsiatra"},id:"MG.",arcs:[[54,-35,55,-29,-9]]},{type:"Polygon",properties:{name:"Ihorombe"},id:"MG.",arcs:[[-34,-28,-30,-56]]},{type:"Polygon",properties:{name:"Itasy"},id:"MG.",arcs:[[-14,56,-47]]},{type:"Polygon",properties:{name:"Melaky"},id:"MG.",arcs:[[-45,-41,-50,57,58]]},{type:"Polygon",properties:{name:"Menabe"},id:"MG.",arcs:[[-49,59,-11,-32,60,-58]]},{type:"Polygon",properties:{name:"Sava"},id:"MG.",arcs:[[-18,61,-52,62]]},{type:"Polygon",properties:{name:"Sofia"},id:"MG.",arcs:[[-62,-21,-6,-43,-44,63,-53]]},{type:"Polygon",properties:{name:"Vakinankaratra"},id:"MG.",arcs:[[-3,-38,-12,-60,-48,-57,-13]]},{type:"Polygon",properties:{name:"Vatovavy-Fitovinany"},id:"MG.",arcs:[[64,-36,-55,-8,-37]]}]}},arcs:[[[7819,6532],[-33,-80],[0,-65],[-86,-110],[34,-119],[-69,-82],[90,-54],[-72,-93],[-121,-46],[34,-37],[138,-37],[35,-64]],[[7769,5745],[-52,-73],[-172,0],[-86,-18],[86,-55],[120,-74],[18,-73],[-173,-55],[-172,-19],[-51,-101],[0,-146],[69,-110],[120,-166],[0,-137],[-120,-18],[-18,-111],[-240,-36],[51,-138],[-120,0],[-52,-165],[-224,9],[-103,46],[-103,-37],[-193,-19]],[[6354,4249],[13,17],[4,19],[0,4],[-3,6],[-3,7],[-3,9],[-2,44],[1,9],[5,5],[16,11],[5,6],[2,6],[0,5]],[[6389,4397],[-4,14],[1,17],[2,6],[3,5],[19,16],[9,11],[4,8],[2,9],[2,5],[4,3],[19,11],[23,19],[5,6],[4,5],[4,11],[10,48],[0,9],[-3,15],[-1,25],[-8,11],[-2,6],[-1,7],[3,22],[-1,6],[-2,3],[-45,45],[-3,5],[-2,10],[0,6],[2,5],[23,24],[7,9],[2,8],[0,9],[2,5],[3,4],[7,5],[5,6],[6,7],[2,7],[4,35],[-1,17],[4,24],[-1,5],[-10,20],[-5,18],[-3,56],[18,49],[46,67],[5,12],[1,10],[-1,9],[11,61],[-1,27],[2,6],[2,4],[2,3],[8,5],[22,10],[5,4],[7,6],[2,5],[-1,5],[-2,4],[-3,3],[-11,10],[-2,5],[-2,7],[0,20],[-2,6],[-21,19],[-4,4],[-1,7],[3,45],[-1,2],[-1,2],[-5,12],[-1,15],[3,8],[3,7],[5,7],[0,4],[-1,4],[-8,7],[-6,7],[-7,16],[-4,8],[-10,10],[-3,4],[-5,32],[-2,4],[-4,3],[-5,3],[-6,2],[-21,4],[-12,4],[-6,3],[-19,5],[-5,3],[-5,4],[-2,4],[-6,15]],[[6427,5637],[45,32],[36,35],[5,8],[-4,9],[-1,5],[0,9],[3,4],[5,4],[15,7],[10,7],[3,5],[1,5],[0,12],[2,9],[4,5],[4,4],[5,2],[3,1],[6,1],[7,1],[8,0],[8,0],[10,0],[10,1],[16,8],[7,5],[4,6],[6,22],[3,27],[5,11],[6,3],[15,3],[4,3],[2,4],[0,17],[4,10],[5,5],[6,3],[14,5],[9,5],[2,4],[-1,9],[3,7],[1,5],[-2,4],[-6,2],[-25,1],[-7,3],[-4,4],[-2,7],[1,5],[4,3],[18,8],[8,5],[4,6],[14,30],[3,6],[5,4],[13,7],[10,6],[17,14],[7,7],[3,6],[-6,13],[-1,8],[2,4],[5,3],[10,5],[9,6],[29,27],[7,10],[2,8],[-3,3],[-3,4],[-3,4],[-2,6],[-2,10],[-3,6],[-3,4],[-3,5],[-1,7],[2,12],[-3,6],[-4,5],[-7,1],[-17,1],[-18,0],[-9,0],[-8,1],[-7,2],[-24,8],[-19,10],[-32,23],[0,3],[-1,19],[-4,6],[-6,5],[-6,4],[-1,3],[16,24],[0,6],[-3,5],[-6,2],[-12,4],[-8,1],[-6,2],[-5,2],[-4,3],[-6,7],[-4,3],[-5,3],[-5,2],[-7,2],[-5,3],[-5,4],[-4,13],[-3,4],[-4,4],[-4,2],[-10,4],[-7,4],[-4,2],[-3,3],[-5,8],[-4,3],[-4,3],[-5,3],[-8,5],[-21,10],[-7,6],[-7,9],[-4,8],[2,5],[3,5],[6,6],[2,6],[-2,26],[-5,19],[-5,7],[-5,8],[-21,19],[-2,4],[-12,34],[0,14]],[[6446,6652],[4,17],[2,5],[10,11],[2,5],[11,44],[1,6],[-2,9],[-11,26],[0,6],[2,5],[9,11],[8,6],[10,5],[17,6],[11,6],[5,5],[1,5],[-1,9],[4,4],[5,1],[7,-1],[11,-3],[7,-1],[4,1],[6,1],[12,3],[7,1],[7,0],[8,-1],[14,-3],[15,-2],[17,0],[8,1],[8,-1],[7,-1],[13,-4],[7,-1],[7,-1],[35,-1],[9,0],[7,1],[7,1],[7,2],[7,1],[24,3],[8,1],[19,6],[7,1],[66,6],[17,-1],[8,-1],[8,0],[8,0],[15,2],[7,-1],[7,-1],[6,-2],[16,-8],[21,-14],[5,-2],[18,-6],[10,-5],[7,-2],[7,-1],[25,-3],[6,-2],[5,-2],[3,-4],[1,-14],[3,-4],[4,-2],[6,0],[14,2],[9,0],[17,0],[32,3],[7,0],[7,-2],[11,-4],[15,-7],[38,-26],[24,-8],[14,-3],[30,-5],[27,-7],[23,-8],[9,-5],[4,-4],[4,-7],[4,-4],[5,-2],[13,-4],[5,-2],[2,-3],[3,-11],[3,-3],[8,-7],[3,-3],[2,-4],[3,-18],[-2,-8],[1,-4],[3,-4],[4,-3],[4,-2],[15,-8],[3,-3],[2,-4],[0,-4],[-2,-4],[-6,-7],[-22,-20],[-6,-7],[-4,-8],[-1,-4],[0,-10],[3,-8],[5,-8],[13,-14],[43,-33],[6,-3],[6,-2],[7,-1],[8,0],[8,1],[6,2],[13,4],[11,4],[25,13],[117,41],[7,2],[7,1],[8,0],[17,0],[23,2],[9,0],[28,-6],[8,-1],[8,0]],[[6173,4045],[-1,-37],[-4,-14],[-6,-7],[-15,-21],[-22,-47],[-5,-18],[5,-61],[4,-6],[4,-3],[5,-3],[3,-3],[9,-11],[5,-3],[5,-2],[22,-4],[7,-2],[6,-2],[14,-8],[5,-2],[13,-4],[5,-3],[4,-2],[4,-4],[5,-3],[6,-3],[9,-3]],[[6260,3769],[-126,-60],[-103,-19],[-17,-100],[-17,-46],[-104,-10],[-69,-36],[-17,-119]],[[5807,3379],[-172,9],[-51,46],[-104,18],[-223,-46],[-103,-73],[-173,0],[-189,64],[-137,101],[-35,55],[-120,-19],[-103,19],[-121,-28],[-120,-55],[-104,-55],[-189,-27],[-172,-46],[-183,0]],[[3508,3342],[4,3],[2,4],[2,4],[10,66]],[[3526,3419],[4,9],[0,6],[-3,10],[-9,17],[-4,9],[0,7],[1,9],[0,9],[1,4],[8,21],[3,4],[7,6],[4,4],[2,4],[2,14],[2,4],[3,4],[5,3],[7,2],[27,1],[7,1],[6,3],[4,3],[1,4],[1,5],[-5,34],[-36,78],[-1,7],[3,3],[3,4],[2,4],[4,9],[1,13],[2,3],[2,3],[14,11],[2,4],[2,4],[1,10],[-1,4],[-11,29],[-1,4],[1,5],[4,8],[6,7],[2,4],[0,6],[-2,7],[-23,50],[-2,6],[-1,14],[-18,38],[-11,14],[-6,8],[-2,6],[0,10],[-1,8],[-4,6],[-9,8],[-2,5],[1,4],[2,4],[1,5],[-1,13],[1,5],[2,4],[4,3],[5,3],[11,4],[6,2],[3,4],[2,4],[-3,10],[1,5],[6,8],[2,5],[-1,4],[-4,3],[-6,3],[-20,5],[-11,4],[-7,4],[-7,4],[-9,7],[-2,6],[1,4],[11,10],[3,4],[2,4],[3,18],[3,7]],[[3517,4186],[30,-17],[35,-9],[10,-1],[12,0],[12,1],[8,2],[14,5],[9,0],[10,-1],[32,-3],[67,-16],[89,-2],[9,-3],[18,-9],[10,-3],[10,0],[25,2],[12,-2],[15,-11],[8,-26],[11,-11],[23,-7],[23,-2],[17,-4],[7,-15],[4,-16],[10,1],[33,20],[34,12],[13,7],[10,12],[18,-8],[41,-11],[15,-6],[10,-11],[2,-12],[-8,-9],[-21,-4],[11,-9],[13,-18],[13,-8],[-33,-10],[-5,-11],[14,-12],[24,-12],[55,-19],[32,-8],[26,-3],[18,2],[33,10],[16,3],[17,-1],[13,-2],[12,-4],[27,-5],[26,-3],[17,0],[12,-2],[8,-5],[7,-5],[11,-5],[32,-1],[26,11],[23,12],[22,3],[92,-24],[30,-4],[33,1],[23,7],[20,3],[23,-8],[27,-7],[27,5],[22,7],[13,2],[35,1],[8,2],[5,2],[5,2],[9,6],[32,15],[42,24],[12,9],[5,3],[18,7],[8,1],[14,1],[8,0],[7,-2],[15,-5],[26,-6],[11,-1],[10,1],[41,9],[63,2],[11,1],[27,6],[24,3],[24,0],[12,1],[8,2],[2,4],[7,22],[1,14],[2,4],[3,3],[5,3],[9,5],[5,3],[19,6],[5,2],[7,3],[10,1],[8,0],[6,-2],[4,-3],[4,-4],[6,-4],[10,-4],[9,-2],[7,2],[5,2],[21,13],[3,2],[6,1],[26,4],[15,2],[11,0],[9,-2],[6,-1],[17,-7],[7,-2],[85,-11],[14,-1],[10,1],[5,3],[4,3],[3,3],[5,4],[6,3],[13,3],[9,0],[8,-2],[6,-2],[5,-3],[7,-7],[6,-4],[7,-1],[7,0],[69,21],[15,3],[42,3],[8,2],[5,2],[19,11],[7,2],[9,2],[13,2],[8,-1],[6,-3],[2,-3],[-2,-3],[-4,-3],[-9,-6],[-3,-3],[-2,-3],[2,-4],[5,-2],[7,-2],[9,-2],[32,-3],[7,-1],[6,-2],[5,-3],[4,-2],[4,-4]],[[6389,4397],[-186,73],[-241,55],[0,92],[35,101],[-155,0]],[[5842,4718],[-69,119],[-147,126],[-18,21],[-39,8],[-43,14],[-96,11],[-64,8]],[[5366,5025],[-25,18],[-57,16],[-43,32],[-18,34],[-47,21],[-23,22],[-43,102],[-138,68],[-183,128],[-119,161],[-45,124]],[[4625,5751],[91,4],[82,10],[21,2],[29,-1],[51,-5],[57,-2],[18,2],[22,6],[17,0],[13,1],[11,-1],[9,-6],[15,-24],[3,-7],[-1,-9],[-5,-15],[6,-6],[15,-4],[72,0],[39,-3],[29,-4],[14,-6],[17,-10],[12,2],[8,5],[15,14],[8,5],[22,10],[13,4],[14,2],[52,4],[15,3],[46,17],[15,4],[21,3],[35,2],[31,0],[37,-3],[58,-11],[27,-6],[13,-6],[0,-6],[0,-6],[3,-7],[10,-15],[8,-6],[11,-4],[40,-11],[12,-4],[9,-6],[6,-6],[12,-7],[22,-5],[38,-6],[59,-4],[46,3],[19,-3],[10,-6],[16,-14],[13,-6],[33,-2],[54,1],[197,19],[40,7],[77,9]],[[9093,6244],[-25,-17],[-12,20],[7,19],[33,37],[6,10],[3,9],[1,21],[5,12],[11,10],[31,18],[93,75],[5,10],[3,12],[4,11],[12,10],[17,6],[53,16],[-6,-18],[-49,-52],[-37,-57],[-67,-75],[-49,-34],[-39,-43]],[[8738,7804],[83,-38],[27,-16],[12,-11],[31,-22],[16,-9],[13,-4],[7,1],[112,24],[7,2],[14,0],[21,-1],[67,-7],[16,0],[7,1],[20,6],[9,2],[13,1],[7,-1],[7,-2],[6,-2],[9,-5],[26,-27],[92,-62],[3,-4],[1,-4],[-2,-3],[-5,-3],[-5,-2],[-6,-2],[-36,-8],[-8,-3],[-5,-2],[-5,-2],[-9,-6],[3,-6],[8,-8],[29,-17],[11,-9],[6,-7],[-1,-4],[-2,-4],[-15,-13],[-3,-3],[-7,-17],[-1,-9],[1,-5],[1,-4],[7,-7],[49,-39],[7,-7],[0,-5],[-2,-4],[-2,-4],[-25,-33],[-2,-4],[-1,-4],[1,-19],[0,-4],[-14,-28],[0,-5],[1,-6],[23,-27],[18,-30],[5,-23],[5,-11],[7,-11],[8,-7],[12,-9],[5,-6],[3,-6],[3,-13],[54,-86],[9,-3],[0,-1]],[[9484,7087],[-30,-2],[-68,31],[-26,16],[-10,21],[-6,38],[-3,5],[-17,4],[-19,8],[-18,9],[-12,8],[-9,10],[-7,12],[-4,12],[1,12],[7,12],[9,9],[2,10],[-14,11],[-8,4],[-8,3],[-9,1],[-12,0],[-7,2],[-39,27],[-3,9],[7,23],[1,13],[-11,37],[2,4],[5,4],[2,5],[-9,7],[-7,1],[-25,1],[-36,-9],[-50,-3],[-89,0],[-45,-8],[-30,-15],[-79,-57],[2,-19],[29,-42],[2,-10],[-2,-10],[-14,-20],[-3,0],[9,-9],[16,-8],[15,-10],[11,-24],[19,-24],[10,-37],[13,-23],[21,-20],[3,-8],[-4,-13],[-10,-12],[-42,-38],[-13,-21],[0,-25],[9,-24],[18,-21],[35,-17],[90,-17],[40,-13],[31,-24],[6,-21],[-29,-68],[-3,-29],[8,-25],[24,-11],[15,-5],[3,-11],[-5,-28],[-6,-11],[-2,-5],[0,-25],[-6,-10],[-72,-65],[-40,-23],[-61,-26],[2,-33],[10,-16],[21,-16],[29,-14],[29,-7],[33,-3],[37,2],[-42,-12],[-43,-9],[-81,-6],[-34,-10],[-69,-3],[-28,-3],[-22,-8],[-17,-11],[-92,-95],[-64,-92],[-70,-85],[-14,-53],[7,-11],[41,-34],[26,-71]],[[8586,5920],[-198,-10],[-52,-55],[0,-55],[-172,-27],[-206,-9],[-189,-19]],[[7819,6532],[68,6],[10,2],[12,3],[5,3],[1,4],[-3,3],[-13,9],[-8,6],[-1,5],[0,5],[5,7],[6,4],[9,5],[13,5],[11,6],[6,5],[14,18],[17,15],[7,9],[4,6],[-1,10],[1,6],[6,9],[7,4],[8,2],[9,1],[7,0],[8,1],[54,15],[28,11],[21,16],[4,5],[2,4],[1,8],[7,8],[81,77],[7,8],[3,7],[-2,4],[-2,4],[-8,11],[-1,3],[1,9],[0,4],[-2,4],[-3,4],[-4,3],[-6,2],[-14,3],[-16,2],[-54,1],[-43,4],[-128,30],[-6,3],[-5,2],[-2,5],[2,6],[19,23],[12,26],[1,1],[13,22],[10,10],[8,6],[34,19],[19,13],[18,18],[4,7],[1,5],[-2,9],[-16,42],[-9,15],[-12,14],[-7,6],[-18,12],[-3,3],[-2,4],[-1,4],[0,5],[3,9],[17,32],[23,90],[0,4],[-1,4],[-3,4],[-2,3],[-3,5],[-2,5],[3,10],[3,5],[5,4],[22,10],[15,10],[6,6],[3,5],[0,3],[-4,3],[-5,0],[-13,-2],[-7,-1],[-7,2],[-6,2],[-5,2],[-7,1],[-7,1],[-14,-2],[-8,-1],[-9,0],[-8,1],[-8,1],[-6,2],[-6,2],[-4,3],[-7,7],[-2,4],[-1,4],[-2,9],[-2,4],[-7,7],[-3,4],[0,6],[4,10],[5,5],[7,3],[16,2],[35,3],[16,2],[7,0],[9,0],[15,-1],[9,0],[8,1],[7,1],[39,10],[30,5],[13,3],[6,3],[7,4],[7,6],[7,3],[6,3],[12,4],[7,1],[7,2],[8,0],[9,1],[18,-1],[10,0],[12,2],[38,12],[15,3],[7,2],[13,7],[5,3],[7,1],[8,1],[8,0],[22,-4],[9,0],[9,1],[10,4],[6,4],[5,4],[5,7],[1,5],[0,4],[-1,5],[-2,4],[-3,3],[-8,6],[-3,3],[-1,5],[0,5],[1,6],[4,4],[6,2],[8,0],[7,-1],[21,-4],[17,-2],[17,0],[10,1],[9,2],[12,4],[5,4],[4,4],[3,14],[0,9],[-2,5],[-2,3],[-4,5],[-1,3],[0,5],[2,5],[12,17],[2,4],[2,9],[-1,9],[-8,16],[-1,5],[0,6],[4,7],[5,4],[6,2],[7,-1],[6,-2],[32,-11],[4,-1],[6,-2],[7,-1],[9,0],[8,0],[9,2],[9,4],[24,18],[6,2],[6,2],[6,2],[13,2],[10,-1]],[[4140,293],[-272,-58],[-172,-44],[-192,-52],[-138,-63],[-26,-9],[-29,-6],[-31,-1],[-69,-32],[-95,-10],[-390,-8],[-21,-2],[-20,-5],[-21,-3],[-24,2],[-13,9],[-13,28],[-11,12],[-42,15],[-147,27],[-41,14],[-18,10],[-17,23],[-23,10],[-101,36],[-93,22],[-54,8]],[[2067,216],[67,113],[241,101],[189,91],[52,110],[-34,129],[0,92],[68,82],[35,138],[69,101],[86,55],[258,27]],[[3098,1255],[54,-91],[161,4],[189,-28],[103,-46],[-69,-110],[69,-82],[155,-64],[0,-83],[-52,-101],[35,-73],[0,-110],[275,-74],[122,-104]],[[4481,1423],[-19,-10],[-8,-7],[-18,-21],[-29,-26],[-23,-29],[-3,-8],[-2,-6],[7,-7],[13,-9],[51,-21],[29,-10],[11,-4],[9,-6],[13,-14],[8,-6],[10,-5],[13,-4],[14,-3],[78,-9],[103,-19],[50,-16],[9,-4],[5,-5],[2,-3],[-4,-13],[4,-7],[15,-11],[6,-8],[9,-24],[13,-6],[23,-3],[76,-1],[26,-4],[15,-5],[11,-5],[10,-6],[41,-30],[5,-6],[2,-8],[-3,-7],[-11,-15],[-6,-16],[-1,-12],[2,-8],[5,-7],[11,-6],[15,-3],[25,0],[19,2],[40,6],[23,6],[11,3],[11,5],[6,6],[12,15],[8,6],[19,11],[7,6],[4,8],[-1,7],[-6,7],[-21,19],[-12,14],[-5,8],[-2,8],[0,8],[4,8],[10,5],[14,2],[40,-6],[19,-2],[37,5],[13,5],[8,4],[3,6],[0,6],[-9,22],[1,19],[9,4],[20,2],[98,-14],[19,-5],[36,-12],[176,-47],[68,-27],[3,-1]],[[5775,1079],[-79,-106],[-10,-5],[-17,3],[-29,7],[-7,-5],[19,-40],[-36,-118],[-115,-143],[-35,-26],[-4,-11],[1,-23],[-9,-11],[-71,-27],[-19,-22],[12,-47],[-16,-19],[-14,9],[-9,9],[-5,11],[0,12],[-12,-11],[-1,-10],[3,-12],[1,-12],[-6,-14],[-10,-7],[-40,-7],[-24,-7],[-38,-18],[-66,-39],[-14,-4],[-5,3],[-14,17],[-16,1],[-4,-1],[-1,-2],[-80,-28],[-30,-14],[-15,-5],[-40,-7],[-16,-5],[-19,-13],[-14,-6],[-11,0],[-14,3],[-14,0],[-13,-8],[-13,-5],[-95,-12],[-21,0],[-46,6],[-141,10],[-158,-2],[-205,-25]],[[3098,1255],[68,129],[-86,91],[-258,-9],[-3,89],[165,133],[-51,130]],[[2933,1818],[8,8],[20,15],[-1,62],[29,88],[22,14],[269,77],[262,53],[67,7],[19,0],[89,-4],[58,1],[33,-5],[101,-31],[31,-14],[23,2],[30,-12],[99,-31],[8,-4],[26,-20],[78,-86],[5,-7],[13,-12],[3,-4],[3,-8],[3,-5],[1,-1],[26,-19],[4,-5],[2,-4],[11,-12],[16,-12],[23,-22],[3,-5],[5,-10],[6,-4],[8,-3],[97,-26],[13,-5],[10,-6],[7,-11],[7,-34],[0,-7],[-5,-6],[-13,-13],[-5,-8],[0,-7],[4,-8],[6,-7],[8,-15],[0,-7],[-4,-7],[-6,-7],[-5,-6],[-7,-14],[-7,-6],[-11,-4],[-26,-8],[-9,-5],[-4,-6],[3,-7],[7,-10],[6,-15],[6,-8],[27,-19],[19,-11],[10,-8],[21,-26],[13,-13],[4,-9],[2,-8],[-1,-6],[-2,-6],[-3,-4],[-7,-4],[-10,-5]],[[3508,3342],[-18,-17],[-9,-6],[-15,-7],[-5,-3],[-2,-4],[0,-4],[26,-74],[1,-9],[-4,-19],[-2,-4],[-3,-3],[-4,-3],[-6,-3],[-6,-1],[-8,-2],[-27,-1],[-7,-1],[-6,-2],[-7,-2],[-16,-1],[-18,-6],[-14,-3],[-5,-2],[-4,-4],[-5,-3],[-7,-1],[-8,-1],[-8,-1],[-5,-3],[-4,-3],[-4,-3],[-5,-8],[-4,-3],[-6,-2],[-16,-2],[-6,-2],[-5,-2],[-4,-3],[-2,-4],[2,-13],[-1,-5],[-2,-4],[-3,-4],[-3,-3],[-52,-30],[-3,-3],[-1,-4],[1,-5],[8,-20],[1,-9],[-1,-5],[-8,-16],[0,-4],[1,-4],[23,-48],[1,-5],[-1,-4],[-1,-4],[-4,-3],[-5,-3],[-27,-6],[-3,-3],[-1,-3],[0,-2],[3,-8],[2,-8],[-1,-3],[-3,-4],[-5,-2],[-6,-2],[-7,-2],[-16,-2]],[[3148,2879],[-17,-1],[-47,-1],[-8,-1],[-8,-1],[-6,-2],[-6,-2],[-5,-3],[-4,-3],[-3,-3],[-3,-4],[-31,-93],[-4,-3],[-5,-3],[-6,-2],[-7,-1],[-16,-2],[-7,-1],[-7,-2],[-5,-3],[-4,-3],[-2,-4],[-4,-8],[-3,-4],[-6,-2],[-6,-2],[-57,-7],[-57,0],[-17,-1],[-14,-2],[-6,-2],[-11,-5],[-4,-3],[-3,-3],[5,-8],[11,-10],[30,-21],[20,-17],[8,-11],[4,-5],[4,-5],[12,-33],[1,-4],[-2,-4],[-6,-8],[-2,-4],[-1,-4],[3,-6],[7,-7],[16,-13],[6,-8],[2,-6],[-2,-4],[-4,-3],[-5,-3],[-21,-9],[-2,-5],[2,-6],[7,-10],[2,-6],[0,-6],[-5,-8],[-4,-3],[-6,-2],[-17,0],[-9,-1],[-5,-2],[-4,-4],[-6,-12],[-7,-7],[-2,-4],[-2,-9],[-1,-10],[1,-5],[2,-5],[7,-13],[2,-4],[-2,-4],[-4,-3],[-6,-2],[-22,-4],[-6,-2],[-4,-3],[-12,-14],[0,-4],[2,-8],[1,-4],[-3,-4],[-4,-3],[-6,-2],[-21,-4],[-31,-5],[-14,-3],[-6,-2],[-5,-3],[-2,-4],[-2,-5],[-2,-4],[-4,-3],[-4,-3],[-5,-3],[-8,-6],[-59,-53],[-10,-16],[-3,-9],[-3,-19],[-4,-9],[-7,-7],[-39,-26],[-3,-4],[-3,-3],[-2,-4],[1,-5],[1,-5],[12,-17],[2,-4],[0,-5],[0,-4],[-1,-5],[-10,-10],[-2,-3],[-1,-3],[-1,-10],[-1,-3],[-3,-4],[-3,-3],[-8,-7],[-6,-7],[-15,-29],[-11,-15],[-1,-4],[1,-5],[4,-11],[1,-10],[1,-5],[3,-4],[4,-5],[5,-4],[6,-7],[2,-4],[4,-13],[2,-5],[4,-5],[4,-4],[4,-3],[4,-2],[11,-6],[14,-8],[40,-21],[8,-2],[11,-2],[21,-2],[13,-1],[55,4],[8,0],[26,-2],[9,0],[23,3],[7,-1],[7,-2],[4,-5],[5,-9],[7,-7],[5,-3],[7,-6],[6,-12],[3,-4],[5,-3],[8,-2],[16,-1],[30,1],[18,-1],[25,-3],[9,0],[9,0],[8,1],[18,7]],[[2067,216],[-146,10],[-144,4],[-202,20],[-26,7],[-20,28],[-45,25],[-10,16],[27,-6],[24,-10],[42,-24],[28,-9],[23,-1],[11,8],[-6,17],[-22,-3],[-18,6],[-35,17],[-23,7],[-21,5],[-22,2],[-27,1],[-48,9],[-83,44],[-48,12],[-44,0],[-16,2],[-109,36],[-16,16],[-9,58],[-13,24],[-100,66],[-14,24],[13,66],[-13,24],[-14,13],[-59,31],[-6,3],[-7,3],[-8,6],[-3,6],[-5,15],[-6,6],[-45,20],[-96,32],[-42,21],[-46,52],[-23,110],[18,126],[-24,71],[3,7],[12,13],[3,7],[-1,9],[-17,22],[-11,29],[-9,13],[-17,13],[19,15],[-2,52],[6,23],[30,14],[93,20],[24,16],[-3,12],[-19,19],[3,14],[21,10],[6,5],[9,12],[0,6],[-4,7],[-15,10],[-37,16],[-26,17],[-58,28],[-10,3],[-8,0],[-10,1],[-11,5],[-3,4],[-5,17],[-15,25],[-5,66],[-18,55],[-19,30],[-38,20],[-94,35],[-14,10],[-23,23],[-20,9],[-25,10],[-47,26],[-28,22],[-16,16],[-6,10],[-8,30],[-17,20],[-7,38],[-16,21],[-9,26],[-6,6],[-19,12],[-7,7],[-10,17],[-1,17],[6,34],[-14,67],[-20,36],[-32,33],[-1,7],[2,7],[-1,7],[-9,19],[-15,17],[3,15],[30,11],[37,2],[29,-14],[9,12],[-2,12],[-6,13],[-1,13],[-47,1],[-19,29],[0,66],[3,9],[13,14],[3,8],[0,17],[3,7],[5,4],[6,2],[5,4],[3,1],[6,1],[6,2],[4,2],[-10,20],[2,10],[6,8],[9,9],[12,7],[16,-10],[2,-13],[-9,-26],[28,20],[5,34],[-2,37],[7,28],[15,14],[92,55],[23,9],[27,2],[30,-9],[0,196],[10,18],[29,34],[9,18],[11,-13],[11,0],[26,13]],[[439,3141],[0,-5],[20,-10],[49,-17],[16,-8],[6,-9],[12,-38],[13,-10],[59,-16],[13,-10],[6,-11],[14,-13],[27,-18],[38,-14],[9,-7],[4,-7],[2,-15],[3,-8],[25,-24],[9,-11],[4,-17],[10,-17],[45,-18],[10,-10],[5,-10],[10,-13],[23,-20],[33,-18],[7,-7],[6,-17],[6,-6],[16,-6],[17,-3],[19,-1],[18,-2],[10,-3],[16,-8],[12,-4],[30,-4],[29,3],[50,6],[22,4],[34,23],[23,12],[42,12],[50,10],[51,2],[46,-14],[22,-21],[13,0],[27,14],[12,11],[6,1],[14,-5],[7,-5],[12,-20],[41,13],[46,6],[105,2],[69,16],[50,7],[31,11],[32,9],[127,99],[0,183],[69,83],[189,-10],[86,-45],[155,-10],[104,55],[137,83],[293,55],[172,92],[137,18],[162,13]],[[6399,2199],[-33,-110],[-26,-45],[-8,-69],[-46,-81],[-52,-72],[-33,-106],[-163,-246],[-16,10],[-8,9],[-8,1],[-15,-10],[-14,-16],[9,-4],[23,-1],[24,-6],[6,-19],[-71,-156],[-75,-98],[-24,-21],[-9,-5],[-22,-7],[-7,-3],[-3,0],[-14,-3],[-2,0],[-9,-3],[3,-3],[6,-2],[0,-4],[0,-2],[-9,-18],[-24,-25],[-4,-5]],[[4481,1423],[260,-35],[120,9],[103,55],[35,138],[-35,156],[-34,110],[17,46],[121,9],[154,-37],[138,-46],[103,0],[0,55],[0,92],[-17,110],[-120,119],[-86,165],[-69,65]],[[5171,2434],[103,73]],[[5274,2507],[69,-55],[223,-55],[121,-9],[51,27],[121,-36],[52,-83],[172,-37],[172,0],[144,-60]],[[7407,3792],[-12,3],[-113,16],[-97,-1],[-69,8],[-11,2],[-12,7],[-4,5],[-2,5],[1,5],[0,9],[-2,4],[-6,7],[-4,3],[-7,3],[-23,9],[-8,3],[-9,3],[-8,1],[-15,0],[-43,-6],[-9,-1],[-9,0],[-15,2],[-16,4],[-31,9],[-24,9],[-4,2],[-29,26],[-5,3],[-5,2],[-7,2],[-8,1],[-13,0],[-9,-1],[-8,-1],[-6,-2],[-5,-3],[-4,-3],[-3,-3],[0,-4],[2,-3],[8,-6],[2,-3],[-1,-4],[-9,-5],[-4,-3],[-3,-4],[-3,-8],[-3,-3],[-13,-9],[-4,-3],[-5,-8],[-1,-4],[-3,-4],[-42,-35],[-4,-8],[-1,-4],[0,-4],[2,-4],[10,-10],[0,-3],[-3,-3],[-5,-3],[-6,-2],[-8,-1],[-8,0],[-12,2],[-15,5],[-27,11],[-25,8],[-7,0],[-13,1],[-17,-1],[-16,-2],[-7,-2],[-19,-6],[-15,-7],[-7,-2],[-7,-1],[-9,0],[-9,0],[-9,0],[-17,2],[-18,1],[-9,0],[-9,-1],[-7,-1],[-7,-2],[-25,-8],[-7,-1],[-8,-1],[-36,-1],[-17,-2],[-8,0],[-9,1]],[[6173,4045],[6,34],[6,12],[37,35],[5,7],[3,7],[0,25],[3,6],[3,4],[7,6],[5,3],[10,5],[23,9],[21,9],[10,6],[42,36]],[[8586,5920],[40,-109],[8,-47],[-17,-51],[-48,-87],[-67,-111],[-2,-13],[2,-12],[6,-10],[6,-6],[15,-9],[7,-5],[3,-9],[-8,-4],[-14,-3],[-14,-4],[-42,-41],[-34,-145],[-10,-10],[-26,-16],[-10,-9],[-33,-50],[-80,-86],[-72,-136],[-107,-124],[-50,-39],[-125,-230],[-173,-223],[-47,-116],[-3,-17],[1,-27],[-45,-66],[-9,9],[-16,12],[-19,6],[-12,-7],[6,-9],[33,-23],[8,-11],[-4,-10],[-113,-119],[-79,-106],[-35,-55]],[[4625,5751],[-21,1],[-19,3],[-14,4],[-11,-2],[-19,-10],[-26,-7],[-23,-8],[-13,-4],[-27,-3],[-13,-2],[-12,-3],[-18,-4],[-23,-2],[-39,-2],[-20,-4],[-13,-5],[-5,-6],[-10,-5],[-14,-3],[-37,-5],[-28,-2],[-19,-3],[-44,-14],[-13,-3],[-15,0],[-17,1],[-15,-3],[-14,-5],[-28,-4],[-17,0],[-9,5],[-1,14],[-8,6],[-10,0],[-28,-7],[-22,-2],[-18,0],[-15,3],[-15,4],[-19,4],[-54,5],[-14,3],[-9,4],[-15,8],[-11,3],[-17,2]],[[3773,5703],[-65,79],[-103,55],[-17,55],[69,37],[-17,55],[-121,9],[-155,-9],[-120,0],[-52,73],[-86,184],[17,73]],[[3123,6314],[259,28],[154,-37],[293,0],[51,-46],[104,-73],[154,-65],[87,28],[68,82],[86,28],[86,55],[155,18],[103,129],[58,121],[183,16],[207,10],[172,-10],[155,-36],[120,-28],[52,37],[-69,55],[-155,64],[103,55],[103,64]],[[5652,6809],[121,-36],[120,-37],[155,0],[69,-28],[120,-27],[209,-29]],[[5390,7434],[-47,-138],[51,-120],[52,-156],[103,-82],[86,-64],[17,-65]],[[3123,6314],[-68,64],[-138,19],[-120,73],[-18,64],[-137,37],[-172,73],[-86,83],[-18,92],[-93,57]],[[2273,6876],[8,7],[10,6],[19,5],[19,3],[40,3],[18,4],[-11,5],[80,20],[40,13],[27,18],[46,40],[31,20],[31,15],[37,12],[85,18],[53,19],[10,-4],[3,-11],[2,-23],[24,-41],[2,-3],[0,-7],[0,-5],[-8,-15],[-2,-13],[2,-9],[11,-6],[21,-3],[21,4],[14,8],[11,9],[15,7],[47,10],[19,6],[10,12],[-4,6],[-27,6],[-16,8],[-11,13],[-1,9],[13,6],[224,12],[9,2],[10,2],[18,6],[11,-20],[10,-8],[5,-1],[2,-3],[1,-11],[-15,-21],[1,-7],[24,6],[9,-14],[10,1],[13,13],[-14,38],[5,8],[21,11],[11,8],[-1,2],[1,19],[3,6],[5,6],[3,5],[-1,8],[-10,9],[-15,6],[-8,7],[10,10],[44,24],[45,15],[15,1],[22,-5],[21,-2],[21,0],[20,-2],[50,-16],[31,2],[83,26],[18,3],[18,2],[21,0],[30,-2],[10,-6],[-2,-25],[1,-11],[5,-6],[11,-3],[20,-2],[12,-1],[24,1],[11,0],[38,-11],[32,0],[0,4],[-14,9],[-9,13],[6,8],[13,6],[10,8],[0,13],[-33,-9],[-32,-13],[-19,-4],[8,16],[20,16],[29,16],[32,13],[32,10],[31,6],[33,4],[68,0],[35,-3],[16,-2],[6,-5],[0,-9],[2,-8],[6,-7],[11,-6],[-16,-13],[-7,-12],[-9,-11],[-25,-9],[30,-13],[21,-11],[18,-14],[48,-51],[22,-14],[30,-13],[2,7],[-1,10],[1,9],[8,4],[11,-1],[15,-14],[11,-5],[27,4],[12,12],[-1,16],[-10,13],[37,2],[26,-13],[24,-17],[28,-12],[14,6],[-15,19],[-33,31],[-27,6],[-57,28],[-34,6],[-44,-2],[-13,2],[-25,8],[3,5],[16,4],[17,8],[36,29],[6,15],[-15,11],[-15,1],[-19,-2],[-15,0],[-7,6],[4,12],[20,20],[16,27],[26,14],[61,25],[86,50],[32,12],[3,-3],[3,-4],[2,-3],[1,-5],[15,5],[37,18],[59,22],[16,8],[32,11],[37,2],[36,0],[31,5],[-18,6],[-42,6],[-6,7],[15,8],[79,10],[142,53],[103,49],[58,9],[39,11],[21,2],[9,-2],[4,-6],[2,-7],[0,-19],[2,-4],[17,-18],[10,-8],[15,-6],[22,-6],[47,-10],[23,-8],[0,-9],[-14,-10],[-21,-18],[-17,-8],[-21,-7],[-14,-8],[-61,-64],[-17,-24],[5,-18],[15,-1],[10,10],[12,24],[16,8],[25,4],[49,4],[47,9],[43,12],[43,6]],[[5366,5025],[-281,14],[-224,9],[-138,-9],[-137,-64],[-69,-129],[-138,-110],[-206,-128],[-138,-101]],[[4035,4507],[-241,-18],[-245,12]],[[3549,4501],[2,3],[3,2],[4,1],[21,4],[6,2],[4,3],[2,6],[-1,22],[5,18],[-2,22],[-2,7],[-3,7],[-13,20],[-5,12],[-13,14],[-2,5],[-1,8],[-3,5],[-10,4],[-9,1],[-8,-1],[-23,-3],[-13,-4],[-19,-5],[-6,-2],[-8,0],[-8,0],[-6,2],[-6,2],[-10,5],[-14,8],[-27,12],[-9,6],[-4,3],[-13,13],[-17,12],[-14,8],[-3,4],[-2,5],[2,9],[3,6],[7,8],[1,4],[-2,5],[-7,6],[-8,2],[-31,6],[-7,4],[-6,6],[-8,13],[-6,6],[-6,4],[-25,8],[-6,2],[-4,3],[-4,3],[-24,33],[-11,10],[-4,2],[-6,2],[-6,2],[-23,4],[-7,2],[-4,4],[0,6],[6,12],[6,7],[13,11],[3,3],[2,5],[1,6],[-9,20],[-3,4],[-8,16],[-6,36],[-4,8],[-5,8],[0,4],[2,5],[7,6],[14,6],[11,4],[23,14],[4,3],[11,10],[5,2],[5,3],[19,5],[5,3],[5,3],[6,6],[4,3],[5,3],[5,3],[24,8],[6,2],[4,3],[4,5],[9,38],[-19,88],[-1,24],[2,4],[3,4],[4,3],[15,8],[0,5],[-6,9],[-39,31],[-10,6],[-23,4]],[[3230,5353],[12,8],[5,5],[5,5],[6,7],[8,6],[20,10],[11,4],[14,1],[20,-3],[16,-3],[15,-4],[10,-5],[9,-3],[10,0],[11,2],[12,3],[18,-2],[54,-9],[12,-1],[52,20],[8,7],[3,7],[-5,24],[1,7],[5,9],[9,8],[35,20],[9,7],[7,7],[6,7],[3,14],[19,36],[10,13],[8,6],[39,20],[6,6],[4,14],[2,7],[22,27],[3,6],[0,7],[-3,14],[1,6],[4,8],[7,8],[8,7],[12,7]],[[7005,9041],[14,-15],[10,-16],[1,-8],[17,-5],[-8,-12],[-15,-14],[-8,-9],[10,-6],[20,-7],[16,-8],[1,-9],[-9,-5],[-13,-4],[-16,-1],[-14,-1],[-6,2],[-5,4],[-2,3],[0,2],[-9,-1],[-19,-4],[-50,4],[-17,4],[-19,7],[-9,0],[0,-10],[-6,0],[-4,2],[-3,1],[-5,1],[-13,9],[-9,16],[-7,31],[6,14],[7,13],[-1,9],[-20,4],[14,10],[20,-3],[21,-6],[19,4],[9,-4],[13,-3],[14,-1],[11,3],[1,6],[-6,5],[-9,6],[-4,6],[1,15],[6,8],[16,4],[28,0],[24,-3],[11,-9],[-2,-13],[-14,-12],[12,-4]],[[8806,9464],[-126,-68],[-120,-64],[-86,-64],[103,-9],[17,-55],[-68,-65],[-18,-128],[-86,-46],[86,-147],[-69,-146],[-34,-55],[-17,-65],[-138,-18],[-69,-119],[-114,-65]],[[8067,8350],[-7,1],[-73,21],[-8,2],[-9,1],[-25,2],[-9,1],[-8,3],[-15,5],[-10,2],[-9,0],[-20,-4],[-17,-2],[-6,-2],[-6,-1],[-5,-3],[-36,-27],[-6,-2],[-6,-2],[-28,-6],[-35,-12],[-10,-5],[-3,-3],[-5,-3],[-6,-2],[-9,-1],[-5,2],[-4,4],[-2,9],[-4,4],[-30,23],[-9,3],[-9,1],[-6,-1],[-11,-5],[-7,-1],[-8,-1],[-17,0],[-8,0],[-21,-5],[-7,-1],[-8,0],[-8,0],[-7,1],[-8,3],[-85,34],[-7,2],[-44,8],[-16,4],[-8,3],[-52,31],[-15,6],[-12,2],[-7,-1],[-7,-2],[-5,-2],[-16,-7],[-12,-4],[-39,-10],[-22,-3],[-34,-3],[-21,-4],[-8,-1],[-13,1],[-17,2],[-32,9],[-17,6],[-24,11],[-7,3],[-53,15],[-9,3],[-34,24],[-18,10],[-8,2],[-6,-1],[-5,-3],[-8,-5],[-8,-2],[-9,-1],[-16,1],[-8,3],[-5,3],[-2,4],[-2,5],[0,9],[7,17],[3,3],[4,3],[33,13],[17,12],[3,3],[22,14],[8,6],[3,3],[3,5],[0,4],[-3,6],[-6,2],[-8,0],[-6,-1],[-13,-4],[-46,-17],[-53,-12],[-12,-4],[-27,-12],[-6,-2],[-53,-12],[-5,-3],[-4,-2],[-1,-4],[1,-4],[5,-7],[1,-4],[1,-4],[-2,-4],[-2,-2],[-2,-1],[-5,-3],[-6,-2],[-6,-1],[-31,-5],[-4,-2],[0,-4],[15,-11],[1,-4],[-3,-3],[-11,-5],[-4,-1],[-10,-5],[-1,0]],[[6603,8447],[-43,7],[-30,11],[-51,25],[105,29],[0,9],[-66,22],[-56,12],[-15,6],[-36,35],[-21,48],[11,41],[60,14],[-15,11],[-17,5],[-13,6],[-2,19],[4,15],[11,22],[19,15],[27,-5],[21,-1],[11,18],[6,33],[12,6],[15,4],[14,0],[11,-7],[-6,-5],[-14,-6],[-9,-8],[10,-11],[8,2],[13,1],[8,2],[-8,-9],[3,-8],[11,-4],[17,3],[5,6],[0,7],[6,6],[21,4],[-25,14],[-2,6],[51,0],[21,-9],[10,-18],[5,-21],[7,-12],[38,9],[21,-7],[10,-15],[7,-17],[23,-34],[4,-12],[0,-5],[1,-5],[4,-4],[22,-4],[-3,-5],[-15,-9],[16,-15],[35,-14],[41,-10],[31,-4],[39,5],[39,13],[19,18],[-23,19],[10,12],[0,38],[8,12],[25,24],[5,10],[-30,25],[-6,13],[27,7],[16,-5],[40,-30],[7,13],[12,13],[14,11],[14,8],[17,3],[26,2],[25,1],[17,-1],[0,3],[0,8],[0,4],[32,-15],[16,-5],[13,2],[-3,7],[-24,25],[-11,21],[-24,16],[-9,8],[-4,10],[-1,10],[5,21],[21,-3],[13,-7],[9,-8],[14,-8],[1,-11],[25,-9],[72,-15],[13,-2],[14,1],[16,4],[17,2],[18,-1],[16,-3],[15,0],[50,7],[50,15],[45,20],[34,23],[-24,15],[20,20],[51,33],[-5,9],[-20,17],[-4,11],[1,24],[3,13],[31,57],[18,21],[38,26],[11,12],[8,11],[3,10],[7,8],[16,11],[19,9],[15,4],[-6,18],[21,48],[3,19],[-8,10],[-27,17],[-12,9],[-8,10],[-6,8],[-7,33],[-5,8],[-21,16],[-8,3],[-8,-1],[-6,0],[-6,8],[0,7],[7,4],[7,4],[5,5],[-1,6],[-8,9],[0,5],[15,18],[3,2],[-10,9],[-16,1],[-19,-4],[-20,-6],[-1,22],[-12,12],[-44,21],[-22,30],[-11,10],[-4,-14],[-56,14],[16,7],[25,20],[15,1],[17,-4],[14,-2],[34,-1],[47,-5],[29,-13],[42,-42],[15,-5],[21,4],[21,7],[13,8],[6,11],[4,39],[3,5],[15,15],[0,4],[-2,10],[2,6],[13,10],[17,7],[20,2],[17,-4],[9,13],[14,6],[17,6],[16,11],[20,-13],[18,10],[18,18],[19,10],[23,-10],[20,4],[16,12],[8,14],[-2,5],[-5,6],[-4,6],[1,8],[8,8],[10,4],[11,4],[10,4],[13,1],[3,4],[-8,10],[-12,7],[-9,1],[-10,0],[-16,2],[-7,3],[-4,5],[-6,4],[-11,4],[-15,1],[-51,-1],[19,13],[55,9],[20,13],[21,-8],[45,-33],[9,6],[10,-6],[2,14],[-11,16],[-20,12],[-28,4],[6,7],[8,5],[9,4],[6,3],[14,4],[4,2],[-1,2],[-5,4],[-3,6],[5,5],[41,24],[24,7],[25,-3],[14,-5],[38,-23],[9,-10],[0,-21],[9,-9],[27,-19],[4,-18],[-4,-19],[6,-21],[24,-27],[3,-11],[-8,-1],[-52,31],[-13,1],[-10,-6],[-9,-9],[-10,-4],[-19,2],[-15,8],[-11,10],[-3,10],[-15,-4],[-10,-5],[-8,-8],[-4,-8],[17,-4],[-4,-9],[-23,-22],[21,0],[28,-2],[25,-4],[11,-7],[-4,-4],[-20,-9],[-4,-4],[3,-7],[4,-6],[7,-3],[10,6],[14,13],[11,3],[12,-5],[14,-11],[-2,-4],[-6,-5],[-1,-4],[14,-2],[27,-1],[14,1],[11,4],[7,13],[-19,20],[6,10],[25,8],[16,-6],[40,-32],[4,-9],[0,-8],[5,-10],[13,-12],[14,-8],[10,-10],[1,-16],[9,10],[12,6],[15,3],[20,1],[20,-2],[9,-4],[6,-5],[23,-12],[8,-9],[1,-9],[-10,-3],[-19,2],[-17,4],[-15,1],[-15,-7],[34,-15],[17,-11],[16,-1],[27,17],[38,-45],[9,-16],[3,-19],[-4,-10],[-13,2],[-11,8],[-4,9],[0,20],[-8,-20],[20,-41],[-7,-17],[-11,-7],[-6,-8],[-1,-8],[4,-9],[13,-12],[10,1],[10,5],[15,1],[21,-7],[27,-13]],[[5807,3379],[-51,-138],[-69,-83],[34,-55],[52,-64],[-86,-211],[-52,-110],[-51,-55],[51,-55],[-206,-55],[-155,-46]],[[5171,2434],[-172,36],[-138,0],[-172,0],[-138,10],[-103,64],[-34,91],[-52,65],[-241,64],[-258,37],[-206,9],[-172,-9],[-121,9],[-216,69]],[[5842,4718],[-52,-74],[-138,-45],[-189,0],[-189,18],[-52,37],[-154,9],[-173,-19],[-172,-55],[-120,-64],[-103,-9],[-138,18],[-327,-27]],[[3230,5353],[-28,-2],[-7,-4],[-8,-4],[-12,-6],[-13,-3],[-13,1],[-9,4],[-6,7],[-5,6],[-7,6],[-12,5],[-27,6],[-14,0],[-10,-1],[-7,-5],[-10,-5],[-28,-9],[-20,-4],[-17,1],[-123,27],[-14,5],[-21,9],[-15,5],[-19,6],[-34,7],[-20,2],[-18,0],[-53,-13],[-26,-8],[-19,-3],[-13,2],[-10,5],[-10,7],[-15,5],[-43,11],[-32,12],[-12,-2],[-10,-4],[-10,-5],[-18,-7],[-7,-3],[-16,-10],[-21,-10],[-8,-6],[-8,-6],[-13,-6],[-17,-6],[-35,-5],[-36,-10],[-54,-23],[-9,-6],[-23,-19],[-6,-6],[-1,-7],[5,-6],[8,-7],[49,-35],[5,-7],[8,-13],[5,-6],[32,-25],[6,-6],[6,-8],[29,-69],[10,-13],[1,-15],[3,-7],[7,-7],[18,-11],[6,-6],[2,-7],[-4,-9],[1,-8],[19,-19],[29,-13],[6,-4],[5,-4],[5,-7],[10,-24],[-1,-10],[-5,-11],[-16,-16],[-7,-11],[-1,-9],[4,-7],[5,-5],[6,-3],[9,-2],[11,-2],[7,-6],[5,-6],[3,-9],[10,-13],[14,-35],[5,-7],[7,-7],[5,-8],[1,-9],[-6,-11],[-9,-9],[-15,-11],[-4,-9],[2,-7],[4,-7],[9,-9],[9,-4],[10,-5],[59,-22],[10,-5],[7,-6],[0,-8],[-9,-5],[-14,-3],[-28,0],[-32,-3],[-20,-6],[-16,-1],[-13,2],[-12,3],[-36,5],[-26,5],[-15,1],[-16,-2],[-16,-3],[-20,-6],[-12,-9],[-11,-11],[-16,-9],[-16,-4],[-47,-7],[-16,0],[-16,1],[-31,5],[-14,3],[-25,8],[-40,9],[-15,0],[-26,-3],[-12,0],[-24,7],[-23,-1],[-18,0],[-30,4],[-38,-3],[-18,1],[-32,4],[-14,3],[-9,6],[-57,41],[-18,11],[-10,3],[-8,3],[-10,2],[-13,1],[-13,1],[-16,0],[-16,-1],[-29,-5],[-13,-2],[-13,0],[-29,4],[-47,3],[-58,11],[-30,3],[-1,0]],[[1440,4725],[-27,18],[-24,24],[-9,25],[9,43],[19,22],[0,45],[-2,2],[-5,6],[-2,2],[3,5],[11,6],[4,4],[8,18],[1,8],[-5,22],[-33,52],[-66,90],[-98,69],[-89,66],[-10,22],[0,145],[-54,142],[-2,49],[40,90],[7,33],[-7,11],[-33,16],[-19,33],[-87,63],[-8,17],[1,23],[12,45],[14,22],[70,58],[25,31],[8,2],[17,-4],[8,5],[13,21],[104,84],[23,12],[10,10],[4,11],[5,34],[38,0],[33,28],[47,63],[10,7],[37,20],[7,6],[12,17],[10,7],[22,13],[16,14],[27,31],[36,27],[48,27],[40,29],[12,33],[-7,14],[-25,31],[-6,15],[7,18],[16,8],[14,-4],[1,-18],[22,20],[11,20],[-1,20],[-13,21],[-52,40],[-21,22],[-3,23],[6,10],[11,10],[13,10],[13,8],[9,7],[3,10],[4,41],[5,10],[11,7],[23,3],[46,0],[47,4],[299,-15],[37,5],[17,-1],[33,-14],[25,-7],[24,-3],[20,3],[13,9]],[[3549,4501],[3,-40],[0,-5],[-3,-4],[-4,-4],[-4,-6],[-2,-10],[1,-6],[3,-4],[5,-3],[5,-4],[6,-7],[13,-26],[30,-36],[3,-8],[2,-11],[3,-8],[8,-16],[2,-5],[-5,-28],[-2,-4],[-3,-4],[-8,-6],[-10,-5],[-11,-5],[-9,-5],[-6,-7],[-3,-4],[-3,-13],[-3,-4],[-7,-7],[-33,-20]],[[439,3141],[37,10],[18,13],[6,2],[76,-1],[52,8],[25,-4],[25,-5],[25,-3],[34,7],[40,15],[33,20],[14,18],[7,22],[49,69],[52,141],[87,85],[28,14],[45,5],[25,11],[74,70],[17,30],[13,34],[19,33],[36,23],[0,-6],[2,-5],[7,-9],[33,40],[19,13],[61,24],[19,9],[-5,24],[14,26],[100,101],[30,23],[82,41],[32,20],[26,23],[22,28],[7,22],[0,31],[-10,25],[-25,2],[-12,20],[-7,5],[-10,3],[-19,1],[-9,1],[-19,8],[-18,12],[-14,13],[-6,12],[6,10],[26,25],[11,8],[11,10],[4,12],[-1,26],[-3,7],[-5,7],[-4,7],[2,8],[7,3],[24,8],[8,5],[7,13],[3,12],[11,7],[30,3],[18,13],[-5,28],[-28,49],[-20,23],[-26,22],[-66,32],[-10,9],[-10,24],[-26,23],[-100,66]],[[8738,7804],[-14,25],[-9,10],[-67,46],[-13,4],[-12,3],[-14,5],[-14,7],[-7,5],[-5,4],[-26,36],[-2,4],[1,3],[4,8],[1,4],[-1,4],[-2,3],[-50,59],[-8,7],[-13,7],[-25,19],[-24,10],[-4,5],[0,4],[3,3],[7,7],[4,3],[10,4],[17,7],[5,3],[2,4],[0,7],[-3,4],[-4,4],[-21,10],[-5,2],[-20,5],[-6,2],[-15,7],[-6,2],[-14,3],[-15,3],[-6,1],[-29,13],[-25,18],[-17,17],[-6,4],[-5,2],[-40,15],[-48,26],[-38,14],[-6,5],[-7,8],[-8,5],[-11,5],[-6,3],[-4,4],[-17,29],[-11,12],[-8,3],[-8,1],[-6,-1]],[[8806,9464],[25,-15],[10,-13],[4,-9],[13,-16],[3,-8],[-4,-9],[-17,-16],[-7,-10],[12,4],[12,1],[12,-1],[11,-4],[8,9],[9,6],[8,6],[7,21],[10,6],[15,1],[19,-4],[-7,-7],[-4,-3],[19,-2],[18,-5],[17,-6],[34,-17],[6,-5],[-6,-18],[2,-7],[18,-19],[12,-8],[18,-3],[19,1],[19,-1],[13,-6],[1,-15],[-10,-3],[-15,-3],[-6,-5],[20,-13],[19,-5],[18,-4],[15,-6],[5,-15],[8,-10],[31,-20],[8,-7],[2,-40],[-12,-59],[1,-11],[7,-15],[2,-11],[5,-10],[37,-28],[4,-13],[-5,-31],[6,-11],[19,-6],[17,3],[15,4],[14,-1],[12,-11],[-1,-12],[-11,-25],[3,-20],[20,-42],[15,-20],[6,-4],[17,-7],[6,-4],[1,-6],[-2,-13],[1,-6],[5,-15],[4,-5],[3,-3],[5,-2],[5,-1],[6,0],[8,-1],[2,-3],[-1,-4],[5,-4],[3,-8],[-11,-25],[8,-12],[10,-8],[32,-35],[12,-24],[15,-146],[27,-54],[4,-22],[-1,-26],[-13,-20],[-4,-13],[-2,-26],[4,-15],[9,-9],[34,-16],[23,-22],[1,-21],[-17,-56],[-13,-23],[-3,-11],[2,-13],[17,-35],[10,-81],[38,-40],[4,-27],[-12,-23],[-31,-9],[13,-9],[36,-10],[17,-7],[10,-9],[9,-13],[17,-47],[98,-112],[25,-20],[67,-43],[13,-11],[12,-25],[14,-8],[18,-5],[17,-7],[19,-18],[11,-24],[8,-47],[-1,-4],[-8,-12],[-13,-11],[-1,-5],[3,-5],[2,-8],[-2,-5],[-13,-17],[-3,-8],[3,-7],[4,-6],[1,-5],[-6,-6],[-17,-9],[-5,-4],[-10,-26],[-24,-36],[-3,-14],[-9,-20],[-122,-119],[-10,-21],[13,-4],[-9,-10],[-99,-67],[-31,-41],[-16,-9],[-22,-3],[-22,2],[-21,-1],[-20,-8],[-28,36],[-23,12],[-3,-1]],[[5390,7434],[44,-8],[50,10],[22,8],[4,13],[-5,4],[-9,1],[-11,0],[-12,2],[-12,4],[-5,3],[-23,22],[-19,47],[0,4],[-4,3],[-17,0],[-13,-1],[-39,-9],[-4,9],[2,8],[13,18],[-19,-1],[-15,0],[-10,3],[-9,15],[-23,7],[-9,7],[-3,16],[12,16],[37,27],[46,21],[59,20],[52,24],[29,43],[20,13],[69,34],[29,9],[31,5],[32,2],[18,8],[4,17],[-9,15],[-22,3],[-16,-7],[-8,-12],[-15,-4],[-21,-1],[-13,4],[-21,17],[8,0],[42,34],[171,94],[19,8],[9,-3],[5,-8],[9,-9],[28,-17],[2,-8],[-6,-44],[5,-12],[6,-2],[9,-2],[9,-3],[4,-8],[-3,-2],[-16,-18],[-78,-48],[-21,-17],[-14,-22],[-14,-89],[4,-9],[20,-6],[16,2],[45,13],[14,5],[24,20],[13,21],[18,19],[39,16],[16,-6],[4,7],[-1,19],[8,12],[98,97],[7,21],[6,9],[45,24],[4,6],[0,14],[5,5],[10,4],[24,6],[8,3],[31,19],[12,10],[5,10],[10,12],[20,6],[20,7],[6,15],[58,-42],[27,-12],[18,-4],[43,-6],[15,-6],[7,-8],[-2,-17],[3,-10],[20,12],[2,16],[-11,15],[-19,8],[38,16],[36,-2],[28,-16],[10,-23],[-2,-50],[8,-14],[23,18],[7,22],[0,30],[-13,27],[-27,11],[-19,4],[-17,7],[-30,17],[-26,7],[-21,-1],[-43,-14],[-18,-3],[-17,-2],[-19,2],[-21,9],[-11,10],[-9,11],[-9,4],[-39,-28],[-17,-9],[-14,-4],[-8,6],[-29,27],[-6,8],[3,3],[13,7],[3,5],[-2,7],[-6,4],[-6,4],[-23,25],[0,4],[6,3],[3,2],[-16,6],[-3,2],[3,32],[8,25],[18,23],[28,18],[32,11],[11,6],[4,11],[1,22],[6,8],[12,8],[33,5],[64,-24],[44,1],[19,3],[27,-2],[11,2],[12,8],[9,12],[5,12],[2,11],[-5,16],[-14,20],[-21,18],[-26,8],[18,10],[29,-4],[57,-21],[11,-3],[11,-3],[12,-3],[8,-6],[2,-9],[-9,-7],[-11,-7],[-6,-9],[-4,-19],[-20,-38],[-5,-19],[0,-7],[2,-11],[9,-9],[18,0],[9,6],[1,8],[-2,9],[2,6],[10,5],[18,6],[14,6],[0,6],[-11,7],[-10,8],[-9,9],[-2,9],[10,16],[37,26],[8,16],[0,17],[-3,17],[-13,13],[-11,2]],[[7407,3792],[-74,-115],[-138,-260],[-14,-12],[-18,-11],[-12,-11],[6,-11],[18,9],[10,-2],[3,-10],[-2,-12],[-9,-11],[-24,-21],[-5,-13],[-5,-25],[-13,-25],[-50,-59],[-5,-10],[-2,-22],[-23,-63],[-20,-26],[-19,-18],[-173,-280],[-175,-202],[-175,-202],[-19,-48],[-45,-49],[-25,-84]]],
transform:{scale:[.0007281734032403245,.0013656313548354895],translate:[43.22291100400008,-25.598565362999892]}},m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();