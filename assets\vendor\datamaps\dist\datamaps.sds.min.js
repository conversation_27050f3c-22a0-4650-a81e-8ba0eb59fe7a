!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo={type:"Topology",objects:{sds:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"North Bahr-al-Ghazal"},id:"SD.NB",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Lakes"},id:"SD.EB",arcs:[[4,5,6,7,8]]},{type:"Polygon",properties:{name:"West Equatoria"},id:"SD.WE",arcs:[[-7,9,10,11]]},{type:"Polygon",properties:{name:"Central Equatoria"},id:"UG.MY",arcs:[[12,13,14,-10,-6]]},{type:"Polygon",properties:{name:"Upper Nile"},id:"SD.UN",arcs:[[15,16,17]]},{type:"Polygon",properties:{name:"Jungoli"},id:"SD.JG",arcs:[[-16,18,19,-13,-5,20]]},{type:"Polygon",properties:{name:"Unity"},id:"SD.WH",arcs:[[-17,-21,-9,21,-1,22]]},{type:"Polygon",properties:{name:"Warap"},id:"SD.WR",arcs:[[-8,23,-2,-22]]},{type:"Polygon",properties:{name:"West Bahr-al-Ghazal"},id:"SD.WB",arcs:[[-3,-24,-12,24]]},{type:"Polygon",properties:{name:"East Equatoria"},id:"SD.EE",arcs:[[-14,-20,25]]}]}},arcs:[[[4002,6686],[1,-1],[11,-3],[6,-1],[7,-4],[5,-5],[5,-1],[5,1],[7,3],[7,1],[6,3],[7,-3],[7,-4],[9,-2],[7,-4],[16,-12],[6,-7],[12,-15],[8,-7],[-1,-5],[-3,-9],[-17,-19],[-19,-12],[-40,-17],[-17,-23],[-14,-35],[-20,-123],[-10,-32]],[[3993,6350],[-367,-36],[-2,-1],[-12,-8],[-8,-2],[-8,0],[-56,9],[-9,2],[-144,8],[-97,-12],[-36,-11],[-25,-15],[-17,-18],[-17,-32],[-18,-67],[-11,-174],[-11,-50],[-24,-62],[-25,-47],[-18,-50],[-55,-329]],[[3033,5455],[-121,-42],[-509,-324],[-92,-85],[-53,-38],[-22,-11],[-21,-7],[-24,-1],[-29,6],[-34,17],[-279,244],[-49,61],[-52,98],[-30,129],[-18,157],[-4,159],[-45,387],[3,755],[29,392],[-4,94]],[[1679,7446],[65,-33],[160,-252],[160,-251],[37,-33],[21,-13],[49,-5],[9,1],[327,149],[220,7],[39,-9],[121,0],[69,-11],[5,0],[17,7],[5,0],[108,-21],[84,6],[14,3],[9,5],[86,-241],[41,-62],[338,-4],[338,-3],[1,0]],[[5673,4114],[3,-9],[23,-44],[5,-6],[8,-2],[7,-1],[21,-7],[7,-4],[57,-65],[28,-17],[13,-12],[8,-6],[21,-7],[9,-7],[5,-11],[3,-26],[3,-10],[5,-5],[15,-9],[6,-5],[12,-18],[6,-7],[17,-9],[13,-14],[19,-27],[13,-33],[9,-16],[10,-7],[15,-6],[7,-16],[4,-20],[6,-20],[26,-35],[4,-9],[1,-12],[1,-10],[6,-4],[10,-10],[5,-23],[5,-54],[5,-23],[7,-17],[11,-11],[15,-3],[12,-9],[5,-22],[3,-48],[10,-30],[55,-88],[7,-14],[5,-19],[4,-21],[3,-45],[20,-59],[10,-52],[7,-29],[10,-12],[9,-7],[9,-16],[9,-18],[26,-90],[2,-23],[3,-12],[6,-15],[7,-14],[8,-10],[5,1],[5,5],[6,-1],[5,-17]],[[6398,2792],[-43,-52],[-31,5],[-57,41],[-287,70],[-104,61],[-157,180],[-43,2],[-62,-44],[-76,-20],[-188,-21]],[[5350,3014],[-37,60],[-86,235],[-29,49],[-29,38],[-17,17],[-14,9],[-38,-6],[-171,-4],[-13,-12],[-10,-22],[-3,-99],[9,-73],[10,-45],[0,-52],[-15,-112],[-16,-53],[-14,-31],[-32,-41],[-60,-103],[-17,-43],[-27,-106],[-10,-55],[-42,-33],[-59,-2],[-371,49],[-30,11],[-29,23],[-13,23],[-16,16],[-17,14],[-24,14],[-28,11],[-23,27],[-62,110],[-23,49],[-50,73],[-37,28],[-23,35],[-14,59],[-16,43],[-34,68],[-7,27],[-2,35],[5,41],[-8,66],[-11,37],[13,39],[-6,46],[9,41],[-5,51],[21,153]],[[3829,3719],[69,-72],[68,-38],[29,-6],[28,1],[31,6],[28,10],[29,16],[26,21],[17,31],[9,25],[105,460],[15,42],[55,109],[278,414],[110,225]],[[4726,4963],[130,-14],[14,-4],[14,-10],[10,-15],[270,-750],[99,-42],[410,-14]],[[5350,3014],[103,-127],[93,-96],[34,-44],[20,-35],[23,-53],[18,-27],[48,-45],[62,-77],[25,-58],[19,-57],[3,-48],[-9,-42],[-39,-80],[-15,-50],[-9,-56],[-13,-338],[-3,-24],[-3,-15],[-5,-12],[-10,-12],[-19,-15],[-118,-52],[-96,-66],[-39,-40],[-112,-148],[-30,-20],[-27,-6],[-22,7],[-21,17],[-55,68],[-19,18],[-19,9],[-22,2],[-18,-5],[-21,-12],[-20,-18],[-20,-25],[-32,-48],[-156,-164],[-24,-18]],[[4802,1202],[4,7],[3,12],[-10,9],[-9,4],[-19,16],[-5,3],[-19,-6],[-7,1],[-12,10],[-25,30],[-9,9],[-36,22],[-10,3],[-25,1],[-26,14],[-9,4],[-6,-1],[-13,-9],[-4,4],[-9,12],[-3,3],[-18,4],[-13,-3],[-7,-14],[-2,-40],[-3,-20],[1,-12],[-2,-12],[-16,-20],[-5,-12],[-6,-27],[-7,-23],[-10,-21],[-44,-51],[-8,-16],[-9,-28],[-12,-27],[-14,-4],[-17,5],[-21,-1],[-10,-13],[-2,-22],[-6,-18],[-22,-5],[-19,13],[-54,74],[-9,9],[-34,20],[-2,-2],[-3,17],[-9,5],[-41,37],[-7,9],[-51,-17],[-19,-2],[-40,4],[-26,-5],[-8,0],[-22,12],[-4,22],[-1,26],[-12,22],[-12,5],[-7,-5],[-8,-10],[-11,-8],[-18,0],[-7,-2],[-35,-91],[-13,-24],[-13,-11],[-35,-11],[-10,-9],[-3,-12],[-2,-13],[-6,-11],[-8,-3],[-8,1],[-8,3],[-8,0],[-19,-6],[-12,-11],[-33,-58],[-16,-21],[-18,-14],[-19,-3],[-20,8],[-7,11],[-3,16],[-8,24],[-28,29],[-36,-2],[-37,-10],[-34,6],[-16,16],[-15,21],[-24,40],[-3,22],[-6,10],[-14,1],[-8,-6],[-14,-20],[-8,-2],[-26,50],[-4,11],[-3,9],[-1,10],[-1,12],[3,26],[4,20],[-2,12],[-44,9],[-10,-2],[-5,2],[-9,12],[-4,2],[-8,-5],[-3,-4],[1,-1],[-1,-3],[0,-5],[-2,-5],[-5,-1],[-15,8],[-4,1],[-25,-5],[-6,1],[-10,8],[-15,22],[-9,10],[-7,3],[-20,6],[-4,0],[-6,19],[-4,27],[-2,47],[7,67],[-3,25],[-3,10],[-6,13],[-7,12],[-8,6],[-8,0],[-16,-7],[-6,0],[-11,12],[-6,19],[-6,48],[-11,27],[-16,13],[-74,11],[-17,8],[-16,17],[-4,12],[-4,24],[-3,11],[-7,11],[-27,27],[-6,4],[-5,4],[-5,5],[-5,6],[6,51],[-2,15],[-8,15],[-25,24],[-14,45],[-23,26],[-48,44],[-18,29],[-14,34],[-22,72],[-4,18],[-4,56],[-7,29],[-2,15],[2,17],[34,126],[-1,11],[1,20],[-3,10],[-8,7],[-6,-1],[-5,-3],[-3,-2],[-3,-1],[-4,-2],[-5,1],[-5,11],[0,7],[4,17],[1,20],[2,7],[-1,7],[-14,13],[-4,5],[-2,7],[0,10],[-3,18],[-7,18],[-10,15],[-12,8],[-17,7],[-4,9],[-1,12],[-6,19],[-7,7],[-36,19],[-8,-1],[-15,-6],[-8,0],[-6,5],[-6,18],[-16,16],[-10,32],[-9,14],[-37,-13],[-18,2],[-10,20],[-8,26],[-11,2],[-14,-7],[-19,-2],[-20,12],[-7,21],[-5,51],[-9,15],[-15,13],[-61,32],[-60,-4],[-27,5],[-50,23],[-13,14],[-8,21],[-7,25],[-9,19],[-16,6],[-34,-32],[-13,-5],[-3,24],[7,23],[13,19],[17,16],[16,12],[14,15],[8,22],[-1,21],[-11,18],[-14,5],[-12,-1],[-8,7],[0,28],[-3,29],[-14,13],[-35,10],[-16,14],[-20,37],[-14,18],[-17,13],[-12,7],[-10,11],[-5,23],[1,21],[5,22],[-1,16],[-16,8],[6,13],[8,8],[8,6],[7,8],[17,47],[27,48],[13,35],[7,11],[-1,13]],[[1913,3599],[23,5],[1296,208],[137,-130],[8,-6],[11,-5],[11,-1],[430,49]],[[6398,2792],[455,-35]],[[6853,2757],[-21,-128],[7,-719],[20,-102],[12,-44],[68,-159],[13,-40],[8,-41],[-1,-36],[-10,-38],[-17,-44],[-48,-96],[-79,-233],[-32,-169],[-16,-47],[-21,-35],[-28,-38],[-89,-85],[-16,-26],[-16,-42],[-56,-208],[-20,-45],[-3,-19],[0,-1]],[[6508,362],[-17,11],[-3,1],[-2,-7],[-67,-102],[-8,-10],[-15,-9],[-47,-9],[-41,-8],[-15,-10],[-10,-17],[-1,-1],[-9,-11],[-16,4],[-108,80],[-70,51],[-33,15],[-35,6],[-40,0],[-22,-8],[-32,-33],[-22,-24],[-8,-4],[-16,-5],[-8,-4],[-26,-28],[-11,-7],[-9,-2],[-17,1],[-9,-4],[-9,-11],[-4,-13],[-3,-13],[-4,-13],[-56,-111],[-18,-49],[-4,-18],[-10,24],[8,59],[-12,20],[-26,14],[-11,12],[-6,17],[-3,39],[-3,12],[-15,15],[-14,-13],[-14,-23],[-18,-14],[-24,1],[-9,-2],[-15,-8],[-27,-20],[-10,-3],[-17,-3],[-9,0],[-7,4],[-9,10],[0,5],[5,7],[5,15],[6,44],[1,23],[-21,168],[-11,29],[-9,4],[-8,-1],[-9,-3],[-8,-2],[-17,1],[-7,1],[-32,14],[-69,56],[-57,24],[-55,1],[-14,7],[-6,11],[-2,11],[0,12],[-2,13],[-5,13],[-20,32],[-8,27],[-5,26],[-8,23],[-20,14],[-35,10],[-13,11],[-13,24],[-19,52],[-13,21],[-20,16],[-10,2],[-10,0],[-9,3],[-7,11],[-1,14],[6,12],[6,11],[3,12],[-8,23],[-19,23],[-22,17],[-19,4],[-38,0],[-23,25],[-10,42],[0,50],[8,34],[1,12],[-1,10],[-6,19],[-1,10],[5,12],[4,6]],[[7530,5104],[-1,0],[-101,59],[-18,17],[-21,26],[-2,32],[5,32],[26,75],[12,43],[7,47],[3,47],[-4,56],[-13,37],[-21,25],[-89,57],[-72,83],[-49,80],[-151,354],[-15,51],[-30,81],[-17,29],[-17,15],[-16,0],[-19,-7],[-21,-13],[-103,-85],[-113,-73],[-28,-9],[-32,-1],[-34,18],[-29,33],[-282,505],[-10,12],[-4,3],[-7,1],[-3,-3],[-39,-17],[-37,-7],[-13,3],[-17,7],[-16,9],[-6,9],[-8,14],[-18,8],[-38,5],[-134,47],[-102,77],[-38,17],[-18,4]],[[5777,6907],[10,269]],[[5787,7176],[181,13],[60,33],[182,241],[182,242],[93,154],[24,32],[52,110],[56,188],[11,22],[200,226],[199,227],[14,36],[4,28],[-60,152],[-13,78],[-3,119],[12,186],[-4,117],[-1,13],[-4,18],[-3,7],[-223,333],[282,2],[281,1],[1,28],[0,15],[-2,13],[-10,40],[-7,53],[0,15],[1,14],[3,28],[0,15],[0,7],[-1,6],[0,6],[3,5],[404,-7],[-5,-94],[-49,-222],[1,-132],[-12,-152],[-3,-13],[-11,-32],[-10,-19],[-11,-18],[-3,-7],[-4,-11],[0,-17],[42,-424],[42,-425],[-3,-22],[-3,-14],[-23,-53],[-5,-17],[-2,-14],[10,-9],[186,-92],[9,-5],[7,-8],[67,-109],[184,-202],[183,-201],[13,-20],[38,-127],[4,-19],[1,-31],[-1,-23],[-9,-76],[3,-34],[6,-23],[6,-26],[0,-8],[-1,-7],[-3,-12],[-54,-146],[-18,-104],[-5,-88],[3,-50],[6,-33],[3,-7],[2,-6],[3,-3],[2,-2],[144,0],[11,-286],[12,-332],[12,-331],[-4,-29],[-3,-25],[-11,-27],[-18,-26],[-84,-101],[-17,-14],[-16,-6],[-48,0],[-29,-4],[-14,-6],[-13,-7],[-9,-8],[-35,-41],[-4,-2],[-9,-2],[-39,7],[-20,31],[-17,40],[-26,30],[-18,3],[-12,-6],[-12,-9],[-16,-4],[-13,7],[-13,14],[-14,11],[-17,-1],[4,-9],[0,-4],[-8,-6],[-57,-9],[-11,3],[-7,0],[-6,-5],[-12,-12],[-8,-4],[-92,28],[-15,-3],[-24,-29],[1,-4],[2,-7],[-6,-8],[-13,-3],[-12,-8],[-4,-8],[-1,-9],[0,-19],[-1,-5],[-4,-8],[-1,-6],[2,-4],[8,-7],[1,-4],[-2,-14],[-7,-22],[-2,-8],[3,-14],[6,-11],[6,-8],[6,-13],[8,-6],[11,-7],[-1,-14],[-6,-6],[-8,-4],[-7,-8],[-7,-12],[-3,-11],[-2,-11],[0,-16],[3,-7],[11,-11],[3,-6],[-2,-23],[-7,-16],[-11,-11],[-14,-9],[-26,-6],[-3,-6],[-2,-9],[-4,-12],[-5,-11],[-48,-66],[-11,-19],[-4,-12],[-4,-24],[-4,-8],[-4,-6],[-4,-10]],[[7530,5104],[0,-2],[-12,-17],[-3,-12],[22,-76],[7,-18],[14,-12],[4,-10],[2,-10],[3,-7],[6,-4],[23,-3],[19,-11],[11,-3],[5,10],[5,3],[27,8],[7,-2],[25,-13],[27,-6],[7,-3],[13,-15],[14,-21],[15,-18],[47,-16],[11,0],[21,19],[7,5],[37,10],[22,1],[14,-8],[8,-8],[15,-6],[9,-5],[8,-10],[12,-21],[9,-9],[18,-7],[55,7],[9,-3],[23,-20],[27,-11],[8,-5],[11,-17],[27,-51],[43,-42],[8,-3],[17,-3],[9,-3],[26,-20],[105,-144],[14,-32],[6,-39],[1,-107],[2,-5],[5,-4],[39,-41],[23,-39],[16,-16],[16,5],[14,9],[12,-10],[8,-20],[4,-23],[0,-36],[0,-12],[2,-26],[7,-12],[6,-3],[6,-6],[8,-15],[35,-34],[4,-7],[4,-20],[6,-5],[6,-4],[2,-4],[120,-39],[55,-51],[7,-15],[6,-18],[4,-20],[1,-22],[-2,-3],[-8,-8],[-2,-1],[1,-7],[4,-12],[1,-4],[6,-47],[10,-12],[15,-4],[36,0],[18,-3],[15,-8],[57,-51],[6,-13],[4,-21],[10,-15],[6,-5],[8,-47],[9,-46],[19,-66],[7,-66],[7,-23],[34,-78],[5,-30],[1,-67],[4,-23],[30,-70],[16,-56],[14,-22],[16,-20],[15,-24],[6,-22],[8,-62]],[[9192,2885],[-1,0],[-660,-266],[-1678,138]],[[5673,4114],[-3,8],[-2,22],[-6,13],[-41,29],[-17,32],[-11,10],[-4,7],[-4,36],[-5,14],[-8,13],[-8,23],[-6,30],[-14,22],[-6,18],[-4,46],[-8,23],[-27,38],[-5,22],[-3,15],[-6,3],[-8,-1],[-7,4],[0,4],[1,14],[-1,4],[-9,12],[-3,4],[-4,4],[-9,11],[-6,11],[5,5],[11,1],[6,4],[3,9],[1,14],[-3,22],[-7,21],[-11,17],[-19,12],[-1,11],[1,13],[-1,10],[-5,7],[-14,14],[-5,10],[2,10],[7,7],[4,7],[-6,22],[6,52],[3,6],[4,5],[4,9],[5,8],[6,7],[2,8],[-8,9],[0,8],[7,1],[4,2],[3,2],[4,2],[-16,7],[-7,0],[0,9],[12,10],[-6,8],[-12,7],[-6,10],[0,32],[-2,8],[-8,8],[-9,40],[-50,81],[9,11],[-10,21],[-17,25],[-11,22],[-1,16],[1,17],[-1,16],[-6,13],[-10,1],[-11,-6],[-7,0],[0,21],[-22,-13],[-12,35],[-3,49],[5,24],[14,6],[-2,13],[-6,13],[0,6],[15,8],[-1,17],[-17,38],[6,3],[17,13],[-5,12],[-4,32],[-8,12],[6,31],[4,6],[6,4],[5,6],[5,31],[4,16],[1,16],[-8,22],[-3,1],[-11,-2],[-3,1],[-1,5],[1,14],[0,5],[-25,22],[-4,5],[-3,13],[-12,17],[-3,10],[3,15],[4,11],[1,10],[-8,7],[0,-3],[0,-9],[0,-3],[-5,0],[0,14],[2,14],[4,14],[5,12],[17,29],[6,17],[0,17],[-5,9],[-6,3],[-6,1],[-6,3],[-34,42],[-7,13],[-6,15],[11,11],[8,13],[4,15],[-5,17],[-6,-8],[-5,18],[1,40],[-1,20],[-4,10],[-10,13],[-4,9],[-1,7],[1,16],[0,8],[-3,18],[-10,23],[-5,14],[-1,20],[2,18],[5,15],[6,10],[5,3],[14,3],[5,2],[15,21],[1,2],[2,6],[2,19],[2,7],[5,4],[12,6],[6,5],[10,13],[3,13],[5,37],[8,21],[11,11],[13,10],[9,14],[-6,8],[2,7],[6,6],[4,9],[0,75],[-2,17],[-6,6],[-7,4],[-9,9],[-10,28],[9,11],[16,8],[9,19],[4,26],[9,16],[12,11],[9,14],[9,37],[8,13],[15,5],[12,6],[9,13],[9,16],[8,12],[14,9],[16,5],[104,-6],[108,-35],[36,-5],[68,0],[13,4],[11,7],[12,9]],[[4726,4963],[-31,668],[-8,23],[-14,25],[-242,224],[-216,444],[-48,22],[-174,-19]],[[4002,6686],[-11,28],[-3,41],[1,48],[17,45],[55,72],[64,66],[17,19],[104,74],[102,50],[196,57],[71,92],[39,84],[2,164],[25,27],[47,38],[226,147],[39,31],[199,-171],[200,-171],[224,-270],[14,-13],[12,-5],[12,9],[10,12],[16,9],[11,3],[96,4]],[[3829,3719],[-34,263],[-8,142],[15,180],[2,186],[-4,45],[-10,28],[-33,36],[-7,22],[4,23],[9,29],[1,30],[-12,26],[-27,21],[-36,17],[-107,79],[-70,73],[-61,51],[-42,24],[-128,29],[-16,9],[-13,12],[-19,26],[-53,90],[-110,242],[-37,53]],[[1913,3599],[-1,26],[-41,31],[-50,25],[-29,20],[-14,20],[-35,33],[-26,44],[-13,8],[-16,6],[-19,15],[-9,14],[-3,13],[-1,13],[-4,14],[0,7],[2,7],[0,7],[-4,4],[-4,0],[-6,-2],[-5,-1],[-5,4],[-8,26],[-3,59],[-5,26],[-38,4],[-10,4],[-2,7],[-1,9],[-5,8],[-8,5],[-17,3],[-9,3],[-26,20],[-4,11],[-2,21],[-4,9],[-14,9],[-38,5],[-13,6],[-8,19],[0,15],[-4,9],[-21,2],[-10,3],[-13,18],[-9,5],[-16,0],[-7,2],[-20,22],[-16,9],[-53,18],[-13,10],[-38,47],[-13,10],[-14,3],[-16,-7],[-22,14],[-33,34],[-47,31],[-12,11],[-9,17],[-4,17],[-6,33],[-7,16],[-5,5],[-14,6],[-7,5],[-5,8],[-8,18],[-5,8],[-12,10],[-50,36],[-18,57],[-4,19],[0,14],[18,24],[56,25],[19,22],[4,21],[-2,16],[-5,16],[-3,18],[3,49],[-1,17],[-4,19],[-13,31],[-9,36],[-8,18],[-11,14],[-21,10],[-5,6],[-5,7],[-6,5],[-21,5],[-12,0],[-26,-8],[-12,-1],[-25,13],[-26,26],[-40,61],[-8,5],[-6,7],[-6,7],[-5,9],[-1,20],[-18,24],[-2,41],[-8,18],[-73,91],[-27,16],[-49,8],[-27,20],[-17,3],[-16,0],[-48,12],[-59,-13],[-27,1],[-27,22],[-8,3],[-7,5],[-4,7],[-4,11],[-20,26],[-29,-4],[-55,-26],[-4,4],[-14,14],[-6,5],[-7,1],[-12,0],[-15,2],[-5,1],[-30,9],[-13,7],[-23,17],[-22,23],[-19,29],[-8,33],[3,38],[10,39],[48,107],[8,12],[35,31],[5,11],[-4,14],[-23,24],[-6,16],[3,17],[16,30],[2,16],[-16,11],[-31,-1],[-9,-1],[4,7],[2,16],[5,9],[49,50],[26,17],[92,78],[19,5],[77,11],[42,19],[8,6],[5,8],[0,122],[1,7],[5,18],[77,174],[3,11],[1,7],[16,218],[6,21],[7,13],[24,30],[52,79],[7,26],[1,13],[-1,305],[3,24],[3,7],[3,5],[3,1],[8,1],[9,2],[8,4],[7,6],[29,32],[29,24],[12,17],[23,42],[11,16],[7,13],[8,21],[5,20],[22,62],[9,33],[1,7],[0,7],[0,7],[-3,11],[-7,22],[-1,6],[-2,12],[1,10],[2,12],[42,123],[9,13],[6,8],[5,2],[13,6],[13,6],[9,3],[21,-3],[10,3],[21,10],[4,0],[10,-1],[6,1],[8,4],[6,2],[5,1],[11,-3],[4,-2],[9,-5],[5,-2],[5,-1],[5,0],[4,1],[8,4],[5,1],[9,-2],[5,0],[5,1],[16,10],[4,2],[10,2],[5,0],[11,1],[8,4],[8,5],[13,5],[9,3],[11,1],[34,-1],[9,2],[11,4],[3,0],[62,30],[37,12],[5,1],[27,0],[19,4],[14,5],[4,1],[35,-2],[6,2],[8,2],[18,12],[37,13],[10,1],[3,-3],[37,-61],[4,-10],[2,-5],[1,-6],[5,-178],[1,-6],[3,-5],[5,-8],[7,-7],[30,-24],[13,-8],[23,-9],[3,-1],[5,-6],[3,-4],[5,-9],[51,-111],[10,-17],[12,-17]],[[9192,2885],[1,-13],[-9,-24],[-3,-13],[0,-13],[4,-13],[14,-20],[3,-13],[-2,-16],[-3,-13],[-1,-13],[1,-15],[8,-25],[69,-131],[12,-31],[7,-67],[9,-22],[38,-37],[68,-54],[33,-35],[6,-23],[4,-14],[-6,-13],[-7,-12],[-6,-12],[-1,-14],[3,-11],[28,-59],[13,-19],[15,-10],[21,4],[19,18],[35,51],[18,17],[15,4],[18,0],[18,-3],[15,-6],[19,-15],[37,-40],[21,-7],[20,5],[15,10],[16,5],[21,-8],[56,-46],[46,-25],[4,-10],[-23,-45],[-5,-26],[2,-23],[17,-43],[8,-28],[0,-23],[-7,-23],[-32,-55],[-5,-15],[-3,-240],[7,-56],[20,-47],[116,-166],[-54,0],[-64,1],[-3,66],[-33,4],[-23,-23],[-5,-47],[0,-1],[-5,-35],[-75,35],[-1,1],[-41,99],[-34,85],[41,141],[-63,26],[-85,0],[32,88],[-19,31],[-66,-50],[-59,-44],[-15,38],[-59,-30],[-69,-35],[-78,-40],[-69,-35],[-65,-33],[-69,-35],[-82,-40],[-58,-39],[-58,-40],[-59,-41],[-67,-46],[-85,-115],[-86,-115],[-86,-114],[-85,-115],[-69,-93],[-70,-95],[-94,-128],[-81,-110],[-63,-84],[-4,-4],[-31,-25],[-37,-6],[-136,10],[-20,1],[-58,4],[-26,7],[-18,12],[-107,118],[-17,10],[-14,-7],[-52,-52],[-66,-46],[-72,-28],[-133,-15],[-155,-17],[-38,-12],[-155,-128],[-11,-16],[0,-15],[3,-41],[0,-17],[-3,-17],[-1,-7],[-5,-10],[-11,0],[-53,14],[-14,10],[-13,18],[-6,12],[-10,24],[-10,7],[-7,0],[-66,6],[-12,8],[-6,19],[-2,53],[-4,22],[-12,27],[-60,92],[-20,22],[-5,3]]],
transform:{scale:[.0011800459831983311,.0008726825848584855],translate:[24.121555623000063,3.490201518000106]}},m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();