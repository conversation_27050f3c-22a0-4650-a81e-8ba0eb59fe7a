!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo={type:"Topology",objects:{pry:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Asunción"},id:"PY.AS",arcs:[[0,1,2]]},{type:"Polygon",properties:{name:"Alto Paraguay"},id:"PY.AG",arcs:[[3,4,5,6]]},{type:"Polygon",properties:{name:"Boquerón"},id:"PY.BQ",arcs:[[7,8,-6]]},{type:"Polygon",properties:{name:"Concepción"},id:"PY.CN",arcs:[[9,10,11,-4,12]]},{type:"Polygon",properties:{name:"Cordillera"},id:"PY.CR",arcs:[[13,14,15,16,17]]},{type:"Polygon",properties:{name:"Presidente Hayes"},id:"PY.PH",arcs:[[-12,18,-17,19,-2,20,-8,-5]]},{type:"Polygon",properties:{name:"San Pedro"},id:"PY.SP",arcs:[[21,22,-18,-19,-11,23]]},{type:"Polygon",properties:{name:"Central"},id:"PY.CE",arcs:[[-16,24,25,26,-3,-20]]},{type:"Polygon",properties:{name:"Guairá"},id:"PY.GU",arcs:[[27,28,29]]},{type:"Polygon",properties:{name:"Misiones"},id:"PY.MI",arcs:[[30,31,32,33,34]]},{type:"Polygon",properties:{name:"Ñeembucú"},id:"PY.NE",arcs:[[35,-34,36,-26]]},{type:"Polygon",properties:{name:"Paraguarí"},id:"PY.PG",arcs:[[37,-29,38,-35,-36,-25,-15]]},{type:"Polygon",properties:{name:"Amambay"},id:"PY.AM",arcs:[[39,-24,-10,40]]},{type:"Polygon",properties:{name:"Alto Paraná"},id:"PY.AA",arcs:[[41,42,43,44,45]]},{type:"Polygon",properties:{name:"Caaguazú"},id:"PY.CG",arcs:[[46,-30,-38,-14,-23,47,-45]]},{type:"Polygon",properties:{name:"Caazapá"},id:"PY.CZ",arcs:[[48,-31,-39,-28,-47,-44]]},{type:"Polygon",properties:{name:"Canindeyú"},id:"PY.CY",arcs:[[-46,-48,-22,-40,49]]},{type:"Polygon",properties:{name:"Itapúa"},id:"PY.IT",arcs:[[-43,50,-32,-49]]}]}},arcs:[[[6011,2630],[-51,37],[-1,15],[-36,85],[-1,0]],[[5922,2767],[26,30],[14,11],[17,9],[34,10],[15,7]],[[6028,2834],[29,-26],[18,-30],[5,-45],[-11,-48],[-30,-30],[-28,-25]],[[5564,6633],[-9,-11],[8,-51],[9,-19],[-13,-75],[4,-29],[10,-27],[11,-15],[19,-6],[30,-1],[22,-5],[11,-13],[12,-40],[55,-65],[5,-14],[-22,-19],[-7,-9],[3,-13]],[[5712,6221],[-411,171],[-17,10],[-9,13],[-9,57],[-16,12],[-31,8],[-559,36],[-62,-7],[-254,20],[-183,-6],[-19,1],[-28,7],[-45,18],[-134,69]],[[3935,6630],[-58,34],[-35,10],[-429,16],[-66,-10],[-110,-2],[-10,4],[-4,12],[-1,37],[-19,111],[0,11],[2,16],[17,48],[107,452],[6,12],[7,11],[17,18],[18,14],[120,68],[7,6],[4,4],[2,5],[3,6],[1,8],[1,11],[0,11],[-49,309],[-11,26],[-7,11],[-64,85],[-24,41],[-4,9],[0,9],[4,32],[0,10],[0,11],[-8,29],[-6,15],[-14,24],[-25,31],[-5,7],[-1,8],[0,5],[24,94],[-31,22],[-83,34],[-2222,626],[-110,67],[-16,13]],[[863,9061],[8,15],[15,32],[15,31],[1,0],[33,88],[33,89],[34,88],[33,89],[23,59],[9,14],[19,8],[106,15],[26,4],[56,8],[56,9],[26,3],[402,84],[403,84],[403,83],[402,84],[179,37],[98,2],[224,2],[224,3],[224,3],[225,3],[96,1],[24,-6],[35,-24],[34,-23],[60,-36],[111,-66],[111,-66],[111,-66],[111,-66],[111,-66],[111,-65],[111,-66],[111,-66],[47,-28],[13,-14],[4,-17],[-4,-40],[2,-39],[21,-69],[4,-37],[-4,-27],[2,-51],[-2,-25],[-18,-64],[1,-31],[1,-19],[7,-5],[10,2],[8,-3],[9,-8],[11,-14],[2,-13],[-43,-14],[-9,-20],[7,-22],[23,-16],[7,2],[6,7],[7,7],[13,1],[8,-5],[9,-9],[6,-12],[-12,-33],[3,-30],[8,-30],[10,-20],[18,-21],[26,-19],[27,-14],[22,-5],[18,-10],[-3,-21],[-19,-35],[-3,-25],[2,-31],[5,-29],[13,-28],[1,-14],[-1,-26],[3,-17],[16,-23],[5,-14],[0,-13],[-9,-23],[0,-14],[8,-13],[11,-9],[10,2],[11,41],[16,10],[19,-3],[16,-11],[5,-8],[7,-18],[4,-7],[8,-7],[18,-12],[11,-10],[3,-12],[-9,-9],[-14,-6],[-26,-5],[-10,-6],[-13,-15],[-18,-12],[-9,-11],[8,-8],[31,-2],[24,-5],[20,-13],[13,-18],[3,-22],[-12,-12],[-49,-26],[-11,-16],[4,-13],[8,-6],[8,-2],[3,-3],[7,0],[27,-22],[27,-28],[8,0],[5,21],[22,-18],[2,-26],[-11,-28],[-32,-48],[1,-17],[18,-36],[8,-54],[-5,-56],[-17,-51],[-27,-38],[-34,-24],[-6,-9],[-1,-15],[6,-9],[8,-7],[3,-6],[10,-13],[19,-6],[13,-11],[-9,-32],[-79,-116],[-19,-41],[-4,-25],[1,-27],[10,-21],[22,-9],[11,-10],[-9,-24],[-15,-28],[-8,-20],[5,-21],[12,-21],[15,-18],[13,-7],[6,-11],[-51,-81],[0,-23],[5,-22],[-1,-23],[-16,-26],[-8,-21],[11,-18],[16,-19],[9,-28],[-5,-15],[-28,-49],[-11,-11],[-3,-12],[-26,-70],[-2,-17],[1,-18],[2,-12],[15,-8]],[[3935,6630],[42,-119],[4,-16],[-4,-7],[-18,-15],[-74,-43],[-28,-13],[-7,0],[-8,2],[-9,5],[-10,7],[-11,10],[-9,6],[-12,6],[-17,-4],[-16,-5],[-123,-70],[-16,-14],[-10,-13],[-6,-14],[-2,-7],[-2,-12],[0,-9],[3,-10],[15,-28],[2,-8],[-1,-9],[-9,-16],[-13,-7],[-18,-4],[-68,0],[-20,-3],[-17,-9],[-7,-10],[-3,-13],[8,-56],[0,-5],[-3,-19],[-7,-15],[-230,-321],[-11,-24],[-4,-20],[0,-30],[-3,-16],[-11,-39],[-3,-13],[5,-61],[-3,-55],[9,-57],[-3,-20],[-6,-25],[-33,-82],[1,-8],[2,-9],[10,-21],[4,-14],[-4,-20],[-13,-35],[-54,-102],[-15,-43],[-11,-47],[-11,-107],[0,-1],[-10,-6],[-118,-59],[-28,-11],[-42,-7],[-385,-20],[-16,-3],[-16,-7],[-38,-19],[-42,-17],[-86,-13],[-16,-5],[-20,-9],[-10,-10],[-7,-11],[-8,-43],[-5,-14],[-16,-25],[-3,-8],[-1,-8],[6,-154],[0,-1]],[[2216,4473],[-35,0],[-24,2],[-35,20],[-40,30],[-43,20],[-46,-12],[-37,22],[-11,11],[-18,27],[-7,6],[-2,17],[-14,25],[-19,22],[-32,17],[-20,32],[-11,11],[11,20],[4,27],[-4,24],[-15,11],[-21,7],[-17,15],[-16,16],[-15,11],[-9,2],[-24,-2],[-8,0],[-11,8],[-23,21],[-10,5],[-25,7],[-12,16],[-7,19],[-10,16],[-77,32],[-28,1],[-16,4],[-12,8],[-14,12],[-18,12],[-19,8],[-22,4],[-26,1],[-11,6],[-11,29],[-19,11],[1,12],[6,14],[5,10],[-21,1],[-14,2],[-10,5],[-61,59],[-17,8],[-56,0],[-16,4],[-10,6],[-19,19],[-16,11],[-18,7],[-13,11],[-5,21],[-1,22],[-4,14],[-7,12],[-13,14],[-13,11],[-27,14],[-13,12],[-27,37],[-10,9],[-132,75],[-43,44],[-16,23],[-1,6],[-3,5],[6,28],[-3,12],[-13,18],[-22,44],[-17,25],[-26,25],[-10,14],[-5,14],[-6,1],[-11,7],[-11,9],[-1,4],[-8,4],[-5,9],[-3,11],[-4,9],[-33,34],[-7,12],[-6,14],[-7,10],[-16,5],[-5,6],[14,28],[-22,14],[0,16],[8,26],[-6,13],[-4,2],[-7,-1],[-15,3],[-30,13],[-15,12],[0,15],[17,22],[6,20],[-9,22],[-27,29],[-6,1],[-8,0],[-7,2],[-3,8],[0,14],[-1,6],[-3,5],[-9,5],[-13,4],[-42,5],[-9,2],[-23,8],[-83,54],[-21,19],[-8,18],[-10,8],[-48,14],[-15,7],[-29,35],[-20,16],[-28,7],[-14,2],[-16,4],[-14,8],[-9,11],[2,10],[8,10],[5,13],[-8,16],[9,0],[-7,9],[-8,8],[-10,6],[-12,2],[27,60],[34,114],[31,107],[52,164],[43,136],[56,175],[41,128],[43,140],[42,134],[38,125],[39,124],[4,79],[0,1],[-2,175],[-1,116],[-2,106],[-1,109],[10,32],[43,63],[26,36],[25,34],[53,73],[53,74],[54,73],[53,73],[20,29],[19,29],[20,29],[20,28],[15,31],[8,16]],[[6909,6382],[10,-11],[252,-59],[48,-18],[-5,-4],[-27,-13],[-7,-5],[-51,-68],[-8,-7],[-39,-24],[-6,-7],[-5,-6],[-3,-7],[-1,-9],[5,-26],[-1,-9],[-4,-7],[-12,-8],[-3,-6],[1,-9],[6,-24],[1,-7],[-2,-23],[3,-22],[0,-14],[-2,-11],[-4,-10],[-2,-13],[3,-11],[5,-9],[11,-14],[3,-9],[-1,-8],[-4,-18],[3,-9],[6,-5],[6,-4],[7,-5],[7,-8],[10,-15],[10,-24],[9,-49],[8,-22],[-2,-12],[-5,-8],[-26,-16],[-8,-9],[-7,-9],[-3,-15],[1,-4],[5,-1],[7,0],[16,0],[22,2],[6,2],[6,3],[9,9],[5,2],[8,-1],[8,-5],[10,-11],[8,-5],[7,-5],[7,-2],[8,-1],[7,1],[7,2],[23,10],[13,3],[6,2],[4,5],[10,18],[12,15],[4,6],[2,7],[4,23],[2,7],[4,11],[1,2],[1,2],[8,7],[10,7],[6,3],[6,2],[63,11],[6,2],[12,5],[5,4],[4,4],[8,10],[14,23],[4,5],[5,4],[5,3],[6,3],[14,3],[6,2],[6,3],[30,22],[5,3],[7,1],[6,0],[7,-2],[18,-6],[5,-7],[4,-6],[171,-417],[1,-7],[-2,-13],[-2,-43],[-1,-7],[-3,-6],[-4,-4],[-6,-3],[-7,0],[-6,2],[-12,5],[-5,0],[-5,-2],[-3,-5],[-2,-7],[1,-8],[2,-12],[-1,-6],[-5,-3],[-6,-2],[-5,-4],[-3,-5],[-3,-6],[-2,-7],[0,-8],[0,-10],[4,-17],[1,-10],[-3,-7],[-4,-3],[-46,-23],[-11,-7],[-4,-4],[-5,-4],[-3,-6],[-5,-11],[-5,-5],[-10,-6],[-5,-4],[-5,-4],[-3,-5],[-3,-6],[-2,-6],[-3,-30],[-3,-12],[-2,-3],[-2,-4],[-2,-3],[-6,-4]],[[7553,5117],[-38,-15],[-15,-17],[-10,-16],[-16,-11],[-16,-14],[-28,-14],[-9,-1],[-7,1],[-8,1],[-10,0],[-48,-12],[-53,-25],[-8,-3],[-12,0],[-17,-1],[-38,-9],[-18,-1],[-11,1],[-8,8],[-9,3],[-15,1],[-31,-3],[-16,0],[-11,1],[-29,15],[-12,1],[-17,1],[-69,-3],[-60,13],[-29,3],[-10,-1],[-8,-3],[-10,-9],[-8,-5],[-8,-2],[-10,-1],[-30,2],[-12,-2],[-17,-4],[-23,-9],[-9,-2],[-7,0],[-10,3],[-9,2],[-12,2],[-9,-1],[-7,-3],[-5,-3],[-5,-4],[-9,-5],[-52,-15],[-85,-13],[-56,1],[-12,-2],[-9,-3],[-20,-10],[-24,-8],[-13,-3],[-9,-1],[-3,1],[-79,-5],[-8,0],[-13,3],[-6,2],[-30,16],[-6,3],[-6,3],[-14,5],[-31,4]],[[6191,4954],[5,12],[0,9],[-5,12],[-8,36],[-1,10],[-3,6],[-17,12],[-8,14],[-21,18],[-7,12],[-3,17],[2,8],[7,8],[10,16],[6,13],[3,7],[-1,26],[-4,7],[-20,18],[-12,9],[-29,36],[-11,9],[-8,5],[-44,20],[-24,15],[-19,18],[-8,19],[1,26],[-2,20],[-9,15],[-22,9],[-48,0],[-19,7],[-7,23],[-11,14],[-53,24],[-18,16],[26,48],[-7,25],[-15,19],[-40,35],[-2,12],[12,11],[15,11],[11,12],[1,14],[-5,11],[-12,16],[-7,21],[-4,6],[-9,10],[-11,4],[-27,-1],[-11,5],[-8,13],[-6,29],[-6,12],[-10,5],[-27,7],[-12,5],[-26,37],[13,36],[40,26],[54,8],[52,-9],[26,1],[12,12],[-6,26],[-13,20],[-30,33],[-17,37],[-15,136],[-7,28]],[[5564,6633],[1,0],[13,-11],[11,-19],[15,-15],[40,-13],[13,0],[20,8],[8,1],[10,-5],[17,-18],[6,-4],[14,3],[19,16],[13,-1],[12,-2],[9,1],[14,7],[-2,2],[0,6],[2,7],[3,5],[4,0],[11,-4],[5,0],[24,8],[15,-1],[16,-4],[24,-1],[14,3],[9,5],[8,2],[11,-5],[19,-48],[14,-25],[15,-14],[21,-5],[29,4],[10,4],[5,4],[6,1],[15,-1],[9,-4],[28,-17],[46,10],[10,0],[25,-9],[13,-1],[17,-6],[39,-24],[7,0],[5,8],[10,-3],[18,-9],[13,0],[7,6],[2,8],[1,4],[19,-2],[53,-12],[23,4],[29,23],[17,4],[5,-5],[3,-19],[9,-6],[11,-1],[7,3],[7,5],[9,2],[20,-3],[35,-13],[11,-5],[1,-3],[1,-5],[6,-5],[21,-3],[7,-2],[7,-1],[9,3],[14,4],[38,-3],[20,2],[29,-9],[13,-1],[13,8],[-1,-19],[6,-5],[10,2],[11,-1],[21,-10],[12,0],[13,31],[14,-18],[13,-31],[2,-15],[28,-4],[16,5]],[[7323,3090],[-58,-319],[-24,-75],[-292,-189],[-7,-9],[1,-11],[7,-15],[68,-131],[1,-3],[5,-9]],[[7024,2329],[-50,18],[-9,1],[-12,1],[-13,-6],[-12,-7],[-8,-6],[-8,-5],[-8,-4],[-9,-2],[-8,-1],[-37,2],[-10,3],[-8,5],[-3,8],[0,5],[5,4],[15,12],[14,15],[3,6],[2,6],[0,5],[-2,5],[-7,14],[-4,9],[-6,31],[-4,10],[-7,8],[-16,4],[-12,0],[-88,-5],[-10,1],[-21,4],[-28,9],[-14,2],[-32,2],[-18,11],[-11,10],[-59,100]],[[6529,2604],[-24,36],[-13,14],[-8,1],[-5,-1],[-6,-4],[-8,-8],[-5,-3],[-7,-1],[-8,0],[-9,3],[-8,5],[-11,15],[-42,70],[-7,23],[-3,18],[-7,15],[-15,21],[-36,36],[-36,58],[-12,10],[-47,29],[-4,3],[-27,37]],[[6181,2981],[39,0],[8,5],[23,23],[10,5],[11,2],[14,6],[15,8],[12,8],[58,71],[11,28],[28,3],[33,-7],[25,-3]],[[6468,3130],[22,-14],[78,-33],[40,-11],[22,-3],[22,2],[24,8],[13,8],[7,8],[2,6],[0,5],[0,4],[-2,8],[0,5],[0,6],[2,7],[2,4],[4,4],[11,7],[9,4],[16,5],[188,32],[65,18],[80,8],[19,-2],[111,-41],[20,-13],[44,-46],[6,-3],[6,-5],[44,-18]],[[6191,4954],[-2,-14],[-11,-22],[-3,-13],[0,-15],[6,-26],[1,-13],[4,-17],[29,-53],[33,-33],[86,-45],[37,-29],[17,-20],[6,-10],[0,-12],[-7,-11],[-21,-8],[-4,-9],[4,-12],[9,-8],[9,-7],[4,-7],[-1,-9],[-8,-36],[-4,-38],[-4,-11],[-28,-18],[-4,-3],[-4,-6],[-9,-9],[-9,-12],[-4,-15],[6,-8],[15,-2],[16,0],[12,-2],[21,-25],[6,-29],[2,-32],[12,-30],[23,8],[20,-12],[10,-21],[-8,-20],[-8,-10],[-1,-13],[5,-10],[12,-4],[42,2],[11,-2],[19,-23],[-11,-24],[-21,-24],[-9,-20],[-22,-23],[-5,-6],[-1,-5],[0,-8],[1,-15],[4,-10],[5,-9],[23,-23],[9,-13],[13,-22],[5,-16],[6,-75],[10,-35],[12,-86],[6,-17],[14,-30],[-2,-12],[-11,-39],[-5,-49],[1,-24],[4,-16],[9,-16],[8,-15],[16,-9],[19,-15],[8,-19],[-26,-48],[-5,-23],[-7,-19],[-18,-12],[-14,-1],[-10,4],[-11,2],[-14,-5],[-4,-9],[-18,-69],[-61,-78],[0,-7],[10,-11],[11,-1],[10,6],[8,1],[3,-19],[-3,-13],[-4,-11],[1,-11],[10,-14],[14,-14],[4,-10],[-5,-8],[-17,-5]],[[6181,2981],[-35,0],[-33,6],[-14,-2],[-6,-16],[-4,-31],[-11,-33],[-16,-32],[-18,-25],[-16,-14]],[[5922,2767],[-36,23],[-23,30],[-38,78],[-14,16],[-38,17],[-18,12],[-52,57],[-17,14],[-23,8],[-95,0],[-16,5],[-8,12],[-4,14],[-8,10],[-11,2],[-27,-3],[-12,1],[-82,38],[-15,0],[-12,18],[-107,68],[-22,0],[-20,-14],[-33,-32],[-15,-10],[-15,-7],[-13,-2],[-15,4],[-7,7],[-3,9],[-8,12],[-76,79],[-28,37],[-15,13],[-27,12],[-263,48],[-6,6],[-3,9],[-7,5],[-9,4],[-9,6],[-17,5],[-61,1],[-24,6],[-228,160],[-38,9],[-12,7],[-24,22],[-11,7],[-8,1],[-22,1],[-24,8],[-44,27],[-28,9],[-34,22],[-50,16],[-11,8],[-6,8],[-9,7],[-18,2],[-11,4],[-16,17],[-39,14],[-20,22],[-24,43],[-11,11],[-46,38],[-29,12],[-4,10],[-3,11],[-4,8],[-8,6],[-26,11],[-27,14],[-12,9],[-13,14],[-10,6],[-22,4],[-20,12],[-30,3],[-12,4],[-8,8],[-21,26],[-46,43],[-428,264],[-22,5],[-24,-3],[-56,-18],[-28,-6],[-90,-3],[-93,13],[-37,-2],[-11,2],[-13,7],[-23,19],[-13,8],[-11,1],[-42,-1],[-74,13],[-39,26],[-22,10],[-42,2],[-7,2],[-19,39],[-1,7],[-11,2],[-21,11],[-13,4],[-14,1],[-54,-3],[-12,3],[-24,20],[-12,3],[-69,-2]],[[7841,4503],[73,-182],[7,-27],[-3,-9],[-7,2],[-3,0],[-5,0],[-5,-2],[-5,-3],[-18,-16],[-15,-20],[-27,-24],[-16,-19],[-4,-4],[-6,-2],[-29,-6],[-6,-3],[-6,-3],[-5,-4],[-23,-29],[-4,-6],[-5,-19],[1,-26],[-7,-57],[0,-23],[2,-15],[26,-21],[6,-6],[5,-12],[3,-13],[6,-39],[4,-19],[7,-15],[7,-11],[8,-10],[10,-9],[10,-6],[10,-5],[23,-7],[9,-4],[7,-6],[5,-8],[25,-53],[63,-81],[26,-49],[17,-50],[12,-26],[14,-17],[35,-20],[20,-16],[23,-23],[10,-27],[9,-18],[8,-12],[11,-10],[64,-35]],[[8208,3378],[-141,-35],[-95,-36],[-25,-19],[-17,-19],[-52,-96],[-11,-14],[-10,-8],[-10,4],[-55,48],[-41,42],[-8,6],[-7,4],[-55,19],[-12,8],[-8,8],[-4,9],[0,10],[1,9],[4,18],[0,13],[-6,15],[-15,12],[-11,1],[-12,-6],[-12,-11],[-10,-12],[-8,-15],[-5,-15],[-11,-16],[-16,-15],[-51,-28],[-13,-10],[-7,-13],[-6,-12],[-9,-14],[-11,-11],[-37,-19],[-12,-7],[-87,-83]],[[7553,5117],[13,-9],[5,2],[10,4],[13,9],[28,14],[22,6],[25,3],[13,3],[11,6],[6,0],[19,-5],[16,-2],[50,-3],[17,-4],[10,-6],[10,-8],[5,-8],[3,-8],[0,-7],[-3,-8],[-6,-12],[-2,-6],[1,-6],[6,-8],[20,-16],[5,-5],[5,-8],[3,-8],[2,-7],[0,-4],[-1,-7],[-3,-6],[-8,-11],[-10,-11],[-98,-76],[-4,-6],[0,-8],[3,-10],[6,-16],[9,-20],[22,-36],[15,-15],[12,-8],[6,0],[6,3],[19,14],[8,6],[13,6],[7,4],[4,6],[3,7],[1,11],[0,17],[1,7],[5,4],[10,4],[73,13],[16,1],[12,-2],[3,-5],[1,-5],[-3,-14],[-1,-16],[4,-44],[-1,-7],[-1,-7],[-8,-17],[-8,-10],[-12,-13],[-27,-33],[-2,-5],[1,-6],[5,-19],[4,-29],[-1,-31],[-1,-7],[-1,-7],[-5,-12],[-21,-36],[-6,-20],[-3,-4],[-2,-3],[-40,-27],[-12,-5],[-9,-2]],[[6529,2604],[-48,-28],[-35,-28],[-8,-10],[-100,-148],[-39,-41],[-42,-38],[-14,-18],[-13,-24],[-15,-42],[-4,-35],[1,-27],[28,-83],[2,-21],[-4,-26],[-32,-56],[-27,-32],[-56,-49]],[[6123,1898],[-16,34],[-30,36],[-6,12],[-4,11],[-2,12],[-4,8],[-5,5],[-24,10],[-13,6],[-31,25],[-28,16],[-26,21],[-16,16],[-16,20],[-10,7],[-7,4],[-8,0],[-25,-6],[-7,0],[-8,1],[-24,8],[-6,1],[-6,-1],[-5,-4],[-3,-5],[-1,-10],[2,-21],[-1,-8],[-1,-5],[-7,-4],[-6,-2],[-28,6],[-4,2]],[[5747,2093],[8,5],[13,17],[-18,41],[-4,23],[18,10],[21,7],[23,16],[20,19],[13,15],[-13,3],[-11,5],[-9,7],[-8,10],[25,35],[15,13],[25,10],[14,1],[12,-1],[12,1],[11,7],[5,9],[6,27],[2,6],[35,-5],[19,1],[8,7],[5,28],[13,9],[16,4],[14,13],[7,22],[15,104],[-1,20],[-9,16],[-16,16],[-22,16]],[[8358,2333],[-7,-13],[-3,-5],[-9,-17],[-16,-39],[-7,-12],[-11,-10],[-15,-4],[-11,1],[-10,3],[-11,1],[-13,-3],[-17,-14],[-9,-11],[-12,-23],[-6,-7],[-16,-8],[-107,-40],[-15,-9],[-30,-21],[-13,-6],[-43,-8],[-14,-6],[-22,-18],[-15,-8],[-39,-10],[-15,-7],[-11,-12],[-6,-12],[-25,-93],[-3,-10],[-2,-2],[-2,-2],[-29,-19],[-67,-30],[-22,2],[-47,28],[-33,24],[-6,4],[-9,4],[-15,0],[-17,-4],[-58,-24],[-12,0],[-8,2],[-8,4],[-7,0],[-10,-4],[-10,-15],[-4,-14],[-2,-15],[-5,-13],[-9,-9],[-20,-7],[-58,-2],[-17,-5],[-13,-9],[-12,-20],[-8,-21],[-8,-53],[-10,-10],[-17,-6],[-30,4],[-38,0]],[[7214,1700],[-80,134],[-7,15],[-5,18],[-6,46],[-1,2],[-1,7],[2,7],[7,9],[8,4],[50,11],[12,6],[6,12],[0,18],[-11,34],[-11,18],[-28,33],[-9,14],[-8,18],[-12,38],[-7,37],[-1,21],[2,20],[8,21],[20,18]],[[7142,2261],[26,14],[65,29],[12,7],[57,49],[16,9],[20,7],[37,6],[16,4],[14,5],[35,21],[21,6],[23,2],[135,-5],[33,2],[18,0],[35,-6],[96,-4],[13,-6],[10,-9],[14,-7],[19,-3],[127,3],[22,-3],[25,-10],[16,-10],[13,-13],[25,-27],[15,-13],[16,-8],[30,-2],[38,5],[72,20],[71,27],[11,0],[6,-2],[14,-16]],[[6980,1260],[34,-20],[7,-7],[10,-15],[10,-17],[22,-33],[19,-16],[51,-31],[18,-8],[17,-3],[37,0],[21,-6],[10,-10],[2,-10],[-2,-11],[-6,-13],[-1,-6],[-1,-2],[3,-11]],[[7231,1041],[48,-438],[-3,-42],[-11,-20],[-60,-7],[-9,-3],[-11,-6],[-31,-32],[-10,-8],[-12,-6],[-11,-12],[-11,-19],[-16,-50],[-4,-25],[2,-20],[8,-11],[8,-8],[42,-36],[6,-7],[4,-9],[0,-7],[-4,-7],[-4,-5],[-5,-10],[-1,-7],[6,-84],[1,-1]],[[7153,161],[-54,-14],[-61,-43],[-44,-8],[0,1],[-43,24],[-74,66],[-41,16],[-44,-5],[-43,-15],[-118,-59],[-39,-8],[-46,2],[-11,1],[-27,1],[-1,0],[-13,4],[-11,6],[-31,24],[-12,6],[-1,0],[-54,13],[-11,3],[-13,6],[-8,8]],[[6353,190],[1,1],[21,80],[107,275],[7,46],[-9,19],[-34,8],[-217,30],[-9,5],[-137,145],[-98,127],[-11,24],[-7,21],[-84,444]],[[5883,1415],[40,-19],[20,-26],[8,-5],[14,-7],[7,-8],[6,-11],[8,-22],[5,-8],[7,-9],[31,-16],[8,-8],[7,-10],[9,-10],[16,-8],[87,-17],[15,-8],[8,-10],[13,-11],[16,-8],[32,-7],[19,-13],[26,-10],[32,-3],[105,21],[61,20],[16,9],[7,12],[2,14],[0,18],[2,16],[7,11],[10,7],[17,7],[9,6],[5,5],[1,5],[4,6],[7,8],[12,6],[6,6],[5,9],[3,11],[7,15],[11,15],[14,14],[18,13],[8,4],[9,3],[12,3],[12,1],[11,0],[11,-2],[9,-2],[70,-34],[26,-16],[39,-30],[22,-12],[22,-3],[14,4],[15,-5],[11,-4],[43,-62]],[[6123,1898],[33,-175],[-2,-13],[-5,-8],[-158,-145],[-46,-81],[-14,-19],[-25,-18],[-23,-24]],[[6353,190],[-19,17],[-11,7],[-25,6],[-5,1],[-31,0],[-120,-16],[-31,3],[-8,3],[-18,5],[-193,89],[-1,0],[-13,5],[-122,23],[-51,16],[-53,10],[-17,3],[-36,14],[-1,0],[-70,14],[-22,4],[-37,1],[-72,-12],[-18,-1],[-130,15],[-324,-25],[-107,-42],[-5,-4],[1,1],[5,59],[-2,25],[-15,23],[-30,20],[-16,14],[-7,16],[6,35],[17,24],[27,15],[61,9],[5,13],[-2,18],[0,22],[3,11],[4,9],[6,9],[7,9],[11,5],[1,0],[6,-5],[3,-9],[0,-4],[1,-3],[9,-5],[0,-1],[10,-2],[4,7],[1,20],[3,8],[4,9],[11,17],[21,24],[9,17],[-18,15],[-2,15],[10,16],[18,11],[53,13],[26,10],[11,15],[11,11],[28,3],[27,2],[16,12],[-8,21],[-22,14],[-14,17],[15,27],[20,5],[21,-6],[20,-3],[16,16],[-4,10],[-10,14],[-1,12],[19,5],[15,0],[9,1],[4,6],[4,94],[5,23],[6,14],[4,1],[6,-4],[37,-6],[15,0],[6,8],[-4,11],[-6,9],[-6,11],[0,14],[4,4],[20,14],[8,7],[-7,7],[-10,17],[-7,9],[-27,18],[-7,7],[-4,20],[3,18],[7,19],[3,21],[4,10],[17,11],[4,12],[-29,25],[-4,15],[8,19],[40,65],[6,17],[3,20],[-6,38],[0,20],[10,8],[19,4],[27,11],[20,14],[-1,16],[-17,12],[-19,8],[-14,13],[-3,21],[12,-4],[10,-5],[6,-7],[5,-9],[10,14],[11,25],[11,40],[12,11],[78,26],[39,21],[138,94],[15,20],[0,16],[-55,14],[9,19],[42,31],[7,9],[7,14],[-1,13],[-27,9],[-4,8],[4,9],[25,9],[25,12],[16,11]],[[7024,2329],[37,-25],[26,-30],[10,-6],[7,-3],[38,-4]],[[7214,1700],[-9,-39],[-6,-11],[-41,-43],[-9,-14],[-12,-45],[-4,-43],[-3,-11],[-6,-9],[-24,-16],[-5,-7],[-2,-7],[1,-12],[2,-10],[-1,-12],[-5,-13],[-16,-15],[-7,-9],[-5,-14],[-1,-14],[-6,-15],[-10,-14],[-23,-18],[-14,-9],[-17,-13],[-11,-27]],[[8466,4887],[0,-1],[-12,-8],[-12,-17],[-21,-54],[-10,-47],[-8,-12],[-13,-12],[-49,-27],[-10,-4],[-11,-2],[-33,-1],[-18,-7],[-25,-14],[-123,-84],[-51,-27],[-12,-11],[-17,-27],[-7,-8],[-11,-7],[-31,-12],[-30,-6],[-42,-4],[-9,-3],[-10,-5],[-7,-2],[-6,-1],[-8,1],[-7,2],[-18,7],[-14,9]],[[6909,6382],[27,29],[8,11],[2,8],[4,2],[16,-6],[9,1],[39,11],[9,-2],[2,-5],[2,-2],[12,7],[6,8],[9,22],[6,5],[20,-10],[41,-44],[19,-4],[5,10],[-12,21],[7,8],[14,3],[9,5],[40,28],[13,13],[20,34],[3,3],[9,4],[4,5],[6,19],[4,8],[18,25],[11,11],[12,10],[21,6],[86,15],[17,-2],[12,-13],[31,-73],[28,-40],[37,-33],[66,-37],[35,-32],[21,-12],[20,-5],[205,-10],[16,4],[15,8],[16,-7],[17,-13],[19,-9],[74,-6],[22,-13],[3,9],[13,25],[60,-77],[48,-34],[12,-13],[10,-29],[4,-102],[8,-44],[21,-39],[31,-31],[39,-21],[25,-8],[15,-9],[9,-14],[4,-25],[-1,-9],[-7,-20],[-1,-10],[3,-11],[8,-19],[1,-11],[-13,-40],[-20,-35],[-16,-36],[3,-45],[27,-93],[0,-21],[-7,-41],[2,-20],[32,-34],[13,-19],[6,-23],[-6,-44],[1,-20],[13,-16],[33,-19],[4,-11],[26,-89],[0,-20],[-28,-55],[-4,-20],[8,-27],[37,-37],[13,-22],[-2,-37],[-32,-67],[3,-42],[7,-36]],[[9864,3440],[-15,-43],[-34,-46],[-9,-19],[-54,-219],[-11,-42],[-2,-22],[1,-20],[9,-24],[31,-46],[3,-23],[-10,-21],[-41,-34],[-14,-22],[-26,-78],[-30,-43],[-22,-27],[-34,-29],[-27,-45],[-14,-42],[-2,-14],[-1,-25],[5,-27],[12,-22],[8,-23],[-3,-29],[-7,-29],[0,-2],[-3,-21],[19,-39],[4,-25],[-18,-10],[-42,2],[-11,-12],[0,-31],[21,-63],[4,-24],[6,-17],[27,-30],[7,-14],[-2,-27],[-11,-22],[-13,-19],[-6,-19],[9,-55],[1,-22],[-10,-18],[-10,-3],[-29,-2],[-10,-4],[-7,-13],[1,-12],[4,-13],[6,-36],[12,-39],[0,-27],[-25,-77],[5,-18],[21,-24],[4,-16],[-7,-13],[-14,-18],[-10,-16],[0,-3]],[[9500,1624],[-1,0],[-2,0],[-68,5],[-13,3],[-7,7],[-3,8],[0,8],[1,8],[0,6],[-1,5],[-3,4],[-4,4],[-6,4],[-23,11],[-18,6],[-22,4],[-19,0],[-9,-3],[-10,-5],[-16,-13],[-7,-4],[-7,-2],[-9,-1],[-9,3],[-11,8],[-3,7],[1,8],[5,17],[1,6],[-1,6],[-2,3],[-4,2],[-5,0],[-8,-3],[-5,-4],[-7,-7],[-9,-12],[-18,-30],[-5,-5],[-8,-8],[-8,-4],[-7,-3],[-16,-4],[-40,-3],[-7,-8],[-5,-5],[-4,-9],[-2,-3],[-3,-2],[-4,-3],[-8,1],[-9,4],[-18,13],[-13,6],[-38,9],[-30,12],[-12,3],[-10,2],[-23,1],[-88,-4],[-25,21],[-41,117]],[[8754,1811],[63,34],[9,9],[6,11],[0,10],[-8,9],[-8,4],[-9,2],[-22,2],[-10,3],[-7,5],[-7,7],[-4,5],[-5,5],[-7,5],[-7,1],[-38,-11],[-21,-3],[-34,-2],[-28,1],[-10,-2],[-16,-7],[-9,2],[-9,3],[-16,8],[-50,18],[-5,4],[-7,10],[-4,13],[4,33],[0,11],[-8,24],[0,12],[4,13],[-4,12],[-6,8],[-6,9],[0,13],[8,22],[23,48],[4,4],[25,19],[7,9],[6,11],[18,53],[4,17],[2,27],[7,17],[9,12],[30,25],[7,10],[2,8],[-1,9],[-5,8],[-16,19],[-5,11],[-2,10],[0,9],[-3,7],[-6,6],[-16,4],[-11,1],[-43,-11]],[[8519,2447],[277,355],[35,36],[14,-1],[22,-14],[30,-28],[13,-6],[28,-10],[15,-3],[14,1],[-2,11],[-6,24],[-1,13],[8,-8],[11,-17],[8,-8],[9,-6],[9,-3],[21,-3],[16,-7],[10,-10],[4,-14],[2,-16],[16,-26],[31,-4],[31,-1],[27,7],[0,15],[-1,7],[-2,6],[-3,5],[-8,10],[-3,5],[-3,6],[-1,7],[0,7],[3,22],[-1,6],[-4,5],[-5,3],[-32,12],[-6,3],[-5,4],[-4,4],[-2,5],[0,6],[7,24],[1,7],[-1,7],[-3,5],[-5,4],[-100,66],[-95,84],[-7,10],[-4,4],[-6,2],[-6,0],[-7,-1],[-7,0],[-5,2],[-5,4],[-4,4],[-5,4],[-6,3],[-6,1],[-22,-3],[-8,0],[-8,1],[-8,2],[-6,2],[-6,3],[-5,4],[-8,8],[-4,5],[-3,5],[-5,12],[-5,11],[-6,11],[-14,19],[-3,8],[-1,11],[5,15],[0,10],[-1,7],[-2,6],[-1,7],[0,9],[2,18],[-1,8],[-1,6],[-2,6],[-3,8],[0,8],[1,13],[8,8],[12,17],[2,11],[-1,11],[-10,28],[-6,23],[3,13],[5,8],[9,5],[8,7],[8,15],[3,14],[-2,49],[5,12],[13,21],[1,10],[-2,12],[-7,27],[-1,18],[1,12],[4,10],[6,8],[36,39],[30,41]],[[8830,3716],[132,42],[26,-6],[-5,-45],[1,-13],[6,-15],[17,-15],[17,-7],[16,-3],[14,3],[29,11],[17,3],[10,0],[91,-27],[23,-12],[14,-13],[2,-11],[0,-8],[-2,-7],[-2,-6],[-1,-7],[2,-6],[8,-8],[10,-3],[10,-1],[43,-2],[17,-2],[9,-4],[5,-4],[0,-5],[-1,-7],[0,-11],[6,-8],[6,-4],[8,1],[6,2],[6,4],[6,6],[5,6],[8,11],[6,5],[8,6],[15,1],[10,-2],[14,-6],[8,-3],[10,-2],[8,1],[17,5],[11,1],[5,-1],[5,-6],[13,-30],[7,-10],[8,-9],[11,-4],[92,-13],[34,-9],[33,-13],[28,-6],[30,0],[27,3],[31,0],[17,-2],[27,-11]],[[8519,2447],[-19,-28],[-3,-15],[0,-6],[-2,-8],[-6,-9],[-11,-12],[-7,-8],[-11,-9],[-13,-7],[-59,-21],[-30,9]],[[8208,3378],[31,35],[34,49],[17,17],[59,43],[130,130],[16,8],[24,7],[311,49]],[[8754,1811],[-15,3],[-5,3],[-4,1],[-4,-1],[-3,-4],[-2,-9],[0,-8],[-1,-8],[-3,-8],[-1,-10],[1,-57],[-4,-11],[-7,-10],[-9,-11],[-50,-44],[-100,-72],[-21,-11],[-81,-28],[-25,-4],[-16,-1],[-17,4],[-21,0],[-25,-2],[-50,-14],[-70,-27],[-13,-14],[-10,-24],[-8,-10],[-22,-17],[-56,-53],[-19,-12],[-20,-9],[-83,-14],[-60,-17],[-17,-13],[-27,-36],[-5,-12],[-11,-29],[-12,-16],[-14,-15],[-17,-13],[-10,-10],[-7,-11],[-11,-38],[-9,-9],[-19,-7],[-37,-5],[-72,-22],[-102,-50],[-138,-49],[-20,0],[-19,4],[-18,10],[-37,28],[-8,4],[-89,18]],[[8466,4887],[1,-5],[-1,-43],[4,-40],[23,-32],[53,-52],[25,-75],[18,-233],[12,-31],[27,-27],[36,-16],[75,-5],[38,-7],[0,-1],[42,-14],[43,-8],[42,3],[41,19],[0,1],[31,14],[75,-1],[36,10],[21,9],[39,2],[21,3],[20,11],[26,33],[16,15],[76,40],[156,51],[20,10],[30,30],[18,9],[32,-8],[202,-107],[24,-17],[65,-86],[112,-62],[33,-17],[1,0],[-25,-18],[-41,-29],[-29,-34],[-11,-37],[3,-20],[15,-38],[6,-45],[9,-15],[29,-35],[9,-27],[16,-38],[-1,-35],[-11,-37],[-15,-27],[-46,-63],[-14,-39],[0,-37],[16,-83],[0,-39],[-45,-124]],[[9500,1624],[-2,-6],[3,-24],[0,-54],[-18,-38],[-2,-6],[-3,-7],[-12,-74],[-5,-19],[-11,-17],[-36,-37],[-34,-26],[-18,-19],[-11,-22],[1,-17],[7,-17],[3,-22],[-1,-28],[-4,-30],[-10,-26],[-16,-20],[-13,-5],[-6,3],[-6,6],[-9,4],[-52,0],[-29,-6],[-20,-17],[-12,-24],[-16,-56],[-9,-21],[-11,-18],[-17,-18],[-20,-9],[-58,-4],[-24,-8],[-77,-71],[-9,-20],[17,-48],[-4,-26],[-19,-14],[-28,-5],[-47,3],[-19,5],[-22,11],[-25,9],[-29,0],[-58,-29],[-55,-6],[-24,-7],[-21,-13],[-20,-20],[-15,-23],[-7,-24],[-3,-56],[-12,-19],[-29,-3],[-33,4],[-24,-3],[-14,-19],[-5,-25],[-6,-21],[-20,-10],[-32,-7],[0,-18],[28,-49],[5,-15],[3,-12],[-3,-13],[-16,-24],[1,-9],[3,-9],[-1,-17],[-11,-28],[-20,-20],[-25,-13],[-29,-5],[-34,-23],[-42,-46],[-45,-32],[-72,36],[-32,8],[-14,8],[-10,12],[-18,45],[-18,23],[-24,8],[-28,0],[-29,-4],[-6,-1],[-22,0],[-27,5],[-79,28],[-1,0],[-29,5],[-31,2],[-30,-16],[-22,-15],[-44,-45],[-27,-11],[-34,-9],[-27,-13],[-7,-27],[5,-32],[-5,-27],[-14,-24],[-23,-24],[-23,-30],[-16,-37],[-22,-30],[-38,-7],[-1,0],[-37,14],[-35,26],[-31,32],[-23,32],[-48,55],[-79,10],[-29,-8]]],
transform:{scale:[.000840590895089512,.0008300943620362039],translate:[-62.65035721899997,-27.58684214299994]}},m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();