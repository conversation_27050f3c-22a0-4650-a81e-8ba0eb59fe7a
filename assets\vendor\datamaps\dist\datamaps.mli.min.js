!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo={type:"Topology",objects:{mli:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Timbuktu"},id:"ML.TB",arcs:[[0,1,2,3,4,5]]},{type:"Polygon",properties:{name:"Kidal"},id:"ML.KD",arcs:[[6,-1,7]]},{type:"Polygon",properties:{name:"Gao"},id:"ML.GA",arcs:[[8,-2,-7]]},{type:"Polygon",properties:{name:"Bamako"},id:"ML.KK",arcs:[[9]]},{type:"Polygon",properties:{name:"Kayes"},id:"ML.KY",arcs:[[10,11]]},{type:"Polygon",properties:{name:"Sikasso"},id:"ML.SK",arcs:[[12,13,14]]},{type:"Polygon",properties:{name:"Mopti"},id:"ML.MO",arcs:[[15,16,-4]]},{type:"Polygon",properties:{name:"Ségou"},id:"ML.SG",arcs:[[17,-15,18,19,-5,-17]]},{type:"Polygon",properties:{name:"Koulikoro"},id:"ML.KK",arcs:[[-19,-14,20,-11,21],[-10]]}]}},arcs:[[[7434,7881],[-2,-7],[-134,-461],[-390,-347],[-276,-240],[-438,-246],[378,-340],[276,-233],[48,-73]],[[6896,5934],[-30,-720],[-114,-58],[-308,-159],[-46,-33],[38,-172],[21,-90],[-7,-33],[7,-28],[42,-3],[257,-27],[180,-19],[39,-5],[23,-3],[20,-12],[-1,-75],[-1,-56],[-14,-84],[5,-187],[2,-88],[3,-106],[24,-136],[5,-27],[10,-42],[29,-133],[13,-65],[36,-247],[0,-1]],[[7129,3325],[-133,-1],[-20,-6],[-11,-9]],[[6965,3309],[-1,0],[-268,83],[-55,56],[-49,40],[-66,36],[-116,37],[-79,-3],[-59,5],[-45,-4],[-96,-14],[-111,-46],[-73,-23],[-49,12],[-40,32],[-31,64],[-64,118],[-11,52],[-59,57],[-33,35],[-23,4],[-48,-13],[-69,-5],[-5,30],[-23,12],[-25,-20],[-53,-64],[-55,2],[-37,-27],[-9,-31],[4,-59],[-20,-19],[-45,-22],[-27,-32],[-53,11],[-70,-9],[-2,76],[-12,44],[-53,42],[-17,61],[-43,-4],[-49,-7],[-17,-12],[-46,-49],[-83,-9],[-45,-33],[-144,-51],[-96,6],[-95,9],[-54,-3],[-35,7],[-48,44],[-39,-44],[-38,-38]],[[4186,3643],[-91,-30],[-1,-1]],[[4094,3612],[0,3],[1,3],[11,66],[12,65],[11,65],[11,66],[11,63],[11,64],[12,64],[11,63],[3,20],[-1,5],[0,4],[-3,3],[-3,4],[-24,18],[-25,18],[-24,18],[-27,20],[-22,16],[-24,18],[-5,11],[-6,11],[-2,27],[-6,61],[-6,60],[-6,61],[-6,61],[-6,60],[-6,61],[-6,61],[-6,61],[-6,60],[-6,61],[-6,61],[-6,61],[-6,60],[-6,61],[-6,61],[-6,60],[-6,63],[-7,62],[-6,63],[-6,62],[-6,63],[-6,62],[-7,62],[-6,63],[-6,62],[-6,63],[-6,62],[-6,63],[-4,39],[-4,39],[-4,39],[-4,40],[-9,92],[-9,84],[-8,85],[-9,84],[-9,84],[-9,84],[-8,84],[-9,84],[-9,84],[-7,73],[-5,39],[-9,87],[-9,87],[-4,39],[-8,81],[-9,82],[-9,81],[-8,81],[-9,81],[-8,82],[-9,81],[-8,81],[-9,81],[-8,82],[-9,81],[-9,81],[-8,81],[-9,81],[-8,82],[-9,81],[-8,81],[-7,64],[-2,17],[-8,81],[-9,82],[-9,81],[-8,81],[-9,81],[-8,82],[-5,41],[-9,92],[-10,91],[-4,42],[-1,4],[-5,55],[-6,53],[-6,54],[-6,53],[-6,54],[-6,54],[-6,53],[-5,54],[-6,53],[134,0],[134,1],[134,0],[134,0],[135,0],[134,0],[134,0],[134,0],[47,-32],[46,-32],[46,-33],[46,-32],[68,-49],[48,-35],[48,-35],[49,-35],[48,-35],[48,-35],[49,-35],[48,-35],[48,-35],[48,-35],[49,-35],[48,-35],[48,-35],[49,-35],[48,-36],[48,-35],[49,-35],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-39],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-39],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-39],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[52,-39],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[8,-7]],[[9997,5274],[-330,0],[-462,0],[-575,0],[-108,-147],[-360,-86],[-384,340],[-126,146],[-180,240],[-576,167]],[[7434,7881],[44,-32],[53,-38],[52,-38],[53,-38],[52,-38],[53,-38],[52,-38],[53,-39],[52,-38],[53,-38],[52,-38],[53,-38],[71,-52],[8,-13],[11,-43],[1,-15],[-7,-74],[-14,-60],[0,-14],[1,-16],[5,-9],[8,-4],[14,-1],[13,0],[24,5],[13,0],[14,-4],[8,-7],[13,-23],[9,-13],[11,-7],[26,-9],[24,-4],[11,-4],[11,-7],[23,-4],[23,-13],[39,-31],[12,-19],[4,-24],[0,-51],[6,-10],[72,-62],[13,-7],[13,-1],[11,2],[9,-1],[10,-7],[6,-14],[2,-13],[4,-8],[14,-1],[6,4],[11,10],[8,2],[7,-1],[5,-3],[5,-4],[6,-4],[38,-14],[9,-2],[16,8],[25,24],[14,10],[13,2],[11,-3],[10,-6],[38,-32],[22,-25],[19,-29],[25,-47],[7,-7],[9,-4],[15,-3],[12,-5],[21,-13],[12,-2],[7,-1],[55,-11],[33,-2],[166,-36],[77,-36],[35,-29],[10,-5],[22,-7],[10,-5],[8,-9],[3,-9],[-12,-136],[1,-26],[8,-24],[3,-4],[3,-2],[3,-3],[3,-6],[0,-4],[-4,-13],[0,-7],[5,-12],[8,-10],[7,-11],[1,-15],[-6,-15],[-11,-9],[-13,-8],[-11,-10],[-5,-12],[-3,-26],[-3,-12],[-13,-14],[-8,-6],[-3,-6],[-5,-13],[-9,-15],[-6,-12],[1,-12],[10,-15],[11,-12],[12,-9],[13,-8],[28,-13],[36,-37],[14,-10],[6,-2],[9,-2],[16,1],[49,13],[63,13],[104,21],[45,10],[58,12],[104,21],[104,21],[0,-83],[0,-83],[0,-83],[1,-83],[0,-2],[0,-82],[0,-83],[0,-83],[1,-83],[0,-84],[0,-36]],[[9997,5274],[0,-47],[0,-83],[1,-83],[0,-84],[0,-83],[0,-83],[1,-83],[0,-43],[0,-70],[-8,-7],[-7,0],[-4,-2],[-4,-12],[-1,-12],[4,-66],[-3,-7],[-8,-14],[-2,-6],[1,-42],[0,-90],[0,-58],[1,-75],[-5,-15],[-9,-9],[-26,-14],[-14,-12],[-12,-14],[-9,-15],[-54,-143],[-3,-18],[3,-19],[7,-20],[0,-1],[1,0],[0,-1],[0,-1],[0,-4],[0,-2],[-1,-2],[-35,-38],[-9,-16],[-4,-12],[-6,-66],[-5,-26],[-7,-20],[-2,-4],[-15,-19],[-23,-14],[-48,-10],[-22,-13],[-48,-56],[-53,-35],[-6,-18],[-5,-77],[-12,2],[-3,1],[-62,12],[-114,21],[-73,13],[-23,-1],[-10,-2],[-5,-4],[-1,-6],[-1,-13],[0,-25],[-4,-8],[-30,-2],[-58,-2],[-59,-2],[-58,-2],[-59,-2],[-58,-2],[-59,-3],[-58,-2],[-59,-2],[-59,-2],[-58,-2],[-59,-2],[-58,-2],[-59,-3],[-58,-2],[-59,-2],[-58,-2],[-45,-2],[-20,-5],[-17,-11],[-41,-41],[-48,-49],[-40,-40],[-51,-51],[-15,-7],[-16,-4],[-93,-4],[-17,-7],[-18,-7],[-16,-5],[-9,0],[-94,36],[-19,-1],[-39,-15],[-19,-4],[-21,0],[-80,22],[-5,-8],[0,-16],[4,-34],[-153,58],[-81,36],[-42,10],[-38,-7],[-38,-25],[-22,-10],[-17,0],[-6,8],[-14,41],[-5,3],[-20,0]],[[2597,1617],[-19,6],[-12,4],[-5,8],[-10,7],[-6,17],[-3,25],[3,19],[8,14],[14,7],[16,2],[16,-4],[13,-9],[9,-16],[3,-17],[2,-21],[-1,-20],[-7,-15],[-21,-7]],[[1892,3605],[12,-15],[30,-26],[33,-27],[17,-18],[86,-51],[27,-32],[5,-16],[5,-53],[28,-78],[39,-30],[64,-4],[39,-9],[9,-14],[10,-44],[-3,-58],[-3,-38],[13,-24],[62,-49],[37,-30],[38,-41],[45,-11],[8,-10],[-2,-21],[-4,-16],[15,-51],[2,-12],[-18,-13],[-39,-6],[-28,-5],[-32,8],[-44,-6],[-27,-6],[-18,2],[-16,17],[-18,36],[-13,15],[-13,7],[-14,-3],[-14,-33],[-20,-31],[-24,-7],[-19,-10],[-53,0],[27,-72],[3,-14],[1,-15],[-4,-19],[-9,-1],[-11,4],[-13,-5],[-3,-10],[3,-23],[-3,-11],[-5,-6],[-2,-6],[-3,-13],[-3,-5],[-4,-2],[-2,-3],[1,-9],[4,-5],[19,-18],[6,-3],[6,-1],[5,-3],[4,-5],[3,-8],[4,-5],[5,1],[6,7],[17,-34],[12,-11],[52,-9],[15,2],[8,16],[-9,2],[1,9],[10,22],[2,16],[4,7],[12,4],[21,0],[16,-6],[12,-14],[8,-22],[1,-10],[-3,-14],[0,-10],[-1,-11],[-6,-8],[-7,-7],[-7,-8],[-9,-22],[-5,-8],[-17,-17],[-6,-10],[-2,-14],[7,-70],[2,-7],[3,-5],[6,-5],[6,-6],[4,-7],[7,-18],[18,-19],[6,-11],[4,-13],[1,-14],[-1,-13],[-7,-9],[-6,-4],[-5,-2],[-4,-3],[-4,-8],[-4,-8],[-12,-41],[-2,-14],[-2,-8],[-4,-8],[-10,-13],[-4,-7],[-3,-15],[-2,-42],[-3,-9],[-3,-7],[-3,-7],[0,-10],[5,-36],[0,-16],[-5,-20],[-27,-46],[-6,-17],[0,-17],[5,-10],[9,-9],[8,-16],[-51,-30],[-10,-9],[-15,-23],[-24,-17],[-28,-9],[-27,-5],[-23,-13],[-20,-43],[-23,-21],[-25,-21],[-25,-51],[21,-16],[-8,-9],[-3,-4]],[[1961,1524],[-72,41],[-26,10],[-46,10],[-8,0],[-4,-4],[-5,-3],[-9,3],[-8,5],[-3,1],[-4,-1],[-11,-8],[-28,-8],[-9,-12],[3,-6],[9,-12],[11,-11],[17,-10],[1,-5],[1,-6],[3,-7],[4,-2],[10,-3],[4,-3],[0,-9],[-3,-13],[-5,-10],[-8,-3],[2,-16],[-3,-8],[-14,-16],[-37,7],[-11,-2],[-25,-12],[-9,-8],[-12,-12],[-69,-25],[-19,-13],[-9,-11],[-4,-10],[-1,-12],[1,-14],[-3,-8],[-7,-12],[-15,-17],[-34,1],[-11,4],[-6,4],[-10,8],[-6,3],[-4,0],[-13,-2],[-6,0],[-11,6],[-20,18],[-11,3],[-13,3],[-21,12],[-11,4],[-8,4],[-12,10],[-7,4],[-7,2],[-10,0],[-2,1],[-4,1],[-5,4],[-8,7],[-6,3],[-4,1],[-15,-1],[-12,3],[-21,11],[-20,4],[-4,4],[-3,5],[-5,3],[-23,-10],[-3,-8],[-2,0],[-2,3],[-4,4],[-1,-2],[-3,-6],[-6,-3],[-5,-1],[-4,-2],[1,-10],[-9,3],[-4,3],[-4,3],[-8,-6],[-21,-19],[-4,-5],[-6,-10],[-29,3],[-11,-5],[-1,-6],[5,-14],[-2,-2],[-5,-3],[-1,-6],[2,-8],[0,-4],[-12,-10],[-13,-6],[-6,-8],[4,-15],[-9,-4],[-13,-7],[-11,-8],[-5,-7],[-2,-9],[-5,-12],[-7,-12],[-6,-6],[-9,-5],[-25,-1],[-18,20],[-24,51],[-16,18],[-3,7],[1,6],[6,12],[1,6],[-4,19],[-11,9],[-14,8],[-14,11],[-24,42],[-11,8],[-15,5],[-12,-1],[-26,-9],[-14,0],[-9,-2],[-4,-7],[-8,-28],[-7,-9],[-20,-16],[-3,-4],[-9,-15],[-4,-2],[-5,-1],[-5,-2],[-3,-19],[-7,-13],[-5,-6],[-4,-4],[-9,-3],[-10,-1],[-20,-9],[-12,0],[-12,5],[-12,7],[-12,9],[-10,9],[-24,36],[-11,10],[-37,18],[-12,11],[-11,27],[10,20],[17,19],[12,27],[-1,15],[-4,27],[3,15],[9,6],[15,-1],[12,2],[0,13],[1,18],[4,17],[1,16],[-8,12],[-1,-4],[-5,-8],[-1,6],[-4,11],[-1,5],[-4,11],[-4,-2],[-4,-7],[1,-5],[-5,5],[-7,13],[-5,3],[-6,3],[4,4],[13,6],[1,8],[-1,5],[-1,5],[-1,7],[3,21],[0,7],[-3,7],[-4,4],[-5,1],[-2,2],[-1,10],[4,5],[5,4],[6,17],[8,3],[8,2],[5,8],[-1,7],[-6,12],[0,3],[4,5],[1,8],[0,8],[-1,6],[-2,6],[-3,5],[-2,3],[-1,1],[-2,0],[-2,1],[-3,1],[0,4],[1,4],[2,3],[1,3],[-2,11],[3,7],[3,7],[3,6],[-2,4],[-3,3],[-3,4],[0,4],[4,3],[4,-2],[4,-2],[3,0],[5,4],[4,3],[2,4],[-1,18],[-2,7],[-2,7],[-5,6],[-2,-12],[-4,-7],[-7,-4],[-10,2],[2,7],[1,13],[0,6],[2,3],[4,3],[1,2],[-6,3],[-2,2],[-2,5],[-4,8],[-8,13],[1,4],[4,5],[0,5],[-6,2],[-10,3],[-8,5],[-7,6],[-8,6],[-11,2],[-3,11],[0,24],[-6,9],[-7,7],[-6,8],[-2,12],[3,7],[7,13],[1,7],[-2,6],[-14,14],[-15,22],[-4,13],[0,15],[-5,-2],[-7,-2],[-7,0],[-2,7],[1,10],[-3,3],[-15,-1],[-12,-3],[-6,1],[-6,5],[-3,7],[-4,5],[-9,2],[-9,-6],[-6,-11],[-4,-14],[-2,-13],[-25,-21],[-3,-4],[-4,1],[-7,6],[-3,5],[-3,13],[-3,5],[-5,4],[-16,8],[-5,2],[-1,7],[8,36],[2,12],[-5,8],[-4,2],[-6,1],[-6,4],[-4,4],[-5,10],[-4,5],[-35,27],[-11,14],[-14,23],[-7,24],[-13,26],[-8,13],[-8,8],[52,32],[20,22],[10,30],[3,28],[0,13],[-3,13],[-11,14],[-11,9],[-10,11],[-6,17],[0,28],[13,52],[5,27],[-1,15],[-23,57],[-6,9],[-8,7],[-24,9],[-8,7],[-5,8],[-5,9],[9,4],[2,8],[-4,9],[-56,23],[-8,2],[1,29],[-2,13],[-7,30],[-1,14],[3,11],[12,8],[4,4],[2,8],[1,8],[1,7],[4,7],[12,12],[4,6],[0,11],[-5,4],[-6,2],[-4,4],[-1,7],[1,5],[2,4],[4,9],[-7,0],[-10,2],[-9,5],[-5,7],[-3,9],[-12,12],[-5,20],[11,-6],[13,-2],[13,2],[26,10],[13,2],[9,-5],[26,-25],[6,-4],[5,8],[3,11],[5,4],[11,-3],[12,0],[12,3],[11,8],[14,21],[9,9],[24,5],[11,7],[9,8],[5,9],[2,6],[4,5],[3,3],[2,3],[2,1],[4,2],[4,2],[0,4],[-4,6],[-2,4],[0,4],[3,24],[3,12],[4,11],[2,12],[-2,9],[-5,8],[-7,6],[-3,0],[-4,-3],[-4,-1],[-6,4],[1,4],[2,20],[7,27],[0,7],[-1,9],[-1,7],[2,14],[11,36],[4,32],[2,11],[20,47],[10,48],[8,21],[15,18],[1,8],[0,23],[16,-3],[2,0],[17,-6],[16,-1],[17,10],[22,24],[10,7],[16,5],[7,3],[6,7],[1,3],[1,4],[-1,3],[-1,3],[-3,2],[-2,2],[-1,2],[-1,3],[1,0],[6,1],[34,-8],[15,-6],[15,-14],[56,-88],[11,-14],[154,-122],[19,-20],[17,-36],[23,-23],[19,-33],[6,11],[9,38],[5,11],[17,17],[7,11],[5,25],[5,11],[10,10],[6,3],[6,0],[5,1],[7,11],[10,6],[5,5],[4,14],[0,28],[2,13],[7,7],[9,0],[37,-6],[25,0],[12,2],[2,0],[20,8],[10,2],[12,-3],[9,2],[62,-1],[16,-4],[30,2],[8,-1],[52,-22],[5,0],[11,2],[5,0],[12,-7],[22,-15],[14,-4],[25,-1],[71,11],[70,-7],[23,6],[34,22],[12,6],[30,6],[150,7],[-7,37],[-10,43],[0,19],[9,18],[37,48],[12,8],[15,-7],[2,-22],[-9,-46],[-4,-61],[106,0],[20,0]],[[4727,1735],[-7,-1],[-4,1],[-2,-2],[-4,-35],[6,-11],[26,-22],[10,-11],[13,-32],[5,-8],[3,-3],[1,-2],[-4,-9],[-9,-13],[-11,-12],[-11,-14],[-5,-16],[22,-57],[6,-29],[-18,-4],[-5,5],[-5,8],[-6,5],[-9,-1],[-7,-4],[-1,-5],[0,-5],[-2,-11],[1,-7],[-1,-4],[-2,-3],[-54,-48],[15,-32],[-5,-8],[-19,0],[-21,-14],[-7,-13],[-1,-11],[-2,-7],[-1,-2],[-12,-6],[-6,0],[-13,1],[-6,-1],[-6,-6],[-5,-9],[-4,-8],[-4,-7],[-9,-6],[-17,-3],[-11,-4],[-12,11],[-12,1],[-26,-10],[-13,0],[-13,3],[-13,1],[-13,-6],[-5,-5],[-6,-3],[-7,-2],[-7,0],[-11,-1],[-24,1],[-9,-1],[-9,-4],[-20,-14],[-19,-6],[-9,-9],[-16,-19],[-23,-21],[-12,-19],[-5,-3],[-4,-1],[-3,2],[-4,2],[-4,3],[-4,-1],[-2,-2],[-3,-2],[-2,-1],[-21,-4],[-2,-2],[-5,-5],[-3,-1],[-3,1],[-8,6],[-3,1],[-8,-1],[-4,-1],[0,-4],[4,-7],[3,-2],[16,-10],[3,-3],[10,3],[5,-1],[7,-2],[11,-5],[10,-7],[7,-9],[5,-14],[0,-12],[-1,-14],[-5,-27],[-4,-6],[-4,-7],[-3,-8],[0,-7],[4,-8],[6,-1],[6,1],[7,-2],[21,-18],[6,-24],[0,-56],[3,-15],[1,-9],[-5,-6],[-13,-7],[-10,-9],[-2,-12],[4,-70],[-1,-11],[-8,-14],[-19,-21],[-5,-15],[-4,-28],[-6,-9],[-15,-10],[-10,-5],[-10,-3],[-61,-9],[-8,-6],[-2,-11],[1,-6],[6,-35],[-4,-17],[14,3],[8,-18],[11,-47],[7,-14],[3,-9],[-9,-39],[0,-5],[0,-7],[-4,-3],[-5,-3],[-4,-5],[-4,-10],[-5,-36],[0,-6],[3,-11],[1,-5],[9,-4],[-6,-6],[-4,-11],[-1,-11],[5,-4],[1,-7],[-8,-35],[-14,-24],[-2,-6],[-5,-34],[-4,-10],[-16,7],[-13,10],[-8,2],[-6,0],[-17,-4],[-33,-4],[-78,-19],[-44,-25],[-9,-8],[-7,-11],[-3,-12],[3,-37],[-7,-6],[-28,11],[-8,-6],[-2,-17],[-4,-14],[-7,-13],[-12,-12],[-54,-1],[-45,18],[-9,6],[-12,16],[-6,4],[-9,-2],[-2,25],[1,9],[8,9],[1,-1],[3,-2],[4,-2],[5,2],[2,3],[-1,5],[-2,4],[0,2],[15,18],[-9,8],[2,13],[9,21],[-10,12],[-2,7],[1,6],[6,12],[2,4],[-3,6],[-4,3],[-5,-1],[-7,-1],[-4,2],[-3,4],[-3,4],[-4,4],[-5,1],[-5,-2],[-3,2],[0,10],[3,5],[11,10],[4,6],[1,8],[-2,8],[-4,9],[5,6],[7,4],[6,4],[3,8],[-2,5],[-5,5],[-7,4],[-11,4],[-2,6],[3,28],[-1,9],[-6,4],[-15,-2],[-14,-7],[-11,-9],[-12,-6],[-16,1],[-12,2],[-13,-1],[-11,-5],[-4,-13],[-1,-26],[3,-9],[7,-5],[8,-4],[4,-6],[-2,-16],[-11,-9],[-15,-3],[-13,1],[-33,8],[-10,3],[-24,15],[-13,9],[-9,9],[-12,23],[-8,6],[-13,-5],[0,-2],[-8,-16],[-2,-1],[-2,-12],[-2,-13],[1,-5],[3,-5],[1,-5],[-3,-31],[-6,-22],[1,-11],[8,-13],[5,-3],[6,-2],[7,-3],[4,-5],[-1,-7],[-3,-6],[-4,-5],[-2,-5],[-3,-22],[0,-3],[-5,-8],[-14,-5],[-14,1],[-23,15],[-13,5],[-13,-2],[-36,-18],[-11,-2],[-35,5],[-13,-2],[-8,-9],[-14,-53],[1,-9],[7,-6],[8,-6],[5,-7],[2,-17],[-8,-15],[-12,-10],[-14,-5],[-15,-1],[-11,11],[-9,15],[-10,13],[-10,6],[-60,19],[-48,8],[-38,1],[-10,2],[-5,9],[-1,16],[4,27],[-4,11],[-13,1],[-17,-7],[-5,-2],[-8,1],[-1,4],[1,6],[0,8],[-11,18],[-2,7],[0,6],[0,7],[0,8],[-4,8],[-5,6],[-6,4],[-7,1],[-8,0],[-15,-20],[-5,-5],[-5,-2],[-11,0],[-6,-1],[-14,2],[-22,14],[-13,-2],[-3,-6],[-2,-8],[-3,-7],[-6,-2],[-7,1],[-5,-1],[-9,-8],[0,-4],[-2,-2],[-6,-3],[7,-10],[-7,-11],[-13,-8],[-12,-3],[8,-12],[-7,-13],[-2,-10],[-3,-5],[-4,-3],[-4,-4],[-2,-7],[-3,-5],[-14,-21],[-6,-5],[-5,-4],[-6,-2],[-16,-3],[-6,-4],[-11,-10],[-11,-6],[-13,-4],[-12,0],[-12,5],[13,32],[4,16],[-1,17],[-4,15],[-7,17],[-9,15],[-11,7],[-33,2],[-16,3],[-12,9],[-3,9],[-1,18],[-2,8],[-6,10],[-4,2],[-5,-4],[-9,-4],[-14,-3],[-14,0],[-11,7],[-15,31],[-18,28],[-7,17],[-9,134],[-3,12],[-16,6],[1,14],[13,38],[4,5],[4,6],[1,13],[-1,69],[-3,15],[-10,16],[-12,13],[-14,7],[-7,0],[-12,-7],[-6,-1],[-11,4],[-5,1],[-7,-1],[-12,-1],[-12,4],[-10,0],[-16,-36],[-6,-10],[-11,-8],[-10,-4],[-10,-1],[-11,0],[-11,2],[-12,-1],[-10,-6],[-10,-3],[-9,7],[-3,10],[3,12],[5,11],[5,9],[28,31],[6,13],[1,8],[-2,6],[1,7],[5,8],[5,2],[12,0],[6,3],[5,11],[3,24],[8,12],[4,3],[12,5],[6,4],[2,1],[6,0],[2,2],[1,5],[-1,4],[-2,3],[-1,3],[7,15],[5,5],[24,-6],[10,-2],[13,1],[11,4],[7,9],[3,18],[-6,3],[-11,-2],[-11,2],[-5,10],[7,9],[13,6],[11,3],[-14,13],[-18,9],[-37,12],[-16,1],[-8,3],[-4,10],[-3,12]],[[2257,886],[9,8],[22,32],[35,85],[21,11],[38,-1],[77,32],[31,2],[51,-14],[22,14],[44,47],[15,7],[46,37],[69,42],[65,31],[39,20],[19,17],[24,61],[15,35],[15,10],[61,33],[22,10],[9,18],[9,35],[21,9],[26,19],[24,44],[34,13],[7,-19],[10,-74],[23,-82],[1,-23],[-19,-48],[0,-37],[21,-22],[18,-9],[28,8],[12,-15],[1,-63],[31,6],[15,-6],[18,-25],[44,-12],[78,-40],[53,-1],[10,-12],[-4,-39],[27,-14],[18,2],[14,33],[9,27],[14,42],[7,28],[38,40],[-8,50],[0,32],[27,31],[30,3],[31,-13],[29,-30],[8,6],[-1,17],[-2,31],[17,9],[18,-1],[42,13],[22,-2],[22,61],[-3,59],[-18,9],[-54,-4],[-20,11],[-12,29],[-32,27],[-22,-4],[0,18],[-8,24],[-1,26],[-21,35]],[[3638,1625],[30,34],[126,21],[14,25],[6,1],[18,-13],[27,-2],[93,52],[99,59],[9,-5],[20,-50],[17,-14],[6,-37],[36,-36],[17,7],[18,43],[48,64],[17,14],[12,-4],[63,-31],[8,-26],[23,-32],[-2,-40],[20,-16],[74,13],[78,12],[23,7],[13,26],[38,53],[38,11],[10,-36],[17,1],[9,11],[53,1],[1,-1],[9,-2],[1,0]],[[6965,3309],[-7,-5],[-33,-35],[-125,-120],[-21,-15],[-24,-11],[-115,-30],[-26,-14],[-210,-147],[-42,-9],[-43,-2],[-50,4],[-29,-1],[-18,-9],[-5,-18],[-11,-165],[-2,-7],[-6,-5],[-29,-14],[-12,-6],[-9,-2],[-20,5],[-142,73],[-46,11],[-33,-9],[-49,-30],[-13,-13],[-35,-41],[-10,-13],[-89,-54],[-16,-28],[3,0],[2,-1],[1,-1],[1,-1],[-24,-88],[-11,-19],[-9,-26],[3,-36],[17,-63],[-22,-9],[-21,-9],[-4,-1],[-4,2],[-5,3],[-5,2],[-14,2],[-5,-1],[-7,-4],[-11,-13],[-7,-3],[-4,11],[-2,10],[-3,6],[-10,11],[-16,10],[-12,0],[-14,-4],[-17,2],[-27,23],[-16,6],[-12,-13],[13,-11],[4,-16],[-3,-37],[-3,-16],[-9,-25],[21,-168],[-8,-6],[-99,-6],[-14,-6],[-2,-19],[7,-23],[1,-13],[-6,-10],[-8,-2],[-7,1],[-26,4],[-13,0],[-4,1],[-6,3],[-9,11],[-4,2],[-8,-1],[-5,1],[-49,42],[-28,18],[-45,40],[-11,5],[-25,7],[-21,9],[-10,2],[-12,-1],[-12,0],[-14,4],[-7,7],[7,8],[6,3],[12,13],[5,2],[5,1],[4,2],[1,8],[-3,1],[-15,3],[-5,3],[-2,4],[-3,13],[-1,1]],[[5029,2254],[3,6],[0,4],[2,64],[-32,68],[-19,94],[-27,59],[-17,7],[-33,-10],[-69,-58],[-52,-98],[-11,-29],[-30,-4],[-46,-11],[-55,-34],[-26,20],[-14,16],[-9,4],[-31,-7],[-22,5],[-16,-4],[-35,-25],[-13,33],[-1,38],[13,57],[-16,31],[1,19],[16,16],[8,35],[-7,27],[-28,2],[-92,-17],[-45,7],[-50,22],[-4,31],[17,91],[-58,86],[-36,27],[-103,68],[-92,62],[1,18],[25,22],[5,47],[11,17],[-5,23],[-3,29],[21,54],[16,59],[26,2],[94,28],[46,-2],[39,34],[44,80],[22,36],[-34,108],[-17,77],[-11,11],[-20,5],[-66,3],[-8,36]],[[5029,2254],[-3,5],[-2,2],[-2,1],[-2,-1],[-2,-2],[-9,-11],[4,-2],[8,-1],[4,-4],[-3,-6],[-8,-8],[-9,-6],[-7,-3],[-7,-2],[-9,-12],[-6,-4],[-16,-4],[-5,-3],[-10,-10],[-6,-13],[-11,-27],[-9,-16],[-11,-15],[-7,-4],[-6,1],[-6,1],[-5,-1],[-10,-7],[-11,-11],[-8,-12],[-3,-10],[4,-6],[13,-8],[1,-6],[-4,-6],[-7,-2],[-39,2],[-10,-4],[-8,-23],[-3,-5],[-4,-6],[0,-9],[4,-6],[17,-17],[17,-39],[7,-11],[18,-17],[11,-15],[5,-19],[-2,-38],[8,-31],[-1,-8],[-13,-43],[-6,-9],[-1,0],[-7,-6],[-11,-4],[-13,-2],[-8,3],[-18,11],[-17,4],[-20,-1],[-21,-6],[-17,-7]],[[3638,1625],[-30,16],[-26,9],[-13,44],[-9,29],[-20,-2],[-28,-11],[-11,8],[-15,50],[-42,16],[-27,12],[-43,-6],[-68,-11],[-26,-13],[-18,1],[-8,15],[7,43],[-2,38],[-13,14],[-30,0],[-37,11],[-30,37],[-8,11],[7,23],[22,25],[7,39],[0,25],[20,41],[11,39],[27,27],[36,12],[56,4],[19,12],[16,46],[-7,12],[-22,17],[-7,20],[-4,67],[-36,45],[-35,39],[-9,45],[9,123],[90,179],[22,40],[47,66],[12,32],[21,7],[75,-23],[18,13],[13,25],[16,23],[0,44],[-13,40],[-20,81],[-30,44],[-36,66],[10,36],[56,79],[19,35],[41,140],[10,80]],[[3602,3604],[60,0],[81,0],[47,0],[44,0],[41,0],[38,0],[81,0],[95,0],[1,0],[1,0],[0,-1],[1,0],[0,1],[0,1],[1,3],[1,4]],[[2257,886],[-3,14],[-3,9],[-15,-14],[-17,2],[-33,16],[-7,10],[-6,12],[-5,14],[-2,12],[-14,49],[-8,6],[-9,-2],[-9,-5],[-13,-2],[-11,4],[-10,7],[-10,5],[-11,-1],[30,172],[0,8],[-4,6],[-4,7],[-3,7],[-1,14],[1,13],[-3,11],[-11,11],[-12,2],[-14,-1],[-14,0],[-11,10],[-2,13],[2,15],[9,26],[1,15],[-5,15],[-8,12],[-9,6],[-12,-6],[-7,0],[-4,8],[-3,7],[-5,10],[-2,7],[1,18],[6,14],[8,13],[5,15],[-1,15],[-6,15],[-11,13],[-21,11]],[[1892,3605],[75,0],[9,0],[56,0],[41,0],[45,0],[48,0],[52,0],[81,0],[87,0],[92,0],[95,0],[99,0],[102,-1],[103,0],[104,0],[103,0],[103,0],[102,0],[99,0],[95,0],[92,0],[27,0]]],
transform:{scale:[.0016501418208820899,.0014856496228622855],translate:[-12.2641304119999,10.14005401700011]}},m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();