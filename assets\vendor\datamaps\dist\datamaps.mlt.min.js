!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo={type:"Topology",objects:{mlt:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Attard"},id:"MT",arcs:[[0,1,2,3,4,5,6,7]]},{type:"Polygon",properties:{name:"Mdina"},id:"MT",arcs:[[8,9,-5]]},{type:"Polygon",properties:{name:"Imtarfa"},id:"MT",arcs:[[-10,10,-6]]},{type:"Polygon",properties:{name:"Rabat"},id:"MT",arcs:[[-7,-11,-9,11,12,13,14,15,16]]},{type:"Polygon",properties:{name:"Mġarr"},id:"MT",arcs:[[17,-16,18,19,20]]},{type:"Polygon",properties:{name:"Mellieħa"},id:"MT",arcs:[[21,-20,22]]},{type:"Polygon",properties:{name:"St. Paul's Bay"},id:"MT",arcs:[[23,24,-21,-22,25]]},{type:"Polygon",properties:{name:"Mosta"},id:"MT",arcs:[[26,27,-8,-17,-18,-25]]},{type:"Polygon",properties:{name:"Naxxar"},id:"MT",arcs:[[28,29,30,31,32,-27,-24,33]]},{type:"Polygon",properties:{name:"Lija"},id:"MT",arcs:[[34,35,-1,-28,-33]]},{type:"Polygon",properties:{name:"Balzan"},id:"MT",arcs:[[36,-2,-36,37]]},{type:"Polygon",properties:{name:"Iklin"},id:"MT",arcs:[[38,39,-38,-35,-32,40]]},{type:"Polygon",properties:{name:"Għargħur"},id:"MT",arcs:[[41,-41,-31,42]]},{type:"Polygon",properties:{name:"Swieqi"},id:"MT",arcs:[[43,44,-43,-30,45]]},{type:"Polygon",properties:{name:"Pembroke"},id:"MT",arcs:[[46,-46,-29,47]]},{type:"Polygon",properties:{name:"St. Julian's"},id:"MT",arcs:[[48,49,50,-44,-47,51]]},{type:"Polygon",properties:{name:"Sliema"},id:"MT",arcs:[[52,53,-49,54]]},{type:"Polygon",properties:{name:"San Ġwann"},id:"MT",arcs:[[-51,55,56,57,-39,-42,-45]]},{type:"Polygon",properties:{name:"Gżira"},id:"MT",arcs:[[58,59,60,-56,-50,-54]]},{type:"Polygon",properties:{name:"Birkirkara"},id:"MT",arcs:[[61,62,63,-37,-40,-58]]},{type:"Polygon",properties:{name:"Msida"},id:"MT",arcs:[[64,65,66,-62,-57,-61]]},{type:"Polygon",properties:{name:"Ta' Xbiex"},id:"MT",arcs:[[67,68,-65,-60]]},{type:"Polygon",properties:{name:"Pietà"},id:"MT",arcs:[[69,-66,-69,70]]},{type:"Polygon",properties:{name:"Santa Venera"},id:"MT",arcs:[[71,72,-63,-67]]},{type:"Polygon",properties:{name:"Ħamrun"},id:"MT",arcs:[[73,74,-72,-70,75]]},{type:"Polygon",properties:{name:"Qormi"},id:"MT",arcs:[[-73,-75,76,77,78,79,-3,-64]]},{type:"Polygon",properties:{name:"Żebbuġ"},id:"MT",arcs:[[80,-12,-4,-80]]},{type:"Polygon",properties:{name:"Dingli"},id:"MT",arcs:[[81,82,-14]]},{type:"Polygon",properties:{name:"Siġġiewi"},id:"MT",arcs:[[-79,83,84,85,86,-82,-13,-81]]},{type:"Polygon",properties:{name:"Valletta"},id:"MT",arcs:[[87,88,89,90,-59,-53,91]]},{type:"Polygon",properties:{name:"Kalkara"},id:"MT",arcs:[[92,93,94,-88,95]]},{type:"Polygon",properties:{name:"Birgu"},id:"MT",arcs:[[96,97,-89,-95]]},{type:"Polygon",properties:{name:"Xgħajra"},id:"MT",arcs:[[98,-93,99]]},{type:"Polygon",properties:{name:"Senglea"},id:"MT",arcs:[[100,101,102,-90,-98]]},{type:"Polygon",properties:{name:"Floriana"},id:"MT",arcs:[[-103,103,104,-76,-71,-68,-91]]},{type:"Polygon",properties:{name:"Cospicua"},id:"MT",arcs:[[105,106,107,-101,-97]]},{type:"Polygon",properties:{name:"Żabbar"},id:"MT",arcs:[[108,109,110,111,-106,-94,-99]]},{type:"Polygon",properties:{name:"Marsa"},id:"MT",arcs:[[112,113,-77,-74,-105]]},{type:"Polygon",properties:{name:"Paola"},id:"MT",arcs:[[-108,114,115,116,-113,-104,-102]]},{type:"Polygon",properties:{name:"Fgura"},id:"MT",arcs:[[117,118,-115,-107,-112]]},{type:"Polygon",properties:{name:"Tarxien"},id:"MT",arcs:[[119,120,121,-116,-119]]},{type:"Polygon",properties:{name:"Żejtun"},id:"MT",arcs:[[122,123,124,-120,-118,-111]]},{type:"Polygon",properties:{name:"Marsaskala"},id:"MT",arcs:[[125,-123,-110,126]]},{type:"Polygon",properties:{name:"Marsaxlokk"},id:"MT",arcs:[[-126,127,128,129,-124]]},{type:"Polygon",properties:{name:"Luqa"},id:"MT",arcs:[[-117,130,131,132,133,134,135,136,-84,-78,-114]]},{type:"Polygon",properties:{name:"Santa Luċija"},id:"MT",arcs:[[137,138,-131,-122]]},{type:"Polygon",properties:{name:"Gudja"},id:"MT",arcs:[[139,-132,-139]]},{type:"Polygon",properties:{name:"Għaxaq"},id:"MT",arcs:[[-130,140,-133,-140,-138,-121,-125]]},{type:"Polygon",properties:{name:"Birżebbuġa"},id:"MT",arcs:[[141,142,-141,-129]]},{type:"Polygon",properties:{name:"Mqabba"},id:"MT",arcs:[[143,144,145,-85,-137]]},{type:"Polygon",properties:{name:"Kirkop"},id:"MT",arcs:[[146,147,-144,-136]]},{type:"Polygon",properties:{name:"Safi"},id:"MT",arcs:[[148,-147,-135]]},{type:"Polygon",properties:{name:"Qrendi"},id:"MT",arcs:[[149,150,-86,-146]]},{type:"Polygon",properties:{name:"Żurrieq"},id:"MT",arcs:[[-149,-134,-143,151,-150,-145,-148]]},{type:"Polygon",properties:{name:"Saint Lawrence"},id:"-99",arcs:[[152,153,154]]},{type:"Polygon",properties:{name:"Għarb"},id:"-99",arcs:[[155,-155,156,157]]},{type:"Polygon",properties:{name:"Għasri"},id:"-99",arcs:[[158,159,-158,160,161]]},{type:"Polygon",properties:{name:"Żebbuġ"},id:"-99",arcs:[[162,163,-162,164]]},{type:"Polygon",properties:{name:"Xagħra"},id:"-99",arcs:[[165,166,167,-163,168]]},{type:"Polygon",properties:{name:"Nadur"},id:"-99",arcs:[[169,170,-166,171]]},{type:"Polygon",properties:{name:"Qala"},id:"-99",arcs:[[172,-170,173]]},{type:"Polygon",properties:{name:"Kerċem"},id:"-99",arcs:[[-160,174,175,176,177,-153,-156]]},{type:"Polygon",properties:{name:"Victoria"},id:"-99",arcs:[[-164,-168,178,179,-175,-159]]},{type:"Polygon",properties:{name:"Fontana, Gozo"},id:"-99",arcs:[[180,181,-176,-180]]},{type:"Polygon",properties:{name:"Munxar"},id:"-99",arcs:[[-182,182,183,184,-177]]},{type:"Polygon",properties:{name:"Sannat"},id:"-99",arcs:[[185,186,-184,187]]},{type:"Polygon",properties:{name:"Xewkija"},id:"-99",arcs:[[188,-188,-183,-181,-179,-167]]},{type:"MultiPolygon",properties:{name:"Għajnsielem"},id:"-99",arcs:[[[189]],[[-173,190,-186,-189,-171]]]}]}},arcs:[[[6561,3424],[116,-21],[162,10]],[[6839,3413],[69,-118],[93,-65],[146,-32]],[[7147,3198],[-23,-119],[-61,-97]],[[7063,2982],[-293,-22],[-162,-10],[-85,-97],[-131,-33],[-139,22],[-131,43],[-147,-32]],[[5975,2853],[-46,172]],[[5929,3025],[8,119],[7,140]],[[5944,3284],[23,97],[16,129]],[[5983,3510],[85,54],[162,22],[146,-76],[93,-75],[92,-11]],[[5975,2853],[-77,10],[-131,0],[-62,65]],[[5705,2928],[39,65],[92,32],[93,0]],[[5705,2928],[-162,65],[-15,151],[77,97],[131,11],[116,0],[92,32]],[[5975,2853],[-8,-151],[47,-43],[54,-378]],[[6068,2281],[-93,-140],[-162,-32],[-208,43]],[[5605,2152],[-70,108],[-15,97],[-62,75],[-138,0],[-270,-43],[-186,11],[-140,-97]],[[4724,2303],[-46,83],[-83,110],[-369,227],[-80,173],[0,711]],[[4146,3607],[117,-75],[100,32],[108,-129],[93,-11],[61,54],[77,97],[101,0],[85,54],[100,-54],[85,11],[77,108],[162,21],[185,33],[139,0],[115,43]],[[5751,3791],[78,-108],[69,-76],[85,-97]],[[5728,3899],[23,-108]],[[4146,3607],[0,419],[-46,506]],[[4100,4532],[163,35],[170,65],[208,108],[92,86],[170,21],[54,76]],[[4957,4923],[147,-43],[46,-65],[-39,-108],[-69,-75],[8,-108],[84,-22],[193,-21],[62,-97],[31,-194],[15,-141],[93,-86],[115,-54],[85,-10]],[[5793,6073],[-157,-460],[-262,-86],[-170,-130],[-208,-75],[-78,-65],[-15,-162],[54,-172]],[[4100,4532],[-69,780],[-149,578],[-273,375],[331,337],[333,231],[365,135],[411,45],[0,-270],[-237,-51],[-153,-89],[-337,-338],[1471,-192]],[[6638,5880],[-200,-418],[-162,-302],[-85,-173],[-23,-150],[54,-141],[0,-107],[-39,-54],[39,-65],[92,-129]],[[6314,4341],[-77,-108],[-69,-65],[-69,65],[-31,118],[-70,65],[-100,86],[-139,33],[-208,-43],[-54,-54],[31,-44],[100,11],[77,-32],[31,-108],[93,-65],[92,-43],[23,-75],[-54,-54],[-100,-65],[-62,-64]],[[5793,6073],[591,-78],[254,-115]],[[6314,4341],[108,-22],[54,-97],[24,-97],[15,-108],[8,-108],[31,-97]],[[6554,3812],[-47,-64],[0,-119],[54,-205]],[[7333,5297],[-47,-396],[-15,-237]],[[7271,4664],[-193,-11]],[[7078,4653],[-116,-54],[-61,-64],[-31,-205],[8,-140],[100,-119]],[[6978,4071],[-77,-129],[46,-130],[-93,-54]],[[6854,3758],[-154,-32],[-146,86]],[[6638,5880],[179,-80],[516,-503]],[[6854,3758],[54,-118]],[[6908,3640],[-69,-227]],[[7063,3564],[61,-108],[39,-129],[-16,-129]],[[6908,3640],[155,-76]],[[7263,3942],[8,-302]],[[7271,3640],[-100,-43],[-108,-33]],[[6978,4071],[100,-11],[85,-32],[100,-86]],[[7302,4028],[-39,-86]],[[7078,4653],[0,-108],[116,-53],[23,-173],[62,-119],[23,-172]],[[7834,4330],[46,-65],[-7,-129],[-93,-87]],[[7780,4049],[-54,65],[-69,-75],[-132,-54],[-154,11],[-69,32]],[[7271,4664],[123,-65],[93,-97],[31,-161],[100,-33],[216,22]],[[8286,4707],[-259,-237],[-185,-43],[-8,-97]],[[7333,5297],[360,-350],[593,-240]],[[8519,4496],[-367,-356],[-89,-299]],[[8063,3841],[-113,-70]],[[7950,3771],[-77,97],[-78,60],[-15,121]],[[8286,4707],[166,-67],[67,-144]],[[8611,3630],[-175,-77]],[[8436,3553],[7,120],[-264,65],[-116,103]],[[8519,4496],[65,-138],[30,-363],[-3,-365]],[[7950,3771],[-70,-158]],[[7880,3613],[-260,-16],[-97,-38]],[[7523,3559],[-252,81]],[[8436,3553],[-147,-129]],[[8289,3424],[-98,75],[-225,6]],[[7966,3505],[-86,108]],[[7523,3559],[93,-87],[28,-87],[-70,-87]],[[7574,3298],[-51,-32],[-15,-92],[-37,-149]],[[7471,3025],[-169,22],[-155,151]],[[7966,3505],[7,-199]],[[7973,3306],[-7,-111],[-66,-92]],[[7900,3103],[-113,125],[-143,49],[-70,21]],[[8289,3424],[-93,-97]],[[8196,3327],[-123,-32],[-100,11]],[[8148,3174],[-136,-87],[-112,16]],[[8196,3327],[-48,-153]],[[7900,3103],[-155,-71],[-42,-93]],[[7703,2939],[-139,32],[-93,54]],[[8160,3032],[-118,-82],[-54,-237],[-154,10]],[[7834,2723],[-131,216]],[[8148,3174],[51,-82],[-39,-60]],[[7834,2723],[-85,-54],[-15,-86]],[[7734,2583],[-85,-108],[-185,-323]],[[7464,2152],[-85,0],[-85,86]],[[7294,2238],[-15,119],[0,118],[-78,76],[-131,21],[-61,76],[-23,151],[46,86],[31,97]],[[7294,2238],[-69,-43],[-47,32],[-84,87],[-108,21],[-155,-32],[-38,-86],[-77,-87],[-162,-64],[-162,86],[-155,75],[-77,65],[-92,-11]],[[5605,2152],[-16,-119],[-108,-215],[-92,-97],[-30,-325]],[[5359,1396],[-199,125],[-436,782]],[[7464,2152],[15,-140],[-123,-119],[-93,-119],[-108,-21],[-23,-76]],[[7132,1677],[-77,-43],[-31,-107],[-62,-141]],[[6962,1386],[-92,-32],[-77,-22],[-93,-97],[23,-107],[-23,-65],[-116,0],[-100,-76],[-91,-209]],[[6393,778],[-113,41],[-921,577]],[[8897,3528],[-141,-137]],[[8756,3391],[-107,-114]],[[8649,3277],[-121,-79]],[[8528,3198],[-239,226]],[[8611,3630],[141,-31],[145,-71]],[[9500,3237],[-177,-147],[-31,-119]],[[9292,2971],[-178,0],[-100,-43]],[[9014,2928],[-9,270],[-110,54],[-139,139]],[[8897,3528],[603,-291]],[[9014,2928],[-211,202]],[[8803,3130],[-154,147]],[[9713,2844],[-205,-99],[-124,65],[-92,161]],[[9500,3237],[213,-393]],[[8803,3130],[-198,-245]],[[8605,2885],[-139,183]],[[8466,3068],[62,130]],[[8466,3068],[-100,-108],[-100,-97]],[[8266,2863],[-54,130],[-52,39]],[[9014,2928],[-62,-140],[-7,-119]],[[8945,2669],[-101,-10],[-146,-11],[-93,0]],[[8605,2648],[-77,75],[77,162]],[[9713,2844],[123,-227]],[[9836,2617],[-220,-152],[-31,-108],[-70,-97],[-77,-11],[-69,-76]],[[9369,2173],[-208,22],[-78,97],[-115,11],[-62,43]],[[8906,2346],[31,119],[46,86],[23,86],[-61,32]],[[8266,2863],[-8,-226],[-69,-226],[-116,-162]],[[8073,2249],[-100,0],[-108,97],[-85,21],[0,98],[-46,118]],[[8605,2648],[-139,-173]],[[8466,2475],[-92,-108],[-39,-118],[-85,-54],[-138,-108]],[[8112,2087],[-39,162]],[[8906,2346],[-93,-32],[-61,-22],[-23,-97],[-54,-43]],[[8675,2152],[-39,108],[23,86],[-69,86],[-124,43]],[[8675,2152],[-101,-76],[-38,-54]],[[8536,2022],[-31,-64]],[[8505,1958],[-185,43],[-124,21],[-84,65]],[[9369,2173],[85,-194],[38,-118],[-31,-87],[8,-75],[39,-75]],[[9508,1624],[-85,-108],[-154,97],[-155,21],[-61,-64],[-162,10]],[[8891,1580],[-124,238],[-108,161],[-123,43]],[[9964,1475],[-171,52],[-147,21],[-138,76]],[[9836,2617],[59,-109],[104,-890],[-35,-143]],[[9964,1475],[-105,-433],[-359,226],[-202,-256]],[[9298,1012],[-68,180],[-123,162],[-108,54],[-93,0]],[[8906,1408],[-15,172]],[[8112,2087],[0,-151],[-8,-129]],[[8104,1807],[46,-140],[0,-76],[8,-140],[62,-65],[115,-97]],[[8335,1289],[85,-118],[62,-130]],[[8482,1041],[-124,-21]],[[8358,1020],[-146,161],[-93,141],[-123,75]],[[7996,1397],[-23,130],[-54,32],[-77,118],[-70,44],[-92,-76]],[[7680,1645],[-139,43],[-100,65],[-116,-97],[-93,-43],[-100,64]],[[8505,1958],[15,-119],[-15,-54]],[[8505,1785],[-185,11],[-216,11]],[[8505,1785],[-15,-118],[-54,-43],[-31,-87],[-8,-118],[0,-65],[-62,-65]],[[8906,1408],[-39,-108],[-123,-32],[-69,-33],[-23,-97],[-62,-86],[-108,-11]],[[9298,1012],[-203,-256],[-129,-307],[0,-449],[-418,191],[-115,80],[-247,28]],[[8186,299],[41,85],[31,64],[-54,140],[16,87],[69,0],[147,-54],[115,21],[23,65],[-54,183],[-38,151]],[[7680,1645],[38,-97],[-38,-65],[-47,-129]],[[7633,1354],[-100,-11],[-146,-11]],[[7387,1332],[-155,-32],[-123,54],[-147,32]],[[7996,1397],[-177,-194]],[[7819,1203],[-70,97],[-116,54]],[[8358,1020],[-92,-97],[-70,32],[-92,54],[-100,-76],[-116,-64],[-85,32],[-85,97],[-7,76],[108,129]],[[7387,1332],[30,-107],[-77,-44],[-7,-75],[54,-54],[-62,-65],[-77,-75],[-154,-205],[-56,-158]],[[7038,549],[-645,229]],[[8186,299],[-650,73],[-498,177]],[[735,9136],[-44,-164],[-86,-95],[0,-138],[-173,-207],[-139,-216]],[[293,8316],[-259,190],[81,393],[-96,397],[-19,309]],[[0,9605],[204,-193],[278,-138],[111,52],[49,-130],[93,-60]],[[920,9119],[-185,17]],[[0,9605],[310,121],[454,78]],[[764,9804],[-11,-142],[93,-129],[55,-173],[19,-241]],[[1457,9041],[-130,9],[-117,-18],[-37,-51]],[[1173,8981],[-173,34],[-80,104]],[[764,9804],[537,92]],[[1301,9896],[-79,-148],[-6,-224],[25,-190],[55,-26],[86,35],[62,-95],[13,-207]],[[2304,9832],[-107,-118],[12,-95],[6,-121],[-24,-69],[-155,-9],[-123,-69],[-92,-120],[-13,-121],[37,-121],[-31,-69],[-68,-8]],[[1746,8912],[-141,0],[-148,129]],[[1301,9896],[602,103],[401,-167]],[[2750,9594],[-78,-251],[0,-405],[-80,-44],[-19,-163],[-37,-104],[-6,-164]],[[2530,8463],[-142,121],[-99,52],[-117,-26],[-148,-35],[-160,35]],[[1864,8610],[12,95],[-55,86],[-75,121]],[[2304,9832],[276,-115],[170,-123]],[[3567,8930],[-124,-96],[-92,-69],[-93,-26],[-18,-86],[-7,-173],[-61,-94],[-216,-35]],[[2956,8351],[-204,0],[-111,26],[-111,86]],[[2750,9594],[670,-488],[147,-176]],[[3038,8155],[-21,110],[-61,86]],[[3567,8930],[360,-433],[-318,-263],[-458,-20],[-113,-59]],[[1173,8981],[74,-112],[37,-130],[68,-43],[6,-69]],[[1358,8627],[-6,-224]],[[1352,8403],[-130,43],[-105,17],[-99,-52],[-166,-129],[-172,-196]],[[680,8086],[-222,108],[-165,122]],[[1864,8610],[-25,-181],[-93,-9],[-86,9],[-68,-69]],[[1592,8360],[-6,120],[-92,61],[-50,86],[-86,0]],[[1592,8360],[-92,-78]],[[1500,8282],[-99,35],[-49,86]],[[1500,8282],[-19,-69]],[[1481,8213],[-68,-138],[-105,-138],[-20,-91]],[[1288,7846],[-316,98],[-292,142]],[[2296,7946],[51,-120]],[[2347,7826],[-355,-90],[-484,42],[-220,68]],[[1481,8213],[93,-26],[197,0],[117,-34],[87,-95],[123,-43],[117,-78],[81,9]],[[2530,8463],[-49,-121],[-56,-69],[-12,-163],[-105,-121],[-12,-43]],[[4029,7410],[-265,-46],[-17,22],[-70,-16],[32,107],[-70,192],[-37,195],[96,77],[184,-9],[289,-120],[182,-221],[-324,-181]],[[3038,8155],[-588,-303],[-103,-26]]],transform:{scale:[3835832802526496e-20,27440081208119336e-21],translate:[14.183604363075489,35.801214911000045]}},m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",
m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();