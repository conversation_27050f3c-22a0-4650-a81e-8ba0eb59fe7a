!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo={type:"Topology",objects:{ury:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Salto"},id:"UY.SA",arcs:[[0,1,2,3,4,5]]},{type:"Polygon",properties:{name:"Soriano"},id:"UY.SO",arcs:[[6,7,8,9]]},{type:"Polygon",properties:{name:"Cerro Largo"},id:"UY.CL",arcs:[[10,11,12,13,14]]},{type:"Polygon",properties:{name:"Durazno"},id:"UY.DU",arcs:[[-12,15,16,17,18,19]]},{type:"Polygon",properties:{name:"Rivera"},id:"UY.RV",arcs:[[-14,20,-2,21]]},{type:"Polygon",properties:{name:"Tacuarembó"},id:"UY.TA",arcs:[[-13,-20,22,23,-3,-21]]},{type:"Polygon",properties:{name:"Treinta y Tres"},id:"UY.TT",arcs:[[24,25,26,27,-16,-11]]},{type:"Polygon",properties:{name:"Canelones"},id:"UY.CA",arcs:[[28,29,30,31,32,33]]},{type:"Polygon",properties:{name:"Florida"},id:"UY.FD",arcs:[[34,-33,35,36,-17,-28]]},{type:"Polygon",properties:{name:"Lavalleja"},id:"UY.LA",arcs:[[37,38,-34,-35,-27]]},{type:"Polygon",properties:{name:"Maldonado"},id:"UY.MA",arcs:[[39,-29,-39,40]]},{type:"Polygon",properties:{name:"Montevideo"},id:"UY.MO",arcs:[[41,42,-31]]},{type:"Polygon",properties:{name:"Rocha"},id:"UY.RO",arcs:[[-41,-38,-26,43]]},{type:"Polygon",properties:{name:"Colonia"},id:"UY.CO",arcs:[[44,45,-8]]},{type:"Polygon",properties:{name:"San José"},id:"UY.SJ",arcs:[[-36,-32,-43,46,-45,47]]},{type:"Polygon",properties:{name:"Artigas"},id:"UY.AR",arcs:[[-6,48]]},{type:"Polygon",properties:{name:"Flores"},id:"UY.FS",arcs:[[-37,-48,-7,-18]]},{type:"Polygon",properties:{name:"Paysandú"},id:"UY.PA",arcs:[[-24,49,50,-4]]},{type:"Polygon",properties:{name:"Río Negro"},id:"UY.RN",arcs:[[-23,-19,-10,51,-50]]}]}},arcs:[[[4546,7995],[10,-16],[3,0]],[[4559,7979],[-50,-161],[-13,-30],[-23,-42],[-19,-18],[-24,-13],[-130,-36],[-25,-11],[-27,-17],[-13,-16],[-8,-15],[-2,-12],[-1,-12],[5,-12],[6,-11],[30,-30],[16,-20]],[[4281,7523],[-11,-39],[-17,-35],[-14,-78],[-6,-20],[-7,-14],[-8,-6],[-23,-22],[-19,-24],[-12,-13],[-11,-8],[-10,-5],[-35,-12],[-14,-8],[-9,-13],[-32,-62],[-9,-12],[-61,-55],[-9,-15],[-55,-143],[-6,-21],[4,-10],[5,-9],[13,-16],[11,-18],[7,-20],[4,-23],[6,-87],[4,-23],[7,-21],[3,-28],[-3,-56]],[[3974,6607],[-99,-24],[-28,-20],[-4,-12],[-10,-17],[-16,-21],[-58,-57],[-12,-15],[-5,-23],[-3,-10],[-10,-18],[-9,-9],[-10,-7],[-25,-8],[-31,-5],[-20,0],[-12,1],[-133,38],[-97,3],[-13,2],[-24,7],[-95,56],[-15,4],[-21,3],[-79,1],[-12,2],[-12,4],[-37,23],[-13,6],[-18,5],[-32,4],[-22,0],[-163,-32],[-21,-1],[-39,8],[-82,30],[-15,0],[-11,-4],[-8,-7],[-12,-7],[-14,-8],[-26,-8],[-18,-3],[-17,0],[-24,7],[-159,18],[-21,5],[-70,50],[-20,10],[-149,40],[-42,19],[-26,20],[-14,14],[-41,60],[-9,8],[-19,8],[-35,7],[-53,2],[-45,6],[-45,16],[-20,11],[-63,42],[-134,64],[-276,57],[-14,4],[-9,6],[-9,6],[-7,8],[-5,9],[-4,9],[-24,72],[-6,9],[-6,8],[-17,13],[-124,66],[-49,18],[-20,3],[-14,-1],[-70,-16],[-8,-5],[-7,-8],[-2,-11],[-2,-19],[0,-2],[-2,-1],[-10,-1],[-21,-1],[-32,8],[-30,3],[-28,-7],[-31,-15],[-6,-3]],[[751,7104],[-22,16],[-46,53],[24,63],[36,28],[70,40],[30,24],[18,39],[9,93],[18,42],[13,14],[30,23],[14,13],[39,78],[19,23],[0,53],[-13,91],[24,91],[47,68],[35,70],[-15,96],[-79,110],[-12,23],[15,36],[35,23],[79,19],[44,-5],[23,9],[10,31],[11,49],[2,21],[-2,86],[-6,51]],[[1201,8575],[45,63],[10,8],[13,8],[18,1],[52,-5],[20,-7],[26,-14],[122,-86],[31,-27],[14,-15],[31,-41],[10,-9],[12,-10],[22,-14],[18,-5],[16,-3],[67,0],[38,-5],[20,-5],[16,-6],[30,-23],[12,-10],[61,-41],[249,-125],[89,-27],[15,-8],[28,-22],[1,-1],[86,-81],[36,-23],[45,-23],[113,-32],[19,-1],[12,2],[11,3],[10,4],[51,36],[18,10],[12,4],[124,15],[45,-3],[213,-36],[27,-1],[44,11],[90,9],[24,5],[20,8],[17,8],[110,87],[18,10],[21,7],[63,8],[22,6],[9,6],[8,6],[27,31],[8,7],[19,11],[42,16],[9,4],[16,13],[6,8],[12,19],[13,31],[11,19],[7,8],[7,7],[13,6],[19,5],[34,3],[18,-3],[15,-3],[105,-36],[298,-62],[12,-4],[31,-18],[38,-31],[14,-16],[28,-43],[21,-24],[48,-38],[14,-15],[19,-24],[15,-15],[70,-51],[2,-1]],[[2462,3794],[-25,-65],[-12,-19],[-134,-156],[-25,-20],[-197,-215],[-20,-33],[-1,-19],[2,-13],[4,-11],[6,-9],[7,-7],[48,-30],[9,-6],[7,-8],[6,-9],[4,-9],[21,-88],[4,-10],[5,-9],[31,-36],[0,-1],[0,-1],[3,-5],[4,-26],[1,-25],[2,-15],[4,-13],[6,-8],[7,-8],[8,-7],[39,-48],[16,-14],[19,-12],[11,-4],[52,-13],[11,-6],[9,-7],[6,-14],[0,-12],[-3,-11],[-4,-10],[-6,-9],[-4,-11],[-3,-11],[-28,-201],[-1,-26],[2,-7],[3,-7],[79,-123],[22,-48],[3,-12],[5,-30],[8,-24],[5,-12],[4,-14],[9,-52],[6,-21],[5,-9],[5,-9],[33,-41],[15,-23]],[[2555,2072],[-196,28],[-155,0],[-53,6],[-10,5],[-35,25],[-32,28],[-36,19],[-40,17],[-35,9],[-323,-80],[-114,-10],[-53,4],[-256,80],[-18,12],[-9,8],[-12,14],[2,-2],[-3,3],[-3,6],[-11,9],[-13,9],[-26,12],[-18,5],[-147,4],[-56,-9],[-34,-1],[-19,2],[-15,4],[-8,4],[-5,3],[-7,6],[-6,7],[-11,17],[-5,10],[-7,21],[-9,19],[-5,8],[-12,14],[-10,10],[-211,50],[-33,3],[-30,-8],[-175,-111],[-169,-64],[-26,-7],[-60,-10],[-1,0]],[[45,2251],[-36,186],[-9,177],[26,176],[-7,42],[-11,41],[-6,43],[10,48],[25,40],[64,78],[13,44],[-12,40],[-12,10]],[[90,3176],[179,97],[101,122],[35,20],[90,0],[53,-9],[21,9],[9,35],[1,78],[5,20],[20,19],[64,31],[22,-13],[32,-28],[10,-1],[19,-15],[15,17],[8,34],[-3,34],[-5,3],[-24,21],[-11,12],[0,16],[1,18],[0,14],[-21,41],[1,14],[20,15],[30,8],[27,-6],[27,-10],[32,-3],[42,9],[8,17],[-4,25],[7,31],[18,17],[56,13],[28,11],[27,22],[19,23],[22,18],[35,9],[32,-4],[53,-29],[29,-10],[23,1],[39,11],[22,2],[21,-4],[27,-19],[57,-13],[71,-27],[37,7],[14,16],[18,45],[13,9],[63,16],[14,6],[36,23],[40,8],[42,-5],[82,-40],[18,-13],[7,-16],[0,-23],[3,-25],[9,-20],[20,-9],[22,6],[39,26],[22,9],[21,1],[19,-3],[77,-23],[33,-16],[22,-25],[21,-81],[29,7],[30,31],[19,26],[12,-54],[10,-22],[22,-9],[21,3],[84,35],[47,26],[16,6]],[[9864,4457],[-1,0],[-226,43],[-47,6],[-15,-2],[-19,-3],[-31,-17],[-15,-5],[-16,-1],[-25,3],[-15,4],[-12,6],[-24,20],[-15,4],[-20,2],[-36,-3],[-19,3],[-12,6],[-13,16],[-8,7],[-8,6],[-9,4],[-9,3],[-202,-23],[-45,2],[-67,11],[-313,-23],[-19,2],[-15,3],[-11,5],[-19,11],[-119,89],[-19,11],[-12,4],[-17,-1],[-21,-4],[-52,-28],[-10,-7],[-12,-14],[-41,-33],[-7,-8],[-5,-9],[-1,-10],[2,-10],[4,-10],[11,-17],[5,-10],[3,-11],[-5,-11],[-11,-8],[-26,-1],[-15,3],[-12,6],[-9,6],[-101,64],[-10,5],[-12,3],[-12,0],[-40,-3],[-14,1],[-14,2],[-12,3],[-10,5],[-18,12],[-11,5],[-52,5],[-39,13],[-71,8],[-12,4],[-10,4],[-9,6],[-16,14],[-9,6],[-18,-1],[-28,-11],[-93,-48],[-39,-12],[-105,-6],[-27,4],[-14,-2],[-12,-6],[-11,-16],[-9,-27],[-5,-10],[-10,-8],[-14,-7],[-27,-4],[-16,1],[-14,4],[-12,0],[-11,-5],[-9,-12],[-13,-21],[-13,-16],[-11,-9],[-15,-6],[-29,-5],[-18,1],[-15,2],[-35,13],[-25,5],[-25,1],[-17,-3],[-87,-33],[-30,-6],[-61,1],[-53,-5],[-24,-6],[-110,-52],[-32,-25],[-29,-30],[-16,-13],[-10,-5],[-64,-24],[-22,-15],[-15,-14],[-12,-8],[-41,-23],[-10,-7],[-12,-13],[-10,-14],[-6,-13],[-2,-15],[4,-17],[28,-44],[3,-14],[-1,-37]],[[6435,3995],[-96,13],[-135,-5],[-30,2],[-19,5],[-8,7],[-21,29],[0,1],[-5,5],[-179,105],[-25,20],[-35,38],[-21,33],[-9,22],[-4,22],[0,13],[11,48],[3,39],[-1,12],[-3,11],[-8,13],[-31,44],[-9,17],[-3,15],[3,11],[5,9],[9,7],[9,4],[25,5],[10,4],[7,7],[3,11],[0,12],[-2,13],[-48,104],[-4,12],[1,24],[6,23],[26,64],[27,108],[1,26],[0,24],[-9,45],[-38,67]],[[5838,5084],[166,72],[65,4],[122,-45],[64,-13],[66,9],[58,25],[49,35],[40,42],[38,60],[29,27],[67,18],[15,19],[13,23],[17,21],[19,12],[40,18],[18,12],[31,37],[40,84],[26,42],[8,22],[-8,23],[-12,23],[-7,22],[3,20],[7,20],[16,30],[43,46],[15,27],[6,47],[20,22],[47,-15],[75,-41],[28,2],[40,11]],[[7102,5845],[40,10],[39,5],[43,12],[28,30],[6,37],[-19,33],[19,24],[47,35],[12,26],[-7,34],[-23,9],[-31,4],[-30,23],[64,85],[23,60],[26,44],[46,104],[9,36],[-9,50],[-33,91],[2,49],[10,20],[17,23],[18,19],[13,8],[6,8],[26,49],[9,11],[1,1]],[[7454,6785],[6,-15],[51,-46],[62,-42],[57,-50],[57,-62],[129,-111],[104,-68],[92,-81],[44,-28],[21,1],[18,22],[34,30],[36,12],[36,-4],[36,-15],[32,-22],[11,-14],[17,-30],[17,-15],[19,-6],[61,-6],[33,-9],[27,-11],[53,-35],[22,-22],[8,-21],[5,-21],[11,-22],[19,-21],[25,-20],[52,-31],[110,-22],[27,-16],[11,-28],[3,-73],[53,-119],[96,-131],[22,-58],[13,-69],[6,-104],[9,-34],[17,-27],[48,-39],[19,-25],[32,-53],[39,-49],[44,-42],[118,-78],[111,-115],[106,-33],[81,-46],[32,-9],[60,-1],[62,-35],[61,-26],[29,-19],[124,-125],[17,-30],[-6,-38],[-24,-29],[-68,-49],[-37,-43]],[[6435,3995],[-17,-32],[-6,-8],[-14,-14],[-16,-13],[-105,-62],[-17,-19],[-14,-43]],[[6246,3804],[-96,-48],[-31,-20],[-104,-91],[-25,-16],[-21,-9],[-13,1],[-12,3],[-31,15],[-13,4],[-16,1],[-42,-19],[-42,-26],[-103,-48],[-15,-15],[-38,-44],[-358,-186],[-42,-16],[-14,0],[-70,11],[-17,1],[-23,-3],[-13,-6],[-9,-8],[-7,-8],[-12,-13],[-18,-14],[-49,-30],[-115,-48],[-63,-18],[-14,-2],[-27,2],[-13,2],[-11,4],[-31,16],[-12,3],[-14,1],[-13,0],[-99,-18],[-63,-5],[-18,-6],[-10,-8],[-4,-11],[-5,-24],[-6,-12],[-11,-12],[-39,-26],[-18,-15],[-14,-6],[-14,-1],[-91,26],[-53,5],[-106,-8],[-13,3],[-12,3],[-11,5],[-8,7],[-7,8],[-5,9],[-14,42],[-4,9],[-6,9],[-7,7],[-9,6],[-13,3],[-14,0],[-88,-9],[-14,1],[-12,3],[-22,9],[-29,17],[-8,7],[-16,15],[-11,7],[-19,3],[-10,-5],[-6,-10],[-1,-12],[1,-12],[6,-49],[2,-51],[-5,-14],[-10,-14],[-25,-18],[-17,-7],[-15,-4],[-149,3]],[[3577,3030],[-35,39],[-13,24],[-2,11],[-3,50],[-2,11],[-6,19],[-6,12],[-10,14],[-12,13],[-9,11],[-116,117],[-37,63],[-7,7],[-20,12],[-104,37],[-62,14],[-73,3],[-17,5],[-17,9],[-23,19],[-10,14],[-5,14],[-6,35],[-7,20],[-11,18],[-14,15],[-8,7],[-22,8],[-108,24],[-63,7],[-101,-14],[-20,5],[-77,40],[-89,81]],[[2462,3794],[0,12],[-10,62],[5,24],[-41,15],[-18,-8],[-20,2],[-13,12],[1,22],[9,9],[33,5],[15,8],[24,24],[32,25],[34,19],[32,8],[35,-6],[76,-30],[37,-7],[43,0],[23,4],[0,14],[-27,32],[-3,19],[31,8],[42,2],[26,5],[-2,9],[-7,16],[0,18],[22,14],[19,0],[16,-10],[13,-12],[10,-6],[31,-25],[18,-6],[8,24],[-9,38],[-1,20],[10,19],[32,8],[36,-5],[24,7],[-1,32],[0,14],[21,45],[16,16],[26,22],[33,13],[55,2],[37,16],[21,25],[34,10],[36,-10]],[[3326,4368],[56,-31],[34,8],[36,25],[32,13],[45,8],[37,1],[33,-1],[48,6],[47,-4],[39,-21],[41,-37],[40,-4],[20,37],[13,20],[52,38],[61,-5],[63,-12],[72,-3],[61,3],[11,61],[33,53],[35,43],[24,42],[83,12],[78,27],[66,3],[66,12],[51,54],[53,49],[26,38],[32,34],[45,22],[40,2],[46,-48],[51,-16],[40,26],[30,27],[10,35],[9,24],[18,19],[-1,28],[-15,38],[4,25],[22,11],[36,-4],[41,-32],[30,-20],[35,2],[30,14],[25,59],[24,29],[30,3],[40,-16],[43,-10],[48,-5],[42,4],[39,11],[25,27],[8,34],[10,30],[4,29],[17,22],[30,14],[32,-9],[17,-14],[3,-33],[3,-11],[8,-11],[14,-12],[11,-2],[11,2],[14,-2],[66,-35],[7,-10],[9,-7],[15,-3],[58,10]],[[7102,5845],[-28,28],[-12,19],[-23,48],[-24,33],[-69,64],[-31,41],[-78,83],[-32,54],[-28,29],[-52,39],[-7,7],[-37,49],[-7,8],[-8,6],[-43,20],[-81,28],[-23,5],[-144,12],[-38,12],[-71,30],[-29,6],[-59,2],[-77,-8],[-116,8],[-183,43],[-55,6],[-27,-2],[-47,-11],[-164,-10],[-52,6],[-52,11],[-14,0],[-12,-2],[-31,-12],[-12,-2],[-13,-1],[-13,2],[-15,7],[-14,13],[-20,28],[-18,30],[-7,20],[-1,12],[4,37],[-2,10],[-4,10],[-11,17],[-5,9],[-2,11],[-1,13],[2,27],[6,24],[16,44],[2,12],[0,12],[-2,11],[-8,20],[-17,67],[-8,20],[-11,18],[-12,9],[-17,11],[-36,14],[-62,19],[-13,6],[-14,9],[-62,55],[-8,9],[-6,8],[-6,13],[-1,1],[-3,8],[-29,79],[-1,12],[3,39],[-2,25],[-3,11],[-8,20],[-19,37],[-8,10],[-9,9],[-32,23],[-136,53],[-89,48],[-154,114],[-10,5],[-27,7],[-36,0],[-13,-2],[-31,-12],[-24,-21],[-45,-64]],[[4559,7979],[46,7],[124,-8],[58,11],[45,1],[18,4],[15,11],[9,11],[7,12],[11,13],[102,64],[27,25],[15,32],[20,70],[25,27],[36,4],[45,-9],[43,-3],[24,21],[3,39],[-6,85],[9,36],[29,28],[40,8],[39,-10],[53,-57],[68,-38],[31,-23],[41,-49],[87,-73],[139,-147],[28,-39],[10,-34],[7,-73],[13,-35],[20,-19],[63,-39],[21,-31],[26,-86],[16,-37],[29,-32],[32,-19],[74,-26],[35,-21],[89,-76],[37,-22],[28,-5],[25,13],[24,32],[36,66],[24,8],[33,-39],[31,-49],[48,-60],[54,-51],[76,-33],[24,-23],[21,-27],[24,-23],[26,-15],[56,-21],[311,-42],[65,0],[28,-9],[24,-23],[34,-64],[21,-28],[99,-74],[28,-28],[22,-37],[8,-36],[3,-83],[19,-46]],[[3326,4368],[17,61],[32,61],[19,69],[-5,41],[0,25],[7,38],[19,31],[37,40],[8,31],[59,228],[9,94]],[[3528,5087],[100,279],[32,45],[55,47],[70,47],[63,55],[56,61],[14,36],[54,228],[9,24],[21,37],[10,20],[13,43],[8,87],[0,1],[1,14],[-1,87],[4,21],[7,14],[8,7],[9,5],[20,6],[16,3],[8,2],[7,6],[5,11],[1,14],[-4,13],[-7,13],[-45,52],[-5,12],[-5,16],[-2,49],[3,44],[-1,14],[-8,20],[-9,18],[-61,69]],[[9864,4457],[-40,-47],[-125,-84],[-53,-52],[-15,-37],[-5,-77],[-13,-37],[-20,-23],[-229,-140],[-64,-52],[-53,-65],[0,-1],[-17,-52]],[[9230,3790],[-1,0],[-178,-38],[-137,-54],[-149,-40],[-11,-8],[-19,-23],[-9,-23],[-6,-24],[-10,-21],[-20,-20],[-45,-17],[-25,-14],[-34,-34],[-59,-76],[-44,-26],[-64,-17],[-20,-18],[-48,-91],[-37,-41],[-45,-33],[-45,-26],[-99,-22],[-22,-19],[-8,-13]],[[8095,3092],[-47,21],[-49,29],[-68,25],[-40,21],[-16,13],[-30,29],[-28,44],[-18,38],[-6,8],[-9,7],[-11,4],[-17,2],[-22,-1],[-54,-12],[-15,-6],[-61,-44],[-10,-5],[-21,-8],[-10,-5],[-8,-7],[-7,-9],[-4,-11],[-5,-23],[-10,-19],[-7,-9],[-7,-8],[-8,-6],[-9,-6],[-75,-28],[-17,-1],[-22,3],[-128,37],[-10,5],[-24,20],[-11,4],[-13,3],[-152,23],[-57,14],[-64,40],[-52,51],[-11,7],[-14,6],[-24,5],[-29,10],[-14,2],[-19,-1],[-48,-17],[-84,-39],[-15,-4],[-18,-1],[-33,5],[-16,6],[-13,8],[-8,6],[-11,5],[-37,10],[-17,10],[-6,1],[-10,0],[-98,-16],[-36,-15],[-92,-50]],[[6220,3263],[-37,44],[-5,14],[-2,20],[3,14],[5,10],[7,9],[7,7],[28,18],[12,14],[14,21],[21,49],[7,26],[3,20],[-18,92],[-2,71],[-17,112]],[[5566,721],[28,-58],[20,-22],[28,-20],[23,-20],[9,-14],[4,-14],[-10,-51],[-1,-14],[2,-17],[5,-11],[7,-9],[14,-13],[37,-49],[16,-28],[3,-15],[2,-16]],[[5753,350],[-6,3],[-47,3],[-125,9],[-77,-8],[-97,24],[-76,31],[-85,-18],[-87,34],[-119,-37],[-66,18],[-126,-39],[-56,-22],[-123,-73],[-93,-61],[-65,-34]],[[4505,180],[-13,18],[-7,21],[-7,38],[0,39],[2,13],[2,12],[20,42],[7,22],[1,12],[-5,10],[-12,6],[-25,-1],[-16,-3],[-15,-2],[-14,3],[-16,11],[-8,10],[-5,9],[-4,4],[-8,5],[-11,4],[-10,5],[-9,7],[-11,18],[-4,10],[-8,14],[-10,3],[-18,0],[-37,-11],[-18,-9],[-12,-10],[-13,-18],[-7,-8],[-8,-7],[-9,-6],[-10,-5],[-20,-7],[-10,1],[-16,4],[-26,13],[-12,8],[-9,7],[-21,34],[-7,8],[-15,15],[-11,6],[-13,5],[-20,3],[-17,0],[-34,-9],[-62,-33]],[[3894,491],[-13,20],[-29,31],[-67,53],[-61,32],[-9,6],[-6,11],[-4,15],[-3,56],[-10,57],[2,14],[6,14],[14,16],[12,8],[22,10],[9,5],[8,7],[7,8],[13,18],[14,16],[32,27],[4,13],[0,16],[-9,28],[-7,14],[-7,10],[-1,9],[0,15],[13,49],[0,39]],[[3824,1108],[33,104],[19,32],[19,10],[24,9],[32,8],[56,3],[28,-1],[18,-3],[21,-6],[83,-42],[22,-2],[32,2],[93,18],[19,8],[14,16],[17,29],[24,66],[5,10],[12,12],[17,14],[168,100],[18,2],[25,-2],[39,-11],[99,-44],[16,-2],[21,-1],[43,5],[21,6],[16,5],[128,71],[51,16],[235,30]],[[5292,1570],[51,-186],[25,-67],[78,-132],[7,-19],[3,-17],[-1,-13],[-23,-102],[-1,-18],[2,-45],[4,-8],[10,-16],[78,-86],[31,-53],[10,-30],[0,-57]],[[6220,3263],[12,-15],[10,-18],[4,-10],[2,-11],[2,-38],[-2,-27],[-6,-16],[-9,-18],[-23,-25],[-11,-11],[-11,-7],[-64,-21],[-19,-10],[-16,-10],[-5,-5],[-1,-1],[-1,-1],[-5,-5],[-9,-14],[-12,-24],[-34,-86],[-9,-13],[-18,-18],[-13,-9],[-12,-7],[-21,-9],[-59,-13],[-22,-8],[-9,-6],[-8,-12],[-8,-16],[-6,-32],[-1,-18],[2,-15],[4,-10],[5,-9],[47,-68],[5,-10],[4,-10],[3,-11],[1,-12],[0,-13],[-6,-54],[-6,-25],[-64,-133],[-7,-21],[-5,-67],[-6,-15],[-11,-14],[-23,-20],[-38,-23],[-16,-13],[-8,-7],[-16,-26],[-23,-55],[-20,-26],[-22,-21],[-12,-15],[-10,-14],[-7,-26],[1,-16],[-1,-11],[-2,-12],[-3,-12],[-9,-12],[-12,-13],[-90,-71],[-23,-23],[-9,-13],[-10,-17],[-16,-35],[-12,-38],[-8,-15],[-14,-17],[-52,-50],[-55,-71]],[[3824,1108],[-14,26],[-9,13],[-103,101],[-5,10],[0,13],[8,17],[9,11],[10,6],[36,18],[9,6],[6,8],[6,10],[4,19],[8,136],[-2,20],[-9,30],[-34,79],[-4,11],[0,12],[5,16],[5,12],[41,65],[4,11],[3,17],[2,23],[-8,84],[-57,247],[-26,67]],[[3709,2196],[23,17],[16,15],[7,8],[5,9],[6,10],[4,11],[3,12],[3,13],[10,191],[-4,18],[-8,22],[-33,58],[-28,39],[-107,357],[-29,54]],[[8095,3092],[-9,-9],[-11,-8],[-13,-6],[-45,-7],[-44,-1],[-34,-14],[-40,-126],[-70,-59],[-151,-80],[-40,-41],[-18,-31],[-8,-43],[18,-117],[-3,-17],[-9,-22],[-41,-63],[-12,-14],[-8,-12],[-6,-17],[-7,-33],[-3,-62],[-5,-24],[-8,-12],[-57,-60],[-45,-66]],[[7426,2148],[-78,-54],[-22,-29],[-3,-12],[-2,-12],[-16,-47],[-24,-47],[-15,-20],[-13,-13],[-43,-15],[-93,-11],[-18,-6],[-23,-10],[-40,-24],[-17,-15],[-12,-13],[-4,-11],[-8,-16],[-54,-75],[-28,-56],[-41,-64],[-5,-16],[1,-6],[5,-15],[1,-7],[1,-9],[0,-10],[-2,-12],[-3,-11],[-6,-42],[-7,-21],[-9,-12],[-12,-10],[-18,-12],[-9,-11],[-6,-12],[-34,-102],[-5,-25],[-3,-43],[-7,-21],[-8,-11],[-11,-6],[-33,-16],[-29,-22],[-17,-10],[-16,-5],[-34,-5],[-59,-28],[-22,-6],[-41,-4],[-25,-14],[-35,-23],[-103,-88],[-23,-16],[-11,-4],[-26,-3],[-59,4],[-27,-1],[-13,-1],[-23,-7],[-213,-163],[-13,-8],[-45,-19],[-49,-12],[-253,-12]],[[7334,341],[-21,-10],[-116,-30],[-61,-46],[-38,-16],[-80,-22],[-325,-141],[-40,-6],[-37,-12],[-48,-51],[-36,-7],[8,21],[4,13],[0,8],[-19,27],[-25,23],[-34,15],[-74,9],[-5,8],[2,13],[-12,18],[-19,13],[-17,8],[-41,7],[-90,-4],[-172,-36],[-72,12],[-50,44],[-97,118],[-66,33]],[[7426,2148],[40,-347],[-2,-10],[-5,-22],[-4,-11],[-21,-40],[-4,-10],[-4,-12],[-19,-113],[5,-126],[7,-35],[4,-47],[-7,-123],[-10,-57],[-5,-7],[-4,-4],[-8,-2],[-11,2],[-10,4],[-13,0],[-13,-6],[-14,-25],[-5,-25],[-2,-40],[2,-20],[4,-15],[13,-16],[6,-13],[4,-19],[4,-54],[3,-15],[6,-9],[16,-17],[8,-14],[8,-24],[1,-17],[-2,-15],[-3,-12],[-9,-21],[-29,-45],[-7,-14],[-15,-55],[-5,-45],[4,-82],[4,-23],[25,-76],[1,-11],[-2,-10],[-4,-8],[-15,-17],[-8,-12],[-7,-21],[-1,-16],[2,-11],[9,-22],[0,-2]],[[4505,180],[-58,-32],[-67,6],[-57,-30],[-27,-15],[-11,-43],[-40,51],[-33,6],[-34,12],[15,18],[3,23],[-21,16],[-30,4],[-11,-31],[-38,-27],[-17,0],[-42,5],[-44,-5],[-59,45],[-113,60],[-29,24],[-4,31],[32,25],[32,29],[48,10],[-7,7]],[[3893,369],[11,9],[5,8],[3,17],[1,15],[-19,73]],[[9230,3790],[-30,-94],[1,-151],[41,-308],[-42,-339],[-6,-183],[53,-84],[38,0],[34,6],[31,0],[31,-20],[18,-34],[13,-36],[23,-23],[61,4],[-33,-21],[-34,-28],[-68,-76],[-42,-68],[-23,-71],[-38,-170],[-36,-103],[-9,-94],[-18,-29],[-66,-42],[-31,-26],[-92,-107],[-101,-70],[-32,-35],[-67,-120],[-18,-21],[-4,-22],[-40,-118],[30,-75],[-2,-37],[-77,-21],[-174,-88],[-138,-89],[-286,-253],[-23,-27],[-8,-36],[1,-31],[-10,-23],[-171,-35],[-43,-3],[8,18],[11,13],[33,26],[-14,11],[-12,12],[-9,16],[-4,18],[3,25],[19,50],[5,9],[-28,25],[-51,19],[-53,12],[-37,1],[21,-14],[70,-28],[-15,-26],[-31,-28],[-35,-22],[-29,-9],[-11,-14],[-1,-28],[14,-22],[31,5],[-3,16],[28,-6],[35,-26],[17,-45],[-15,-26],[-35,-26],[-421,-204]],[[2555,2072],[25,-50],[16,-62],[8,-17],[9,-13],[10,-6],[32,-14],[13,-10],[61,-66],[69,-99],[11,-22],[1,-14],[0,-6],[-2,-6],[-4,-5],[-13,-14],[-83,-62],[-9,-5],[-14,-6],[-80,-21],[-30,-15],[-45,-29],[-8,-10],[-8,-16],[-7,-27],[-1,-16],[2,-14],[5,-9],[6,-9],[37,-40],[7,-12],[-1,-14],[-13,-42],[-10,-16],[-11,-13],[-10,-21],[-3,-23],[2,-40],[-2,-19],[-6,-14],[-7,-8],[-115,-90]],[[2387,1077],[-67,12],[-167,4],[-103,-6],[-34,26],[-87,-2],[-48,-27],[-23,27],[-48,4],[-52,-22],[-66,-27],[-60,55],[-48,2],[-54,-10],[-76,-30],[-105,-37],[-34,4],[-48,-27],[-55,5],[-54,-6],[-62,8],[18,17],[-16,31],[-52,14],[-34,50],[5,82],[-48,47],[-37,56],[-139,150],[-62,27],[-22,31],[-29,67],[-33,29],[-47,18],[-49,8],[-44,3],[-51,9],[-23,24],[-22,77],[-22,44],[-158,210],[-35,27],[-70,38],[-31,26],[-26,39],[-14,42],[-10,55]],[[3893,369],[-21,22],[-37,14],[-99,36],[-69,10],[-66,-3],[-40,-25],[-31,4],[-29,13],[-70,40],[-33,14],[-33,20],[-126,32],[-154,18],[-147,58],[-32,20],[-25,51],[-29,25],[-63,41],[-44,46],[-39,34],[-35,25],[-50,29],[-28,22],[3,28],[-12,14],[-28,17],[-11,10],[-35,36],[-34,27],[-60,24],[-29,6]],[[2555,2072],[80,-4],[272,-48],[21,2],[26,5],[46,17],[22,10],[15,9],[78,87],[32,24],[5,4],[8,3],[129,30],[43,15],[38,21],[76,57],[58,-13],[205,-95]],[[1201,8575],[-5,37],[-12,53],[-18,73],[-15,45],[-42,86],[-28,40],[-42,79],[-8,80],[24,74],[52,61],[369,320],[41,66],[14,79],[-27,89],[-9,45],[16,19],[42,1],[47,-43],[36,-107],[59,-37],[47,-5],[82,14],[45,1],[25,-12],[29,-42],[29,-7],[18,9],[43,36],[24,11],[37,-1],[23,-15],[20,-17],[28,-11],[14,5],[24,27],[11,5],[14,-7],[24,-24],[9,-6],[61,12],[54,41],[38,58],[10,64],[14,53],[40,66],[51,59],[47,31],[19,-1],[35,-16],[20,0],[37,18],[13,4],[69,13],[37,1],[91,-24],[69,12],[72,1],[67,-44],[35,-56],[18,-21],[117,-76],[58,-27],[32,-20],[28,-26],[17,-28],[7,-41],[-6,-22],[2,-13],[30,-18],[19,-5],[18,1],[18,-2],[20,-12],[11,-12],[16,-26],[70,-75],[23,-18],[31,-27],[100,-61],[32,-30],[45,-72],[27,-35],[25,-21],[87,-46],[110,-87],[61,-30],[38,-34],[59,-66],[11,-23],[5,-22],[8,-19],[21,-17],[36,-22],[16,-15],[13,-17],[62,-115],[28,-33],[123,-94],[30,-57],[12,-61],[-9,-61],[-33,-56],[-8,-44],[-1,-135],[-12,-93],[1,-44],[10,-15]],[[3528,5087],[-60,29],[-26,16],[-103,81],[-20,11],[-102,29],[-33,4],[-13,-1],[-23,-5],[-19,-1],[-24,2],[-73,13],[-20,1],[-47,-10],[-41,-3],[-41,3],[-46,10],[-59,33],[-103,44],[-28,7],[-20,2],[-10,-4],[-19,-9],[-35,-25],[-19,-10],[-11,-4],[-24,-4],[-119,-1],[-18,-4],[-20,-9],[-122,-82],[-14,-6],[-19,-5],[-33,-5],[-19,1],[-18,6],[-40,16],[-16,2],[-14,-2],[-28,-16],[-33,-10],[-48,-8],[-18,1],[-68,11],[-25,1],[-18,-2],[-10,-5],[-17,-12],[-15,-15],[-6,-7],[-17,-15],[-9,-5],[-44,-13],[-11,-4],[-8,-6],[-7,-8],[-9,-17],[-2,-4],[-1,-1],[-12,-30],[-5,-9],[-14,-16],[-8,-8],[-18,-12],[-326,-45],[-8,-7],[-6,-8],[-6,-10],[-3,-12],[-6,-42],[-9,-11],[-13,-11],[-28,-12],[-17,-2],[-14,3],[-10,5],[-165,16],[-23,5],[-18,12],[-39,10],[-45,4],[-22,5],[-15,6],[-6,7],[-2,4],[-12,35],[-10,18],[-6,8],[-8,7],[-41,21],[-9,6],[-8,7],[-51,55],[-6,7],[-9,7],[-12,7],[-21,8],[-16,3],[-96,-5],[-55,-10]],[[465,5088],[-11,45],[-5,47],[1,5],[18,22],[47,91],[119,160],[9,63],[-19,59],[-128,160],[-22,43],[11,42],[41,64],[20,79],[6,92],[-7,88],[-19,64],[-59,63],[-23,41],[13,42],[51,55],[30,20],[41,17],[89,16],[45,17],[20,30],[113,316],[17,90],[-14,92],[-50,58],[-48,35]],[[90,3176],[-40,30],[-12,41],[12,37],[29,33],[68,61],[19,41],[3,47],[-31,178],[-2,87],[29,66],[77,26],[175,-9],[80,6],[69,31],[71,61],[30,37],[13,34],[3,48],[9,37],[28,77],[13,82],[-16,62],[-37,49],[-82,79],[-19,34],[-10,37],[-3,43],[4,93],[-4,47],[-14,35],[14,16],[-16,52],[-21,144],[-22,29],[-15,32],[-27,109]]],
transform:{scale:[.0005329057895789584,.00048770204920492037],translate:[-58.43936113199993,-34.973402601999936]}},m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();