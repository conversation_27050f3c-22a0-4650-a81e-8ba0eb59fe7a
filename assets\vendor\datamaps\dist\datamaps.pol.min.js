!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo={type:"Topology",objects:{pol:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Warmian-Masurian"},id:"PL.WN",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Pomeranian"},id:"PL.PM",arcs:[[-4,5,6,7,8]]},{type:"Polygon",properties:{name:"Lower Silesian"},id:"PL.DS",arcs:[[9,10,11,12]]},{type:"Polygon",properties:{name:"West Pomeranian"},id:"PL.ZP",arcs:[[13,14,15,-8]]},{type:"Polygon",properties:{name:"Lubusz"},id:"PL.LB",arcs:[[16,-13,17,-15]]},{type:"Polygon",properties:{name:"Greater Poland"},id:"PL.WP",arcs:[[-7,18,19,20,-10,-17,-14]]},{type:"Polygon",properties:{name:"Kuyavian-Pomeranian"},id:"PL.KP",arcs:[[-3,21,22,-19,-6]]},{type:"Polygon",properties:{name:"Silesian"},id:"PL.SL",arcs:[[23,24,25,26,27]]},{type:"Polygon",properties:{name:"Łódź"},id:"PL.LD",arcs:[[28,29,-28,30,-20,-23]]},{type:"Polygon",properties:{name:"Masovian"},id:"PL.MZ",arcs:[[31,32,-29,-22,-2,33]]},{type:"Polygon",properties:{name:"Świętokrzyskie"},id:"PL.SK",arcs:[[34,35,36,-24,-30,-33]]},{type:"Polygon",properties:{name:"Podlachian"},id:"PL.PD",arcs:[[37,-34,-1,38]]},{type:"Polygon",properties:{name:"Lublin"},id:"PL.LU",arcs:[[39,40,-35,-32,-38]]},{type:"Polygon",properties:{name:"Subcarpathian"},id:"PL.PK",arcs:[[41,42,-36,-41]]},{type:"Polygon",properties:{name:"Opole"},id:"PL.OP",arcs:[[-31,-27,43,-11,-21]]},{type:"Polygon",properties:{name:"Lesser Poland"},id:"PL.MA",arcs:[[-43,44,-25,-37]]}]}},arcs:[[[8670,9236],[2,-13],[-25,-82],[-57,-60],[-66,-45],[-223,-32],[-93,-134],[100,-136],[6,-51],[10,-47],[51,-50],[106,-150],[-4,-80],[-40,-78],[-35,-100],[-57,-62],[-278,-175],[-97,-112],[-172,-121],[-75,-11],[-32,-14],[-29,-24],[-63,-22],[-142,8],[-73,-32]],[[7384,7613],[-121,-10],[-24,-14],[-29,-57],[-40,-30],[-46,-7],[-88,8],[-76,-92],[-90,-30],[-95,1],[-73,-43],[-64,-14],[-71,1],[-36,-30],[-33,-41],[-140,-83],[-36,-11],[-39,4],[-31,-25],[-17,-57],[-26,-32],[-30,-15],[-75,-15],[-315,51],[-9,10],[-6,16],[-16,11],[-17,3],[-39,-12],[-26,-53],[-29,-25],[-34,-9]],[[5613,7013],[-15,157],[-52,134],[-55,40],[-114,31],[-107,65],[-77,26],[-18,1],[-24,11],[-15,23],[-31,58],[-24,74],[-87,172]],[[4994,7805],[148,279],[35,39],[42,27],[80,-5],[57,25],[2,28],[12,19],[15,48],[19,19],[11,23],[0,28],[5,28],[-50,14],[-99,-8],[-36,17],[20,68],[-26,36],[-52,6],[-48,29],[-33,56],[-18,73],[-3,34],[16,24],[44,44],[15,28],[11,31],[-10,43],[-21,37],[-21,65],[6,59],[25,28],[88,-12],[24,3],[0,-13],[-10,-9],[-7,-15],[-4,-21],[0,-25],[13,13],[18,47],[7,10],[6,7],[25,41],[25,24],[13,8],[13,3],[77,49],[17,7],[39,5],[65,48],[13,6],[15,2],[8,9],[6,19],[7,16],[16,4],[-2,7],[-2,19],[-2,8],[11,-1],[4,2]],[[5623,9308],[206,-11],[387,-21],[288,-15],[288,-16],[357,-19],[433,-23],[277,-14],[278,-15],[233,-13],[187,-10],[69,23],[19,16],[25,46]],[[4994,7805],[-54,-25],[-114,-7],[-54,23],[-31,20],[-66,69],[-16,11],[-13,16],[-20,12],[-81,11],[-58,-21],[-37,-25],[-85,52],[-180,30],[-32,32],[-30,39],[-35,3],[-169,-26],[-126,25],[-27,-21],[-41,-76],[-69,-26],[-32,-31],[-5,-22],[-4,-24],[-1,-37],[-62,-25],[-138,37],[-37,-4],[-28,-40],[-24,-54],[-30,-107],[-28,-18]],[[3267,7596],[-45,54],[-53,30],[-203,9],[-59,36],[-41,75],[-48,60]],[[2818,7860],[-17,186],[41,43],[30,54],[-11,36],[-17,29],[-20,10],[-1,33],[50,24],[55,-7],[-19,40],[-23,34],[-28,25],[-38,78],[-24,29],[-42,31],[-1,94],[-8,94],[-63,195],[139,34],[16,25],[-4,42],[-19,27],[-11,25],[8,30],[26,27],[11,46],[-8,82],[-30,63],[-83,119],[-69,140],[-2,3]],[[2656,9551],[74,30],[43,7],[37,14],[96,104],[301,140],[377,71],[170,58],[266,24],[165,0],[22,-8],[36,-32],[14,-7],[21,-4],[340,-203],[63,-80],[10,-19],[10,-26],[1,-23],[-18,-9],[-4,6],[-5,12],[-6,13],[-18,12],[-12,33],[-17,20],[-47,80],[-10,5],[-35,7],[-9,4],[-9,16],[-28,10],[-60,40],[-59,28],[-9,14],[-32,24],[-9,4],[-12,-5],[-6,-9],[-15,-49],[-1,-11],[0,-25],[5,-6],[24,-11],[5,-6],[12,-21],[15,-32],[-1,-13],[-4,-11],[1,-12],[11,-12],[-6,-33],[12,-14],[19,4],[16,21],[-6,-29],[8,-22],[13,-19],[25,-78],[8,-17],[-4,-39],[1,-68],[6,-64],[14,-28],[16,-6],[30,-23],[16,-5],[13,-2],[17,-5],[14,-9],[6,-15],[17,-24],[168,-54],[38,-3],[31,17],[8,-8],[12,-7],[27,-8],[375,56],[82,40],[40,9],[31,15],[62,64],[17,7],[21,-17],[49,-17],[78,-4]],[[2243,4714],[87,18],[40,-12],[113,-65],[18,-43],[3,-56],[43,-81],[97,-58],[54,-64],[115,-44],[207,39],[37,92],[42,9],[117,-1],[111,-32],[99,-69],[-10,-49],[-14,-42],[-24,-17],[-14,-37],[6,-74],[23,-62],[59,-15],[61,13],[38,-11],[36,-31],[-28,-72],[11,-176],[13,-85]],[[3583,3689],[-33,-22],[-34,-9],[-35,3],[-32,-12],[-26,-30],[-58,-145],[-11,-41],[-6,-45],[-16,-52],[-32,-23],[-47,-66],[-36,-90],[-83,-134],[-72,-148],[-23,-96],[-36,-68],[-60,-11],[-52,-43],[-72,-196],[-5,-21]],[[2814,2440],[-28,19],[-23,3],[-27,-18],[0,-24],[32,-52],[12,-31],[7,-36],[10,-34],[19,-21],[24,-15],[18,-22],[11,-31],[1,-42],[13,-19],[2,-22],[-6,-15],[-12,5],[-11,18],[-5,10],[-6,5],[-14,0],[-3,-5],[0,-20],[-2,-7],[-9,0],[-19,7],[-9,0],[-12,-10],[-16,-25],[-10,-9],[-11,-3],[-12,-4],[-20,2],[-20,-3],[-22,-21],[-18,-30],[-11,-24],[-12,-19],[-22,-17],[-31,-46],[-40,-4],[-42,24],[-35,35],[-17,25],[-10,22],[-11,58],[-11,33],[-30,34],[-13,25],[-14,22],[-25,51],[-15,22],[-22,17],[-44,18],[-18,26],[-3,15],[1,15],[-1,16],[-7,16],[-9,3],[-46,0],[-11,-4],[-7,-7],[-8,-1],[-13,11],[-4,10],[-13,44],[-33,6],[-9,31],[8,27],[20,-5],[-2,5],[-5,19],[6,1],[9,6],[6,3],[15,14],[18,11],[15,14],[7,22],[16,6],[32,-10],[17,4],[10,15],[20,22],[9,13],[-3,-1],[0,10],[1,13],[2,9],[4,10],[3,5],[0,5],[-4,13],[31,14],[-9,33],[-85,98],[-30,19],[-33,9],[-36,1],[-13,-3],[-7,-17],[-8,-22],[-12,-17],[-17,-4],[-15,8],[-31,26],[-42,5],[-62,-66],[-43,-8],[-2,5],[0,5],[0,6],[2,5],[15,13],[2,21],[-6,23],[-8,23],[-13,27],[-14,14],[-16,3],[-59,-24],[-28,-1],[-6,2],[-51,94],[-3,10],[-2,12],[-25,2],[-83,-21],[-10,3],[-8,8],[-13,22],[-11,8],[-120,46],[-80,30],[-41,-4],[-10,-12],[-9,-18],[-11,-13],[-2,1],[-12,4],[-7,14],[1,16],[3,17],[0,17],[-12,32],[-16,14],[-18,8],[-19,18],[-11,18],[-12,29],[-9,30],[-3,24],[7,32],[9,17],[1,17],[-16,28],[-27,14],[-57,1],[-9,25],[-5,26],[-12,7],[-12,-10],[-9,-22],[0,-1],[-4,-17],[-11,-2],[-14,7],[-12,13],[-8,17],[-51,12],[-19,18],[-3,-14],[-4,-8],[-15,-11],[-5,-1],[-6,-3],[-5,-5],[-5,-6],[5,-19],[16,-14],[15,-24],[1,-49],[-3,-9],[-10,-12],[-3,-6],[-1,-10],[1,-6],[1,-5],[1,-59],[-2,-15],[-114,9],[-36,-11],[-21,1],[0,32],[14,25],[36,43],[50,129],[45,123],[3,19],[0,33],[2,17],[5,12],[8,8],[6,11],[2,22],[2,43],[8,48],[13,46],[17,36],[-4,5],[-3,5],[5,50],[-15,33],[-22,29],[-19,35],[-3,12],[-4,30],[0,2],[-6,72]],[[825,4110],[19,12],[159,90],[29,0],[59,-54],[29,-3],[50,93],[34,43],[73,27],[140,-83],[71,19],[110,156],[98,205],[22,16],[35,13],[30,32],[15,45],[19,28],[152,-30],[26,-30],[4,-60],[55,-75],[79,19],[61,57],[49,84]],[[2818,7860],[-37,12],[-38,-1],[-36,-16],[-26,-47],[-19,-56],[-26,-109],[-30,-6],[-184,5],[-22,-33],[1,-68],[15,-61],[63,-62],[75,-20],[37,-20],[31,-41],[7,-49],[-31,-39],[-24,-43],[-28,-28],[-74,-24],[-144,-87],[-81,-131],[-96,-94],[-62,-25],[-62,-8],[-61,37],[-55,19]],[[1911,6865],[18,82],[-26,83],[-51,12],[-54,-8],[-52,-43],[-90,-118],[-50,-38],[-206,0],[-169,-60],[-56,-3],[19,-28],[28,-4],[-38,-75],[-61,-74],[-53,-8],[-166,58],[-98,-57],[-63,-144],[-78,-115],[-51,-22],[-85,-14],[-22,-34],[-24,-92]],[[483,6163],[-58,68],[-60,25],[-28,41],[-19,9],[-11,11],[-34,70],[-15,19],[-106,81],[-28,15],[-32,40],[-20,10],[-22,1],[-21,7],[-18,14],[-11,24],[27,24],[10,21],[4,32],[-2,22],[-12,60],[-6,18],[0,10],[48,37],[61,33],[58,49],[31,33],[13,31],[3,14],[30,97],[-2,25],[-6,24],[-4,28],[3,23],[7,21],[10,15],[11,6],[7,11],[26,53],[-13,12],[-4,10],[-15,13],[-6,11],[-3,17],[-1,31],[-1,14],[-7,32],[-21,100],[-11,89],[-20,36],[-11,35],[-24,39],[-7,35],[-1,38],[3,63],[-2,17],[-21,66],[-15,34],[4,26],[-1,48],[9,2],[18,7],[14,13],[1,14],[-18,12],[10,6],[3,6],[-3,6],[-10,6],[6,12],[10,-8],[14,-8],[12,-9],[12,-33],[14,-11],[27,-13],[20,-22],[11,-10],[44,-7],[54,-20],[18,-15],[15,-22],[33,-64],[12,-15],[5,5],[1,27],[5,16],[8,10],[14,11],[-1,7],[0,7],[1,10],[-11,3],[-25,2],[-12,6],[-31,76],[7,38],[24,74],[12,-10],[9,3],[11,7],[16,0],[-7,44],[3,34],[7,31],[4,32],[-7,0],[-3,-21],[-7,-13],[-24,-25],[6,-12],[-9,4],[-7,5],[-5,6],[-6,9],[5,5],[9,12],[7,6],[-14,29],[-24,14],[-110,12],[-16,17],[13,33],[-4,4],[-1,2],[-2,6],[-9,-17],[-8,-2],[-9,4],[-12,3],[-8,-3],[-7,-9],[-7,-11],[-8,-11],[29,-5],[17,-8],[8,-11],[-2,-21],[-10,-15],[-13,-6],[-9,7],[-27,-18],[-14,-4],[-14,-1],[0,-12],[14,-6],[-1,-3],[-13,-3],[-9,2],[-16,8],[-12,2],[-17,11],[-48,71],[-23,11],[-2,-1],[-8,27],[-18,22],[10,3],[8,5],[17,46],[16,-7],[11,-9],[31,-12],[99,-8],[39,8],[39,21],[72,53],[264,119],[227,60],[278,118],[180,49],[197,53],[193,88],[161,21],[111,37],[-13,-13],[-50,-22],[22,-16],[88,16],[0,10],[-7,0],[0,13],[48,0],[-1,8],[-1,2],[-1,-1],[-4,3],[2,27],[-23,1],[-54,-17],[34,17],[41,28],[62,72],[-12,-25],[-23,-24],[-8,-12],[9,3],[18,4],[16,6],[14,17],[35,16],[12,15],[-11,25],[-20,1],[-9,-11],[-15,-15],[43,73],[115,149],[0,-10],[-5,-17],[10,-4],[16,6],[13,15],[6,23],[-6,7],[-13,-6],[-15,-14],[21,41],[35,35],[68,42],[208,31],[7,2]],[[1911,6865],[-20,-98],[3,-109],[-97,-130],[31,-29],[21,-85],[-9,-27],[-20,-13],[-94,-31],[-15,2],[-7,-16],[-1,-38],[-5,-37],[4,-95],[35,-101],[13,-60],[7,-62],[0,-52],[-26,0],[-22,-15],[43,-79],[20,-51],[2,-105],[-33,-101],[-5,-25],[-2,-26],[-12,-50],[0,-58],[8,-54],[23,-36],[27,-30],[55,-31],[18,-53],[23,-45],[36,-37],[37,-1],[17,8],[17,-11],[1,-35],[-9,-32],[-5,-59],[15,-46],[43,-31],[45,-16],[73,52],[66,-76],[31,-127]],[[825,4110],[-4,46],[9,21],[-11,24],[-18,20],[-16,13],[-22,12],[-47,15],[-23,14],[-21,16],[-65,24],[-22,24],[-3,18],[-3,13],[9,33],[19,33],[12,34],[-2,45],[-10,43],[-13,30],[-48,43],[-23,28],[-10,44],[-4,45],[-14,19],[-18,14],[-16,26],[-3,37],[13,29],[57,62],[19,26],[15,31],[8,34],[4,104],[5,17],[22,41],[4,16],[2,16],[3,15],[8,15],[18,24],[-65,51],[-10,25],[6,44],[11,32],[4,24],[-14,24],[0,10],[19,28],[0,35],[-15,30],[-28,12],[-28,7],[-34,19],[-23,26],[6,31],[-21,50],[-23,75],[-6,68],[52,47],[24,41],[16,40],[-4,18],[-19,18],[8,40],[28,61],[-31,27],[-6,6]],[[3267,7596],[-28,-63],[-72,-97],[12,-51],[84,-68],[48,-112],[-91,-79],[9,-72],[31,-62],[-14,-106],[-45,-107],[81,-30],[76,-59],[49,-112],[-22,-121],[-12,-23],[-16,-6],[-17,1],[-17,-13],[-7,-45],[36,-36],[16,-40],[19,-33],[41,-8],[40,14],[18,16],[31,-27],[8,-23],[5,-28],[8,-11],[11,-6],[63,-23],[70,-4],[75,-42],[102,-98],[36,-20],[37,-2],[33,-15],[29,-37],[33,-32],[101,-28],[34,16],[58,71],[19,-5],[14,-10],[8,-32],[12,-27],[19,-30],[24,-8],[15,9],[10,19],[18,9],[20,2],[34,-17],[30,-37],[165,-142],[28,-65],[36,3],[104,46],[35,6],[95,-77]],[[4906,5619],[2,-55],[19,-43],[15,-4],[3,-17],[-62,-62],[-100,-45],[-10,-48],[-9,-107],[-51,-63],[-67,-17],[-34,4],[-32,-2],[-13,-53],[-8,-59],[12,-60],[19,-55],[-5,-54],[-47,-89],[-12,-14],[-144,17],[-57,-29],[-26,-37],[-47,-118],[-25,-104],[-5,-114],[10,-94],[1,-91],[-28,-81],[-47,-36],[-54,28],[-50,-16],[-17,-54],[-25,-42],[-73,-17],[-16,-39],[3,-55],[18,-47],[58,-55],[10,-47],[6,-80]],[[4018,3665],[-114,-73],[-118,-47],[-64,3],[-52,46],[-6,42],[-13,37],[-33,18],[-35,-2]],[[5613,7013],[-48,-40],[-51,-25],[6,-32],[0,-34],[8,-50],[-2,-34],[-6,-33],[-9,-29],[-19,2],[-19,14],[-19,8],[-21,-21],[-19,-29],[-42,-37],[-48,7],[15,-70],[-5,-63],[-20,-23],[-8,-35],[11,-36],[16,-32],[29,-72],[1,-57],[-43,-12],[-67,-204],[-26,-56],[-6,-66],[21,-22],[4,-32],[-74,-127],[-4,-29],[-6,-29]],[[5162,5715],[-20,1],[-19,-4],[-12,-20],[-10,-19],[-37,-12],[-123,-2],[-18,-11],[-7,-16],[-10,-13]],[[5615,3097],[86,-18],[12,-7],[4,-19],[0,-21],[-5,-14],[-68,-12],[-38,-77],[28,-36],[75,-24],[34,-28],[28,-68],[48,-63],[-40,-6],[-69,-64],[-20,-47],[35,-20],[68,-11],[28,-12]],[[5821,2550],[6,-37],[3,-31],[-126,-86],[-32,-6],[-56,5],[-54,-9],[-89,-76],[-25,-6],[-26,0],[-55,35],[-27,-44],[-28,-61],[-43,-68],[-67,11],[-31,-27],[53,-58],[46,-71],[-236,-293],[-22,-36],[-22,-50],[-12,-57],[20,-29],[27,-3],[21,-30],[2,-57],[30,-60],[51,-22],[20,-41],[16,-53],[32,-33],[37,-19],[68,-13],[-4,-45],[-17,-38],[-8,-36],[13,-31],[23,-18],[5,-20]],[[5314,1037],[-5,3],[-6,-3],[-4,-8],[-86,-107],[-8,-7],[-14,-6],[-10,-2],[-31,0],[-19,-5],[-17,-8],[-14,-16],[-14,-24],[-16,-56],[2,-2],[3,-5],[2,-8],[-2,-11],[-5,-4],[-12,0],[-3,-1],[-7,-34],[-2,-21],[-8,-14],[-30,-13],[-25,-6],[-11,1],[-9,5],[-20,17],[-9,3],[-22,-6],[-39,-25],[-25,-2],[-19,4],[-6,19],[4,57],[-2,25],[-5,23],[-1,25],[8,28],[-28,20],[-99,10],[4,29],[-3,35],[-14,73],[-5,16],[-11,29],[-5,17],[-7,59],[-4,13],[-15,13],[-15,-3],[-16,-8],[-15,-1],[-12,8],[-20,26],[-13,12],[-14,4],[-31,3],[-13,11],[-6,13],[-10,49],[-24,54],[-17,52],[-1,46],[28,35],[-27,16],[-4,21],[3,26],[-7,31],[-14,10],[-40,-28],[-23,0],[-74,46],[-39,15],[-40,-6],[2,0],[2,-1],[1,-2],[2,-2],[-28,-27],[-15,-4],[-20,18],[-5,12],[-7,26],[-4,10],[-12,10],[-24,5],[-11,6],[-32,30],[-15,9],[-64,13],[-13,17],[7,32],[-59,42],[-14,4]],[[3887,1797],[2,28],[59,128],[126,29],[60,50],[96,50],[11,50],[1,87],[-17,220],[9,35],[22,4],[21,12],[2,30],[-11,31],[14,38],[29,8],[145,-2],[-12,62],[-32,37],[-32,27],[-26,40],[-20,65],[24,69],[18,108],[27,61],[78,55],[7,60],[-14,32],[-6,39],[8,26],[12,16],[35,132]],[[4523,3424],[50,26],[141,5],[32,17],[32,24],[35,-8],[31,-34],[130,-76],[65,-14],[79,0],[35,33],[67,-14],[60,-60],[93,-161],[34,-11],[100,6],[108,-60]],[[5162,5715],[96,-70],[105,-23],[51,-26],[92,-78],[208,45],[31,34],[38,4],[27,-36],[24,-44],[31,-18],[35,-11],[28,-20],[12,-43],[4,-61],[20,-47],[15,-8],[15,4],[69,-29],[39,-28],[13,-18],[-10,-86],[-33,-21],[8,-36],[27,-33],[24,-37],[-3,-33],[9,-27],[36,-13],[105,13],[67,-32],[72,-60],[59,-83],[6,-28],[-9,-20],[-10,-16],[-5,-21],[9,-49],[54,-60],[15,-43],[-2,-41],[-13,-28],[-73,11],[-59,34],[-78,0],[-26,-96],[30,-76],[9,-53],[4,-55],[8,-27],[37,-19],[12,-16],[0,-55],[-16,-47],[-64,-64],[-13,-47],[3,-50]],[[6295,3928],[-28,-23],[-37,-97],[-33,-17],[-33,-5],[-32,-17],[-69,-71],[-32,-10],[-111,17],[-31,-12],[-24,-35],[-25,-103],[46,-75],[30,-27],[10,-57],[-8,-56],[-22,-29],[-132,105],[-36,-7],[-12,-55],[-1,-55],[4,-54],[-17,-51],[-35,-26],[-29,-29],[-23,-42]],[[4523,3424],[-75,65],[-24,49],[-26,35],[-12,-7],[-9,-18],[-13,-12],[-71,-2],[-223,62],[-30,26],[-22,43]],[[8945,5643],[-32,-63],[-95,-147],[-28,-71],[-33,-44],[-39,-16],[-117,64],[-41,10],[-18,-13],[-17,-19],[-6,-21],[-5,-26],[-55,-47],[-110,11],[-38,-5],[-96,-76],[-35,-12],[-50,11],[-137,-29],[-144,1],[-76,-23],[-71,-56],[6,-43],[16,-49],[20,-44],[4,-46],[24,-63],[35,-57],[-55,-35],[-38,-58],[50,-82],[-7,-39],[-25,-25],[-70,-27],[-73,12],[-36,16],[-36,-11],[-10,-43],[1,-18],[13,-23],[15,-18],[27,-14],[52,-12],[17,2],[25,12],[9,-1],[16,-11],[3,-16],[0,-19],[2,-24],[18,-30],[3,-21],[-15,-19],[0,-12],[31,-3],[-1,-70],[-25,-36],[-23,-21],[-2,-41],[40,-51],[-6,-50],[-12,-51],[2,-36],[-11,-2],[-10,-9],[-12,-27],[-18,-106],[-8,-25],[-4,-15],[-2,-20],[2,-13],[10,-31],[8,-55],[24,-66]],[[7676,3526],[-319,-71],[-165,55],[-77,8],[-68,47],[-7,27],[-4,28],[-30,36],[-8,58],[-31,11],[-31,-32],[-29,-44],[-33,-20],[-139,-24],[-121,27],[-25,21],[-21,36],[-24,29],[-164,108],[-30,81],[-55,21]],[[7384,7613],[13,-60],[23,-55],[16,-68],[25,-196],[56,-144],[37,-73],[53,-30],[54,-8],[46,-37],[-16,-31],[4,-43],[26,-47],[38,-22],[24,-26],[15,-42],[13,-63],[27,-50],[71,-39],[76,12],[50,56],[48,-38],[-16,-94],[25,-37],[31,-23],[56,30],[58,17],[16,-81],[6,-89],[12,-60],[3,-64],[-3,-37],[56,-63],[21,-30],[22,-19],[7,-10],[2,-17],[-3,-14],[-5,-14],[0,-15],[3,-22],[5,-14],[8,-8],[14,-3],[3,-8],[-6,-56],[7,-19],[17,-25],[20,-16],[17,8],[12,-20],[24,-7],[53,3],[155,-33],[78,11],[29,-10],[23,-23],[10,-39],[21,12],[18,-12],[28,-35],[-5,-10],[-5,-17],[-3,-8],[17,-11],[16,5],[15,11]],[[7676,3526],[4,-23],[-4,-10],[-8,-7],[-9,-5],[-7,-1],[0,-12],[19,-15],[3,-33],[-8,-74],[-1,-5],[-2,-4],[-3,-5],[-1,-10],[2,-10],[5,-5],[4,-3],[2,-6],[2,-16],[26,-104],[5,-37]],[[7705,3141],[3,-23],[1,-64],[-8,-67],[-7,-27],[-12,-31],[-15,-25],[-18,-10],[-26,-5],[-17,-14],[-29,-51],[-47,-62],[-7,-27],[-6,-33],[-12,-26],[-31,-40],[-18,-17],[-64,-19],[-95,-46],[3,-8],[10,-27],[-19,-22],[-25,-16],[-26,-6],[-18,9],[-62,-59],[-3,-1]],[[7157,2424],[-14,1],[-4,-3],[0,-8],[1,-8],[-1,-5],[-11,-9],[-7,0],[-9,9],[-5,-24],[-12,-12],[-12,-7],[-5,-9],[-1,-14],[-4,-3],[-5,-1],[-7,-6],[-18,-26],[0,-3],[-18,-4],[-9,9],[-9,13],[-15,6],[-10,-2],[-41,-22],[-9,-9],[-10,-11],[-11,-11],[-15,-4],[-74,-3],[-10,-4],[-9,-8],[-10,-14],[-9,-6],[-27,-3],[-14,-10],[-15,11],[-13,-9],[-14,-16],[-16,-8],[-38,40],[-7,-11],[-6,-29],[-21,-24],[-4,-2],[-9,0],[-4,-4],[-3,-8],[-1,-18],[-3,-9],[-14,-15],[-32,-17],[-13,-21],[-4,-18],[-30,-35],[-131,3],[-73,49],[-19,44],[-45,70],[-17,40],[-17,78],[-46,99],[-77,56],[-83,29],[-34,39],[-39,23],[-94,0]],[[9034,5631],[-8,-6],[-18,0],[-31,20],[-19,4],[-13,-6]],[[8670,9236],[1,3],[25,12],[21,-3],[60,-31],[11,-2],[23,5],[10,-2],[11,-11],[5,-13],[5,-14],[6,-11],[11,-7],[34,-4],[7,-10],[-4,-16],[-6,-20],[0,-18],[18,-25],[23,0],[24,10],[20,5],[18,-9],[62,-52],[38,-23],[81,-31],[19,-17],[19,-16],[10,-15],[18,-35],[9,-13],[10,-7],[22,-6],[9,-6],[16,-24],[14,-34],[11,-39],[8,-36],[14,-80],[1,-39],[-15,-26],[-12,-10],[-6,-7],[-4,-7],[-1,-19],[4,-15],[8,-13],[11,-9],[6,-7],[3,-9],[-1,-9],[-4,-10],[1,-50],[10,-42],[14,-40],[10,-43],[9,-91],[10,-35],[25,-36],[2,-105],[24,-119],[85,-267],[47,-100],[20,-54],[40,-162],[17,-48],[18,-25],[10,-24],[8,-25],[12,-27],[17,-20],[17,-14],[11,-20],[-2,-42],[-9,-24],[-12,-21],[-10,-23],[-1,-34],[8,-29],[31,-40],[11,-25],[2,-14],[-3,-20],[0,-11],[9,-48],[0,-13],[-3,-11],[-11,-23],[-3,-13],[1,-19],[4,-10],[4,-9],[2,-13],[9,-207],[2,-52],[-14,-73],[-40,-51],[-132,-94],[-167,-50],[-88,-54],[-88,-76],[-122,-196],[-40,-51],[-18,-31],[-47,-99],[4,-1],[7,-6]],[[9034,5631],[7,-7],[11,-17],[3,-22],[-7,-31],[22,-15],[72,-20],[15,6],[13,-14],[62,-24],[21,-3],[0,-10],[-7,-16],[3,-3],[14,6],[13,-6],[9,-10],[9,-1],[14,17],[4,-8],[10,-8],[6,-8],[17,17],[4,-13],[-3,-21],[-4,-6],[28,-58],[19,-7],[47,2],[19,-12],[7,-13],[5,-14],[6,-11],[9,-5],[12,-9],[4,-19],[1,-20],[9,-21],[14,-65],[11,-29],[-15,-13],[-14,-21],[-18,-48],[-7,-31],[-1,-56],[-6,-19],[4,-2],[2,0],[0,-2],[1,-8],[-12,-7],[-4,-37],[-11,-14],[15,-31],[19,-27],[-12,-39],[-19,-21],[-21,-13],[-17,-21],[-4,-12],[-4,-19],[-5,-39],[3,-9],[7,-12],[2,-11],[-9,-4],[-4,-5],[3,-11],[6,-12],[5,-7],[-12,-17],[-7,-4],[-8,-2],[0,-12],[11,-13],[1,-17],[-5,-46],[3,-25],[9,-24],[22,-40],[-7,-1],[-7,1],[13,-27],[16,-6],[13,-9],[4,-23],[2,-10],[7,-24],[15,-12],[18,-7],[15,-10],[-16,-35],[2,-10],[40,-65],[8,-20],[-11,-6],[-7,-12],[-2,-17],[7,-23],[-19,-8],[-17,-20],[-14,-25],[-5,-23],[6,-37],[16,-8],[19,-1],[17,-12],[55,-130],[23,-29],[51,-35],[47,-52],[11,-21],[-16,-9],[-4,-16],[15,-34],[26,-44],[9,-23],[11,-71],[0,-11],[-4,-13],[7,-7],[13,-6],[12,-9],[11,-18],[3,-14],[1,-16],[6,-22],[15,-27],[67,-67],[83,-50],[13,-22],[-12,-29],[-31,-7],[-33,-1],[-20,-9],[-26,16],[-28,-4],[-23,-19],[-12,-29],[2,-33],[14,-22],[24,-12],[28,-4],[0,-10],[-13,-30],[14,-26],[27,-19],[27,-7],[-6,-39],[8,-36],[25,-67],[-23,-44],[10,-81],[7,-23],[6,-5],[-2,-3],[-12,-20],[-19,-22],[-64,-37],[-3,-20],[2,-33],[-2,-23],[-5,-18],[-22,-56],[-52,-24],[-181,-3],[-34,-12],[-18,-9],[-13,-15],[-11,-25],[-12,-45]],[[9516,2281],[-1,0],[-100,64],[-32,53],[-63,44],[-74,9],[-57,-6],[-26,-12],[-82,-102],[-65,-58],[-75,-17],[-224,8],[-137,42],[-10,25],[34,10],[16,10],[-27,20],[-139,-30],[-35,18],[-26,43],[-7,43],[16,35],[25,36],[31,12],[31,-5],[28,18],[-12,41],[-23,48],[-3,24],[1,23],[-9,16],[-157,92],[-233,65],[-18,35],[45,93],[14,43],[-32,31],[-35,24],[-38,15],[-40,-1],[-66,-51],[-74,4],[-132,98]],[[9516,2281],[-1,-3],[-14,-22],[-79,-94],[-29,-25],[-54,-46],[-45,-39],[-228,-273],[-30,-51],[-37,-32],[-40,-48],[-108,-176],[-42,-47],[-5,-15],[-8,-34],[-5,-12],[-8,-10],[-18,-7],[-9,-7],[-9,-13],[-62,-124],[-17,-19],[-11,-5],[-21,-5],[-11,-10],[-7,-15],[-18,-55],[-75,-113],[-25,-66],[19,-61],[7,-25],[6,-73],[4,-20],[2,-9],[13,-23],[22,-25],[6,-14],[4,-27],[13,-157],[-3,-24],[-17,-53],[-14,-60],[-16,-37],[-5,-21],[6,-9],[4,3],[13,19],[16,-12],[-1,-1],[-4,-1],[32,-26],[30,-43],[18,-15],[45,-28],[12,-18],[0,-14],[-9,-34],[-1,-23],[4,-16],[16,-31],[1,-4],[2,-6],[-11,-27],[-20,10],[-23,22],[-17,11],[-11,4],[-19,29],[-10,11],[-11,1],[-22,-8],[-36,3],[-22,-2],[-21,3],[-25,19],[-37,47],[-20,7],[-21,-23],[-35,19],[-78,4],[-37,12],[-50,58],[-21,9],[-55,-2],[-47,16],[-8,7],[-2,11],[3,11],[0,12],[-11,13],[-9,2],[-23,-3],[-10,1],[-12,5],[-32,24],[-71,15],[-28,23],[-7,55],[-13,60],[-28,52],[-36,38],[-54,30],[-37,38],[-18,12],[-20,-4],[-17,-18],[-14,-19],[-11,-8],[-15,14],[-34,58],[-16,19],[-10,4],[-16,-4],[-7,1],[-10,8],[-19,20],[-10,8],[-19,5],[-71,-9],[-15,-7]],[[7376,724],[-1,1],[-1,4],[-36,114],[-18,125],[6,59],[-4,61],[-34,119],[-60,72],[-108,51],[-29,30],[17,43],[99,85],[-13,49],[-38,22],[-28,2],[-21,28],[-8,24],[-9,20],[1,66],[8,75],[-27,259],[6,96],[37,70],[31,81],[11,144]],[[3887,1797],[-16,4],[4,-39],[5,-2],[8,3],[9,-1],[7,-11],[2,-17],[-5,-8],[-33,-11],[-40,-1],[-18,-5],[-29,-29],[-37,-12],[-36,8],[-65,71],[-11,17],[-8,21],[-5,21],[-7,19],[-11,14],[-10,11],[-3,10],[3,9],[10,9],[0,1],[0,1],[0,2],[0,1],[-14,4],[-27,15],[-13,3],[-11,-2],[-9,-6],[-10,-2],[-15,8],[-49,68],[-2,12],[8,17],[10,7],[49,2],[26,11],[43,28],[20,19],[9,26],[-15,32],[-11,11],[-2,18],[2,15],[11,32],[2,6],[1,5],[-1,4],[-2,4],[-19,28],[-5,5],[-17,7],[-7,-4],[-2,-12],[-6,-14],[-29,-45],[-17,-15],[-24,-7],[-89,17],[-47,-5],[-30,-39],[-5,-3],[-5,-2],[-5,2],[-5,3],[-10,32],[-21,20],[-23,-2],[-10,-32],[-14,12],[-7,17],[-2,22],[2,22],[-5,4],[-4,5],[-4,6],[-2,7],[18,8],[-21,9],[-46,-1],[-24,7],[-35,25],[-10,11],[-6,15],[-6,22],[-4,19],[3,5],[-12,4],[-31,-7],[-13,0],[-13,7],[-23,22],[-11,7],[-103,31],[-23,-3],[-15,10]],[[7376,724],[-19,-8],[-14,5],[-37,-9],[-17,-1],[-97,31],[-56,33],[-14,4],[-18,-14],[-31,-51],[-17,-18],[-22,-4],[-15,11],[-14,18],[-18,14],[-16,2],[-40,-10],[-15,-8],[-21,-25],[13,-16],[23,-15],[12,-26],[-8,-16],[-19,-4],[-21,0],[-16,-4],[-20,-22],[-33,-54],[-21,-21],[-24,-9],[-18,4],[-16,13],[-17,19],[-18,16],[-16,2],[-17,-1],[-22,4],[-16,13],[-89,118],[-15,4],[-38,-7],[-22,3],[-9,0],[-10,-7],[-16,-22],[-11,-12],[-24,-9],[-22,6],[-84,48],[-16,-4],[1,-30],[-52,-2],[-41,18],[-12,-1],[-10,-8],[-4,-11],[-2,-16],[-5,-24],[-7,-24],[-5,-7],[-77,-8],[-15,-10],[-22,-29],[-10,-10],[-22,3],[-2,3],[-6,-9],[-19,-48],[-6,-20],[-7,-19],[-12,-17],[-6,-60],[-10,-42],[-19,-17],[-34,18],[-51,54],[-28,16],[-32,-4],[-18,-15],[-19,-22],[-14,-17],[-22,-9],[-46,4],[-26,11],[-13,19],[1,1],[4,22],[37,69],[5,4],[6,2],[6,4],[2,10],[-2,7],[0,1],[-8,9],[-1,4],[-1,0],[-13,11],[-4,6],[1,7],[8,13],[1,7],[-6,83],[-5,28],[-9,33],[-9,8],[-34,-15],[-20,-3],[-22,3],[-57,22],[2,20],[0,27],[1,10],[5,10],[-40,1],[-21,6],[-17,15],[-5,12],[-16,54],[-19,87],[-12,34],[-23,17],[-8,9],[-16,33],[-9,4]]],
transform:{scale:[.001002023542254234,.000584489561156115],translate:[14.12392297300002,48.99401316400011]}},m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();