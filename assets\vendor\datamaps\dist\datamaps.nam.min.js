!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo={type:"Topology",objects:{nam:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Karas"},id:"NA.KA",arcs:[[0,1]]},{type:"Polygon",properties:{name:"Hardap"},id:"NA.HA",arcs:[[2,3,-2,4,5,6]]},{type:"Polygon",properties:{name:"Khomas"},id:"NA.KH",arcs:[[-7,7,8,9]]},{type:"Polygon",properties:{name:"Kunene"},id:"NA.KU",arcs:[[10,11,12,13,14,15]]},{type:"Polygon",properties:{name:"Erongo"},id:"NA.ER",arcs:[[16,-8,-6,17,-15]]},{type:"Polygon",properties:{name:"Otjozondjupa"},id:"NA.OD",arcs:[[18,19,-9,-17,-14,20,21]]},{type:"Polygon",properties:{name:"Omusati"},id:"NA.OS",arcs:[[22,23,-11,24]]},{type:"Polygon",properties:{name:"Oshana"},id:"NA.ON",arcs:[[25,-12,-24,26]]},{type:"Polygon",properties:{name:"Ohangwena"},id:"NA.OW",arcs:[[27,28,-27,-23,29]]},{type:"Polygon",properties:{name:"Omaheke"},id:"NA.OH",arcs:[[30,-3,-10,-20]]},{type:"Polygon",properties:{name:"Kavango"},id:"NA.OK",arcs:[[31,32,-22,33,-28,34]]},{type:"Polygon",properties:{name:"Oshikoto"},id:"NA.OT",arcs:[[-34,-21,-13,-26,-29]]},{type:"Polygon",properties:{name:"Caprivi"},id:"NA.CA",arcs:[[-32,35]]}]}},arcs:[[[6102,2781],[0,-35],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[1,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-33],[-1,-25],[-23,-6],[-8,0],[-24,3],[-8,-1],[-19,-11],[-33,-30],[-22,-6],[-36,-3],[-16,-6],[-15,-11],[-13,-7],[-74,-5],[-6,-2],[-8,-5],[-5,-6],[-4,-7],[-7,-8],[-4,-7],[-6,-15],[-5,-3],[-7,-3],[-4,-7],[-21,-67],[-8,-12],[-13,-10],[-15,-7],[-60,-16],[-11,-4],[-8,1],[-17,14],[-11,0],[-9,-9],[-9,-11],[-12,-24],[-3,-17],[5,-18],[27,-47],[0,-10],[-9,-6],[-24,-2],[-5,-3],[-8,-13],[-6,-6],[-42,-22],[-30,-10],[-29,-2],[-12,16],[-4,4],[-8,2],[-26,4],[-5,2],[-8,8],[-18,30],[-13,11],[-154,22],[-141,-20],[-27,-15],[-16,-5],[-3,2],[-9,8],[-4,2],[-4,-1],[-6,-4],[-4,-1],[-15,-2],[-8,-3],[-5,-4],[-15,-6],[-18,3],[-31,12],[-16,1],[-65,-9],[-27,-9],[-13,0],[-62,21],[-29,15],[-45,37],[-25,16],[-26,11],[-124,27],[-23,-2],[-8,-3],[-15,-10],[-7,-4],[-10,0],[-23,7],[-16,7],[-4,16],[1,21],[-4,18],[-11,7],[-13,-2],[-14,-7],[-15,-4],[-30,-3],[-33,-8],[-28,4],[-1,25],[9,35],[5,33],[-8,18],[-27,10],[-6,14],[-4,19],[-20,26],[-6,15],[3,12],[9,11],[13,8],[12,4],[12,8],[6,20],[2,23],[-1,17],[-4,10],[-12,15],[-5,9],[-3,10],[-1,9],[-2,8],[-7,11],[-2,8],[-1,10],[-3,9],[-9,4],[-19,-1],[-47,-11],[-23,5],[-16,19],[-4,39],[2,19],[1,19],[-7,14],[-19,6],[-14,6],[-10,15],[-8,18],[-10,12],[-9,3],[-7,1],[-15,-4],[-8,-4],[-25,-19],[-20,5],[-8,-2],[-11,-11],[-8,-4],[-8,2],[-7,9],[-6,1],[-5,-3],[-12,-15],[-3,-2],[-5,-66],[-6,-8],[-3,0],[-4,3],[-12,0],[-3,2],[-3,1],[-5,-3],[-1,-5],[2,-5],[13,-16],[0,-6],[-11,-3],[-16,0],[-4,-3],[-3,-8],[2,-6],[6,-8],[4,-10],[-1,-10],[-7,-6],[-8,3],[-7,5],[-6,3],[-13,-6],[-4,-15],[3,-17],[6,-13],[15,-24],[5,-15],[-7,-7],[-11,-7],[-4,-17],[-1,-19],[-2,-14],[-8,-14],[-13,-18],[-15,-12],[-12,1],[-3,8],[-3,10],[-3,10],[-6,4],[-8,-3],[-56,-55],[-28,-9],[-21,-11],[-19,-13],[-13,-6],[-14,-10],[-8,-13],[-9,-9],[-19,3],[-9,6],[-45,37],[-23,26],[-11,17],[-79,79],[-13,6],[-6,5],[-45,61],[-47,55],[-98,71],[-19,20],[-38,48],[-53,43],[-10,11],[-23,37],[-24,26],[-4,5],[-1,10],[5,17],[1,8],[-9,26],[-20,28],[-74,74],[-9,15],[-4,20],[0,43],[-1,6],[-5,11],[-71,114],[-11,27],[-9,13],[-13,5],[-6,8],[-22,44],[-30,39],[-4,9],[-10,66],[-4,8],[-5,6],[-5,7],[-2,10],[6,34],[-11,54],[5,12],[-14,31],[-5,16],[-1,19],[1,34],[-5,17],[-11,14],[-6,3],[-13,1],[-7,2],[-1,4],[-5,9],[-6,7],[-2,0],[-2,19],[-5,15],[-24,50],[-4,14],[3,23],[-7,16],[-11,17],[-6,14],[-2,20],[2,16],[6,12],[11,4],[8,2],[7,3],[6,0],[2,-8],[0,-32],[5,0],[3,17],[15,43],[0,9],[-5,11],[-15,51],[-9,50],[-17,31],[-25,26],[-28,19],[-44,19],[-6,7],[-6,65],[3,63],[-15,23],[-2,6],[3,9],[4,1],[6,-2],[7,3],[10,27],[-1,35],[-8,36],[-11,28],[-34,56],[-4,27],[13,31],[-6,0],[-38,46],[-12,22],[-2,10],[0,7],[2,11],[0,66],[3,17],[22,44],[2,13],[0,19],[-3,17],[-17,17],[-4,21],[-3,38],[-19,50],[-4,39],[-7,31],[-1,17],[23,62],[-2,10],[15,59],[2,34],[-7,32],[-10,22]],[[2298,3297],[2,0],[684,-5],[18,-67],[58,-411],[3,-8],[3,-7],[7,-1],[9,2],[27,9],[9,2],[10,0],[8,-6],[8,-8],[149,-208],[10,-8],[9,0],[10,4],[9,6],[15,2],[18,-1],[34,-12],[11,1],[5,5],[-39,207],[-1,8],[1,8],[6,4],[8,4],[37,12],[9,1],[9,-4],[8,-5],[7,-6],[5,-6],[1,-4],[-4,-6],[-3,-5],[0,-8],[11,-39],[31,23],[12,17],[28,62],[9,15],[6,-2],[7,-8],[17,-35],[6,-8],[6,-3],[10,-1],[13,2],[130,-27],[5,-4],[13,-6],[18,-14],[32,-4],[33,-11],[21,-14],[7,-7],[3,-11],[1,-10],[2,-9],[4,-5],[14,2],[56,17],[22,0],[39,15],[11,-2],[122,-54],[3,145],[16,11],[22,9],[13,7],[5,3],[5,-1],[8,-4],[10,-4],[141,-15],[97,9],[357,87],[165,15],[50,-27],[17,-14],[56,-30],[322,-29],[12,4],[67,36],[15,16],[6,5],[7,4],[7,-5],[4,-4],[65,-101],[7,-8],[6,-2],[9,4],[23,19],[6,3],[20,4],[35,-1],[40,-6],[19,-7],[18,-14],[11,-5],[11,-4],[71,-14],[32,-14],[9,-2],[15,5],[83,41],[11,3],[15,1],[10,-9],[34,5]],[[4966,4543],[-1,-11],[68,-139],[5,-4],[4,3],[13,14],[7,6],[7,3],[8,-6],[8,-7],[37,-40],[11,-7],[15,-5],[60,-12],[10,1],[138,58],[6,0],[40,-2],[5,-2],[0,-5],[-3,-7],[-1,-7],[2,-6],[15,-3],[69,-2],[3,1],[4,4],[33,44],[9,5],[11,1],[18,-4],[7,-7],[3,-8],[3,-148],[51,-6],[470,-3]],[[6101,4242],[0,-23],[0,-143],[0,-143],[0,-144],[1,-143],[0,-143],[0,-10],[0,-47],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-46],[0,-47],[0,-47],[0,-11]],[[2298,3297],[-16,32],[-9,33],[-2,67],[-9,32],[-43,83],[-17,25],[-37,37],[-10,25],[-25,30],[-5,21],[3,9],[10,13],[3,4],[0,35],[-4,15],[-16,31],[-16,43],[-40,80],[-16,57],[-14,28],[-5,38],[-6,13],[4,16],[0,28],[-3,29],[-6,18],[5,12],[5,23],[5,11],[5,2],[7,1],[6,3],[3,8],[10,20],[-6,10],[1,47],[-13,40],[-3,15],[11,120],[-4,29],[-8,23]],[[2043,4503],[415,-42],[18,-29],[6,-6],[3,-3],[3,-2],[6,-2],[8,-2],[17,-1],[54,5],[74,-14],[16,2],[10,-1],[84,-24],[215,-99]],[[2972,4285],[-6,-66],[4,-20],[8,-23],[45,-24],[20,-2],[55,3],[7,2],[4,5],[4,7],[2,8],[7,7],[9,5],[34,1],[95,31],[5,1],[23,-3],[9,0],[8,4],[45,25],[5,2],[-1,-6],[-8,-16],[-3,-10],[-2,-11],[3,-16],[-3,-6],[-6,-4],[-14,-2],[-4,-1],[-5,-4],[0,-9],[4,-12],[41,-33],[41,-9],[-7,20],[-14,51],[8,22],[29,29],[6,5],[6,0],[24,-6],[66,-5],[7,0],[3,3],[3,5],[5,10],[1,6],[0,5],[-6,18],[-3,6],[-5,4],[-7,3],[-56,16],[-7,0],[-13,-7],[-6,-1],[-64,16],[-5,4],[-1,5],[1,8],[12,48],[35,74],[5,7],[4,4],[10,-8],[7,-7],[2,-5],[2,-41],[2,-6],[6,-3],[49,0],[8,2],[7,5],[76,78],[2,7],[0,8],[-5,26],[-2,7],[-5,5],[-50,29],[-3,2],[-4,4],[1,6],[3,7],[85,176],[11,15],[8,1],[18,-9],[13,-12],[16,-4],[14,-2],[17,4],[27,1],[33,-9],[60,15],[37,17],[44,33],[49,11],[31,-1],[32,-11],[13,-7],[6,-11],[4,-22],[1,-35],[12,-19],[25,-5],[67,11],[50,2],[42,10],[12,1],[2,0],[-27,-28],[-5,-7],[-2,-6],[0,-8],[4,-43],[-1,-7],[-4,-4],[-27,-8],[-3,-5],[0,-8],[9,-51],[3,-3],[4,-2],[12,-4],[6,-3],[2,-3],[2,-7],[6,-74],[4,4],[124,-2],[8,0],[6,3],[2,7],[7,8],[11,7],[140,3],[37,-10],[8,-4],[6,-5],[26,-35],[6,-5],[7,-2],[29,14],[7,2],[6,-5],[13,-3],[11,1],[14,4],[57,26],[31,8],[9,5],[2,9],[1,17],[2,8],[7,5],[63,31],[51,31],[17,1],[21,-34]],[[2972,4285],[62,6],[14,15],[5,32],[0,5],[-8,33],[-1,7],[9,119],[-1,8],[-1,7],[-6,4],[-57,28],[-5,3],[-5,5],[1,7],[2,8],[20,49],[4,8],[4,6],[8,2],[24,5],[6,1],[4,3],[1,5],[0,4],[-18,101],[60,21],[7,4],[5,4],[-3,6],[-33,42],[-4,8],[1,8],[1,8],[14,42],[1,8],[1,9],[-2,18],[-3,17],[-2,8],[-1,3],[0,4],[9,76],[5,11],[8,12],[20,10],[11,9],[13,3],[9,1],[44,1],[8,2],[4,8],[1,10],[1,86],[-1,7],[-2,7],[-5,2],[-7,1],[-55,-2],[-3,2],[-3,4],[13,19],[58,63],[10,6],[11,0],[45,-13],[9,-1],[10,5],[11,6],[22,20],[9,16],[26,23],[7,8],[9,2],[9,0],[11,-2],[10,0],[8,1],[3,6],[1,4],[0,37],[4,13],[8,14],[35,27],[30,14]],[[3492,5484],[59,14],[8,-5],[10,-4],[4,-5],[5,3],[6,6],[11,16],[5,7],[7,4],[7,-3],[15,-11],[8,-5],[6,3],[7,4],[8,0],[22,-3],[22,1],[8,7],[3,8],[-3,7],[-3,6],[-7,1],[-25,-4],[-6,1],[-3,5],[-1,7],[1,4],[4,6],[21,39],[6,7],[7,3],[7,0],[4,-2],[4,-6],[6,-18],[6,-7],[9,-6],[21,-2],[9,-5],[10,-10],[7,2],[34,13],[10,0],[8,5],[7,6],[15,16],[8,7],[9,5],[6,-4],[5,-8],[5,-8],[5,-7],[3,-1],[7,5],[49,43],[7,6],[8,3],[5,-5],[5,-8],[4,-9],[4,-8],[6,-5],[8,2],[71,35],[9,10],[5,9],[0,26],[1,8],[4,8],[13,16],[6,7],[6,5],[4,-4],[3,-7],[4,-18],[3,-7],[3,-3],[7,1],[35,16],[8,5],[5,5],[1,8],[-1,15],[1,4],[5,0],[30,-14],[7,-1],[4,5],[3,7],[8,23],[0,5],[-2,7],[-15,45],[-1,8],[5,2],[15,-1],[9,1],[7,5],[7,7],[6,6],[3,5],[5,16],[7,8],[8,7],[25,15],[4,4],[9,23],[5,9],[7,5],[8,3],[35,10],[8,1],[6,-1],[7,-6],[12,-13],[5,-2],[5,1],[21,7],[35,8],[20,10],[9,7],[15,16],[26,22],[35,17],[31,7],[16,-2],[78,22]],[[4726,6040],[6,-49],[-10,-20],[-18,-23],[-12,-22],[-3,-10],[0,-6],[1,-4],[3,-3],[2,-3],[11,-11],[5,-7],[6,-11],[3,-3],[3,-2],[3,-1],[3,-5],[2,-7],[2,-18],[-4,-8],[-6,-5],[-5,-3],[-6,-6],[-6,-14],[-21,-32],[-2,-8],[2,-5],[4,-7],[2,-7],[0,-14],[-4,-7],[-7,-6],[-26,-11],[-7,-1],[-11,0],[-7,0],[-6,-3],[-3,-7],[-8,-74],[2,-13],[4,-13],[20,-44],[24,-34],[9,-8],[9,-2],[30,0],[6,-4],[4,-6],[20,-33],[10,-12],[14,-12],[6,-6],[-3,-5],[-25,-14],[-6,-6],[3,-5],[6,-6],[19,-16],[1,0],[1,1],[23,20],[6,4],[4,-3],[2,-6],[21,-108],[9,-87],[4,-17],[21,-65],[2,-24],[1,-89],[6,-5],[7,0],[52,21],[6,0],[2,-5],[0,-8],[-2,-16],[-1,-7],[-2,-5],[-7,-4],[-33,-14],[-3,-2],[-6,-5],[0,-8],[1,-8],[3,-8],[4,-7],[11,-7],[16,-21],[7,-3],[8,-2],[16,-2],[7,-2],[4,-2],[3,-7],[7,-19],[6,-27],[-4,-6],[-7,-3],[-42,-3],[-7,-2],[-6,-6],[-5,-9],[-3,-11],[-1,-10],[5,-8],[104,-89],[7,-9],[0,-7],[-2,-11],[-44,-114]],[[1847,9635],[-1,-18],[-2,-8],[5,-6],[-4,-14],[-14,-28],[-7,-17],[-23,-38],[-15,-20],[2,-22],[31,-20],[4,-18],[-2,-22],[-3,-16],[-7,-13],[-14,-17],[10,-39],[71,-158],[22,-101],[12,-311],[-7,-118],[4,-57],[30,-100],[20,-45],[13,-47],[-13,-85],[-5,-86],[31,-97],[15,-23],[20,-18],[17,-28],[3,-37],[-8,-14],[-9,-11],[-6,-35],[92,-3],[14,31],[1,45],[-41,-2],[-3,36],[1,33],[35,7],[203,2],[170,24],[46,-10],[44,-4],[79,27]],[[2658,8134],[-2,-46],[138,2],[26,4],[30,-5],[94,-25],[62,-8],[27,2],[26,-6],[59,-34]],[[3118,8018],[89,-14],[52,-15],[42,-5],[99,-32],[137,-21],[44,-1],[47,-13],[44,-25],[9,-8]],[[3681,7884],[1,-13],[6,-37],[-8,-305],[7,-31],[-42,-30],[-20,-6],[-65,0],[-4,-2],[-3,-2],[0,-47],[0,-9],[12,-36],[1,-7],[-5,-2],[-7,-1],[-21,1],[-132,-72],[-8,-7],[-4,-6],[2,-68],[4,-28],[1,-10],[-2,-10],[-4,-11],[-8,-7],[-11,-7],[-11,-5],[-11,-2],[-10,1],[-13,10],[-12,3],[-10,1],[-10,-1],[-6,-2],[-37,-19],[-7,-1],[-46,0],[-7,-2],[-52,-22],[-25,-18],[-10,-9],[-47,-59],[-12,-8],[-45,-20],[-10,-4],[-11,1],[-61,14],[-51,3]],[[2867,6992],[-33,7],[-31,12],[-15,7],[-11,11],[-6,8],[-4,6],[-7,5],[-53,7],[-7,-8],[-4,-10],[-18,-21],[-41,-59],[-6,-5],[-103,-103],[-8,-10],[-3,-11],[-5,-4],[-35,-23],[-16,-9],[-39,-34],[-7,-8],[-5,-4],[-12,-5],[-3,-2],[-2,-3],[-5,-10],[-3,-4],[-4,-3],[-5,-3],[-7,-4],[-4,-5],[-3,-6],[-3,-4],[-3,-2],[-9,-2],[-15,0],[-3,-1],[-4,-2],[-18,-15],[-16,-9],[-6,-6],[-8,-9],[-5,-4],[-4,-2],[-7,-1],[-13,-5],[-19,-9],[-6,-2],[-14,0],[-7,-1],[-11,-3],[-5,0],[-23,3],[-41,-1],[-6,1],[-27,13],[-5,1],[-6,0],[-6,-1],[-10,-2],[-21,-1],[-17,-3],[-5,1],[-4,2],[-16,12],[-3,2],[-5,2],[-31,3],[-19,-4],[-38,-14],[-4,-1],[-5,0],[-4,1],[-5,1],[-46,22],[-6,1],[-7,0],[-13,-2],[-7,-3],[-7,-4],[-8,-5],[-5,-3],[-5,-2],[-21,-1],[-16,-4],[-10,-5],[-20,-11],[-6,-2],[-4,-1],[-34,0],[-55,-22],[-15,-11],[-10,-12],[-6,-3],[-12,-1],[-4,-2],[-3,-4],[-2,-5],[-3,-6],[-4,-3],[-7,-1],[-9,-1],[-26,-7],[-6,-3],[-3,-2],[-8,-13],[-7,-7],[-7,-4],[-25,-9],[-47,-32]],[[1411,6471],[-19,25],[-66,125],[-31,44],[-9,31],[-11,11],[-20,17],[-15,29],[-7,28],[-8,68],[-20,75],[-64,132],[-18,74],[-4,35],[-15,68],[-26,70],[-17,29],[-30,10],[-32,23],[-25,46],[-23,103],[-57,117],[-7,24],[-32,45],[-54,106],[-6,19],[-8,12],[-19,42],[-6,9],[-33,63],[-10,26],[-4,16],[-5,38],[-8,18],[-24,34],[-15,29],[-36,96],[-15,25],[-22,29],[-27,22],[-7,11],[-9,60],[-6,17],[-121,173],[-82,58],[-37,44],[-4,4],[-13,20],[-8,4],[-7,6],[-37,24],[-12,43],[-2,16],[-10,26],[-1,9],[0,9],[0,8],[1,8],[-17,27],[-29,81],[-4,6],[-61,68],[-11,38],[-6,49],[-13,40],[-8,29],[-10,80],[-17,61],[-11,86],[-9,42],[-5,28],[-7,44],[3,66],[25,167],[8,12],[10,-2],[13,-7],[7,-2],[11,1],[4,4],[2,5],[4,7],[14,10],[30,16],[35,28],[30,15],[34,11],[34,5],[5,2],[5,1],[5,0],[7,-5],[26,-9],[8,2],[4,4],[4,2],[7,-2],[4,-5],[2,-5],[1,-7],[3,-6],[4,-5],[8,-5],[8,-5],[8,-2],[7,-4],[3,-8],[2,-8],[3,-3],[53,6],[48,-2],[10,4],[10,9],[8,1],[18,-11],[14,-6],[43,-3],[26,-7],[9,1],[18,10],[33,31],[18,7],[19,3],[14,7],[26,24],[33,17],[25,9],[5,7],[6,12],[6,7],[6,2],[13,5],[7,4],[3,8],[1,9],[4,8],[17,6],[15,7],[23,6],[38,25],[80,15],[17,6],[16,1],[13,-11],[10,6],[6,-5],[4,-8],[8,-4],[17,-3],[9,0],[7,3],[0,-7],[19,10],[12,3],[5,-3],[4,-1],[18,5],[13,4],[14,-5],[26,-20],[13,-4],[18,-2],[15,-7],[11,-12],[10,-33],[10,-11],[7,-13],[-7,-24],[63,-38],[64,-58],[71,-43],[70,-42],[9,-8],[34,-50],[11,-9],[17,-3],[9,1],[12,9],[6,2],[9,-2],[13,-4],[16,-2],[12,-4],[9,0],[8,2],[12,8],[5,2],[32,-6],[17,2],[7,17],[0,4],[9,0]],[[2867,6992],[-3,-89],[2,-8],[5,-12],[37,-12],[6,-1],[8,1],[25,6],[8,0],[4,-3],[2,-5],[12,-43],[7,-9],[58,-57],[148,-84],[44,-45],[35,-14],[31,-19],[8,-4],[6,2],[4,4],[32,36],[4,4],[3,-2],[2,-5],[5,-28],[5,-10],[8,-11],[20,-18],[10,-8],[12,-7],[6,-4],[25,-27],[3,-4],[5,-12],[4,-6],[5,-3],[7,-2],[72,-9],[0,-48],[-6,-27],[22,-103],[-3,-25],[-29,-30],[-29,-41],[-9,-11],[-3,-8],[-3,-10],[-7,-66],[-23,-94],[29,-95],[1,-19],[4,-14],[3,-2],[6,-3],[7,-7],[12,-19],[1,-13],[-1,-10],[-4,-8],[-3,-8],[-1,-8],[4,-7],[5,-8],[2,-4],[0,-4],[-3,-4],[-5,-23],[-1,-24],[-6,-8],[-6,-4],[-8,1],[-7,-1],[-3,-2],[3,-7],[24,-37],[3,-8],[-5,-3],[-15,-7],[-8,-11],[-25,-19],[-4,-6],[1,-8],[20,-50],[0,-6],[-1,-4],[-5,-7],[-3,-4],[-1,-7],[2,-8],[4,-10],[24,-41]],[[2043,4503],[-28,76],[-9,37],[0,9],[6,7],[18,34],[9,9],[0,-17],[4,-8],[4,4],[3,19],[-2,32],[-13,97],[-8,29],[-13,22],[-10,23],[-8,27],[-5,29],[-3,55],[1,8],[3,8],[10,15],[2,8],[0,35],[2,1],[5,0],[2,-3],[-4,-7],[3,-5],[4,-8],[3,-4],[-4,-16],[-2,-17],[6,-9],[15,8],[-1,-10],[-9,-25],[11,11],[17,34],[10,7],[9,8],[5,19],[4,39],[-5,145],[-14,110],[-18,29],[-8,37],[-26,58],[-15,49],[-23,53],[-5,6],[-48,57],[-15,27],[-1,21],[-2,7],[-113,157],[-56,54],[-6,14],[-61,59],[-14,8],[0,9],[3,8],[2,4],[5,21],[0,5],[-2,11],[-4,8],[-59,93],[-11,54],[-23,59],[-76,106],[-46,81],[-28,37]],[[6836,8150],[0,-66],[0,-104],[1,-104],[0,-104],[0,-104],[0,-104],[1,-104],[0,-105],[0,-104],[1,-83]],[[6839,7168],[-1,0],[-18,-1],[-13,-7],[-11,-2],[-47,-3],[-40,-13],[-10,-2],[-52,-2],[-27,2],[-38,12],[-79,11],[-30,0],[-26,-5],[-15,1],[-34,7],[-14,4],[-19,10],[-13,4],[-599,72],[-20,2],[-44,0],[-21,-4],[-14,-7],[-24,-24],[-22,-12],[-355,-143],[-8,-4],[-26,-23],[-21,-25],[-132,-232],[-4,-12],[-23,-115],[-5,-17],[-20,-43],[-5,10],[-6,6],[-7,6],[-12,-1],[-53,-13],[-15,-10],[-8,-32],[-3,-25],[0,-7],[3,-1],[7,0],[24,6],[6,0],[2,-3],[0,-7],[-2,-17],[-2,-8],[-4,-8],[-9,-8],[-27,-17],[-6,-6],[0,-8],[1,-8],[2,-8],[11,-21],[2,-7],[-4,0],[-78,22],[-7,1],[-7,-1],[-8,-3],[-7,-5],[-7,-7],[-2,-16],[-1,-38],[1,-6],[4,-2],[7,-1],[15,2],[13,3],[3,-2],[2,-6],[6,-26],[1,-9],[-2,-7],[-11,-11],[-1,-8],[0,-8],[4,-35],[0,-9],[-2,-7],[-19,-11],[-1,-5],[0,-4],[2,-9],[2,-7],[0,-6],[-2,-24],[6,-35],[0,-10],[-3,-11],[-20,-27],[-16,-10],[-47,-23],[-8,-9]],[[3681,7884],[0,1],[4,8],[6,5],[202,111],[70,93],[31,53],[53,14],[10,-3],[9,-6],[4,-9],[7,-7],[11,-5],[24,1],[13,-5],[10,-8],[9,-17],[2,-6],[0,-7],[-3,-24],[1,-7],[5,-2],[7,-1],[7,1],[4,2],[1,7],[0,7],[2,8],[3,5],[19,6],[16,2],[4,-1],[3,-6],[3,-26],[0,-8],[-1,-6],[-5,-2],[-14,-2],[-3,-3],[-2,-7],[2,-33],[1,-8],[2,-4],[6,-6],[8,-4],[10,-3],[16,3],[11,-2],[9,-3],[25,-17],[4,-4],[3,-5],[1,-13],[5,-6],[14,-12],[9,-4],[11,-3],[18,1],[13,-5],[13,-8],[26,-24],[3,-3],[4,-2],[46,-2],[11,3],[23,11],[18,5],[9,0],[5,-5],[3,-7],[5,-15],[4,-7],[6,0],[10,5],[34,21],[8,6],[5,8],[-4,10],[-26,35],[-7,11],[-2,10],[0,11],[2,19],[5,6],[3,5],[5,11],[27,15],[2,9],[2,10],[3,9],[7,4],[10,3],[9,2],[7,4],[5,6],[4,15],[3,24],[0,30],[1,11],[2,9],[6,7],[2,12],[-1,11],[-6,57],[1,8],[5,3],[9,2],[10,0],[12,4],[14,9],[18,23],[13,10],[22,11],[8,10],[6,87],[11,72]],[[4782,8493],[769,-46],[-1,-311],[1285,14],[1,0]],[[2836,9635],[2,-56],[22,-49],[18,-28],[8,-15],[9,-26],[6,-68]],[[2901,9393],[-265,-288],[-1,-13],[13,-7],[85,-53],[32,-12],[28,-20],[8,-41],[1,-43],[38,-309],[-15,-47],[-23,-38],[4,-46],[32,-89],[-5,-49],[-30,-31],[12,-26],[18,-23],[19,-7],[17,-11],[-25,-13],[-28,-4],[-30,-16],[-29,-20],[-26,-9],[-28,-7],[-22,-9],[-23,-5],[-1,-11],[1,-12]],[[1847,9635],[50,0],[190,0],[189,0],[190,0],[189,0],[181,0]],[[3161,9379],[0,-272],[10,-2],[11,-12],[4,-18],[4,-67],[-6,-48],[-8,-22],[-34,-50],[-13,-9],[-9,-15],[1,-37],[8,-37],[-5,-60],[6,-21],[10,-20],[12,-43],[33,-42],[3,-22],[-14,-12],[-16,-1],[-16,-6],[-23,-31],[-32,-35],[-28,-40],[-11,-26],[-3,-27],[0,-51],[5,-11],[2,-23],[24,-57],[13,-25],[8,-21],[13,-17],[24,-17],[-6,-26],[-15,-7],[-11,-10],[1,-23],[8,-60],[7,-19],[0,-19]],[[2901,9393],[1,-5],[13,-45],[112,16],[134,20]],[[4639,9634],[0,-313]],[[4639,9321],[-924,-3],[-265,-121],[-61,26],[-228,156]],[[2836,9635],[9,0],[189,0],[190,0],[189,0],[189,0],[190,0],[189,-1],[125,0],[65,0],[189,0],[189,0],[90,0]],[[6839,7168],[0,-21],[0,-104],[0,-104],[0,-104],[1,-104],[0,-103],[0,-1],[0,-48],[0,-48],[1,-48],[0,-48],[0,-48],[0,-48],[0,-48],[0,-48],[0,-48],[1,-48],[0,-48],[0,-48],[0,-48],[0,-48],[0,-48],[0,-48],[1,-34],[-10,-31],[-42,0],[-173,0],[-173,0],[-173,0],[-173,0],[0,-71],[1,-126],[0,-125],[0,-125],[0,-125],[0,-144],[0,-143],[0,-143],[1,-87],[0,-56],[0,-144],[0,-143],[0,-120]],[[6844,9154],[5,-294]],[[6849,8860],[-14,0],[0,-24],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[0,-34],[1,-104],[0,-38]],[[4782,8493],[-136,9],[-7,819]],[[4639,9634],[100,0],[189,0],[40,0],[6,0],[1,-5],[2,-8],[5,-3],[5,-5],[12,-31],[1,-9],[2,-2],[18,-5],[25,-53],[3,-10],[10,-5],[38,-35],[3,-6],[3,-15],[4,-7],[7,-8],[14,-7],[7,-6],[42,-48],[15,-21],[10,-9],[29,-8],[66,-35],[96,-19],[63,3],[25,11],[16,3],[8,0],[22,-5],[33,3],[12,-9],[117,-37],[127,1],[42,11],[17,2],[15,-5],[6,-15],[7,-6],[16,-7],[17,-6],[11,-1],[4,4],[9,16],[4,3],[117,0],[28,-17],[26,-10],[7,-1],[43,5],[20,0],[10,1],[10,5],[6,-6],[10,10],[18,5],[20,-1],[17,-8],[26,23],[8,-5],[10,1],[25,4],[8,-3],[29,-26],[19,5],[18,-9],[17,-16],[14,-17],[8,-6],[25,-14],[22,-17],[9,-5],[19,5],[29,2],[26,-3],[12,-7],[6,-7],[12,-6],[12,-2],[5,4],[6,7],[12,-4],[23,-12],[16,-16],[19,2],[22,9],[24,5],[10,22],[58,17]],[[6844,9154],[10,3],[68,20],[13,-6],[12,4],[12,6],[14,2],[11,-2],[30,2],[12,-3],[33,-20],[42,-16],[22,-12],[12,-17],[4,-1],[14,4],[53,11],[186,38],[185,39],[185,38],[186,39],[150,32],[151,32],[151,32],[151,32],[61,13],[30,7],[26,5],[14,1],[22,5],[32,7],[31,7],[32,7],[32,7],[31,7],[32,7],[31,7],[32,7],[32,7],[31,7],[32,7],[31,7],[32,7],[32,7],[31,7],[32,7],[22,5],[13,1],[14,-2],[39,-2],[8,-5],[6,3],[31,10],[13,2],[13,-3],[32,-12],[22,-4],[14,-8],[18,-4],[6,-4],[5,-6],[6,-5],[12,-5],[7,-1],[7,1],[7,3],[12,11],[8,5],[4,6],[5,6],[7,2],[33,0],[63,-11],[4,-1],[4,-4],[5,-4],[8,-2],[23,1],[51,-11],[20,-10],[9,-15],[15,8],[9,-7],[1,-1],[8,-13],[12,-9],[7,-1],[14,5],[7,1],[3,-2],[-5,-15],[0,-5],[5,-7],[4,-3],[5,-1],[9,0],[2,-3],[13,-13],[3,-2],[6,-24],[1,-4],[7,-2],[5,-4],[4,-6],[2,-6],[6,10],[5,0],[11,-12],[3,-5],[-1,-11],[1,-5],[3,-3],[9,-5],[3,-3],[10,-14],[6,-2],[12,-1],[10,-3],[10,-6],[8,-9],[5,-11],[-48,10],[-30,0],[-25,-26],[-24,-11],[-23,-1],[-7,17],[-20,-14],[-10,-1],[-6,9],[-11,-5],[-6,4],[-5,7],[-8,5],[5,12],[-8,-1],[-4,-4],[-2,-6],[-6,-6],[-5,-3],[-54,-19],[-14,-1],[-13,-4],[-17,-15],[-16,-4],[-4,-2],[-5,-12],[-6,-3],[-7,-1],[-6,-3],[-6,-3],[-4,-4],[-19,-27],[-26,-18],[-11,-10],[-37,-49],[-3,-1],[-2,-5],[-11,-13],[-2,-5],[-7,-2],[-34,-4],[-10,-3],[-25,26],[-1,13],[-4,4],[-5,3],[-5,5],[-13,27],[-9,9],[-16,3],[-25,2],[-11,-5],[-12,-13],[-21,-40],[-7,-5],[-7,1],[-13,7],[-8,3],[-15,2],[-15,-2],[-26,-14],[-35,-47],[-25,-19],[-27,-5],[-6,-4],[-21,-22],[-6,-5],[-21,-9],[-9,-8],[-4,-4],[-2,-6],[-4,3],[-3,2],[-4,2],[-4,-1],[-26,-20],[-2,-29],[-12,-12],[-22,-16],[-9,-9],[-13,-21],[-9,-9],[-12,-4],[-69,-81],[-11,-8],[-15,-3],[-18,-22],[-5,-4],[-3,-2],[-26,-10],[-13,-1],[-10,9],[-6,35],[-11,36],[-7,11],[11,17],[-7,18],[-24,28],[6,13],[-4,8],[-8,7],[-4,9],[-3,10],[-8,3],[-10,2],[-10,5],[-4,6],[-10,14],[-11,11],[-8,-9],[-7,1],[-6,3],[-4,2],[-1,5],[1,14],[0,5],[-5,8],[-22,28],[-9,21],[-8,12],[-2,5],[-1,6],[1,20],[-2,2],[-6,1],[-5,2],[-3,6],[0,11],[-1,5],[-4,4],[-9,5],[-28,2],[-51,-5],[-64,-6],[-87,-8],[-65,-14],[-62,-13],[-62,-13],[-63,-13],[-62,-13],[-62,-13],[-63,-13],[-62,-13],[-62,-13],[-63,-13],[-62,-13],[-62,-13],[-62,-13],[-63,-13],[-62,-13],[-62,-13],[-63,-13],[-12,-3],[-13,-3],[-12,-2],[-13,-3],[-14,0],[-14,-1],[-35,-2],[-34,-1],[-35,-2],[-35,-1],[-41,-2],[-42,-2],[-42,-2],[-41,-2],[-23,-1]]],
transform:{scale:[.0013543513782378284,.0012009511904190367],translate:[11.717621290000068,-28.95936818399987]}},m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();