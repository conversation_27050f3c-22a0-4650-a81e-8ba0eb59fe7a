!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo={type:"Topology",objects:{ltu:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Alytaus"},id:"LT.AS",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Kauno"},id:"LT.KS",arcs:[[4,5,-4,6,7,8]]},{type:"Polygon",properties:{name:"Marijampoles"},id:"LT.MA",arcs:[[-7,-3,9,10]]},{type:"Polygon",properties:{name:"Panevezio"},id:"LT.PA",arcs:[[11,12,-5,13,14]]},{type:"Polygon",properties:{name:"Šiauliai"},id:"LT.SH",arcs:[[-14,-9,15,16,17]]},{type:"Polygon",properties:{name:"Taurages"},id:"LT.TG",arcs:[[-16,-8,-11,18,19,20]]},{type:"Polygon",properties:{name:"Utenos"},id:"LT.UN",arcs:[[21,-12,22]]},{type:"Polygon",properties:{name:"Vilniaus"},id:"LT.VI",arcs:[[23,-1,-6,-13,-22]]},{type:"MultiPolygon",properties:{name:"Klaipedos"},id:"LT.KP",arcs:[[[24]],[[-20,25,26]]]},{type:"Polygon",properties:{name:"Telšiai"},id:"LT.TL",arcs:[[-17,-21,-27,27]]}]}},arcs:[[[5936,2570],[17,-89],[-4,-24],[-1,-37],[-9,-12],[-5,-12],[2,-10],[29,-42],[8,-29],[-7,-59],[-7,-33],[-1,-31],[7,-27],[34,-29],[150,-27],[29,3],[56,22],[22,20],[40,24],[30,24],[23,2],[47,-13],[50,-31],[11,-13],[5,-23],[-3,-23],[-5,-26],[3,-23],[12,-20],[18,-2],[15,7],[27,24],[21,5],[33,-8],[23,-11],[18,4],[80,79],[139,69],[85,9],[3,-178],[-2,-18],[-5,-25],[-31,-22],[-46,-13],[-17,-13],[-7,-12],[-6,-18],[-8,-54],[-9,-120],[-12,-58],[-61,-107],[-184,-245],[-5,-38],[8,-31],[17,-20],[41,-27],[16,-14],[11,-21],[7,-25],[12,-57],[31,-81],[0,-1]],[[6681,1010],[-50,-41],[-7,-25],[0,-29],[2,-26],[-1,-15],[-31,-27],[-16,-26],[-10,-2],[-3,-13],[8,-60],[20,-84],[23,-67],[15,-74],[-3,-107],[-1,-1],[-22,-67],[-30,-22],[-110,-24],[-31,3],[-25,33],[-22,77],[-6,5],[-7,2],[-6,-1],[-41,-43],[-108,-29],[-86,-67],[-195,-238],[-62,-42],[-61,1],[-54,22],[-57,-4],[-33,9],[-18,38],[-30,112],[-48,79],[-53,25],[-56,-14],[-105,-77],[-51,-19],[-109,-2],[-28,10],[-50,43],[-26,15],[-26,-6],[-130,-102],[-13,-3],[-14,6],[-31,24],[-12,4],[-26,-14],[-30,-30],[-106,-21],[-84,-48],[-26,-4],[-29,15],[-153,125],[-59,21],[7,23],[1,21],[-5,20],[-10,18],[-18,20],[-14,28],[-7,36],[1,42],[8,17],[10,15],[20,24],[25,60],[0,88],[-25,183],[-13,83],[-19,88],[-23,78],[-28,56],[-16,13],[-37,13],[-17,16],[-16,31],[-31,80],[-16,34],[-32,36]],[[4103,1328],[0,1],[6,71],[16,126],[-2,25],[-5,26],[0,27],[5,27],[13,42],[5,34],[19,67],[30,52],[25,27],[41,22],[54,13],[15,11],[2,21],[-4,10],[-21,20],[-8,15],[13,27],[16,20],[129,66],[2,28],[0,13],[-4,41],[-1,31],[5,34],[11,30],[14,21],[143,153],[16,6],[19,-7],[28,-39],[14,-33],[31,-43],[89,-32]],[[4819,2281],[17,-24],[159,9],[4,-12],[-4,-13],[8,-3],[24,11],[45,36],[58,63],[99,51],[7,7],[3,9],[0,12],[-2,11],[-4,8],[-13,19],[-3,6],[-4,10],[-3,12],[-2,15],[1,14],[2,13],[5,12],[6,10],[11,13],[12,11],[39,17],[33,-1],[34,-13],[15,-12],[16,-8],[15,-3],[187,51],[35,17],[32,8],[27,-7],[36,-33],[15,-27],[31,-19],[24,-7],[152,26]],[[5007,6582],[33,-2],[12,-4],[14,-11],[9,-25],[5,-27],[8,-69],[8,-32],[17,-25],[24,-14],[32,4],[14,15],[15,2],[20,-9],[34,-41],[18,-32],[50,-66],[9,-30],[0,-33],[-3,-32],[-11,-60],[2,-21],[8,-10],[18,9],[30,38],[16,10],[21,2],[28,-9],[33,11],[84,54],[17,19],[18,8],[14,-8],[21,-34],[17,-32],[19,-24],[37,-15],[22,0],[12,14],[2,19],[2,19],[12,1],[80,-57],[24,-26],[21,-34],[22,-70],[45,-62]],[[5940,5893],[-26,-116],[-7,-23],[-7,-37],[2,-39],[-4,-36],[-3,-49],[11,-45],[33,-71],[13,-138],[15,-34],[20,-24],[67,-31],[22,-21],[2,-14],[-10,-8],[-17,-5],[-12,-17],[-6,-30],[6,-56],[12,-36],[16,-28],[15,-18],[28,-27],[15,-18],[4,-22],[-2,-31],[1,-47],[19,-46],[19,-17],[21,2],[34,23],[15,-4],[9,-17],[23,-104],[51,-33],[18,-41],[6,-41],[0,-19],[-5,-20],[-10,-15],[-14,-14],[-30,-23],[-10,-10],[-9,-16],[-9,-11],[-21,-17],[-9,-11],[-6,-11],[-9,-24],[-11,-32],[-33,-64],[15,-8],[22,-37],[88,-58],[91,-115],[31,-16],[73,-7],[31,-17],[30,-32],[38,-66],[8,-25],[-49,-52],[-29,-81],[-23,-31],[-112,-82],[-20,-22],[-11,-20],[-5,-24],[-11,-74],[-5,-18],[-11,-33],[-8,-29],[-6,-37],[-3,-36],[-1,-39],[-4,-46],[-12,-18],[-18,-6],[-63,20],[-19,1],[-10,-13],[-21,-47],[-70,-28],[-29,-24],[-15,-30],[-3,-38],[5,-31],[6,-32],[4,-29],[2,-20],[-4,-46],[24,-38],[2,-16],[1,-22],[-5,-23],[-4,-36],[-9,-23],[-13,-16],[-15,-4],[-16,5],[-13,10],[-16,-5],[-9,-24],[-5,-36],[-5,-83],[-7,-31],[-5,-18],[-18,-31]],[[4819,2281],[10,30],[9,23],[15,27],[4,28],[-3,41],[-30,115],[-11,26],[-12,7],[-31,-5],[-20,5],[-18,22],[-20,36],[-38,41],[-12,18],[-2,24],[9,29],[15,21],[12,23],[1,21],[-14,26],[-48,24],[-9,11],[-6,13],[-9,9],[-11,8],[-7,12],[0,15],[12,20],[17,12],[43,21],[16,13],[9,34],[2,39],[-32,182],[5,50],[-1,29],[-8,26],[-8,11],[-8,20],[-6,25],[3,42],[-13,22],[-13,7],[-17,-1],[-13,3],[-5,13],[3,19],[18,29],[7,25],[7,18],[3,11],[20,13],[-6,41],[-5,20],[-10,32],[-6,44],[1,22],[3,25],[1,21],[-6,22],[-12,24],[-37,51],[-46,49],[-31,20],[-25,5],[-41,-30],[-41,-11],[-38,161],[-15,26],[-19,26],[-83,29],[-8,22],[3,21],[10,23],[13,18],[12,13],[18,11],[19,5],[73,-10],[15,2],[14,6],[10,12],[9,17],[19,49],[11,22],[8,9],[9,6],[10,3],[22,1],[13,4],[13,17],[0,35],[-10,35],[-32,39],[-37,3],[-39,-7],[-37,7],[-88,95],[-35,14]],[[4233,4663],[19,121],[28,70],[22,40],[6,30],[0,38],[-4,41],[-1,72],[2,39],[9,31],[30,51],[-34,56],[-43,9],[-96,-29],[-15,4],[-13,16],[-9,25],[-28,31],[-19,8],[-68,-17],[-108,0],[-31,15],[-10,17],[-7,47],[-9,27],[-29,29],[-16,9],[-13,-1],[-9,-11],[-12,-10],[-18,-7],[-32,-5],[-23,12],[-18,2],[-15,-4],[-7,-7],[-13,-17],[-10,-7],[-49,-12],[-43,0],[-19,16],[-3,23],[9,25],[23,33],[3,16],[-6,15],[-116,120],[-21,31],[-11,10],[-7,1],[-3,-7],[-1,-11],[2,-13],[0,-13],[-3,-12],[-7,-7],[-8,-6],[-4,-7],[-5,-13],[-4,-11],[-10,-9],[-9,-7],[-6,-7],[-7,-14],[-9,-14],[-18,-12],[-10,5],[-7,17],[-3,24],[-4,22],[-32,47],[-33,34],[-16,29],[-9,30],[-1,28],[7,57],[1,33],[-3,30],[-11,29],[-11,10],[-14,-1],[-28,-14],[-21,-1],[-9,4],[-37,42],[-3,40],[6,35],[-4,24],[-9,12],[-36,10],[-34,32],[-19,32],[-38,98]],[[2929,6211],[53,59],[98,49],[126,25],[22,-8],[12,-17],[3,-29],[6,-24],[11,-16],[45,-14],[262,136],[61,55],[16,21],[10,23],[5,29],[32,67],[35,35],[106,17],[37,-2],[30,-10],[12,-11],[8,-22],[15,-62],[11,-25],[15,-11],[23,11],[15,18],[86,69],[129,-143],[37,-30],[58,-21],[28,0],[18,10],[3,16],[1,23],[2,11],[7,8],[20,5],[10,6],[10,11],[8,10],[7,8],[11,5],[15,-4],[17,-11],[22,-30],[32,-15],[10,-8],[2,-17],[-8,-30],[-11,-22],[-7,-28],[-5,-20],[20,-62],[50,-3],[27,-24],[14,-7],[11,7],[25,66],[18,30],[32,43],[78,73],[232,151]],[[4103,1328],[-34,39],[-137,70],[-65,54],[-105,119],[-31,20],[-34,-10],[-41,-24],[-39,0],[-31,58],[0,41],[10,45],[6,37],[-10,21],[-59,10],[-19,17],[-11,25],[-7,31],[-10,30],[-18,25],[-17,5],[-39,-10],[-18,3],[-103,71],[-36,6],[-42,-26],[-2,-6],[-43,-107],[-32,-35],[-103,244],[-45,135],[-10,153],[12,303],[37,187],[8,71],[-2,42],[-11,89],[1,40],[7,28],[37,73],[1,16],[-5,17],[-3,19],[3,19],[6,6],[15,5],[18,21],[29,22],[14,21],[24,52],[53,52],[25,37],[22,68],[5,70],[-7,72],[-26,158],[-14,50],[-21,32],[-101,63],[-14,31],[19,53],[-23,11],[-16,25],[-12,26],[-11,17],[-16,7],[-96,-1],[-28,19],[-20,48],[-23,87],[-28,177],[-19,78],[-16,25]],[[2802,4605],[0,1],[48,29],[13,-20],[31,-30],[10,-6],[22,-1],[144,35],[51,29],[20,21],[85,38],[86,15],[93,-34],[24,21],[21,25],[32,21],[52,18],[542,-133],[157,29]],[[8571,8105],[-34,-78],[-1,-71],[-2,-39],[-10,-26],[-22,-37],[-17,-24],[-35,-34],[-22,-36],[-12,-35],[-8,-44],[-46,-90],[-16,-54],[-27,-123],[-9,-23],[-11,-8],[-31,6],[-34,19],[-13,3],[-15,-13],[-25,-65],[-13,-26],[-63,-96],[-4,23],[-2,11],[-4,14],[-6,10],[-21,6],[-184,-8],[-23,-8],[-26,-25],[-70,-46],[-32,64],[-6,18],[-15,17],[-17,-2],[-12,-11],[-20,-25],[-9,-3],[-11,9],[-11,19],[-13,40],[-6,29],[-34,42],[-129,-95],[-12,-19],[-12,-28],[10,-58],[-3,-28],[-9,-9],[-16,3],[-37,16],[-19,-1],[-13,-4],[-196,-100],[-49,-13],[-26,-1],[-15,8],[-3,12],[5,34],[-4,17],[-25,28],[-11,16],[-13,30],[-5,8],[-25,24],[-7,4],[-107,11],[-51,-17],[-32,-23],[-24,-4],[-54,6],[-15,-5],[-9,-10],[-5,-14],[-6,-13],[-14,-13],[-16,3],[-49,27],[-21,-2],[-25,-15],[-19,-5],[-112,22],[2,-50],[-3,-18],[-4,-30],[9,-18],[36,-36],[8,-23],[0,-18],[-8,-28],[-14,-20],[-19,-7],[-19,12],[-12,17],[-12,9],[-50,-18],[-13,-16],[6,-15],[16,-17],[23,-17],[19,-24],[17,-34],[11,-49],[1,-26],[-5,-20],[-31,-32],[-18,-23],[-14,-34],[-4,-35],[1,-67],[1,-15],[4,-14],[5,-14],[0,-25],[-33,-72]],[[6306,6400],[-33,-13],[-9,-8],[-9,-15],[2,-20],[9,-19],[31,-30],[12,-18],[2,-19],[-10,-17],[-28,-7],[-20,6],[-40,28],[-19,4],[-17,-11],[-20,-43],[-10,-39],[-11,-68],[-12,-37],[-20,-42],[-61,-87],[-15,-10],[-14,10],[-13,1],[-12,-3],[-49,-50]],[[5007,6582],[16,48],[7,14],[19,35],[26,65],[14,51],[18,12],[37,-3],[16,10],[24,30],[14,3],[17,-2],[16,1],[11,21],[0,33],[-23,118],[3,43],[14,21],[19,16],[16,24],[9,28],[-3,59],[1,32],[-9,97],[-8,24],[-27,43],[-5,28],[0,19],[-4,16],[-9,15],[-21,15],[-19,7],[-28,36],[-21,114],[65,10],[6,8],[7,17],[-2,24],[0,24],[12,31],[22,18],[47,29],[36,36],[16,40],[-5,59],[8,18],[14,40],[2,40],[-6,61],[-10,24],[-15,14],[-16,5],[-19,12],[-18,16],[-18,29],[-8,27],[-4,29],[-2,104],[0,23],[2,21],[4,17],[5,13],[8,10],[8,0],[9,-5],[11,-11],[8,-3],[9,0],[12,22],[8,31],[11,78],[15,43],[39,74],[5,38],[1,39],[5,28],[10,35],[12,21],[17,15],[52,19],[12,22],[3,31],[2,60],[-3,28],[-9,16],[-8,-1],[-12,-10],[-9,1],[-12,10],[-9,43],[6,37],[11,30],[14,26],[3,24],[-3,19],[-4,61]],[[5472,9275],[45,11],[206,138],[52,12],[55,-28],[108,-102],[56,-19],[58,32],[98,74],[33,43],[25,43],[113,197],[67,67],[62,36],[154,87],[17,1],[38,-19],[14,4],[10,34],[2,43],[7,42],[23,28],[37,-15],[30,-66],[44,-164],[44,-237],[19,-73],[17,-40],[57,-81],[16,-38],[31,-126],[49,-118],[62,-57],[408,-91],[180,-40],[115,27],[86,-53],[29,-7],[101,10],[21,-12],[13,-34],[31,-132],[15,-41],[21,-27],[98,-87],[49,-73],[129,-187],[154,-132]],[[2929,6211],[-42,27],[-9,92],[35,72],[-3,17],[-11,15],[-18,15],[-64,127],[-18,27],[-79,64],[-24,29],[-11,29],[-14,44],[-3,25],[1,22],[7,21],[5,25],[-7,15],[-36,22],[-19,27],[-9,27],[-3,20],[1,34]],[[2608,7007],[53,63],[15,10],[14,22],[1,30],[-9,64],[-3,32],[3,34],[5,38],[5,51],[-6,36],[-21,65],[2,25],[22,27],[19,11],[24,8],[20,12],[24,23],[18,22],[7,38],[-5,72],[3,32],[13,23],[27,10],[24,4],[25,16],[17,29],[25,96],[50,78],[-6,43],[20,20],[22,15],[2,13],[-9,11],[-17,14],[-14,19],[-20,41],[-13,14],[-13,-4],[-7,-16],[-7,-21],[-14,-15],[-18,-8],[-22,5],[-16,23],[-23,41],[-7,29],[0,20],[9,14],[25,24],[9,16],[6,19],[0,31],[-1,51],[-14,63],[2,32],[17,22],[80,22],[7,6],[7,8],[7,20],[4,5],[21,6],[28,18],[-28,43],[-7,26],[-5,33],[3,31],[7,29],[-1,20],[-12,18],[-22,8],[-38,-5],[-51,-21],[-18,-14],[-13,-20],[-23,-13],[-30,-7],[-65,12],[-74,-47],[-23,1],[-24,11],[-21,19],[-7,32],[-3,27],[-8,17],[-12,2],[-15,-1],[-8,7],[1,28],[17,38],[-6,17],[-9,13],[-63,26],[24,228],[48,90],[28,33],[27,50],[11,42],[-1,33],[-18,34],[-4,14],[7,22],[13,25],[13,40],[20,15],[21,0],[45,-17],[24,-2],[24,7],[24,24],[14,32],[9,35],[0,33],[-9,31],[-11,23],[-5,25],[9,18],[12,11],[22,26]],[[2814,9781],[45,-30],[75,-102],[30,-16],[26,5],[240,127],[63,56],[50,44],[60,15],[55,-40],[42,-113],[60,-193],[78,-77],[85,27],[86,118],[-15,40],[31,26],[197,59],[38,-10],[115,-62],[176,-96],[61,-6],[103,78],[54,21],[119,-8],[46,10],[14,-7],[15,-27],[2,-29],[-2,-24],[2,-13],[53,-11],[118,35],[56,-2],[22,-9],[187,-77],[160,-162],[57,-41],[52,-12],[2,0]],[[2802,4605],[-10,15],[-41,31],[-179,-70],[-283,46]],[[2289,4627],[0,2],[0,84],[3,25],[19,57],[7,38],[14,29],[7,20],[-3,46],[-29,22],[-15,-4],[-15,1],[-14,5],[-14,8],[-15,4],[-36,-9],[-19,2],[-18,12],[-31,41],[-17,13],[-17,2],[-44,-10],[-12,-8],[-31,-39],[-20,-17],[-16,4],[-7,19],[10,63],[1,19],[-3,24],[-26,76],[-21,88],[-11,24],[-15,21],[-162,95],[-54,10],[-20,13],[-4,32],[16,52],[128,190],[29,33],[23,34],[12,33],[13,108],[27,20],[-1,33],[-6,11],[-10,12],[-55,19],[-68,43],[-36,37],[-16,11],[-16,-4],[-30,-34],[-17,-1],[-18,26],[-35,192],[-8,30],[-25,37],[-9,28],[-11,129],[-6,21],[-25,27],[-3,38],[88,51],[13,15],[10,25],[5,20],[-8,34]],[[1622,6709],[193,36],[22,-10],[23,-3],[18,9],[42,54],[9,18],[2,17],[-2,24],[1,23],[5,28],[7,22],[6,17],[8,7],[13,4],[22,-9],[22,0],[21,6],[30,28],[59,34],[140,23],[42,5],[51,-33],[21,-6],[42,4],[18,-8],[21,-15],[20,-4],[130,27]],[[9896,5305],[-1,1],[-5,15],[-35,87],[-15,15],[-24,16],[-24,1],[-78,-32],[-16,-13],[-11,-17],[-85,-70],[-190,-72],[-230,15],[-85,26],[-60,50],[-33,19],[-20,-1],[-16,-10],[-17,-16],[-30,-7],[-22,16],[-11,38],[2,38],[12,38],[11,28],[1,27],[-11,28],[-22,27],[-45,28],[-23,8],[-107,-5],[-6,-1],[-11,-6],[-10,-9],[-11,-15],[-7,-14],[-11,-33],[-6,-14],[-10,-16],[-16,-19],[-23,-15],[-29,-10],[-19,7],[-9,14],[-3,16],[-4,16],[-12,19],[-12,3],[-49,-17],[-20,7],[-15,18],[-17,45],[-20,33],[-65,-47],[-63,27],[-74,62],[-34,-157],[-10,-32],[-51,-79],[-48,-41],[-10,-22],[-5,-27],[-6,-59],[7,-33],[7,-27],[25,-43],[52,-63],[11,-22],[9,-23],[28,-45],[18,-20],[31,-22],[14,-16],[15,-12],[18,-9],[9,-16],[-6,-25],[-40,-44],[-6,-23],[3,-17],[10,-13],[6,-10],[-5,-16],[-14,-20],[-33,-40],[-4,-31],[4,-28],[8,-21],[-8,-11],[-23,-1],[-92,23],[-16,11],[-4,16],[-6,15],[-53,1],[-176,-55],[-232,-96],[-22,-15],[-26,-6],[-27,5],[-108,50],[-81,3],[-10,89],[-5,19],[-10,29],[-29,30],[-24,17],[-70,23],[-12,14],[-4,21],[10,44],[10,61],[-4,79],[58,30],[16,12],[25,29],[29,24],[19,32],[15,11],[21,8],[13,10],[3,18],[-23,24],[-82,64],[-20,0],[-17,-4],[-17,1],[-11,16],[-7,47],[8,24],[26,44],[9,23],[6,26],[-1,21],[-11,18],[-27,7],[-23,-5],[-20,-11],[-16,-3],[-17,11],[-21,42],[-47,48],[-16,21],[-7,17],[4,78],[-130,-14],[-16,5],[-16,11],[0,20],[1,27],[1,30],[-2,33],[-7,26],[-9,9],[-12,-2],[-68,-44],[-33,-12],[-15,5],[-6,13],[2,25],[1,25],[-8,23],[-18,8],[-82,3],[-28,13],[-20,21],[-11,24],[-8,26],[-6,25],[-2,23],[2,26],[6,22],[10,16],[25,20],[9,13],[0,23],[-11,34],[-42,63],[-235,233]],[[8571,8105],[95,-83],[106,-153],[168,-190],[43,-87],[40,-119],[89,-210],[107,-105],[236,-149],[96,-35],[97,-9],[16,-93],[6,-105],[-4,-103],[-16,-87],[-33,-68],[-42,-63],[-33,-71],[-8,-94],[32,-82],[-5,-46],[-60,-80],[-15,-43],[-23,-146],[-11,-37],[-41,-99],[-16,-73],[7,-40],[27,-25],[101,-50],[29,-2],[103,37],[282,-65],[39,-40],[16,-66],[-19,-63],[-41,-41],[-43,-15]],[[9896,5305],[-67,-23],[-26,-21],[-49,-63],[-27,-49],[-12,-42],[-11,-109],[-19,-113],[-26,-57],[-37,-9],[-180,106],[-24,-4],[-15,-19],[-10,-23],[-10,-15],[-17,-7],[-14,-1],[-189,65],[-77,-18],[-53,-111],[-5,-45],[2,-97],[-3,-47],[-8,-33],[-63,-181],[-30,-60],[-28,-58],[-26,-37],[-61,-47],[-207,-56],[-31,1],[-62,19],[-31,1],[-64,-36],[-20,-23],[-7,-29],[-3,-36],[-10,-40],[-36,-55],[-40,-41],[-33,-47],[-18,-73],[2,-35],[15,-78],[1,-33],[-8,-33],[-8,-7],[-12,-1],[-16,-11],[-38,-44],[-18,-35],[-5,-53],[6,-199],[-3,-41],[-6,-41],[-9,-36],[-8,-39],[-2,-49],[12,-78],[42,-139],[10,-75],[-9,-78],[-24,-61],[-32,-45],[-105,-82],[-26,-47],[-18,-80],[-8,-102],[2,-82],[-7,-75],[-30,-85],[-78,-127],[-32,-84],[-3,-99],[24,-50],[37,-19],[73,6],[102,73],[26,9],[23,-19],[10,-32],[5,-38],[18,-69],[7,-15],[7,-2],[19,9],[8,-3],[16,-24],[15,-29],[14,-35],[11,-43],[7,-13],[9,-5],[8,-8],[5,-24],[-4,-17],[-20,-37],[-6,-18],[-1,-179],[-14,-61],[-39,-40],[-20,-4],[-64,12],[-18,-12],[-27,-50],[-18,-15],[-37,-1],[-151,34],[-46,30],[-39,50],[-13,70],[13,27],[49,23],[12,29],[-10,34],[-41,29],[5,39],[34,27],[40,-4],[15,15],[-120,220],[-9,22],[-11,15],[-21,7],[-43,-28],[-69,-135],[-41,-36],[-140,-11],[-108,52],[-30,-7],[-22,-51],[-35,-184],[-28,-82],[-69,-105],[-75,-65],[-76,-17],[-134,71],[-78,15],[-79,-12],[-9,-7]],[[110,5495],[0,-71],[-110,37],[0,1],[128,290],[71,231],[70,319],[18,329],[10,66],[-9,19],[9,37],[-14,79],[4,244],[-14,107],[55,-90],[24,-173],[4,-342],[-4,-49],[-16,-65],[-4,-47],[0,-147],[-19,-64],[-27,-65],[-9,-60],[32,-54],[0,-24],[-24,-41],[-32,-74],[-27,-88],[-11,-80],[-22,-79],[-83,-146]],[[2289,4627],[-31,5],[-185,-63],[-22,-30],[-19,-36],[-32,-31],[-39,-5],[-46,19],[-38,43],[-15,64],[0,65],[-6,43],[-20,20],[-44,-6],[-45,-45],[-14,-9],[-17,9],[-42,37],[-60,19],[-33,29],[-57,67],[-118,34],[-30,32],[-29,52],[-120,123],[-69,45],[-172,8],[-62,65],[-106,240],[-52,68],[-42,-8],[-140,-153],[2,19],[-8,90],[0,29],[11,16],[13,-6],[12,2],[10,39],[-3,32],[-12,46],[-14,41],[-11,17],[-14,18],[-5,41],[1,88],[-18,3],[-98,-123],[-10,29],[0,9],[5,3],[65,196],[14,30],[27,43],[0,96],[-20,157],[-21,117],[-2,30],[-8,14],[-16,23],[-16,32],[-8,37],[2,48],[3,31],[0,29],[-5,40],[-64,222],[-12,69],[-5,73],[-11,39],[-71,160],[-16,25],[-15,13],[-11,25],[-8,96],[-16,77],[-27,330],[0,172],[4,43],[16,55],[4,36],[0,335],[-9,137],[65,21],[100,1],[68,25],[26,74],[12,108],[29,126],[43,86],[61,85],[63,68],[52,35],[77,5],[27,11],[112,111],[125,123],[64,42],[150,9],[89,53],[389,234]],[[1771,9768],[-6,-39],[-22,-24],[-62,-41],[-11,-18],[-5,-29],[3,-23],[9,-23],[14,-16],[15,-23],[11,-34],[0,-81],[4,-40],[6,-31],[20,-33],[9,-22],[49,-146],[7,-38],[-4,-17],[-15,-10],[-17,-2],[-16,-9],[-11,-17],[-11,-42],[-10,-27],[-9,-16],[-19,-65],[-42,-115],[-13,-16],[-18,-15],[-83,1],[-24,-6],[-14,-17],[-17,-30],[-14,-14],[-18,-8],[-66,-4],[-18,-14],[-81,-99],[-11,-81],[-60,-150],[-17,-30],[-16,-17],[-39,-12],[-21,-21],[-7,-25],[1,-21],[4,-24],[4,-26],[-8,-159],[2,-36],[6,-35],[8,-23],[31,-62],[6,-21],[5,-30],[-7,-20],[-24,-27],[-6,-20],[-8,-13],[-90,-96],[-23,-35],[-11,-23],[-5,-18],[-1,-21],[3,-24],[71,-9],[16,6],[22,13],[32,37],[9,-5],[3,-19],[-1,-26],[1,-23],[20,-41],[13,-21],[8,-16],[2,-17],[-5,-16],[-9,-18],[-4,-19],[6,-24],[14,-5],[20,3],[21,9],[81,2],[40,19],[27,3],[13,-12],[5,-15],[-5,-20],[-2,-22],[-1,-23],[-3,-19],[-7,-22],[2,-23],[34,-75],[5,-21],[4,-18],[8,-56],[8,-23],[13,-27],[7,-18],[5,-26],[51,-96],[90,-103]],[[1771,9768],[30,19],[189,113],[77,-6],[32,-22],[63,-60],[34,-17],[505,22],[109,-33],[4,-3]]],
transform:{scale:[.0005876739229360665,.00025560169128088024],translate:[20.92456870056236,53.886841126000036]}},m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();