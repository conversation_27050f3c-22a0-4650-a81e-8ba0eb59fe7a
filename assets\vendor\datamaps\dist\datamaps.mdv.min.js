!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo={type:"Topology",objects:{mdv:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:"Haa Alifu"},id:"MV.HA",arcs:[[[0]],[[1]],[[2]],[[3]],[[4]],[[5]],[[6]],[[7]],[[8]],[[9]],[[10]],[[11]],[[12]],[[13]],[[14]]]},{type:"MultiPolygon",properties:{name:"Haa Dhaalu"},id:"MV.HD",arcs:[[[15]],[[16]],[[17]],[[18]],[[19]],[[20]],[[21]],[[22]],[[23]],[[24]]]},{type:"MultiPolygon",properties:{name:"Shaviyani"},id:"MV.SH",arcs:[[[25]],[[26]],[[27]],[[28]],[[29]],[[30]],[[31]],[[32]],[[33]],[[34]],[[35]],[[36]],[[37]],[[38]],[[39]],[[40]],[[41]],[[42]],[[43]]]},{type:"MultiPolygon",properties:{name:"Raa"},id:"MV.RA",arcs:[[[44]],[[45]],[[46]],[[47]],[[48]],[[49]],[[50]],[[51]],[[52]],[[53]],[[54]],[[55]]]},{type:"MultiPolygon",properties:{name:"Noonu"},id:"MV.NO",arcs:[[[56]],[[57]],[[58]],[[59]],[[60]]]},{type:"MultiPolygon",properties:{name:"Baa"},id:"MV.BA",arcs:[[[61]],[[62]],[[63]],[[64]],[[65]],[[66]],[[67]],[[68]],[[69]],[[70]],[[71]],[[72]]]},{type:"MultiPolygon",properties:{name:"Lhaviyani"},id:"MV.LV",arcs:[[[73]],[[74]],[[75]],[[76]],[[77]],[[78]],[[79]],[[80]],[[81]],[[82]],[[83]],[[84]]]},{type:"MultiPolygon",properties:{name:"Kaafu"},id:"MV.KA",arcs:[[[85]],[[86]],[[87]],[[88]],[[89]],[[90]],[[91]],[[92]],[[93]],[[94]],[[95]],[[96]],[[97]],[[98]],[[99]],[[100]],[[101]]]},{type:"MultiPolygon",properties:{name:"Alifu Alifu"},id:"MV.AA",arcs:[[[102]],[[103]],[[104]],[[105]]]},{type:"MultiPolygon",properties:{name:"Alifu Dhaalu"},id:"MV.AD",arcs:[[[106]],[[107]],[[108]],[[109]],[[110]],[[111]],[[112]],[[113]]]},{type:"MultiPolygon",properties:{name:"Faafu"},id:"MV.FA",arcs:[[[114]],[[115]],[[116]],[[117]],[[118]]]},{type:"Polygon",properties:{name:"Malé"},id:"MV.MA",arcs:[[119]]},{type:"MultiPolygon",properties:{name:"Vaavu"},id:"MV.WA",arcs:[[[120]],[[121]],[[122]]]},{type:"MultiPolygon",properties:{name:"Meemu"},id:"MV.ME",arcs:[[[123]],[[124]],[[125]],[[126]],[[127]],[[128]],[[129]]]},{type:"MultiPolygon",properties:{name:"Dhaalu"},id:"MV.DA",arcs:[[[130]],[[131]],[[132]],[[133]],[[134]]]},{type:"MultiPolygon",properties:{name:"Thaa"},id:"MV.TH",arcs:[[[135]],[[136]],[[137]],[[138]],[[139]],[[140]],[[141]],[[142]],[[143]]]},{type:"MultiPolygon",properties:{name:"Laamu"},id:"MV.LM",arcs:[[[144]],[[145]],[[146]],[[147]],[[148]],[[149]],[[150]],[[151]],[[152]],[[153]],[[154]],[[155]],[[156]]]},{type:"MultiPolygon",properties:{name:"Gaafu Alifu"},id:"MV.GA",arcs:[[[157]],[[158]],[[159]],[[160]],[[161]]]},{type:"MultiPolygon",properties:{name:"Gaafu Dhaalu"},id:"MV.DD",arcs:[[[162]],[[163]],[[164]],[[165]],[[166]],[[167]],[[168]]]},{type:"Polygon",properties:{name:"Gnaviyani"},id:"MV.NA",arcs:[[169]]},{type:"MultiPolygon",properties:{name:"Addu"},id:"MV.SE",arcs:[[[170]],[[171]],[[172]],[[173]],[[174]],[[175]]]}]}},arcs:[[[4670,9632],[-28,3],[8,9],[146,-3],[-73,-7],[-53,-2]],[[2879,9654],[-8,-2],[-19,1],[-1,0],[7,6],[17,1],[18,-1],[17,-1],[-20,-2],[-11,-2]],[[4272,9656],[-9,-1],[-21,1],[9,6],[15,1],[18,-2],[17,0],[-20,-2],[-9,-3]],[[2708,9664],[-25,5],[8,2],[19,1],[16,1],[-5,-4],[6,-2],[1,-2],[-20,-1]],[[3840,9667],[-10,-1],[-12,0],[-9,1],[11,6],[14,1],[18,-1],[16,-1],[-19,-2],[-9,-3]],[[2581,9677],[-17,1],[-3,0],[-1,0],[8,6],[18,1],[17,-1],[16,-1],[-19,-2],[-11,-3],[-8,-1]],[[3948,9721],[-22,-1],[-24,5],[5,2],[21,1],[16,1],[-5,-3],[6,-3],[2,-2],[1,0]],[[4904,9722],[-45,-8],[-28,10],[9,12],[28,12],[39,9],[46,0],[-9,-18],[-40,-17]],[[2260,9802],[-16,-2],[13,7],[0,-1],[3,-4]],[[4760,9799],[-2,0],[-1,0],[-5,0],[-6,0],[-7,13],[7,8],[20,4],[42,1],[-36,-26],[-8,0],[-4,0]],[[1925,9843],[13,7],[0,-4],[-13,-3]],[[2760,9897],[16,-18],[-39,7],[0,4],[0,1],[23,6]],[[2481,9934],[-13,-7],[-7,4],[20,3]],[[2134,9980],[-9,-12],[-51,8],[2,2],[28,1],[30,1]],[[1869,9994],[-19,-2],[12,7],[7,-2],[0,-3]],[[25,9108],[15,-1],[17,0],[-1,0],[-19,-3],[-15,-3],[-12,-1],[-10,3],[10,4],[15,1]],[[2566,9291],[-9,-1],[-21,1],[9,6],[15,1],[1,0],[16,-2],[18,0],[-20,-2],[-9,-3]],[[2274,9296],[-44,1],[-5,2],[9,2],[5,3],[14,-3],[18,-1],[12,-1],[-9,-3]],[[3235,9318],[-22,-1],[-7,1],[-17,4],[5,2],[21,1],[15,1],[-1,0],[-3,-4],[6,-2],[2,-2],[1,0]],[[3338,9372],[-7,-2],[-22,2],[10,6],[15,1],[1,0],[17,-2],[17,-1],[-20,-1],[-11,-3]],[[3600,9436],[-72,-9],[5,7],[17,2],[24,0],[26,0]],[[3848,9475],[-1,0],[8,10],[24,3],[63,1],[-23,-5],[-12,-4],[-17,-4],[-42,-1]],[[3295,9545],[-21,-1],[-7,2],[-18,3],[9,2],[18,1],[17,1],[-5,-3],[1,-1],[5,-2],[1,-2]],[[2158,9554],[-10,-1],[-18,1],[-1,0],[9,6],[15,1],[18,-2],[16,0],[-19,-2],[-10,-3]],[[4184,9509],[-2,0],[41,21],[154,34],[66,24],[44,0],[12,-1],[0,-1],[0,-3],[9,-3],[-128,-40],[-75,-18],[-121,-13]],[[5296,8686],[-15,-5],[-34,0],[50,5],[-1,0]],[[5264,8717],[-4,0],[19,3],[30,2],[-1,-1],[-44,-4]],[[5e3,8745],[-36,-1],[49,4],[-1,0],[-12,-3]],[[5433,8772],[-2,0],[3,4],[11,1],[15,1],[19,2],[-13,-3],[-7,-3],[-8,-2],[-18,0]],[[4999,8800],[-46,-5],[17,3],[30,2],[-1,0]],[[5485,8822],[-26,-14],[-25,5],[5,3],[23,2],[23,4]],[[3223,8860],[-2,-1],[14,3],[35,2],[-2,0],[-45,-4]],[[5233,8876],[-35,-4],[-36,5],[7,2],[25,3],[18,4],[5,-4],[13,-3],[3,-3]],[[4102,8942],[-2,0],[52,5],[-22,-4],[-28,-1]],[[4907,8947],[-3,-1],[5,1],[55,10],[-20,-7],[-37,-3]],[[3684,8970],[-3,0],[49,5],[-1,-1],[-17,-3],[-28,-1]],[[4840,8992],[-9,-5],[-55,2],[-1,0],[-8,2],[17,4],[10,4],[1,0],[10,-4],[10,0],[10,-1],[15,-2]],[[4070,9001],[-4,0],[16,2],[33,2],[-3,0],[-42,-4]],[[2519,9022],[-8,-3],[-43,2],[-7,2],[11,2],[4,2],[15,-2],[2,0],[17,-2],[3,0],[6,-1]],[[4702,9029],[-22,-2],[-12,-3],[-14,-1],[-18,1],[8,4],[15,0],[21,0],[22,1]],[[3183,9047],[-7,-3],[-46,1],[-5,2],[11,3],[6,2],[12,-3],[20,-1],[9,-1]],[[2084,9138],[-8,-3],[-43,1],[-2,1],[-7,2],[10,2],[6,2],[16,-2],[18,-2],[5,0],[5,-1]],[[3355,9135],[-1,0],[53,10],[-24,-7],[-28,-3]],[[1988,9153],[-8,-2],[-43,1],[-2,0],[-6,2],[11,2],[5,3],[15,-3],[18,-1],[7,-1],[3,-1]],[[2974,7883],[-15,-2],[-18,7],[9,1],[26,0],[26,1],[-13,-2],[-15,-5]],[[2391,7889],[-19,-1],[-12,5],[11,2],[23,0],[23,1],[-14,-3],[-5,-2],[-7,-2]],[[3053,7921],[-10,4],[-2,1],[11,1],[21,0],[25,1],[-14,-2],[-5,-3],[-7,-2],[-19,0]],[[1837,7923],[-18,-1],[-14,5],[10,2],[23,0],[26,1],[-14,-3],[-6,-2],[-7,-2]],[[3082,8081],[-1,0],[-11,5],[10,1],[24,0],[24,2],[-1,-1],[-15,-2],[-4,-3],[-7,-1],[-19,-1]],[[3061,8158],[-20,-1],[-11,5],[11,1],[22,1],[24,1],[0,-1],[-14,-2],[-7,-3],[-5,-1]],[[3002,8211],[-17,-3],[-5,-3],[-9,-2],[-23,0],[-10,6],[13,2],[1,0],[6,0],[18,0],[26,0]],[[2789,8274],[-1,0],[39,11],[-10,-7],[-28,-4]],[[2717,8328],[-25,3],[-14,2],[1,3],[26,1],[26,3],[-14,-12]],[[2636,8387],[-36,4],[-7,4],[16,10],[1,-1],[26,-17]],[[2310,8539],[-14,-15],[-41,8],[6,4],[49,3]],[[2139,8550],[-5,-2],[-14,1],[-14,2],[-2,5],[12,1],[20,-2],[25,0],[-1,0],[-17,-2],[-4,-3]],[[6623,8241],[-110,-28],[-1,0],[-7,0],[-3,0],[28,9],[96,20],[-3,-1]],[[6549,8306],[-36,-4],[-37,4],[9,3],[23,3],[20,4],[3,-4],[15,-3],[3,-3]],[[7010,8362],[-4,0],[52,9],[39,4],[32,1],[31,-1],[-150,-13]],[[6960,8456],[-21,-1],[-29,4],[9,2],[23,2],[23,2],[-5,-3],[0,-3],[0,-3]],[[6598,8541],[52,-16],[-81,5],[-20,4],[49,7]],[[2666,7143],[-2,0],[6,13],[24,5],[32,1],[30,0],[-90,-19]],[[2226,7167],[-16,-2],[-31,1],[-1,0],[50,1],[-2,0]],[[2102,7336],[-2,-1],[9,3],[25,2],[-32,-4]],[[2242,7339],[32,4],[-1,0],[-5,-3],[-1,0],[-12,-1],[-13,0]],[[3357,7449],[-21,-1],[33,4],[-12,-3]],[[3431,7459],[-36,-1],[-22,4],[8,1],[17,2],[11,2],[1,0],[8,-4],[13,-2],[0,-2]],[[3772,7499],[-1,0],[33,4],[-3,-3],[-29,-1]],[[3951,7517],[33,-2],[-3,0],[-29,1],[-30,2],[3,0],[26,-1]],[[1610,7597],[-2,0],[33,4],[-1,0],[-8,-3],[-22,-1]],[[4184,7607],[-32,-1],[32,4],[0,-3]],[[4004,7679],[-32,-4],[0,2],[34,2],[-2,0]],[[2504,7745],[-26,-1],[33,5],[-7,-4]],[[7604,7611],[0,-1],[-32,2],[55,1],[-1,0],[-22,-2]],[[8405,7650],[-49,-2],[-70,3],[-1,0],[55,4],[57,3],[55,1],[53,0],[-100,-9]],[[7296,7690],[-2,0],[27,2],[1,0],[12,-1],[17,0],[-1,0],[-54,-1]],[[8841,7723],[-21,-2],[-37,1],[59,1],[-1,0]],[[8940,7735],[-1,0],[33,2],[30,-1],[-2,0],[-60,-1]],[[6705,7753],[-17,-1],[-30,2],[-2,2],[11,2],[3,3],[1,0],[10,-4],[19,-2],[5,-2]],[[5975,7769],[15,-2],[1,0],[28,0],[3,0],[17,0],[-11,-4],[-89,-1],[-2,0],[-14,3],[14,1],[27,1],[11,2]],[[8869,7792],[0,-1],[-44,35],[17,7],[47,-10],[20,-10],[-8,-11],[-32,-10]],[[6178,7844],[-3,0],[27,2],[31,-1],[-55,-1]],[[7980,7875],[-24,-2],[-34,1],[58,1]],[[6328,7884],[-2,-1],[21,7],[1,-5],[-20,-1]],[[7278,7993],[-29,-2],[-27,1],[-2,0],[58,1]],[[6996,5811],[-8,2],[9,4],[14,1],[17,-1],[14,0],[-19,-2],[-17,-3],[-10,-1]],[[7054,5907],[-3,-1],[50,6],[-1,0],[-4,-3],[-42,-2]],[[7348,5930],[-22,-1],[16,4],[21,2],[54,4],[-2,0],[-67,-9]],[[6178,5943],[-32,-4],[0,2],[33,2],[-1,0]],[[7707,6133],[-35,4],[-20,3],[-1,3],[27,1],[28,2],[1,-13]],[[6872,6162],[-1,0],[34,3],[-1,0],[-10,-2],[-22,-1]],[[8525,6453],[-30,-3],[-29,0],[-2,0],[63,3],[-2,0]],[[8676,6471],[-21,-1],[-18,3],[5,2],[16,1],[15,3],[-2,-3],[7,-3],[-2,-2]],[[8912,6480],[-12,1],[-18,0],[-2,0],[-18,5],[12,2],[23,0],[24,1],[-1,0],[-8,-3],[4,-3],[0,-1],[-4,-2]],[[9205,6510],[-20,-1],[-18,3],[6,2],[15,2],[14,2],[-2,-3],[6,-3],[-1,-2]],[[9479,6567],[-2,0],[51,4],[-19,-3],[-30,-1]],[[9526,6595],[-26,-5],[1,3],[27,2],[-2,0]],[[6476,6622],[5,-12],[-32,3],[0,2],[13,3],[14,4]],[[8134,6801],[-22,-1],[-6,1],[-10,2],[6,2],[14,2],[15,2],[-1,-3],[5,-3],[-1,-2]],[[6743,6820],[-21,-2],[-17,3],[6,2],[16,2],[13,3],[0,-3],[5,-3],[-2,-2]],[[7543,6933],[-22,-1],[-4,1],[-12,2],[6,2],[16,2],[13,2],[0,-3],[5,-3],[-2,-2]],[[7136,7259],[-101,-1],[23,6],[21,0],[25,-2],[32,-3]],[[204,6011],[-19,-2],[-48,2],[-1,0],[-2,2],[-1,0],[20,2],[18,3],[5,-3],[11,-2],[7,0],[6,-1],[5,-1],[-1,0]],[[2723,6348],[-31,0],[-9,1],[0,3],[-10,3],[2,0],[17,-3],[18,-1],[11,-1],[2,-2]],[[2523,6349],[-30,1],[-56,5],[126,-5],[-1,0],[-39,-1]],[[2548,6576],[-9,-21],[-67,1],[-17,0],[-35,11],[128,9]],[[1406,5379],[-37,-2],[-28,2],[-1,0],[6,2],[17,2],[9,3],[1,0],[11,-3],[19,-2],[4,-2],[-1,0]],[[1044,5386],[-49,0],[-3,2],[18,3],[9,3],[1,0],[9,-2],[4,-1],[23,-2],[11,-1],[-23,-2]],[[2124,5426],[-1,0],[38,9],[39,4],[42,1],[38,-1],[-156,-13]],[[2440,5518],[-37,-2],[-29,2],[5,2],[16,2],[11,3],[11,-3],[13,-1],[7,-1],[3,-2]],[[117,5653],[-26,-5],[-19,2],[-26,2],[0,4],[17,3],[9,4],[14,-4],[23,-3],[9,-3],[-1,0]],[[2536,5666],[-35,-3],[-29,2],[4,2],[17,2],[11,3],[13,-3],[19,-2],[0,-1]],[[2476,5715],[-38,-3],[-4,1],[-23,2],[4,2],[17,1],[11,3],[12,-3],[14,-1],[4,0],[3,-2]],[[2464,5798],[-37,-3],[-28,3],[5,2],[16,2],[11,2],[12,-2],[7,-1],[11,-1],[3,-2]],[[2056,4830],[-43,0],[-5,1],[13,3],[8,5],[11,-3],[13,0],[6,-1],[11,-1],[-14,-4]],[[2779,4866],[-33,-3],[-29,2],[2,2],[15,2],[9,3],[1,0],[11,-2],[20,-2],[4,-2]],[[3372,4986],[-41,0],[-5,1],[11,3],[8,5],[12,-3],[20,-1],[12,-1],[-17,-4]],[[3283,5049],[-34,-3],[-2,0],[-26,2],[3,2],[14,2],[9,4],[1,-1],[13,-2],[18,-2],[4,-2]],[[1373,5123],[-1,0],[-2,2],[11,3],[8,4],[12,-3],[20,0],[4,-1],[5,-1],[-14,-4],[-43,0]],[[8062,6325],[62,-8],[-8,-11],[-25,-20],[-104,-37],[-109,-16],[-147,-11],[-224,2],[-96,6],[-426,20],[9,7],[748,-4],[80,6],[52,21],[67,30],[121,15]],[[7808,5188],[1,0],[-26,6],[1,0],[8,-1],[13,-1],[1,-1],[2,-3]],[[9998,5304],[1,0],[-18,1],[-25,1],[-2,0],[-8,4],[2,-1],[50,-5]],[[7709,5368],[-22,1],[-3,5],[25,-6]],[[6239,4437],[-28,6],[8,2],[21,1],[19,1],[-8,-4],[8,-2],[0,-3],[-20,-1]],[[6687,4456],[-81,-13],[11,8],[24,3],[29,2],[17,0]],[[8107,4591],[-18,-3],[-5,5],[2,0],[13,-1],[8,-1]],[[6327,4600],[-1,0],[-32,2],[0,2],[17,2],[11,2],[7,-3],[12,-3],[4,-1],[-18,-1]],[[8230,4673],[-20,-11],[-7,3],[5,2],[22,6]],[[8433,4682],[-78,-14],[9,8],[26,4],[27,1],[17,1],[-1,0]],[[8683,4863],[-39,-3],[43,8],[-4,-5]],[[2074,4320],[-18,-5],[-32,1],[-5,1],[2,3],[-6,3],[23,-1],[25,0],[11,-2]],[[1768,4325],[-60,4],[-3,3],[26,3],[26,4],[11,-14]],[[3212,4395],[-24,-1],[-29,6],[93,4],[-40,-9]],[[3089,4664],[-42,-3],[-4,0],[-9,1],[0,4],[-5,5],[60,-7]],[[1711,4681],[-24,-4],[-1,4],[48,10],[-23,-10]],[[3322,3660],[-14,-1],[1,1],[50,2],[-22,-2],[-15,0]],[[3913,3682],[-6,-1],[-26,1],[-6,3],[9,1],[18,1],[20,1],[-9,-3],[0,-3]],[[4081,3694],[-55,0],[-10,3],[9,3],[6,5],[13,-3],[37,-8]],[[2448,3798],[-1,0],[-30,1],[50,2],[-19,-3]],[[2258,3868],[9,-14],[-42,4],[-2,4],[19,3],[16,3]],[[6153,3888],[-2,-1],[18,8],[0,-1],[-3,-4],[-13,-2]],[[6344,3942],[-1,0],[-81,57],[3,10],[67,-16],[37,-14],[8,-3],[8,-17],[-41,-17]],[[5900,4082],[-14,2],[-18,2],[-2,3],[16,3],[14,5],[4,-15]],[[4271,4154],[-2,0],[43,4],[-16,-4],[-25,0]],[[6467,3177],[-50,-8],[-21,1],[-12,5],[-18,5],[1,0],[100,-3]],[[6699,3186],[-58,-1],[4,5],[3,1],[14,3],[22,2],[12,4],[9,-6],[17,-5],[-9,-3],[-14,0]],[[7289,3226],[-65,-8],[-24,0],[-7,4],[-7,4],[103,0]],[[7622,3228],[0,-1],[-4,1],[-2,0],[-5,1],[-2,0],[21,4],[21,4],[47,4],[99,6],[2,0],[-68,-8],[-109,-11]],[[7938,3271],[-5,-2],[-9,4],[9,3],[6,1],[12,2],[20,3],[-2,-1],[-31,-10]],[[5306,3310],[-76,-4],[3,7],[16,1],[16,-2],[9,0],[32,-2]],[[8015,3311],[-3,-1],[84,42],[30,10],[-40,-27],[-29,-13],[-42,-11]],[[8096,3404],[-20,4],[1,3],[21,7],[-2,-14]],[[5975,3431],[-46,-4],[-27,2],[3,2],[13,3],[1,3],[24,-2],[26,-2],[6,-2]],[[8043,3483],[-11,-6],[-25,0],[18,3],[18,3]],[[6572,3491],[0,-6],[-7,2],[7,4]],[[8111,3541],[-9,5],[7,3],[19,2],[22,3],[-39,-13]],[[8262,3589],[166,0],[1,0],[-18,-2],[-29,-3],[-119,-10],[-55,-4],[0,5],[11,5],[43,9]],[[7754,1380],[-44,-9],[-1,0],[-19,6],[-6,1],[7,3],[30,-1],[34,1],[-1,-1]],[[7555,1534],[24,-11],[-39,2],[-6,3],[11,3],[9,4],[1,-1]],[[6569,1698],[-1,0],[-17,0],[-11,5],[6,1],[20,1],[14,1],[-6,-3],[0,-3],[-5,-2]],[[6435,1856],[-5,-2],[-8,0],[-10,0],[-10,5],[9,1],[1,0],[8,0],[8,1],[13,1],[-6,-3],[0,-3]],[[6236,1961],[-13,5],[7,2],[17,1],[15,1],[-6,-2],[-1,-1],[0,-2],[-1,-1],[-2,-1],[-7,-1],[-9,-1]],[[3913,1157],[-38,4],[-10,3],[-3,1],[-1,1],[-6,4],[-12,6],[32,-4],[35,-3],[16,-4],[-13,-8]],[[4984,1188],[-44,-7],[-16,0],[-6,4],[-12,6],[79,-3],[-1,0]],[[3328,1216],[-40,2],[-3,1],[-4,2],[4,2],[5,2],[4,4],[34,-13]],[[6498,1249],[-89,-7],[14,8],[24,1],[26,-1],[25,-1]],[[2819,1267],[-45,4],[-2,2],[25,2],[11,-3],[21,0],[6,-1],[4,-1],[-20,-3]],[[2586,1389],[1,0],[-32,3],[-6,3],[8,3],[3,2],[1,0],[25,-11]],[[2397,1520],[-3,-12],[-34,6],[0,2],[37,4]],[[7022,519],[74,-11],[19,-6],[-5,-9],[-61,7],[-26,7],[-6,6],[5,6]],[[4476,9],[89,-9],[-63,0],[-21,3],[-5,6]],[[4789,9],[-48,-2],[8,8],[27,3],[84,4],[-27,-5],[-19,-4],[-25,-4]],[[4113,42],[25,-13],[-29,2],[-5,2],[5,4],[4,5]],[[5196,81],[-119,-22],[-4,13],[29,12],[46,5],[48,-8]],[[4060,61],[-70,0],[-152,47],[-35,24],[129,8],[0,-9],[-57,-1],[-10,-4],[8,-5],[-11,-7],[70,-9],[62,-13],[45,-16],[21,-15]],[[5259,128],[-20,-24],[-59,9],[-18,10],[25,11],[78,8],[9,-4],[-5,-3],[-9,-3],[-1,-4]]],transform:{scale:[.00010684662216223326,.0007796596716671717],translate:[72.684825066,-.688571872999901]}},m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",
m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();