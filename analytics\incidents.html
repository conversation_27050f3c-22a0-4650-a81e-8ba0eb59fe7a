<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>

  <link rel="stylesheet" href="../assets/vendor/apexcharts/dist/apexcharts.css">
  <style type="text/css">
    .apexcharts-tooltip.apexcharts-theme-light
    {
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  </style>
</head>

<body class="bg-gray-50 dark:bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="lg:ms-65 lg:hidden fixed top-0 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 bg-white border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
    <div class="flex justify-between xl:grid xl:grid-cols-3 basis-full items-center w-full py-2.5 px-2 sm:px-5">
      <!-- Logo & Sidebar Toggle -->
      <div class="flex items-center gap-x-3">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
          <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->

        <div class="lg:hidden">
          <!-- Sidebar Toggle -->
          <button type="button" class="w-7 h-9.5 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13" />
            </svg>
          </button>
          <!-- End Sidebar Toggle -->
        </div>

        <div class="lg:hidden">
          <!-- Templates Dropdown -->
          <div class="hs-dropdown  relative  [--scope:window] [--auto-close:inside] inline-flex">
            <button id="hs-dropdown-preview-navbar" type="button" class="hs-dropdown-toggle  group relative flex justify-center items-center size-8 text-xs rounded-md text-gray-800 hover:bg-gray-100 focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <span class="">
                <svg class=" size-4 shrink-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </span>

              <span class="absolute -top-0.5 -end-0.5">
                <span class="relative flex">
                  <span class="animate-ping absolute inline-flex size-full rounded-full bg-red-400 dark:bg-red-600 opacity-75"></span>
                  <span class="relative inline-flex size-2 bg-red-500 rounded-full"></span>
                  <span class="sr-only">Notification</span>
                </span>
              </span>
            </button>

            <!-- Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-full min-w-90 md:w-125 transition-[opacity,margin] duration opacity-0 hidden z-61 overflow-hidden border border-gray-200 bg-white rounded-xl shadow-xl dark:bg-neutral-800 dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-preview-navbar">
              <!-- Tab -->
              <div class="p-3 pb-0 flex flex-wrap justify-between items-center gap-3 border-b border-gray-200 dark:border-neutral-700">
                <!-- Nav Tab -->
                <nav class="flex gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pmn-item-pro" aria-selected="true" data-hs-tab="#hs-pmn-pro" aria-controls="hs-pmn-pro" role="tab">
                    Pro
                  </button>
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pmn-item-free" aria-selected="false" data-hs-tab="#hs-pmn-free" aria-controls="hs-pmn-free" role="tab">
                    Free
                  </button>
                </nav>
                <!-- End Nav Tab -->

                <!-- Switch/Toggle -->
                <div class="mb-2 flex items-center gap-x-0.5">
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:hidden flex shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="dark">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                    </svg>
                    Switch to Dark
                  </button>
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:flex hidden shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="light">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="4"></circle>
                      <path d="M12 2v2"></path>
                      <path d="M12 20v2"></path>
                      <path d="m4.93 4.93 1.41 1.41"></path>
                      <path d="m17.66 17.66 1.41 1.41"></path>
                      <path d="M2 12h2"></path>
                      <path d="M20 12h2"></path>
                      <path d="m6.34 17.66-1.41 1.41"></path>
                      <path d="m19.07 4.93-1.41 1.41"></path>
                    </svg>
                    Switch to Light
                  </button>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <!-- End Tab -->

              <!-- Tab Content -->
              <div id="hs-pmn-pro" class="" role="tabpanel" aria-labelledby="hs-pmn-item-pro">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (21)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../pro/pricing.html">
                      Purchase
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/dashboard/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Dashboard
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img55.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img55.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img58.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img58.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/video-call/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img61.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img61.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Video Call
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/startup/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img32.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img32.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Startup
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/payment/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img8.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img8.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Payment
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img16.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img16.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Chat
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img21.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img21.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ecommerce/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        E-Commerce
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img46.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img46.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/crm/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img11.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img11.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CRM
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/workspace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img18.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img18.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Workspace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 bg-gray-100 dark:bg-neutral-700 transition" href="../../pro/analytics/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img9.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img9.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Analytics
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/calendars/day.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img14.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img14.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Calendars
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/smart-home/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img35.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img35.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Smart Home
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img52.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img52.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/inbox/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img26.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img26.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Inbox
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/project/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img10.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img10.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Project
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop-marketplace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img29.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img29.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop Marketplace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img49.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img49.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/files/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img12.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img12.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Files
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                      Examples (780<!-- (780) -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                      Templates (21)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->

              <!-- Tab Content -->
              <div id="hs-pmn-free" class="hidden" role="tabpanel" aria-labelledby="hs-pmn-item-free">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (5)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../templates.html">
                      Free download
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img5.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img5.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img2.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img2.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img3.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img3.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../examples.html">
                      Examples (220+<!-- 222 -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../templates.html">
                      Templates (5)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->
            </div>
            <!-- End Dropdown -->
          </div>
          <!-- End Templates Dropdown -->
        </div>
      </div>
      <!-- End Logo & Sidebar Toggle -->

      <div class="lg:hidden h-9.5 flex justify-end items-center gap-x-2">
        <!-- Account Dropdown -->
        <div class="hs-dropdown inline-flex   [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
          <button id="hs-pro-dnad" type="button" class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
            <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
          </button>

          <!-- Account Dropdown -->
          <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnad">
            <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
              <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                <div class="grow">
                  <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                    James Collison
                  </span>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Preline@HS
                  </p>
                </div>
              </a>
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
                Billing
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                Settings
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                  <circle cx="12" cy="7" r="4" />
                </svg>
                My account
              </a>
            </div>
            <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
              <!-- Switch/Toggle -->
              <div class="flex flex-wrap justify-between items-center gap-2">
                <label for="hs-pro-dnaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                <label for="hs-pro-dnaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                  <input data-hs-theme-switch type="checkbox" id="hs-pro-dnaddm" class="peer sr-only">
                  <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                  <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                </label>
              </div>
              <!-- End Switch/Toggle -->
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Customization
                <div class="ms-auto">
                  <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                    New
                  </span>
                </div>
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Manage team
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Sign out
              </a>
            </div>
            <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
              <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M8 12h8" />
                  <path d="M12 8v8" />
                </svg>
                Add team account
              </button>
            </div>
          </div>
          <!-- End Account Dropdown -->
        </div>
        <!-- End Account Dropdown -->
      </div>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN SIDEBAR ========== -->
  <aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
    hs-overlay-open:translate-x-0
    -translate-x-full transition-all duration-300 transform
    w-82 h-full
    hidden
    fixed inset-y-0 start-0 z-60
    bg-white
    lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
    dark:bg-neutral-800" tabindex="-1" aria-label="Sidebar">
    <div class="h-full flex">
      <!-- Nav Sidebar -->
      <div class="w-16 flex flex-col h-full max-h-full">
        <div class="p-4 flex flex-col items-center">
          <!-- Logo -->
          <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
            <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            </svg>
          </a>
          <!-- End Logo -->
        </div>

        <!-- Content -->
        <div class="h-full px-4 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <!-- Nav -->
          <ul class="flex flex-col items-center space-y-1">
            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                  <polyline points="9 22 9 12 15 12 15 22" />
                </svg>
                <span class="sr-only">Dashboard</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Dashboard
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
                <span class="sr-only">Users</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Users
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 9 5 12 1.8-5.2L21 14Z" />
                  <path d="M7.2 2.2 8 5.1" />
                  <path d="m5.1 8-2.9-.8" />
                  <path d="M14 4.1 12 6" />
                  <path d="m6 12-1.9 2" />
                </svg>
                <span class="sr-only">Events</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Events
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2" />
                  <rect width="18" height="18" x="3" y="4" rx="2" />
                  <circle cx="12" cy="10" r="2" />
                  <line x1="8" x2="8" y1="2" y2="4" />
                  <line x1="16" x2="16" y1="2" y2="4" />
                </svg>
                <span class="sr-only">Attributes</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Attributes
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4 text-indigo-600 dark:text-indigo-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="m16 12-4-4-4 4" />
                  <path d="M12 16V8" />
                </svg>
                <span class="sr-only">Upgrade</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Upgrade
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 22v-5" />
                  <path d="M9 8V2" />
                  <path d="M15 8V2" />
                  <path d="M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z" />
                </svg>
                <span class="sr-only">Integrations</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Integrations
              </span>
            </li>
            <!-- End Item -->
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Content -->

        <footer class="hidden lg:block text-center border-t border-gray-200 dark:border-neutral-700">
          <!-- Account Dropdown -->
          <div class="hs-dropdown  inline-flex  [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
            <button id="hs-pro-dsad" type="button" class="w-full flex items-center gap-x-3 text-start py-4 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
            </button>

            <!-- Account Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dsad">
              <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
                <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                  <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                  <div class="grow">
                    <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                      James Collison
                    </span>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      Preline@HS
                    </p>
                  </div>
                </a>
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <line x1="2" x2="22" y1="10" y2="10" />
                  </svg>
                  Billing
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  Settings
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                  My account
                </a>
              </div>
              <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
                <!-- Switch/Toggle -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                  <label for="hs-pro-dsaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                  <label for="hs-pro-dsaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                    <input data-hs-theme-switch type="checkbox" id="hs-pro-dsaddm" class="peer sr-only">
                    <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                    <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                  </label>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Customization
                  <div class="ms-auto">
                    <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                      New
                    </span>
                  </div>
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Manage team
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Sign out
                </a>
              </div>
              <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
                <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                  <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M8 12h8" />
                    <path d="M12 8v8" />
                  </svg>
                  Add team account
                </button>
              </div>
            </div>
            <!-- End Account Dropdown -->
          </div>
          <!-- End Account Dropdown -->
        </footer>

        <!-- Sidebar Close -->
        <div class="lg:hidden absolute top-4 -end-6 z-10">
          <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-e-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="7 8 3 12 7 16" />
              <line x1="21" x2="11" y1="12" y2="12" />
              <line x1="21" x2="11" y1="6" y2="6" />
              <line x1="21" x2="11" y1="18" y2="18" />
            </svg>
          </button>
        </div>
        <!-- End Sidebar Close -->
      </div>
      <!-- End Nav Sidebar -->

      <!-- Secondary Sidebar -->
      <div class="w-72 bg-white border-x border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
        <div class="flex flex-col h-full max-h-full">
          <!-- Quick Actions -->
          <div class="py-3 px-2 border-b border-gray-200 dark:border-neutral-700">
            <button type="button" class="py-1.5 ps-3 pe-1.5 w-full inline-flex items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700/50">
              Quick actions
              <span class="ms-auto flex items-center gap-x-1 py-px px-1.5 border border-gray-200 rounded-md dark:border-neutral-700">
                <svg class="shrink-0 size-2.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
                </svg>
                <span class="text-[11px] uppercase">k</span>
              </span>
            </button>
          </div>
          <!-- End Quick Actions -->

          <!-- Content -->
          <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <!-- Nav -->
            <nav class="hs-accordion-group size-full flex flex-col justify-between" data-hs-accordion-always-open>
              <!-- Body -->
              <div>
                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/index.html">
                      Overview
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/visitors.html">
                      Visitors
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/survey.html">
                      Survey
                      <div class="ms-auto">
                        <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-md text-[10px] leading-4 font-medium bg-white border border-gray-200 text-gray-800 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                          Beta
                        </span>
                      </div>
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 bg-gray-100 focus:bg-gray-200 dark:bg-neutral-700 dark:focus:bg-neutral-600" href="../../pro/analytics/incidents.html">
                      Incidents
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/empty-states.html">
                      Empty Contents
                    </a>
                  </li>
                  <!-- End Link -->
                </ul>

                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li class="hs-accordion active" id="projects-accordion">
                    <button type="button" class="hs-accordion-toggle py-1 px-3 flex justify-center items-center gap-x-1 text-xs text-gray-500 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-500 dark:focus:bg-neutral-700" aria-expanded="true" aria-controls="projects-accordion-sub">
                      <svg class="hs-accordion-active:rotate-90 shrink-0 size-3 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                      Channels
                    </button>

                    <div id="projects-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="projects-accordion">
                      <ul class="hs-accordion-group mt-1 flex flex-col" data-hs-accordion-always-open>
                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/index.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />
                                <path d="m3.3 7 8.7 5 8.7-5" />
                                <path d="M12 22V12" />
                              </svg>
                            </span>
                            PRO
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="18" height="7" x="3" y="3" rx="1" />
                                <rect width="9" height="7" x="3" y="14" rx="1" />
                                <rect width="5" height="7" x="16" y="14" rx="1" />
                              </svg>
                            </span>
                            Examples
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="4" width="20" height="16" rx="2" />
                                <path d="M10 4v4" />
                                <path d="M2 8h20" />
                                <path d="M6 4v4" />
                              </svg>
                            </span>
                            Templates
                          </a>
                        </li>
                        <!-- End Link -->
                      </ul>
                    </div>
                  </li>
                  <!-- End Link -->
                </ul>
              </div>
              <!-- End Body -->

              <!-- Footer -->
              <footer class="p-2 pb-4 flex flex-col gap-y-3">
                <!-- Credits Card -->
                <a class="p-3 group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="flex justify-between items-center gap-x-1">
                    <div class="grow">
                      <p class="font-medium text-sm text-gray-800 dark:text-neutral-200">
                        65/100 credits used
                      </p>
                      <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                        Invite team members
                      </p>
                      <p class="mt-2 flex items-center gap-1 text-xs text-indigo-600 dark:text-indigo-500">
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
                        </svg>
                        Upgrade now
                        <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                          <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                        </svg>
                      </p>
                    </div>

                    <!-- Circular Progress -->
                    <div class="relative size-14">
                      <svg class="size-full -rotate-90" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-100 dark:text-indigo-500/40" stroke-width="4"></circle>
                        <!-- Progress Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-600 dark:text-indigo-500" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="35" stroke-linecap="round"></circle>
                      </svg>
                    </div>
                    <!-- End Circular Progress -->
                  </div>
                </a>
                <!-- End Credits Card -->

                <!-- News Card -->
                <a class="group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 transition duration-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="p-2 w-full h-32 rounded-md">
                    <img class="size-full object-cover rounded-md dark:hidden" src="../assets/img/demo-previews/img32.webp" alt="Card Image">
                    <img class="size-full object-cover rounded-md dark:block hidden" src="../assets/img/demo-previews-dark/img32.webp" alt="Card Image">
                  </div>

                  <div class="p-3">
                    <div class="flex items-center gap-2">
                      <div class="grow">
                        <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                          New
                        </p>
                        <p class="text-wrap font-medium text-sm text-gray-800 group-hover:text-indigo-600 group-focus:text-indigo-600 dark:text-neutral-200 dark:group-hover:text-white dark:group-focus:text-white">
                          Preline Startup Demo
                        </p>
                      </div>

                      <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </a>
                <!-- End News Card -->
              </footer>
              <!-- End Footer -->
            </nav>
            <!-- End Nav -->
          </div>
          <!-- End Content -->
        </div>
      </div>
      <!-- End Secondary Sidebar -->
    </div>
  </aside>
  <!-- ========== END MAIN SIDEBAR ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" class="lg:ps-82 pt-15 lg:pt-0">
    <div class="pt-3.5 pb-10 px-2 sm:px-5 flex flex-col gap-y-5">
      <!-- Header -->
      <div class="flex flex-wrap justify-between items-center gap-2">
        <div>
          <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Incidents (230)
          </h1>
        </div>

        <!-- Button Group -->
        <div class="flex flex-wrap items-center gap-3">
          <!-- Calendar Dropdown -->
          <div class="hs-dropdown [--auto-close:inside] [--placement:top-right] inline-flex">
            <button id="hs-pro-dnic" type="button" class="py-1.5 px-2.5 inline-flex items-center text-[13px] rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <svg class="shrink-0 me-2 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                <line x1="16" x2="16" y1="2" y2="6" />
                <line x1="8" x2="8" y1="2" y2="6" />
                <line x1="3" x2="21" y1="10" y2="10" />
                <path d="M8 14h.01" />
                <path d="M12 14h.01" />
                <path d="M16 14h.01" />
                <path d="M8 18h.01" />
                <path d="M12 18h.01" />
                <path d="M16 18h.01" />
              </svg>
              Last 24 hours
              <svg class="shrink-0 ms-1.5 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>

            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-79.5 transition-[opacity,margin] duration opacity-0 hidden z-50 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnic">

              <!-- Calendar -->
              <div class="p-3 space-y-0.5">
                <!-- Months -->
                <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                  <!-- Prev Button -->
                  <div class="col-span-1">
                    <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                      <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6" />
                      </svg>
                    </button>
                  </div>
                  <!-- End Prev Button -->

                  <!-- Month / Year -->
                  <div class="col-span-3 flex justify-center items-center gap-x-1">
                    <div class="relative">
                      <select data-hs-select='{
                            "placeholder": "Select month",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                        <option value="0">January</option>
                        <option value="1">February</option>
                        <option value="2">March</option>
                        <option value="3">April</option>
                        <option value="4">May</option>
                        <option value="5">June</option>
                        <option value="6" selected>July</option>
                        <option value="7">August</option>
                        <option value="8">September</option>
                        <option value="9">October</option>
                        <option value="10">November</option>
                        <option value="11">December</option>
                      </select>
                    </div>

                    <span class="text-gray-800 dark:text-neutral-200">/</span>

                    <div class="relative">
                      <select data-hs-select='{
                            "placeholder": "Select year",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                        <option selected>2023</option>
                        <option>2024</option>
                        <option>2025</option>
                        <option>2026</option>
                        <option>2027</option>
                      </select>
                    </div>
                  </div>
                  <!-- End Month / Year -->

                  <!-- Next Button -->
                  <div class="col-span-1 flex justify-end">
                    <button type="button" class=" size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                      <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </button>
                  </div>
                  <!-- End Next Button -->
                </div>
                <!-- Months -->

                <!-- Weeks -->
                <div class="flex pb-1.5">
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Mo
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Tu
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    We
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Th
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Fr
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Sa
                  </span>
                  <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                    Su
                  </span>
                </div>
                <!-- Weeks -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      26
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      27
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      28
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      29
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                      30
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      1
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      2
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      3
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      4
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      5
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      6
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      7
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      8
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      9
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      10
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      11
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      12
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      13
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      14
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      15
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      16
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      17
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      18
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      19
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                      20
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      21
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      22
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      23
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      24
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      25
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      26
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      27
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      28
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      29
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      30
                    </button>
                  </div>
                </div>
                <!-- Days -->

                <!-- Days -->
                <div class="flex">
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                      31
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      1
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      2
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      3
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      4
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      5
                    </button>
                  </div>
                  <div>
                    <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                      6
                    </button>
                  </div>
                </div>
                <!-- Days -->
              </div>
              <!-- End Calendar -->
            </div>
          </div>
          <!-- End Calendar Dropdown -->
        </div>
        <!-- End Button Group -->
      </div>
      <!-- End Header -->

      <!-- Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-5">
        <!-- Card -->
        <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="pb-5 flex flex-wrap justify-between items-center gap-2">
            <div class="flex flex-wrap items-center gap-2">
              <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                Incidents status by creation time
              </h2>
            </div>

            <div class="flex flex-wrap items-center gap-2">
              <a class="group inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400" href="#">
                Manage incidents
                <svg class="shrink-0 size-3.5 group-hover:opacity-100 group-focus:opacity-100 transition duration-300" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                  <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
          <!-- End Header -->

          <!-- Apex Line Chart -->
          <div id="hs-pro-ascich" class="min-h-[255px] -mx-2"></div>

          <!-- Grid -->
          <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <!-- Legend Indicator -->
            <div class="flex items-center gap-2">
              <span class="shrink-0 size-3 inline-block bg-cyan-400 rounded-xs"></span>
              <div class="grow">
                <span class="block text-sm text-gray-800 dark:text-neutral-200">
                  New (12)
                </span>
              </div>
            </div>
            <!-- End Legend Indicator -->

            <!-- Legend Indicator -->
            <div class="flex items-center gap-2">
              <span class="shrink-0 size-3 inline-block bg-blue-500 rounded-xs"></span>
              <div class="grow">
                <span class="block text-sm text-gray-800 dark:text-neutral-200">
                  Active (24)
                </span>
              </div>
            </div>
            <!-- End Legend Indicator -->

            <!-- Legend Indicator -->
            <div class="flex items-center gap-2">
              <span class="shrink-0 size-3 inline-block bg-indigo-700 rounded-xs"></span>
              <div class="grow">
                <span class="block text-sm text-gray-800 dark:text-neutral-200">
                  Closed (80)
                </span>
              </div>
            </div>
            <!-- End Legend Indicator -->
          </div>
          <!-- End Grid -->
        </div>
        <!-- End Card -->

        <div class="flex flex-col gap-y-5">
          <!-- Card -->
          <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
            <!-- Header -->
            <div class="pb-5 flex flex-wrap justify-between items-center gap-2">
              <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                  Incident by severity
                </h2>
              </div>

              <div class="flex flex-wrap items-center gap-2">
                <a class="group inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400" href="#">
                  Analyze SOC efficiency
                  <svg class="shrink-0 size-3.5 group-hover:opacity-100 group-focus:opacity-100 transition duration-300" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                    <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
            <!-- End Header -->

            <!-- Multiple Progress Bars -->
            <div class="flex gap-1.5 w-full h-2.5">
              <div class="overflow-hidden bg-red-500 rounded-full" role="progressbar" aria-valuenow="23" aria-valuemin="0" aria-valuemax="100" style="width: 23%"></div>

              <div class="overflow-hidden bg-orange-400 rounded-full" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%"></div>

              <div class="overflow-hidden bg-yellow-400 rounded-full" role="progressbar" aria-valuenow="62" aria-valuemin="0" aria-valuemax="100" style="width: 62%"></div>

              <div class="overflow-hidden bg-gray-100 bg-[linear-gradient(135deg,var(--color-gray-400)_25%,transparent_25%,transparent_50%,var(--color-gray-400)_50%,var(--color-gray-400)_75%,transparent_75%,transparent)] bg-[length:7px_7px] rounded-full dark:bg-[linear-gradient(135deg,var(--color-neutral-600)_25%,transparent_25%,transparent_50%,var(--color-neutral-600)_50%,var(--color-neutral-600)_75%,transparent_75%,transparent)] dark:bg-neutral-800" role="progressbar" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100" style="width: 15%"></div>
            </div>
            <!-- End Multiple Progress Bars -->

            <!-- Grid -->
            <div class="mt-7 grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  163
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <div class="flex items-center gap-0.5">
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                  </div>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      High
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  168
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <div class="flex items-center gap-0.5">
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                  </div>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      Medium
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  382
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <div class="flex items-center gap-0.5">
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                  </div>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      Low
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  66
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <div class="flex items-center gap-0.5">
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                    <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                  </div>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      Informational
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->
            </div>
            <!-- End Grid -->
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
            <!-- Header -->
            <div class="pb-5 flex flex-wrap justify-between items-center gap-2">
              <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                  Closed incidents by classification
                </h2>
              </div>

              <div class="flex flex-wrap items-center gap-2">
                <a class="group inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400" href="#">
                  Analyze SOC efficiency
                  <svg class="shrink-0 size-3.5 group-hover:opacity-100 group-focus:opacity-100 transition duration-300" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                    <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
            <!-- End Header -->

            <!-- Multiple Bars Progress -->
            <div class="flex gap-1.5 w-full h-2.5">
              <div class="overflow-hidden bg-blue-500 rounded-full" role="progressbar" aria-valuenow="2" aria-valuemin="0" aria-valuemax="100" style="width: 2%"></div>

              <div class="overflow-hidden bg-indigo-500 rounded-full" role="progressbar" aria-valuenow="2" aria-valuemin="0" aria-valuemax="100" style="width: 2%"></div>

              <div class="overflow-hidden bg-cyan-400 rounded-full" role="progressbar" aria-valuenow="55" aria-valuemin="0" aria-valuemax="100" style="width: 55%"></div>

              <div class="overflow-hidden bg-gray-100 bg-[linear-gradient(135deg,var(--color-gray-400)_25%,transparent_25%,transparent_50%,var(--color-gray-400)_50%,var(--color-gray-400)_75%,transparent_75%,transparent)] bg-[length:7px_7px] rounded-full dark:bg-[linear-gradient(135deg,var(--color-neutral-600)_25%,transparent_25%,transparent_50%,var(--color-neutral-600)_50%,var(--color-neutral-600)_75%,transparent_75%,transparent)] dark:bg-neutral-800" role="progressbar" aria-valuenow="41" aria-valuemin="0" aria-valuemax="100" style="width: 41%"></div>
            </div>
            <!-- End Multiple Bars Progress -->

            <!-- Grid -->
            <div class="mt-7 grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  0
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <span class="shrink-0 w-3.5 h-1 inline-block bg-blue-500 rounded-full"></span>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      True Positive
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  0
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <span class="shrink-0 w-3.5 h-1 inline-block bg-indigo-500 rounded-full"></span>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      False Positive
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  55
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <span class="shrink-0 w-3.5 h-1 inline-block bg-cyan-400 rounded-full"></span>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      Benign Positive
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->

              <div>
                <span class="block text-2xl text-gray-800 dark:text-neutral-200">
                  41
                </span>

                <!-- Legend Indicator -->
                <div class="flex items-center gap-2">
                  <span class="shrink-0 w-3.5 h-1 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                  <div class="grow">
                    <span class="block text-sm text-gray-500 dark:text-neutral-500">
                      Undetermined
                    </span>
                  </div>
                </div>
                <!-- End Legend Indicator -->
              </div>
              <!-- End Col -->
            </div>
            <!-- End Grid -->
          </div>
          <!-- End Card -->
        </div>
        <!-- End Col -->
      </div>
      <!-- End Grid -->

      <!-- Card -->
      <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
          <div class="flex flex-wrap items-center gap-2">
            <h2 class="font-medium text-gray-800 dark:text-neutral-200">
              Table
            </h2>
          </div>

          <div class="flex flex-wrap items-center gap-2">
            <button type="button" class="py-1.5 px-2.5 inline-flex items-center gap-x-2 text-[13px] rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                <path d="M12 18v-6" />
                <path d="m9 15 3 3 3-3" />
              </svg>
              Export
            </button>

            <button type="button" class="py-1.5 px-2.5 inline-flex items-center gap-x-2 text-[13px] rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="21" x2="14" y1="4" y2="4" />
                <line x1="10" x2="3" y1="4" y2="4" />
                <line x1="21" x2="12" y1="12" y2="12" />
                <line x1="8" x2="3" y1="12" y2="12" />
                <line x1="21" x2="16" y1="20" y2="20" />
                <line x1="12" x2="3" y1="20" y2="20" />
                <line x1="14" x2="14" y1="2" y2="6" />
                <line x1="8" x2="8" y1="10" y2="14" />
                <line x1="16" x2="16" y1="18" y2="22" />
              </svg>
              Filter
            </button>
          </div>
        </div>
        <!-- End Header -->

        <!-- Filter -->
        <div class="py-3 px-5 border-b border-gray-200 dark:border-neutral-700">
          <div class="flex justify-between gap-x-3">
            <div class="w-1/2">
              <!-- Search Input -->
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
                  <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.3-4.3" />
                  </svg>
                </div>
                <input type="text" class="py-1 sm:py-1.5 ps-10 pe-8 block w-full bg-gray-100 border-transparent rounded-lg sm:text-sm focus:bg-white focus:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:border-transparent dark:text-neutral-400 dark:placeholder:text-neutral-400 dark:focus:bg-neutral-800 dark:focus:ring-neutral-600" placeholder="Search...">
                <div class="hidden absolute inset-y-0 end-0 flex items-center z-20 pe-1">
                  <button type="button" class="inline-flex shrink-0 justify-center items-center size-6 rounded-full text-gray-500 hover:text-indigo-600 focus:outline-hidden focus:text-indigo-600 dark:text-neutral-500 dark:hover:text-indigo-500 dark:focus:text-indigo-500" aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10" />
                      <path d="m15 9-6 6" />
                      <path d="m9 9 6 6" />
                    </svg>
                  </button>
                </div>
              </div>
              <!-- End Search Input -->
            </div>

            <!-- Switch/Toggle -->
            <div class="flex flex-wrap justify-between items-center gap-2">
              <label for="hs-pro-avdchli" class="flex-1 cursor-pointer text-sm text-gray-500 dark:text-neutral-500">Group columns</label>
              <label for="hs-pro-avdchli" class="relative inline-block w-9 h-5 cursor-pointer">
                <input type="checkbox" id="hs-pro-avdchli" class="peer sr-only">
                <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-4 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
              </label>
            </div>
            <!-- End Switch/Toggle -->
          </div>
        </div>
        <!-- End Filter -->

        <!-- Table Section -->
        <div class="overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
              <thead>
                <tr>
                  <th scope="col" class="px-4 py-2">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </th>

                  <th scope="col">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Status
                      </span>
                    </div>
                  </th>

                  <th scope="col">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Source
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="min-w-72">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Incident name
                      </span>
                    </div>
                  </th>

                  <th scope="col">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Resources
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="min-w-50">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Time generated
                      </span>
                    </div>
                  </th>

                  <th scope="col">
                    <div class="px-5 py-2 text-start">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        Severity
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="min-w-34">
                    <div class="px-5 py-2 text-center">
                      <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                        In audit scope
                      </span>
                    </div>
                  </th>
                </tr>
              </thead>

              <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Port scanning activity
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      7/31/2023, 11:42:29 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Medium
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Use of malicious URL detected
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unknown
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      7/23/2022, 8:41:47 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unusual application behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unknown
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      4/2/2024, 12:42:38 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Informational
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Preline
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Suspicious login attempt
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unknown
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      11/21/2021, 6:17:44 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Medium
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ShieldX
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unusual application behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Germany
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      11/9/2023, 5:49:19 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Low
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      CyberWall
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Data exfiltration behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Canada
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      3/14/2023, 7:53:42 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      NetGuard
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Data exfiltration behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Singapore
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      4/14/2019, 1:25:16 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Informational
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      NetGuard
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Port scanning activity
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Germany
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      8/2/2020, 7:35:47 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Low
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      CyberWall
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Suspicious login attempt
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Germany
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      2/19/2023, 4:35:26 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Medium
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      SafeLink
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Multiple failed logins
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      10/18/2021, 1:06:49 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Low
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Data exfiltration behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      12/22/2019, 5:19:26 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Preline
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Suspicious login attempt
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unknown
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      4/27/2023, 2:33:34 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Medium
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      CyberWall
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Use of malicious URL detected
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      USA
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      2/19/2022, 6:22:18 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      NetGuard
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Data exfiltration behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      11/26/2023, 3:27:26 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-orange-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Medium
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      SafeLink
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Use of malicious URL detected
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unknown
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      9/9/2023, 3:32:44 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Informational
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Preline
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Suspicious login attempt
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      USA
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      1/9/2024, 9:26:20 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Informational
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-green-500 text-white rounded-full">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6 9 17l-5-5" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Multiple failed logins
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Germany
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      12/20/2019, 10:25:31 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-700 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Closed
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ThreatEye
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Unusual application behavior
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      2/1/2023, 1:29:20 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-red-500 rounded-full"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        High
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-cyan-400 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        New
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Preline
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Use of malicious URL detected
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Singapore
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      11/5/2023, 3:17:16 PM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Low
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>

                <tr>
                  <td class="size-px whitespace-nowrap text-center px-4.5 py-4">
                    <input type="checkbox" class="shrink-0 border-gray-300 rounded-sm text-indigo-600 focus:ring-indigo-500 checked:border-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800">
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="flex items-center gap-x-2">
                      <span class="block size-3 bg-indigo-500 rounded-xs"></span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Active
                      </span>
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      ShieldX
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Suspicious login attempt
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      Japan
                    </span>
                  </td>
                  <td class="size-px px-5 py-1">
                    <span class="text-sm text-gray-600 dark:text-neutral-400">
                      8/6/2022, 4:18:45 AM
                    </span>
                  </td>
                  <td class="size-px whitespace-nowrap px-5 py-1">
                    <span class="flex items-center gap-x-2">
                      <span class="flex items-center gap-0.5">
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-yellow-400 rounded-full"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                        <span class="shrink-0 w-1 h-3.5 inline-block bg-gray-300 rounded-full dark:bg-neutral-600"></span>
                      </span>
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        Low
                      </span>
                    </span>
                  </td>
                  <td class="size-px px-5 py-1 whitespace-nowrap text-center">
                    <span class="inline-flex shrink-0 justify-center items-center size-4 bg-gray-300 text-gray-800 rounded-full dark:bg-neutral-600 dark:text-neutral-200">
                      <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                      </svg>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- End Table -->
          </div>
        </div>
        <!-- End Table Section -->

        <!-- Footer -->
        <div class="py-3 px-5 border-t border-gray-200 dark:border-neutral-800">
          <!-- Footer -->
          <div class="grid grid-cols-2 items-center gap-y-2 sm:gap-y-0 sm:gap-x-5">
            <p class="text-sm text-gray-800 dark:text-neutral-200">
              <span class="font-medium">20</span>
              <span class="text-gray-500 dark:text-neutral-500">results</span>
            </p>

            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
              <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Previous">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg>
                <span class="sr-only">Previous</span>
              </button>
              <div class="flex items-center gap-x-1">
                <span class="min-h-9.5 min-w-9.5 flex justify-center items-center bg-gray-100 text-gray-800 py-2 px-3 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-white" aria-current="page">1</span>
                <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">3</span>
              </div>
              <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Next">
                <span class="sr-only">Next</span>
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </button>
            </nav>
            <!-- End Pagination -->
          </div>
          <!-- End Footer -->
        </div>
        <!-- End Footer -->
      </div>
      <!-- End Card -->
    </div>
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <!-- Required -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>

  <!-- Apexcharts -->
  <script src="../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../assets/vendor/apexcharts/dist/apexcharts.min.js"></script>
  <script src="../assets/vendor/preline/dist/helper-apexcharts.js"></script>

  <!-- JS INITIALIZATIONS -->
  <script>
    window.addEventListener('load', () => {
      // Apex Stacked Column Chart
      (function () {
        buildChart('#hs-pro-ascich', (mode) => ({
          chart: {
            type: 'bar',
            height: 240,
            stacked: true,
            toolbar: {
              show: false
            },
            zoom: {
              enabled: false
            }
          },
          series: [
            {
              name: 'New',
              data: [5, 8, 3, 2, 6, 4, 7, 1]
            },
            {
              name: 'Active',
              data: [4, 6, 2, 3, 5, 3, 6, 2]
            },
            {
              name: 'Closed',
              data: [3, 5, 1, 2, 4, 2, 5, 1]
            }
          ],
          colors: [
            'oklch(0.789 0.154 211.53)',
            'oklch(0.623 0.214 259.815)',
            'oklch(0.457 0.24 277.023)'
          ],
          plotOptions: {
            bar: {
              horizontal: false,
              columnWidth: '40%',
              borderRadius: 0,
            }
          },
          legend: {
            show: false,
            position: 'top',
            horizontalAlign: 'left',
            offsetX: 0
          },
          dataLabels: {
            enabled: false
          },
          stroke: {
            show: true,
            width: 0,
            colors: ['transparent']
          },
          xaxis: {
            categories: [
              '12am', '1am', '2am', '3am', '4am', 
              '5am', '6am', '7am'
            ],
            axisBorder: {
              show: false
            },
            axisTicks: {
              show: false
            },
            crosshairs: {
              show: false
            },
            labels: {
              style: {
                colors: 'oklch(0.707 0.022 261.325)',
                fontSize: '13px',
                fontFamily: 'Inter, ui-sans-serif',
                fontWeight: 400
              },
              offsetX: -2,
              formatter: (title) => title.slice(0, 3)
            }
          },
          yaxis: {
            labels: {
              align: 'left',
              minWidth: 0,
              maxWidth: 140,
              style: {
                colors: 'oklch(0.707 0.022 261.325)',
                fontSize: '13px',
                fontFamily: 'Inter, ui-sans-serif',
                fontWeight: 400
              },
              formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
            }
          },
          tooltip: {
            custom: function (props) {
              const { categories } = props.ctx.opts.xaxis;
              const { dataPointIndex } = props;

              return buildTooltip(props, {
                title: categories[dataPointIndex],
                mode,
                valuePrefix: '',
                hasCategory: false,
                hasTextLabel: true,
                wrapperExtClasses: 'min-w-28',
                labelDivider: ':',
                labelExtClasses: 'ms-2'
              });
            }
          },
          states: {
            hover: {
              filter: {
                type: 'darken',
                value: 0.9
              }
            }
          },
        }), {
          colors: [
            'oklch(0.789 0.154 211.53)',
            'oklch(0.623 0.214 259.815)',
            'oklch(0.457 0.24 277.023)'
          ],
          grid: {
            borderColor: 'oklch(0.928 0.006 264.531)'
          },
          xaxis: {
            labels: {
              style: {
                colors: 'oklch(0.707 0.022 261.325)'
              }
            }
          },
          yaxis: {
            labels: {
              style: {
                colors: 'oklch(0.707 0.022 261.325)'
              }
            }
          }
        }, {
          grid: {
            borderColor: 'oklch(0.371 0 0)'
          },
          xaxis: {
            labels: {
              style: {
                colors: 'oklch(0.556 0 0)',
              }
            }
          },
          yaxis: {
            labels: {
              style: {
                colors: 'oklch(0.556 0 0)'
              }
            }
          }
        });
      })();
    });
  </script>

</body>
</html>