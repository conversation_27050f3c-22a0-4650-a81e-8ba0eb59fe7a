!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo={type:"Topology",objects:{syr:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Lattakia"},id:"SY.LA",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Tartus"},id:"SY.TA",arcs:[[4,5,6,-3]]},{type:"Polygon",properties:{name:"Ar Raqqah"},id:"SY.RA",arcs:[[7,8,9,10,11,12]]},{type:"Polygon",properties:{name:"Aleppo"},id:"SY.HL",arcs:[[-12,13,14,15]]},{type:"Polygon",properties:{name:"Hamah"},id:"SY.HM",arcs:[[-14,-11,16,-5,-2,17]]},{type:"Polygon",properties:{name:"Homs (Hims)"},id:"SY.HI",arcs:[[18,19,20,21,-6,-17,-10]]},{type:"Polygon",properties:{name:"Idlib"},id:"SY.ID",arcs:[[-18,-1,22,-15]]},{type:"Polygon",properties:{name:"Hasaka (Al Haksa)"},id:"SY.HA",arcs:[[23,-8,24]]},{type:"Polygon",properties:{name:"Dayr Az Zawr"},id:"SY.DY",arcs:[[25,-19,-9,-24]]},{type:"Polygon",properties:{name:"As Suwayda'"},id:"SY.SU",arcs:[[26,27,28]]},{type:"Polygon",properties:{name:"Rif Dimashq"},id:"SY.RD",arcs:[[29,-29,30,31,32,33,-21],[34]]},{type:"Polygon",properties:{name:"Quneitra"},id:"SY.QU",arcs:[[35,36,-32]]},{type:"Polygon",properties:{name:"Dar`a"},id:"SY.DR",arcs:[[-28,37,38,-36,-31]]},{type:"Polygon",properties:{name:"Damascus"},id:"SY.DI",arcs:[[-35]]},{type:"Polygon",properties:{name:"UNDOF"},id:"SY",arcs:[[-37,-39,39,-33]]}]}},arcs:[[[649,7001],[1,-8],[-1,-2],[-3,-11],[-2,-5],[-3,-8],[-1,-7],[1,-7],[1,-7],[7,-5],[10,-5],[22,-2],[11,1],[9,1],[13,6],[12,3],[102,6],[8,-3],[6,-4],[5,-8],[2,-7],[0,-9],[-1,-7],[-12,-17],[-37,-27]],[[799,6869],[-25,-23],[-6,-18],[-3,-50],[-3,-13],[-4,-9],[-22,-17],[-16,-17],[-4,-10],[-1,-9],[3,-6],[18,-26],[5,-11],[4,-13],[4,-23],[4,-62],[-6,-133],[-29,-220],[1,-8],[9,-38],[25,-77],[39,-202],[4,-78]],[[796,5806],[-58,32],[-20,2],[-21,-14],[-11,-6],[-6,-2],[-226,-19],[-10,2],[-44,41],[-23,17],[-64,23],[-1,0]],[[312,5882],[-24,53],[21,67],[-7,34],[-12,129],[4,29],[-15,16],[-73,106],[-31,17],[-15,12],[-6,20],[-11,11],[-24,-4],[-24,-9],[-8,-6],[-16,10],[2,22],[8,27],[1,24],[-9,11],[-52,30],[2,10],[3,20],[4,11],[-9,0],[-21,-12],[0,12],[30,12],[30,20],[23,29],[9,36],[-4,22],[-13,27],[-3,25],[8,15],[35,12],[58,121],[3,11],[-1,46],[-6,39],[-16,47],[-20,43],[-20,29],[73,-5],[36,9],[15,30],[6,38],[39,64],[15,-6],[29,7],[31,12],[29,5],[19,-23],[0,-26],[-4,-26],[3,-24],[18,-16],[23,-6],[47,-2],[44,-22],[88,-69],[25,5]],[[796,5806],[1,-38],[17,-235],[0,-30],[-12,-47],[-5,-11],[-5,-8],[-10,-7],[-6,-5],[-10,-14],[-6,-3],[-21,-2],[-6,-2],[-9,-8],[-6,-3],[-14,-2],[-6,-2],[-5,-3],[-10,-8],[-5,-3],[-5,-2],[-7,0],[-14,4],[-15,-1],[-16,-11],[11,-54],[70,-15],[25,-1],[57,6],[14,-3],[9,-5],[9,-24],[6,-28],[4,-13],[3,-5],[6,-7],[38,-29],[8,-11],[5,-13]],[[886,5163],[-27,-9],[-12,-6],[-5,-3],[-2,-3],[-4,-5],[-2,-5],[-3,-8],[-3,-18],[-1,-9],[0,-7],[3,-19],[0,-13],[-1,-4],[-2,-6],[-5,-7],[-39,-37],[-24,-33],[-6,-7],[-5,-4],[-10,-3],[-3,-2],[-2,-2],[-2,-2],[-35,-52],[-103,-203],[-3,-14],[-2,-39],[-4,-21]],[[584,4622],[-37,3],[-40,-7],[-34,1],[-33,11],[-26,23],[-11,6],[-13,4],[-20,-1],[-6,37],[-27,62],[-10,53],[-15,30],[-3,18],[0,81],[-4,13],[-27,49],[-6,20],[-8,42],[-48,142],[9,6],[1,9],[9,13],[2,14],[-2,14],[-9,13],[0,13],[7,9],[1,1],[-4,3],[-4,14],[12,28],[8,15],[12,13],[8,30],[-5,30],[-8,26],[-6,23],[0,96],[10,23],[70,119],[32,35],[-14,53],[-33,73]],[[5588,8737],[35,-112],[13,-33],[76,-106],[21,-19],[13,-6],[15,-4],[16,-2],[27,4],[13,3],[23,10],[7,2],[6,1],[8,-1],[7,-1],[138,-62],[94,-56],[16,-18],[10,-17],[2,-6],[2,-8],[3,-25]],[[6133,8281],[-98,-471],[-147,-725],[-16,-123],[0,-21],[10,-4],[44,-32],[12,-6],[-12,-27],[-638,-1014]],[[5288,5858],[-839,245],[-441,38],[-133,-18]],[[3875,6123],[-24,39],[-17,20],[-60,48]],[[3774,6230],[-289,334],[-22,31],[-3,7],[-4,8],[-3,14],[-1,10],[0,9],[9,71],[38,181],[42,111],[14,24],[7,11],[4,5],[58,89],[30,33],[7,10],[3,6],[11,28],[7,24],[2,6],[3,8],[3,10],[2,21],[-2,11],[-6,10],[-60,54],[-1,1],[-19,36],[-20,27],[-21,24],[-14,20],[-14,29],[-18,59],[-6,32],[-2,25],[2,14],[3,14],[6,13],[33,55],[19,47],[14,29],[16,24],[16,19],[9,7],[25,15],[62,34],[41,12],[56,2],[249,44],[123,73],[98,75],[18,18],[14,16],[57,92],[18,40],[10,34],[11,68],[0,1],[11,58],[7,56],[3,134],[-1,8],[-2,6],[-2,7],[-4,5],[-5,4],[-6,2],[-6,1],[-7,-2],[-10,-6],[-37,-35],[-15,-11],[-12,-4],[-6,-1],[-7,0],[-5,3],[-5,4],[-5,11],[-5,16],[-8,35],[-1,17],[2,12],[5,3],[6,2],[75,11],[8,5],[7,7],[8,18],[3,12],[1,10],[-1,8],[-19,89]],[[4376,8840],[43,-49],[92,-51],[105,-14],[245,12],[33,11],[78,6],[231,-83],[81,3],[304,62]],[[3774,6230],[-641,-2],[-127,21],[-67,21],[-26,13],[-99,75],[-1,1],[-29,22],[-247,-20],[-87,2],[-11,3],[-13,7],[-54,39],[-128,49]],[[2244,6461],[-63,28],[-8,6],[-22,24],[-10,7],[-8,6],[-13,4],[-7,5],[-48,61],[-8,14],[-6,35],[-4,14],[-10,15],[-13,29],[-22,76],[8,13],[9,9],[17,8],[5,3],[10,8],[7,9],[5,7],[6,12],[1,7],[1,8],[0,8],[-1,8],[-4,8],[-8,9],[-43,33],[-31,31],[-18,13],[-23,4],[-7,2],[-7,3],[-9,12],[-12,19],[-76,148],[-81,127],[-2,8],[-1,7],[0,17],[-3,63],[-2,15],[-4,13],[-9,9],[-14,12],[-126,63],[-13,9],[-14,13],[-14,17],[-11,24],[-31,44],[19,34],[50,44],[8,10],[7,11],[7,9],[4,4],[3,2],[14,9],[5,5],[11,16],[30,32],[7,11],[7,12],[2,7],[2,8],[0,11],[-6,14],[-40,54],[-46,47],[-8,17],[-3,31],[-2,7],[-3,6],[-3,5],[-5,4],[-8,1],[-10,-2],[-31,-26],[-8,-3],[-49,1],[-52,-12],[-18,-11]],[[1391,7966],[-1,0],[-17,22],[-25,10],[-49,6],[-15,16],[7,0],[8,18],[10,30],[0,18],[-2,15],[-4,14],[-7,13],[-7,6],[-24,11],[-11,10],[-8,15],[-28,108],[-4,33],[6,30],[45,77],[7,86],[-1,89],[20,84],[18,47],[5,39],[2,39],[6,44],[19,39],[24,35],[17,39],[-5,49],[14,1],[14,-2],[2,-1],[277,-102],[94,-7],[29,-7],[30,-13],[28,-17],[24,-23],[45,-46],[12,-25],[2,-42],[-13,-64],[5,-23],[29,-27],[29,-17],[14,4],[12,19],[23,25],[28,14],[25,-3],[25,-8],[28,-4],[96,27],[33,3],[244,-48],[63,-1],[114,79],[107,73],[91,43],[249,57],[241,118],[42,12],[27,19],[146,99],[101,41],[51,6],[99,-13],[285,-92],[75,-44],[159,-179]],[[3875,6123],[2,-34],[-5,-222],[-7,-33],[-16,-42],[-15,-29],[-36,-59],[-6,-13],[-7,-22],[-4,-16],[-2,-18],[0,-16],[8,-67],[-6,-9],[-10,-11],[-198,-96],[-25,-20],[-41,-42],[-6,-9],[-1,-4],[-2,-17],[-1,-3],[-1,-4],[-2,-2],[-9,-13],[-1,-3],[-8,-21],[-7,-14],[-1,-4],[-1,-4],[0,-4],[1,-16],[-1,-9],[-1,-8],[-1,-4],[-1,-3],[-1,-3],[-2,-3],[-3,-6],[-1,-3],[-2,-8],[-2,-6],[-2,-3],[-27,-34],[-4,-2],[-11,-5],[-5,-1],[-4,0],[-8,10],[-58,81],[-14,22],[-10,12],[-85,76],[-2,2],[-1,0],[-5,4],[-13,13],[-5,8],[-2,3],[-1,3],[-4,27],[-2,20],[-2,12],[-7,28],[-1,3],[-2,3],[-8,9],[-2,3],[-1,3],[-3,6],[0,4],[-3,6],[-5,8],[-7,11],[-2,2],[-5,4],[-3,1],[-6,3],[-30,0],[-8,1],[-7,2],[-5,3],[-5,4],[-2,2],[-17,23],[-10,8],[-10,7],[-3,2],[-4,0],[-6,0],[-125,-32],[-70,-27],[-20,-5],[-7,0],[-13,1],[-8,-3],[-11,-6],[-46,-36],[-30,-17],[-10,-8],[-6,-7],[-2,-3],[-2,-2],[-5,-13],[-2,-7],[-3,-28],[0,-52],[1,-8],[7,-23],[1,-8],[1,-4],[-1,-4],[0,-4],[-1,-3],[-3,-5],[-1,-2],[-2,-1],[-2,-2],[-5,-1],[-6,0],[-12,2],[-6,3],[-4,2],[-5,8],[-13,26],[-2,2],[-3,2],[-6,3],[-8,1],[-17,-1],[-11,2],[-9,0],[-20,-3],[-8,0],[-6,-2],[-7,-3],[-13,-10],[-10,-5],[-10,-3],[-14,1],[-11,4],[-15,11],[-3,2],[-3,1],[-5,-1],[-5,-4],[-8,-11],[-2,-7],[-1,-6],[0,-4],[0,-4],[-3,-2],[-5,-1],[-23,6],[-17,0],[-3,-1],[-4,-2],[-5,-3],[-4,-8],[-2,-5],[-4,-16],[-1,-3],[-2,-2],[-11,-12],[-2,-2],[-1,-3],[-2,-3],[-5,-26],[-3,-7],[-1,-3],[-2,-2],[-8,-10],[-9,-8],[-5,-1],[-6,0],[-18,5],[-13,7],[-5,3],[-2,2],[-4,6],[-5,12],[-6,16],[-2,3],[-2,3],[-4,4],[-5,4],[-5,3],[-6,3],[-4,0],[-8,0],[-8,-4],[-35,-25],[-10,-10],[-5,-6],[-2,-12],[-2,-3],[-3,0],[-12,7],[-13,5],[-2,2],[-5,4],[-8,5],[-3,2],[-3,1],[-4,0],[-7,0],[-5,-2],[-5,-5],[-16,-24],[-5,-2],[-7,-3],[-26,-2],[-13,-4],[-10,-8],[-74,-66],[-9,-5],[-32,-12],[-37,91],[-46,100],[-1,4],[-7,10],[-23,21],[-19,6],[-15,2],[-10,-2],[-9,-3],[-23,-16],[-6,-3],[-7,-1],[-14,0],[-9,-3],[-9,-4],[-20,-21],[-6,-2],[-47,-4],[-5,1],[-3,1],[-12,-1],[-13,-3],[-6,0],[-4,0],[-2,2],[-14,-6],[-119,-66],[-73,3],[-34,-8],[-18,-12],[-6,-3],[-3,-1],[-7,0],[-9,3],[-14,6],[-5,5],[-3,5],[0,9],[0,4],[-1,4],[-2,3],[-2,2],[-2,2],[-3,1],[-4,0],[-11,1],[-3,1],[-1,3],[-1,3],[0,4],[1,4],[1,3],[5,8],[4,6],[1,3],[0,4],[0,4],[-1,3],[-1,3],[-2,2],[-2,3],[-5,3],[-6,3],[-5,0],[-5,0],[-14,-4],[-3,-2],[-2,-1],[-2,-2],[-12,-17],[-12,-22],[-37,-45],[0,-15],[1,-13],[-1,-6],[-3,-6],[-8,-11],[-3,-7],[-1,-5],[0,-18],[-1,-3],[-2,-5],[-12,-14],[-3,-6],[-8,-2],[-5,-1],[-46,11],[-11,19],[-4,4],[-6,3],[-11,0],[-5,-1],[-4,-2],[-4,-4],[-17,-27],[-5,-4],[-2,-2],[-4,-1],[-6,4],[-7,9],[-13,22],[-7,8],[-4,4],[-3,1],[-3,1],[-3,1],[-1,4],[1,5],[9,13],[3,3],[4,3],[-1,6],[-10,18]],[[799,6869],[7,-14],[3,-5],[6,-5],[6,-4],[12,-4],[11,-2],[20,2],[18,7],[5,3],[6,3],[6,1],[6,0],[6,-2],[6,-5],[8,-12],[4,-8],[3,-9],[2,-6],[2,-7],[2,-6],[21,-7],[66,-2],[10,-7],[3,-26],[-10,-88],[-21,-107],[-3,-36],[12,-134],[-2,-11],[-3,-8],[-4,-4],[-1,-7],[0,-1],[33,-80],[7,-34],[10,-12],[32,-10],[53,27],[7,1],[10,-3],[26,-15],[5,-4],[4,-5],[5,-12],[4,-12],[3,-6],[3,-6],[4,-5],[4,-4],[7,-3],[7,-2],[15,-1],[29,2],[15,-2],[100,-30],[78,-3],[181,54],[7,0],[6,0],[7,-3],[5,-3],[45,-44],[6,-11],[3,-7],[6,-11],[4,-5],[5,-4],[16,-11],[4,-5],[2,-5],[4,-5],[6,-3],[10,0],[15,3],[11,1],[10,4],[9,7],[26,44],[33,35],[5,8],[13,24],[4,6],[5,2],[7,-1],[5,-4],[4,-6],[3,-5],[3,-6],[2,-7],[1,-7],[1,-15],[0,-9],[-1,-8],[-2,-8],[-15,-33],[-1,-8],[0,-7],[0,-8],[5,-28],[1,-8],[0,-24],[2,-7],[3,-6],[4,-4],[6,-4],[6,-3],[7,-1],[15,0],[33,4],[32,-1],[8,1],[8,4],[6,3],[5,6],[13,22],[8,12],[5,4],[5,4],[5,3],[6,1],[7,-1],[7,-2],[6,-2],[5,0],[2,6],[1,83],[-2,15],[-2,6],[-3,6],[-4,5],[-5,3],[-6,1],[-6,-1],[-5,-4],[-4,-4],[-3,-6],[-9,-19],[-4,-6],[-4,-5],[-5,-3],[-6,-2],[-7,0],[-7,1],[-7,2],[-5,4],[-4,5],[-1,7],[-1,8],[1,8],[4,14],[29,68],[8,15],[8,9],[20,13],[31,15],[10,7],[9,13],[37,72],[17,41],[4,10],[4,6],[29,17]],[[5288,5858],[151,-134],[48,-66],[95,-221],[5,-18],[0,-19],[-9,-115],[1,-21],[7,-18],[6,-12],[248,-386],[846,-1338],[1,0]],[[6687,3510],[-108,-72],[-166,-110],[-166,-111],[-166,-110],[-166,-111],[-167,-110],[-166,-111],[-166,-110],[-166,-111],[-166,-110],[-167,-111],[-166,-110],[-166,-111],[-368,-254],[-321,-226]],[[3896,1632],[-1,1],[-612,545],[-45,31],[-38,34],[-10,7],[-14,7],[-3,1],[-2,1],[-25,5],[-15,7],[-57,15],[-23,12],[-15,11],[-42,27],[-102,88],[-101,152],[-8,27],[-6,12],[-8,27],[-2,3],[-1,5],[-2,12],[-15,39],[-9,17],[-145,149],[-15,13],[-26,17],[-120,134],[-23,17],[-11,10],[-78,142],[-1,2],[-5,9],[-9,26],[-1,3],[-1,4],[-1,3],[-2,3],[-8,7],[-30,11],[-30,-4],[-16,0],[-7,2],[-15,6],[-146,93],[-1,1],[-1,0],[-9,5],[-17,14],[-52,52],[-6,13],[-4,7],[-3,2],[-7,6],[-2,2],[-4,9],[-3,6],[-1,4],[-15,28],[-2,7],[-12,19],[-2,3],[-8,18],[-23,38],[-10,22],[-8,13],[-27,31],[-23,37],[-20,23],[-34,45],[-5,9],[-1,3],[-18,30],[-9,8],[-7,1],[-11,-2],[-46,-15],[-52,-34],[-4,-1],[-13,1],[-107,15],[-26,17],[-8,1],[-35,0],[-24,-5],[-37,-23],[-30,-10]],[[1323,3765],[-8,19],[-37,40],[-9,29],[-8,37],[12,26],[14,22],[3,26],[-8,15],[-9,3],[-11,-1],[-11,5],[-16,19],[-33,51],[-7,15],[-4,16],[1,17],[5,10],[5,9],[5,14],[5,33],[1,22],[-8,17],[-24,19],[-11,-4],[-11,8],[-22,30],[-25,12],[-20,18],[-15,26],[-10,34],[-21,8],[-41,3],[-44,-2],[-29,-9],[-4,-2],[-4,0],[-4,0],[-4,2],[-5,8],[-15,31],[26,30],[41,24],[29,25],[7,32],[-2,29],[3,24],[22,15],[23,-7],[16,-14],[10,9],[6,63],[-36,-13],[-37,0],[-36,13],[-30,30],[-36,70],[-22,16],[-31,-24],[-5,-15],[-1,-39],[-5,-18],[-14,-17],[-15,-7],[-91,-6],[-9,4],[-18,14],[-7,4],[-10,-2],[-18,-12],[-6,-3],[-66,6]],[[649,7001],[4,1],[16,59],[9,140],[34,58],[48,19],[36,-11],[24,6],[13,71],[30,13],[59,-25],[32,12],[-4,56],[5,289],[2,29],[9,15],[12,14],[2,17],[-21,27],[17,19],[19,-24],[19,-15],[21,-7],[41,-6],[9,-3],[8,0],[13,6],[7,11],[2,15],[5,12],[17,7],[28,4],[114,-19],[28,0],[107,22],[9,16],[-2,45],[-5,28],[-11,33],[-14,31]],[[8393,6462],[-1,1],[-168,25],[-295,8],[-115,26],[-54,22],[-93,22],[-28,13],[-48,31],[-1126,1187],[-187,206],[-113,199],[-32,79]],[[5588,8737],[486,99],[322,130],[150,95],[54,20],[56,11],[56,28],[108,77],[198,142],[31,20],[32,13],[32,7],[35,2],[69,21],[201,119],[74,30],[282,45],[358,-77],[68,-29],[32,-10],[418,22],[626,157],[170,42],[66,42],[113,113],[75,106],[22,14],[28,23],[17,-22],[2,-23],[-3,-20],[2,-8],[20,-4],[46,-24],[8,5],[10,11],[10,9],[9,-3],[7,-11],[7,-20],[7,-10],[51,-44],[10,-6],[10,-25],[-27,-112],[17,-25],[12,-12],[3,-28],[1,-57],[10,-23],[11,-21],[9,-22],[0,-29],[-1,-1],[-46,-38],[-96,-97],[-1,0],[-155,-177],[-300,-343],[-203,-231],[-39,-36],[-41,-21],[-467,-106],[-97,-17],[-44,-22],[-31,-44],[0,-1],[-132,-277],[-13,-54],[-48,-501],[0,-33],[6,-34],[39,-98],[116,-272],[16,-64],[7,-66],[7,-274],[-9,-63],[-23,-60],[-51,-83]],[[8393,6462],[-71,-115],[-14,-60],[-13,-195],[-63,-246],[-15,-122],[1,-46],[9,-329],[13,-349],[-4,-52],[-13,-49],[-258,-547],[-54,-131],[-34,-54],[-44,-31],[-369,-109],[-220,-147],[-166,-110],[-166,-111],[-167,-110],[-58,-39]],[[2662,752],[-120,-85],[-257,-185],[-166,-120],[-230,-168],[-242,-187],[-19,-7],[-21,1],[-96,28],[-33,2],[-26,-18],[-54,46],[-206,29]],[[1192,88],[-1,12],[7,57],[1,5],[-5,96],[1,9],[10,39],[2,8],[1,16],[-2,8],[-3,9],[-15,26],[-21,27],[-7,12],[-1,5],[1,7],[8,20],[10,15],[1,3],[-2,12],[-17,31],[-25,-3],[-4,0],[-3,1],[-5,3],[-6,5],[-3,6],[-6,7],[-4,2],[-4,0],[-3,0],[-29,-5],[-23,0],[-25,5],[-2,4],[-1,5],[1,13],[2,7],[5,15],[1,7],[-1,6],[-9,23],[-1,9],[2,11],[3,4],[3,2],[19,7],[6,3],[2,1],[2,2],[8,11],[2,2],[5,4],[11,6],[4,4],[5,5],[1,2],[1,3],[4,15],[0,9],[-4,7],[-7,8],[-19,15],[-10,6],[-7,3],[-30,3],[-7,2],[-23,11],[-3,1],[-4,0],[-7,1],[-7,1],[-4,2],[-2,3],[-3,4],[-4,8],[-2,6],[-1,5],[-1,12],[0,9],[1,12],[4,10],[16,17],[-12,133],[0,32],[3,5],[1,2],[1,6],[-5,21],[-27,72],[-4,20],[-4,14],[-1,12],[0,5],[1,4],[2,6],[12,28],[1,4],[0,3],[0,5],[0,3],[-1,4],[-1,3],[-4,9],[-2,7],[-1,4],[0,5],[3,5],[6,7],[24,20],[18,12],[21,22],[46,40],[11,6],[16,4],[61,5],[88,-7],[8,1],[3,1],[1,3],[1,5],[-3,11],[-7,16],[-3,6],[0,4],[-1,4],[-2,47],[-2,7],[-1,3],[-2,3],[-2,2],[-3,2],[-3,1],[-4,0],[-7,-1],[-3,0],[-23,-10],[-2,0],[-3,0],[-3,1],[-3,1],[-2,2],[-2,2],[-4,5],[-1,3],[-1,4],[0,31],[-1,8],[-1,8],[-2,7],[-11,25],[-4,13],[-3,15],[0,5],[0,6],[2,11],[1,12],[0,5],[-1,7],[-3,11],[-1,3],[-1,5],[1,6],[1,9],[3,9],[5,9],[7,10],[9,9],[25,22],[7,1],[9,1],[42,-6],[12,3],[30,17]],[[1274,1758],[380,61],[2,-2],[14,-17],[7,-6],[12,-14],[13,-11],[44,-26],[5,-4],[16,-22],[7,-12],[1,-7],[3,-24],[3,-6],[3,-6],[53,-60],[3,-4],[2,-4],[3,-7],[4,-17],[1,-17],[1,-8],[2,-8],[1,-3],[1,-3],[4,-5],[31,-25],[5,-3],[26,-4],[9,0],[98,-24],[7,-5],[3,-2],[4,-5],[7,-6],[18,-12],[5,-4],[2,-2],[1,-3],[2,-3],[1,-3],[0,-3],[1,-4],[-1,-5],[-1,-7],[1,-12],[0,-2],[1,-3],[2,-5],[3,-6],[3,-5],[2,-2],[1,0],[2,-2],[4,-5],[2,-3],[2,-5],[1,-3],[2,-3],[1,-3],[1,-2],[4,-5],[3,-3],[3,-4],[3,-13],[76,-49],[20,-10],[2,0],[6,3],[2,1],[3,2],[4,5],[7,2],[43,-2],[24,-9],[10,-12],[7,-11],[3,-6],[2,-7],[4,-20],[7,-91],[1,-8],[1,-2],[1,-2],[4,-8],[204,-230],[74,-60],[13,-16],[25,-37],[1,-1]],[[3896,1632],[-128,-89],[-262,-184],[-191,-137],[-257,-186],[-258,-185],[-138,-99]],[[1274,1758],[-4,21],[-1,32],[-1,4],[-1,3],[0,4],[-3,6],[-2,3],[-51,66],[-3,6],[-3,6],[-6,25],[-3,6],[-2,6],[-3,4],[-2,3],[-6,6],[-6,0],[-5,-1],[-25,-19],[-8,-12],[-5,-6],[-74,-51],[-1,0],[0,-1],[-6,-4],[-45,-46],[-5,-8],[-1,-4],[-1,-3],[-1,-22],[-1,-4],[-1,-3],[-1,-3],[-2,-2],[-3,-3],[-3,-1],[-8,0],[-12,3],[-39,12],[-6,4],[-4,5],[-4,5],[-15,10],[-2,3],[-2,3],[-6,16],[-2,2],[-2,3],[-4,4],[-3,1],[-6,2],[-28,5],[-4,1],[-3,2],[-2,2],[-3,2],[-1,3],[-2,3],[0,3],[-2,21],[-1,3],[-1,3],[-2,3],[-2,2],[-3,2],[-16,10],[-2,2],[-5,4],[-7,10],[-12,23],[-2,3],[-2,2],[-3,2],[-2,1],[-5,-3],[-4,-7],[-8,-19],[-2,-8],[0,-7],[5,-22],[1,-8],[-1,-4],[0,-3],[-2,-8],[-2,-7],[-9,-17],[-6,-16],[-14,-8],[-63,-24],[-22,-13],[-7,-2],[-119,-11],[-3,-2],[-22,-22],[-9,-7],[-5,-3],[-10,-4],[-8,-1],[-29,0],[-10,3],[-5,8],[-9,19],[-6,6],[-4,3],[-33,15]],[[358,1779],[2,13],[1,10],[-1,30],[2,16],[0,9],[-1,4],[0,4],[-4,4],[-6,3],[-19,4],[-18,1],[-34,7],[-4,-1],[-3,-1],[-6,-2],[-2,-3],[-2,-2],[-1,-2],[-1,-3],[-2,-9],[-2,-6],[-2,-3],[-2,-3],[-3,1],[-4,5],[-9,24],[-2,7],[0,4],[-1,8],[1,9],[2,12],[3,11],[2,5],[1,4],[6,11],[8,10],[1,3],[0,3],[-16,12],[-62,36],[0,1]],[[180,2015],[-12,32],[-8,25],[0,26],[8,27],[15,22],[25,20],[12,15],[5,19],[-5,30]],[[220,2231],[28,18],[47,44],[24,64],[-21,40],[17,27],[36,12],[35,0],[29,14],[30,23],[24,30],[4,37],[-13,23],[-25,21],[-28,15],[-21,6],[-35,22],[-33,7],[-20,14],[6,43],[6,6],[19,9],[7,7],[4,20],[-4,36],[2,16],[11,18],[28,28],[9,23],[21,34],[49,47],[21,43],[14,21],[25,11],[27,9],[27,12],[53,37],[10,-7],[20,-26],[12,-9],[31,1],[24,16],[21,19],[23,9],[26,-5],[77,-42],[57,-15],[29,5],[18,26],[-3,41],[-26,18],[-59,11],[-44,38],[-10,13],[-9,19],[-3,7],[32,72],[21,36],[24,32],[59,58],[50,70],[32,18],[17,-2],[33,-13],[20,1],[25,13],[8,19],[3,23],[9,28],[29,41],[67,64],[35,64],[42,52],[0,2]],[[702,2394],[24,4],[36,-20],[-32,-37],[8,-20],[34,25],[91,-51],[28,-5],[6,24],[34,39],[14,44],[-3,37],[1,22],[-24,36],[-42,4],[-83,-28],[-45,-1],[-58,-36],[11,-37]],[[358,1779],[-12,-76],[-1,-21],[2,-3],[21,-17],[4,-4],[4,-7],[7,-16],[2,-8],[1,-5],[-19,-34],[-10,-25],[-2,-3],[-18,-125],[-1,-15],[8,-19],[17,-29],[0,-1],[8,-19],[7,-11],[6,-8],[3,-7],[9,-35],[2,-11],[1,-8],[-2,-3],[-2,-2],[-2,0],[-3,1],[-3,1],[-2,2],[-3,2],[-1,3],[-2,3],[-1,3],[-2,7],[-1,3],[-1,3],[-3,2],[-3,1],[-7,2],[-27,-2],[-4,-1],[-5,-3],[-6,-6],[-2,-5],[-1,-5],[1,-3],[2,-3],[4,-5],[2,-7],[2,-12],[5,-39],[-1,-9],[-1,-3],[-7,-13],[-45,-41],[-80,-82],[-11,-18]],[[185,1043],[0,1],[21,87],[45,97],[33,37],[17,41],[-38,36],[-32,14],[-19,177],[6,52],[33,4],[19,0],[0,14],[-9,0],[-5,8],[-3,8],[5,7],[8,5],[8,5],[7,5],[1,7],[0,21],[0,32],[-5,11],[-3,11],[-8,6],[-6,2],[-9,-8],[-4,0],[-4,5],[-4,11],[0,9],[10,11],[0,8],[-5,11],[-17,23],[-12,20],[-7,23],[-11,63],[-1,8],[-4,6],[-8,6],[-15,0],[-14,2],[-10,1],[-1,8],[4,3],[7,5],[10,4],[11,10],[7,17],[1,19],[-4,17],[0,2]],[[1192,88],[-55,7],[-24,17],[-85,10],[-29,10],[-23,14],[-132,141],[-97,75],[-49,55],[-16,11],[-8,-3],[-17,-18],[-7,-4],[-9,2],[-16,7],[-9,1],[-56,-8],[-21,0],[-18,1],[-6,2],[0,8],[-8,23],[-68,116],[-14,33],[-1,38],[4,34],[-7,22],[-35,3],[-22,-4],[-15,6],[-14,14],[-13,19],[5,-1],[5,8],[2,13],[-2,14],[-6,3],[-19,0],[-8,3],[-26,29],[-14,9],[-162,43],[-13,19],[0,1],[-7,5],[-7,2]],[[70,868],[32,52],[25,17],[11,8],[10,18],[31,52],[9,17],[-3,11]],[[70,868],[-9,-2],[-10,-6],[40,68],[76,99],[11,51],[-6,25],[18,60],[26,49],[10,4],[21,45],[-35,65],[-8,25],[-20,190],[5,26],[-57,27],[0,29],[17,30],[16,39],[-5,58],[-33,23],[-6,93],[-43,33],[-10,16],[50,79],[-57,44],[33,17],[11,14],[25,20],[4,26],[4,11],[3,33],[8,12],[-2,11],[36,24],[37,25]]],
transform:{scale:[.0006654451666166578,.0005012365860586113],translate:[35.723399285000085,32.313041687000066]}},m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();