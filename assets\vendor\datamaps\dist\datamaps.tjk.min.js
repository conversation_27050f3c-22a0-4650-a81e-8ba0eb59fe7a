!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo={type:"Topology",objects:{tjk:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Gorno-Badakhshan"},id:"TJ.BK",arcs:[[0,1,2]]},{type:"Polygon",properties:{name:"Khatlon"},id:"TJ.KL",arcs:[[-1,3,4]]},{type:"Polygon",properties:{name:"Tadzhikistan Territories"},id:"TJ.RR",arcs:[[-2,-5,5,6,7],[8]]},{type:"MultiPolygon",properties:{name:"Leninabad"},id:"TJ.LE",arcs:[[[9]],[[10]],[[-7,11]]]},{type:"Polygon",properties:{name:"Dushanbe"},id:"TJ.DU",arcs:[[-9]]}]}},arcs:[[[3779,3025],[-1,1],[-21,40],[-8,19],[-2,26],[14,48],[54,134],[16,66],[2,28],[1,128],[-3,66],[-4,36],[0,17],[1,20],[10,54],[15,54],[3,17],[0,23],[-5,39],[-4,20],[-5,15],[-10,17],[-2,18],[0,27],[4,50],[18,55],[87,105],[15,30],[4,34]],[[3958,4212],[63,77],[14,22],[8,21],[5,29],[10,27],[18,26],[42,48],[14,23],[24,53],[2,30],[-6,27],[-14,25],[-38,40],[-6,14],[5,9],[33,19],[23,19],[70,86],[20,18],[52,25],[20,14],[17,9],[16,-5],[16,-30],[13,-56],[6,-10],[11,-2],[39,8],[41,-4],[19,2],[14,7],[44,45],[62,38],[26,9],[26,3],[17,-8],[8,-14],[-1,-26],[-3,-21],[1,-20],[6,-21],[41,-105],[5,-32],[-5,-48],[1,-25],[-2,-25],[-5,-26],[-12,-36],[-1,-27],[12,-24],[38,-16],[56,4],[45,16],[20,4],[15,-4],[17,-16],[14,-25],[7,-36],[4,-45],[7,-33],[12,-25],[35,-34],[23,-48],[9,-12],[8,-6],[15,1],[20,10],[33,30],[22,17],[22,5],[19,-8],[16,-16],[13,-22],[16,-36],[6,-6],[5,5],[4,14],[5,23],[6,17],[20,32],[9,11],[18,6],[40,-2],[11,5],[10,12],[5,22],[0,61],[3,21],[7,11],[16,10],[13,10],[9,19],[9,29],[6,11],[9,14],[14,29],[8,12],[13,7],[22,2],[18,-4],[20,1],[17,5],[58,27],[9,0],[8,-3],[11,-14],[5,-4],[6,2],[8,10],[9,23],[8,23],[10,13],[20,8],[21,11],[15,20],[15,25],[18,20],[35,15],[20,5],[63,5],[21,10],[23,19],[36,45],[18,27],[22,53],[37,40],[-37,57],[-13,34],[-16,53],[-5,32],[2,32],[5,23],[4,49],[14,61],[40,64],[73,98],[10,25],[3,20],[-2,18],[-2,27],[3,26],[9,18],[13,15],[30,24],[44,24],[18,15],[9,19],[4,22],[1,66],[5,53]],[[6246,5757],[1,-1],[14,1],[53,161],[29,123],[15,35],[23,11],[28,6],[48,33],[22,2],[42,-15],[22,-2],[20,5],[40,59],[14,8],[20,-7],[32,-30],[21,0],[14,13],[25,42],[13,16],[22,9],[22,-1],[236,-86],[19,1],[55,16],[27,-2],[80,-24],[22,-10],[19,1],[63,47],[16,4],[16,-7],[22,-15],[45,-18],[41,5],[130,62],[73,19],[9,24],[2,32],[10,35],[31,31],[140,48],[45,6],[118,-18],[36,-26],[-1,-45],[14,-131],[1,-47],[-3,-36],[-40,-160],[-10,-68],[7,-66],[31,-73],[54,-75],[11,-32],[12,-78],[8,-37],[14,-30],[76,-81],[42,-66],[19,-17],[12,-39],[4,-38],[-6,-35],[-19,-28],[-27,-13],[-62,0],[-29,-20],[-18,-26],[-18,-37],[-10,-41],[3,-42],[16,-34],[37,-58],[14,-32],[4,-45],[-8,-101],[5,-35],[46,-108],[8,-33],[13,-95],[11,-36],[24,-38],[22,-20],[46,-25],[22,-18],[39,-50],[20,-17],[123,-6],[21,7],[17,39],[-10,35],[-17,38],[-1,44],[10,12],[51,24],[9,15],[2,19],[-1,20],[3,19],[18,38],[18,16],[22,0],[79,-39],[25,-6],[28,1],[89,44],[27,-5],[182,-126],[217,-117],[23,-28],[20,-32],[21,-25],[29,-6],[29,4],[24,-7],[20,-22],[20,-38],[1,0],[0,-1],[11,-25],[9,-11],[23,-19],[9,-14],[3,-19],[2,-22],[9,-65],[0,-45],[-8,-42],[-14,-37],[-55,-86],[-12,-37],[-4,-41],[0,-219],[25,-263],[24,-65],[90,-72],[16,-63],[-5,-145],[3,-61],[0,-15],[4,-13],[14,-12],[11,-14],[-3,-16],[-22,-50],[-9,-7],[-5,-12],[4,-30],[6,-17],[9,-13],[65,-51],[16,-22],[5,-42],[-5,-21],[-9,-24],[-20,-39],[-9,-21],[-4,-21],[-2,-20],[-4,-19],[-17,-35],[-17,-29],[-14,-32],[-5,-44],[12,-80],[30,-66],[38,-51],[116,-96],[22,-29],[65,-122],[37,-51],[40,-41],[-62,-29],[-30,-22],[-16,-39],[-3,-85],[-11,-28],[-119,-63],[-31,-7],[-24,-20],[-11,-40],[-14,-37],[-26,-18],[-38,31],[-59,142],[-36,56],[-164,144],[-37,-30],[-130,-12],[-11,0],[-11,4],[-46,20],[-28,17],[-24,-3],[-22,-7],[-37,10],[-15,1],[-13,6],[-18,44],[-13,12],[-14,-2],[-15,-14],[-7,30],[-15,-62],[-31,-6],[-37,14],[-34,0],[-21,-31],[-2,-79],[-23,-39],[-32,-19],[-142,-41],[-96,-51],[-27,-8],[-72,-49],[-55,-9],[-26,-11],[-48,-64],[-18,-6],[-20,7],[-29,3],[-12,-6],[-21,-23],[-14,-2],[-9,10],[-18,36],[-10,13],[-26,3],[-54,-25],[-20,18],[-6,48],[16,45],[27,33],[27,14],[49,8],[19,13],[22,26],[21,36],[9,34],[0,96],[1,19],[9,58],[-46,8],[-55,-2],[-89,34],[-153,80],[-57,-2],[-80,-63],[-21,9],[-22,19],[-30,5],[-32,-4],[-26,-13],[-20,-21],[-62,-96],[-14,-10],[-28,15],[-11,-5],[-49,-55],[-20,-35],[-22,-67],[-14,-32],[-26,-25],[-92,-13],[-91,-79],[-29,-48],[-31,-16],[-60,-17],[-51,-44],[-38,-75],[-60,-178],[-53,-120],[-8,-44],[-11,-22],[-191,-41],[-43,-31],[-88,23],[-58,-17],[-129,-75],[-50,-50],[-13,-21],[-19,-40],[-11,-17],[-44,-36],[-33,-53],[-371,-398],[-50,-30],[-63,-17],[-62,0],[-60,19],[-53,41],[-43,65],[-18,40],[-14,44],[-9,46],[-9,106],[-13,46],[-72,169],[-11,42],[-4,49],[0,94],[-5,27],[-22,50],[-8,24],[-2,28],[2,139],[10,94],[8,35],[9,20],[-5,56],[47,115],[1,64],[8,29],[3,48],[-2,97],[-10,88],[4,32],[15,52],[12,92],[-18,185],[6,100],[4,13],[16,30],[6,20],[3,23],[6,71],[15,71],[2,22],[-3,26],[-11,48],[-3,22],[11,41],[47,43],[20,41],[5,41],[1,55],[-2,50],[-4,28],[10,15],[-39,69],[-39,37],[-46,4],[-156,-76],[-23,-25],[-25,-20],[-28,16],[-48,56],[-14,3],[-10,-4],[-6,5],[-5,29],[2,25],[12,51],[9,59],[38,101],[18,94],[23,65],[8,68],[24,99],[7,52],[-9,101],[-30,68],[-44,41],[-105,62],[-79,116],[-49,51],[-36,26],[-16,5],[-15,0],[-10,-7],[-10,-1],[-13,16],[-8,19],[-11,42],[-8,18],[-13,20],[-13,16],[-14,12],[-17,6],[-29,-1],[-10,-16],[4,-52],[-13,-24],[-31,11],[-52,35],[-15,-3],[-8,-10],[-4,-10],[-9,-6],[-12,1],[-20,10],[-12,3],[-40,0],[-21,-7],[-9,-16],[-16,-39],[-74,-11],[-24,-21],[0,-16],[26,-27],[-11,-27],[-45,-48],[-12,-9],[-29,3],[-12,-10],[-5,-20],[0,-24],[1,-23],[0,-20],[-17,-78],[-13,-9],[-18,-7],[-15,-12],[-12,-57],[-38,-104],[-33,-127],[-16,-38],[-13,-19],[-43,-27],[-14,-14],[-56,-83],[-58,-108],[-10,-11],[-25,-19]],[[3779,3025],[-6,-5],[-7,-12],[-6,-20],[-14,-9],[-15,-4],[-9,-7],[-5,-21],[0,-19],[4,-19],[10,-19],[-29,-16],[-30,-7],[-29,9],[-26,30],[-15,-58],[6,-71],[18,-67],[22,-48],[14,-9],[42,-19],[9,-19],[4,-33],[8,-30],[12,-24],[12,-16],[-7,-27],[3,-32],[13,-67],[1,-33],[-3,-20],[-5,-19],[-2,-29],[-16,-33],[-6,-6],[-6,-9],[1,-18],[1,-21],[-1,-15],[-7,-17],[-3,-5],[-7,-9],[-4,2],[-25,-2],[-2,0],[-19,-67],[-3,-20],[-9,-8],[-44,-79],[-37,-21],[-38,10],[-38,20],[-36,8],[9,-17],[-40,11],[-12,6],[-5,6],[-7,10],[-5,15],[-6,11],[-17,5],[-27,14],[-14,33],[-12,36],[-21,27],[-30,2],[-53,-39],[-26,-10],[-13,-9],[-13,-16],[-14,-10],[-13,10],[-9,10],[-29,13],[-10,2],[-32,-6],[-54,-33],[-28,-8],[-174,22],[-26,-16],[-22,-96],[-28,-39],[-56,-59],[-5,-11],[-8,-27],[-5,-12],[-10,-13],[-18,-18],[-7,-13],[-8,-36],[-2,-43],[1,-88],[2,-8],[12,-4],[4,-10],[-1,-13],[-3,-3],[-3,-2],[-2,-7],[1,-21],[2,-21],[4,-20],[6,-16],[11,-34],[8,-79],[11,-21],[-10,-50],[21,-14],[24,-7],[-4,-30],[-44,-105],[-21,-30],[-52,-47],[-20,-34],[-14,-19],[-15,-8],[-5,-1],[-29,-9],[-26,-18],[-25,-3],[-121,117],[-7,5],[-28,28],[-12,19],[-23,81],[-23,28],[-29,27],[-26,34],[-18,83],[-20,40],[-26,34],[-25,20],[-88,16],[-4,3],[-8,2],[-3,-8],[6,-27],[9,-15],[20,-22],[6,-10],[5,-33],[-7,-20],[-14,-6],[-19,12],[-16,33],[-12,36],[-14,30],[-27,9],[-28,-13],[-5,-26],[15,-69],[4,-49],[-6,-22],[-15,2],[-48,43],[-15,11],[-16,2],[-27,-4],[-49,10],[-24,-1],[-14,-25],[6,-9],[4,-10],[7,-28],[-29,-6],[-15,-33],[-7,-37],[-5,-17],[-23,-5],[-48,-22],[-26,-5],[-6,-10],[-18,-44],[-11,-10],[-13,-1],[-61,-20],[-80,-9],[19,-53],[-9,-34],[-25,-19],[-33,-5],[-25,9],[-25,14],[-25,3],[-24,-26],[-10,-38],[1,-46],[9,-96],[-14,-33],[-30,0],[-53,25],[-33,-6],[-24,-28],[-20,-31],[-24,-14],[-16,-14],[-84,-105],[-14,-13],[-15,-9],[-10,1],[-6,0],[-11,12],[-7,18],[-5,20],[-8,19],[-43,79],[-12,14],[-30,23],[-14,17],[-20,48],[-14,40],[-18,28],[-57,20],[-32,24],[-29,30],[-16,31],[-1,34],[11,147],[6,33],[24,69],[14,78],[7,82],[-4,97],[-26,181],[5,96],[36,141],[54,135],[66,119],[69,94],[93,185],[38,105],[55,234],[22,33],[40,19],[60,12],[19,8],[7,7]],[[1145,2898],[0,-1],[35,-38],[13,-21],[18,-23],[32,-4],[97,22],[24,9],[5,-6],[5,-8],[4,-10],[4,-8],[7,-4],[7,1],[25,9],[8,7],[7,15],[20,106],[13,46],[17,34],[40,26],[6,8],[3,16],[3,43],[1,52],[5,42],[-2,11],[-6,7],[-20,9],[-8,5],[-5,9],[1,12],[2,12],[20,59],[54,125],[4,15],[3,13],[3,28],[0,27],[-1,38],[-3,23],[-17,78],[0,11],[2,13],[5,12],[20,34],[23,30],[10,4],[12,-1],[47,-19],[43,-10],[10,-1],[12,3],[17,13],[10,9],[7,11],[15,30],[5,8],[8,5],[10,4],[13,1],[11,-5],[8,-7],[21,-46],[13,-2],[48,17],[9,5],[9,8],[10,18],[6,14],[16,49],[5,10],[6,9],[25,21],[86,45],[175,14],[59,40],[5,1],[8,0],[10,-3],[9,-6],[11,-9],[6,-10],[6,-9],[8,-18],[3,-11],[2,-7],[5,-42],[104,-5],[12,-8],[13,-12],[0,-12],[-3,-10],[-4,-10],[-13,-17],[-5,-11],[-2,-14],[7,-26],[8,-11],[12,-6],[14,0],[23,-2],[13,4],[9,7],[14,15],[16,27],[26,33],[23,36],[8,20],[5,9],[7,8],[5,9],[12,29],[7,8],[24,20],[5,5],[10,16],[5,9],[30,30],[34,29],[57,75],[12,28],[117,244],[7,9],[9,5],[18,-2],[8,-3],[8,-6],[6,-7],[9,-18],[3,-10],[5,-9],[8,-7],[11,-1],[23,10],[11,9],[9,10],[47,120],[76,114],[13,15],[6,5],[60,32],[20,14],[17,2],[24,-10],[18,-14],[11,-16],[10,-18],[45,-62],[16,-33],[29,-42],[33,-26],[19,-15],[9,-35],[-1,-30],[-7,-52],[-5,-54],[4,-20],[5,-6],[5,7],[37,62],[35,47],[6,7],[10,5],[13,3],[60,-2],[76,10],[8,-5],[6,-7],[4,-7],[7,-12],[52,-55],[5,-8],[5,-10],[3,-10],[6,-21],[6,-38],[7,-35]],[[1145,2898],[13,14],[10,29],[6,35],[9,39],[1,1],[15,44],[50,107],[32,127],[20,134],[-5,89],[-31,70],[-102,127],[-66,53],[-22,32],[-47,122],[-50,67],[-12,33],[-5,33],[-2,73],[-8,35],[-31,67],[-9,30],[-4,50],[5,132],[-3,46],[-17,86],[-4,42],[10,35],[33,32],[6,14],[1,26],[-5,21],[-8,20],[-4,23],[5,50],[14,12],[23,-1],[29,9],[22,18],[20,24],[16,31],[11,38],[1,51],[-12,46],[-14,27]],[[1036,5091],[1,0],[34,13],[63,3],[23,9],[72,41],[20,6],[17,1],[9,-4],[25,-17],[11,-5],[8,1],[12,7],[8,6],[46,26],[37,-3],[25,10],[45,25],[14,22],[18,50],[41,40],[0,1],[3,9],[11,62],[7,28],[9,10],[9,4],[88,-6],[121,65],[17,4],[7,-6],[4,-10],[3,-10],[9,-8],[15,-5],[78,8],[72,-8],[81,-29],[7,-7],[19,-7],[5,-3],[32,-11],[41,-11],[39,24],[8,11],[6,9],[17,18],[93,73],[30,17],[21,9],[124,-6],[21,2],[9,9],[5,12],[3,84],[2,14],[5,18],[7,18],[17,21],[12,8],[12,2],[10,-1],[96,18],[14,0],[4,-4],[46,-58],[9,-19],[6,-20],[3,-24],[1,-26],[-2,-28],[-7,-41],[-1,-13],[1,-12],[5,-10],[7,-9],[7,-1],[7,5],[5,8],[11,18],[12,13],[19,12],[44,19],[14,12],[7,12],[-3,10],[-1,12],[-1,10],[1,16],[29,36],[5,10],[8,16],[4,17],[5,25],[7,94],[-3,18],[-4,13],[-16,24],[-7,13],[-5,22],[89,73],[12,2],[14,1],[11,-30],[8,-18],[8,-9],[10,-8],[18,-7],[11,0],[9,4],[14,10],[13,12],[12,15],[15,28],[12,33],[7,13],[11,13],[10,4],[129,25],[13,-2],[5,-19],[5,-11],[10,-11],[8,0],[7,5],[5,9],[11,13],[15,15],[36,19],[19,5],[14,-2],[7,-7],[4,-9],[2,-12],[0,-27],[1,-13],[4,-11],[6,0],[6,5],[26,26],[135,92],[7,7],[4,10],[9,28],[10,17],[9,7],[10,3],[8,-3],[7,-6],[11,-16],[6,-7],[13,-3],[51,3],[133,54],[16,2],[8,8],[6,10],[8,28],[3,9],[7,5],[70,13],[17,8],[12,10],[4,10],[7,26],[4,11],[6,9],[20,16],[46,49]],[[4235,6456],[1,-2],[13,-16],[30,-17],[13,-16],[7,-32],[-3,-31],[-6,-31],[0,-28],[19,-47],[34,-28],[39,-10],[35,8],[39,30],[15,6],[20,1],[11,-3],[10,6],[16,25],[24,16],[28,-18],[31,-28],[35,-14],[41,30],[24,63],[19,75],[26,63],[42,39],[53,20],[55,4],[72,-19],[16,-1],[14,15],[41,65],[18,18],[44,34],[9,10],[18,26],[9,9],[12,6],[37,9],[44,29],[23,3],[43,-36],[21,-26],[2,-2],[18,-34],[8,-37],[-7,-34],[-33,-66],[-10,-36],[26,-80],[53,-34],[62,-4],[130,38],[26,1],[20,-25],[11,-52],[4,-60],[-5,-46],[-15,-34],[-17,-24],[-13,-29],[-3,-47],[10,-46],[19,-36],[23,-25],[24,-11],[47,8],[47,28],[126,123],[22,16],[47,14],[45,34],[24,5],[22,-24],[14,-50],[7,-58],[3,-49],[11,-34],[27,-16],[55,-13],[11,-9],[19,-21],[27,-20],[4,-14],[0,-18],[3,-22],[12,-37],[13,-26]],[[1730,4277],[29,17],[8,-26],[-9,-27],[13,-28],[-1,-34],[3,-50],[24,-24],[24,2],[24,9],[2,23],[-2,38],[35,45],[38,22],[8,30],[15,33],[-22,7],[-7,29],[-23,8],[-13,10],[2,27],[-14,-1],[-5,36],[-8,32],[-13,33],[-9,35],[-11,15],[-5,-17],[-2,-32],[5,-42],[-7,-27],[-34,-23],[-28,-12],[-4,-21],[12,-41],[-33,-33],[8,-13]],[[4190,7272],[44,-2],[41,26],[16,-6],[9,-42],[-2,-41],[-13,-17],[-42,-11],[-37,-26],[-65,-73],[-8,8],[-36,86],[-13,22],[-43,42],[-17,33],[-10,38],[2,36],[19,-1],[155,-72]],[[4220,9798],[2,-39],[-26,8],[-21,28],[-37,68],[-22,30],[-1,2],[-15,47],[21,7],[35,-20],[29,-37],[18,-39],[17,-55]],[[1036,5091],[-24,50],[-43,134],[-19,33],[-1,0],[-10,4],[-10,1],[-11,-1],[-10,-4],[-21,-22],[-21,-11],[-21,6],[-20,27],[-10,8],[-22,10],[-18,1],[-18,-7],[-17,-12],[-98,-46],[-34,-1],[-163,37],[-22,31],[-2,2],[-6,56],[14,178],[-2,31],[0,1],[-12,27],[-8,4],[-21,-6],[-9,2],[-7,10],[-10,26],[-6,9],[-50,28],[-104,6],[-155,76],[-36,47],[0,1],[-9,90],[2,33],[3,16],[6,10],[9,12],[3,3],[14,6],[11,-5],[8,5],[10,76],[16,77],[2,84],[8,92],[16,89],[12,21],[19,3],[33,-6],[7,2],[6,4],[5,7],[4,8],[-9,102],[26,34],[102,22],[119,118],[47,21],[60,-2],[437,-157],[191,-8],[105,-52],[63,-6],[63,8],[79,29],[25,16],[0,1],[-9,29],[-5,14],[17,35],[55,48],[31,45],[17,45],[7,55],[13,370],[13,49],[15,-2],[3,-1],[14,-8],[43,53],[21,-22],[34,-77],[17,-12],[9,15],[3,23],[0,11],[0,4],[9,34],[4,4],[14,3],[4,5],[16,43],[0,33],[-28,84],[-13,49],[-4,43],[8,18],[27,-25],[26,-79],[20,-99],[29,-63],[54,32],[-4,51],[-113,214],[-6,23],[-2,21],[3,48],[-4,17],[-23,25],[-3,21],[17,42],[20,-22],[29,-72],[16,7],[46,55],[26,11],[93,-20],[29,6],[10,29],[2,33],[-4,35],[-8,32],[-60,59],[-299,-80],[-12,-3],[-46,24],[-57,49],[-33,50],[27,26],[52,19],[71,26],[28,3],[75,-18],[29,6],[45,35],[25,14],[208,45],[29,-7],[41,-28],[44,-30],[115,-48],[63,-3],[48,31],[0,54],[-93,115],[-5,68],[34,5],[49,-25],[41,9],[9,113],[-8,50],[-14,44],[-33,82],[-12,41],[-28,129],[-51,119],[0,48],[46,43],[78,8],[18,16],[5,31],[-8,122],[0,84],[9,69],[20,59],[33,58],[18,21],[20,15],[42,19],[23,-4],[17,-19],[16,-23],[20,-17],[41,-5],[29,-37],[49,-106],[16,-18],[32,-29],[14,-24],[23,-54],[14,-24],[16,-20],[39,-20],[34,14],[31,38],[53,92],[26,29],[28,20],[235,101],[121,126],[133,72],[93,76],[71,113],[35,242],[48,17],[57,-32],[16,-24],[24,-36],[3,-8],[4,-7],[41,-47],[233,-359],[11,-20],[-3,-43],[-25,13],[-45,53],[-25,-4],[-12,-29],[3,-39],[20,-36],[117,-37],[49,-38],[-11,-77],[-3,-20],[0,-1],[-20,-17],[-4,-9],[-3,-11],[-4,-9],[-21,-6],[-11,-8],[-18,-19],[-19,-24],[-78,-87],[-45,-76],[-16,-19],[-19,-13],[-19,-7],[-18,-11],[-17,-24],[-31,-71],[-13,-23],[-12,-7],[-88,-28],[-38,-34],[-21,-45],[3,-48],[2,-11],[2,-6],[17,-33],[-1,0],[3,-3],[11,-18],[3,-10],[-17,-80],[64,-39],[26,-10],[28,3],[24,10],[25,4],[28,-16],[34,-77],[17,-106],[23,-97],[51,-51],[54,-5],[55,7],[182,77],[114,14],[26,-4],[26,-55],[-21,-56],[-43,-44],[-39,-18],[-68,21],[-17,-9],[-9,-10],[-24,-17],[0,3],[-5,6],[-6,4],[-7,-1],[-5,-6],[-7,-14],[-50,-59],[-18,-15],[-82,-37],[-33,-45],[-6,-58],[1,-71],[-11,-79],[-17,-42],[-22,-32],[-26,-22],[-27,-14],[-55,-2],[-15,-11],[-8,-20],[-11,-51],[-11,-19],[-27,13],[-10,30],[4,40],[11,43],[17,32],[20,20],[21,15],[20,19],[19,51],[-13,41],[-30,28],[-32,14],[-102,21],[-48,29],[-45,8],[-23,17],[-21,31],[-16,32],[-18,28],[-28,19],[-29,3],[-64,-5],[-28,12],[-182,164],[-45,7],[-504,-248],[-21,-4],[-20,3],[-8,11],[-14,35],[-10,5],[-6,-11],[-5,-39],[-4,-15],[-26,-53],[-21,-52],[-8,-59],[10,-73],[5,-13],[3,-14],[1,-16],[-1,-15],[29,-75],[2,-30],[-123,-60],[-62,145],[-37,58],[-18,5],[-5,-5],[0,-14],[-6,-23],[-24,-76],[-59,-244],[-14,-89],[-4,-90],[9,-42],[19,-31],[23,-26],[18,-31],[9,-38],[5,-43],[-6,-274],[18,-55],[50,29],[8,16],[3,18],[5,15],[12,8],[7,-7],[31,-45],[27,-12],[55,12],[28,0],[24,5],[23,12],[22,18],[43,52],[22,12],[112,10],[52,-9],[50,-23],[54,-43],[49,-21],[99,29],[52,-8],[39,-25],[11,12],[20,40],[21,27],[27,11],[28,0],[26,-9],[43,-34],[20,-11],[22,10],[10,22],[36,106],[20,21],[12,-22],[6,-43],[0,-39],[-14,-82],[3,-32],[22,-12],[22,8],[114,116],[17,9],[13,-3],[27,-11],[12,0],[8,7],[7,13],[7,12],[13,6],[21,6],[41,22],[22,3],[18,-10],[29,-35],[16,-13],[22,-3],[47,12],[22,-4],[31,-33],[18,-48],[25,-110]]],
transform:{scale:[.0007822216955695406,.0004361772033203289],translate:[67.34269006300022,36.67864084900012]}},m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();