!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo={type:"Topology",objects:{yem:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:null},id:"-99",arcs:[[[0]],[[1]]]},{type:"Polygon",properties:{name:"Sa`dah"},id:"YE.SD",arcs:[[2,3,4,5]]},{type:"MultiPolygon",properties:{name:"Al Hudaydah"},id:"YE.HU",arcs:[[[6]],[[7]],[[8]],[[9,10,11,12,13,14,15,16]]]},{type:"Polygon",properties:{name:"Al Mahwit"},id:"YE.MW",arcs:[[17,-10,18,19]]},{type:"Polygon",properties:{name:"Dhamar"},id:"YE.DH",arcs:[[20,21,-13,22,23]]},{type:"Polygon",properties:{name:"Hajjah"},id:"YE.HJ",arcs:[[24,-19,-17,25,-5]]},{type:"Polygon",properties:{name:"Amran"},id:"YE.AM",arcs:[[26,27,-20,-25,-4]]},{type:"Polygon",properties:{name:"Ibb"},id:"YE.IB",arcs:[[28,29,30,-14,-22]]},{type:"Polygon",properties:{name:"Lahij"},id:"YE.LA",arcs:[[31,32,33,34,35,36,37]]},{type:"MultiPolygon",properties:{name:"Ta`izz"},id:"YE.TA",arcs:[[[38]],[[39,-36,40,-15,-31]]]},{type:"Polygon",properties:{name:"Al Mahrah"},id:"YE.MR",arcs:[[41,42]]},{type:"Polygon",properties:{name:"Al Bayda'"},id:"YE.BA",arcs:[[43,44,-38,45,-29,-21,46,47]]},{type:"Polygon",properties:{name:"Al Dali'"},id:"YE.DL",arcs:[[-37,-40,-30,-46]]},{type:"Polygon",properties:{name:"Al Jawf"},id:"YE.JA",arcs:[[48,49,50,-27,-3,51]]},{type:"Polygon",properties:{name:"Shabwah"},id:"YE.SH",arcs:[[52,53,-44,54,55]]},{type:"Polygon",properties:{name:"Ma'rib"},id:"YE.MA",arcs:[[-55,-48,56,-50,57]]},{type:"Polygon",properties:{name:"Sana'a"},id:"YE.SN",arcs:[[-57,-47,-24,58,-11,-18,-28,-51],[59]]},{type:"MultiPolygon",properties:{name:"Hadramawt"},id:"YE.HD",arcs:[[[60]],[[61]],[[62]],[[63]],[[64,-56,-58,-49,65,-42]]]},{type:"Polygon",properties:{name:"Amanat Al Asimah"},id:"YE.",arcs:[[-60]]},{type:"Polygon",properties:{name:"Raymah"},id:"YE.",arcs:[[-59,-23,-12]]},{type:"Polygon",properties:{name:"`Adan"},id:"YE.AD",arcs:[[-34,66]]},{type:"Polygon",properties:{name:"Abyan"},id:"YE.",arcs:[[67,-32,-45,-54]]}]}},arcs:[[[21,4255],[-5,-10],[-14,32],[-2,13],[6,11],[10,14],[8,-13],[0,-19],[3,-22],[-6,-6]],[[259,5236],[0,-11],[-6,-10],[-52,11],[-16,-5],[-7,6],[2,24],[-4,23],[4,8],[6,1],[34,-32],[2,-4],[22,-7],[9,-1],[6,-3]],[[2272,7725],[-18,-463],[-66,-58],[-48,9],[-51,-2],[-54,-21],[-45,-27],[-107,-93],[-14,-27],[-33,-79],[-11,-17],[-14,-44],[-8,-12],[-12,-3],[-14,-28],[-34,-37],[-11,-10],[-7,-9],[-9,-4],[-11,-9],[-17,-24],[-11,-25],[-16,-53],[-10,-73],[-12,-51]],[[1639,6565],[-57,-30],[-34,2],[-37,12],[-82,60],[-58,24],[-31,1],[-21,-12],[-12,-14],[-22,-21],[-16,-11],[-105,-44]],[[1164,6532],[-90,13],[-61,-6],[-17,4],[-14,14],[-136,67],[-17,19]],[[829,6643],[-4,34],[-8,22],[-2,20],[12,30],[7,9],[8,6],[6,7],[3,16],[-1,18],[-5,16],[-9,11],[-30,15],[-9,13],[-10,53],[-17,57],[-3,24],[6,36],[13,57],[3,27],[0,32],[-4,32],[-7,24],[-3,24],[5,30],[17,57],[4,30],[0,33],[5,29],[16,23],[22,16],[18,7],[18,15],[3,29],[-8,32],[-16,21],[-46,26],[2,6],[39,83],[70,104],[32,34],[39,43],[18,12],[18,4],[14,-6],[28,-2],[13,-6],[59,-44],[35,-35],[15,-30],[41,-79],[33,-46],[17,-10],[14,0],[30,6],[32,-5],[15,1],[18,10],[5,-4],[0,-16],[-2,-29],[3,-11],[3,-7],[5,-5],[9,-3],[19,5],[13,21],[10,29],[13,25],[17,16],[14,-3],[31,-27],[21,-6],[15,13],[13,26],[11,36],[12,14],[17,-3],[34,-17],[15,2],[62,24],[78,15],[50,-4],[55,-19],[23,2],[50,33],[18,7],[164,-2],[99,-1]],[[428,2255],[-2,17],[0,4],[6,30],[21,63],[34,43],[17,11],[10,-14],[-3,-28],[-24,-46],[-6,-31],[-8,-14],[-45,-35]],[[509,2637],[-6,-10],[-22,1],[11,40],[0,1],[-17,11],[-20,19],[-8,11],[-9,16],[-1,6],[-6,30],[7,22],[16,26],[19,20],[13,7],[6,10],[22,-50],[5,-20],[0,-16],[-11,-54],[-1,-45],[2,-25]],[[341,4764],[9,-3],[11,5],[10,11],[8,-2],[0,-18],[-10,-56],[-2,-19],[2,-12],[-2,-12],[-4,-6],[1,-3],[5,3],[-1,-9],[-17,-43],[-9,-15],[-11,1],[-9,13],[-9,24],[0,33],[8,35],[2,75],[10,25],[10,19],[8,11],[3,12],[4,11],[18,16],[5,-5],[4,-12],[-20,-24],[-3,-9],[4,-3],[0,-6],[-3,-9],[-7,-10],[-12,-10],[-3,-8]],[[933,4936],[2,-62],[4,-16],[0,-32],[4,-25],[1,-41],[-2,-48],[4,-33],[2,-40],[21,-57],[18,-23],[20,-31],[34,-17],[8,-25],[9,-41],[16,-48],[9,-42],[12,-33],[25,-43],[21,-19]],[[1141,4260],[26,-44],[25,-23],[22,-9],[16,0],[15,4],[20,-31],[-1,-17],[-5,-7],[-13,-34]],[[1246,4099],[-11,-14],[-12,-3],[-25,16],[-9,2],[-6,-21],[-23,-44],[-16,-22],[-15,-38],[-13,-64],[11,-107],[-4,-23],[-9,-21],[-3,-25],[1,-26],[2,-22],[-3,-24],[3,-28],[32,-146],[2,-23],[11,-37],[2,-17],[1,-38],[-3,-27],[-29,-60]],[[1130,3287],[-15,-2],[-6,-19],[27,-199],[1,-49],[10,-49],[14,-21],[18,-21],[25,-14],[46,-48],[15,-12],[13,3],[10,17],[13,12],[10,15],[13,9]],[[1324,2909],[2,-40],[4,-26],[-5,-39],[-31,-88],[-13,-14],[-8,9],[-12,9],[-15,-12],[-3,-18],[12,-26],[6,-18],[2,-23],[-10,4],[-21,-12]],[[1232,2615],[-67,-20],[-20,-11],[-29,-22],[-61,-140],[-8,-28],[-9,-19],[-18,-5],[-28,-14],[-23,-17],[-57,-80],[-3,-3]],[[909,2256],[1,32],[-4,51],[-26,90],[-4,15],[-8,119],[-5,14],[-53,99],[-18,78],[-17,65],[-18,46],[-10,109],[-6,52],[-8,44],[-15,73],[-23,135],[-7,29],[-4,45],[-10,42],[-3,47],[-2,13],[-4,8],[-15,19],[-3,8],[1,17],[4,7],[6,-2],[8,-12],[9,-29],[9,-66],[13,-19],[0,42],[-1,20],[-5,18],[12,59],[-9,49],[-16,44],[-9,47],[2,10],[7,15],[2,9],[-1,8],[-2,5],[-2,2],[-3,18],[-6,7],[-5,5],[-3,5],[-2,7],[-4,7],[-4,10],[-6,50],[0,16],[-9,51],[-39,85],[-9,53],[-1,30],[1,16],[6,-3],[4,-11],[-1,-10],[-3,-11],[0,-16],[6,-27],[10,-30],[13,-26],[16,-17],[0,21],[6,48],[-1,30],[-9,52],[-3,48],[-4,18],[-7,15],[-21,35],[-9,18],[-5,25],[-3,34],[4,78],[-4,26],[-10,24],[-29,22],[-26,39],[-16,18],[-18,16],[-17,11],[-18,7],[-16,0],[-11,-13],[-5,-29],[-9,-4],[-18,12],[-16,24],[-1,32],[-5,20],[9,2],[27,-11],[13,4],[9,10],[5,15],[3,20],[-1,15],[-8,22],[-2,17],[21,69],[6,16],[4,-19],[-2,-24],[-6,-22],[-6,-15],[15,-12],[12,-14],[33,-48],[4,-11],[2,-18],[-2,-28],[2,-9],[11,-8],[1,11],[-1,8],[-3,5],[-3,5],[12,23],[5,32],[-2,112],[-7,51],[-13,41],[-4,20],[1,16],[2,15],[1,18],[-1,17],[-2,17],[-3,18],[-11,36],[-33,78],[1,12],[10,21],[-8,14],[-7,17],[-5,20],[-2,24],[8,52],[-2,22],[-17,10],[6,23],[8,12],[9,10],[10,15],[7,15],[3,12],[1,16],[0,23],[4,40],[11,28],[15,20],[14,16],[15,21],[8,26],[4,31]],[[546,5523],[131,-136],[55,12],[34,-3],[18,15],[12,15],[31,13],[83,11],[21,-28],[9,-40],[-1,-46],[-5,-37],[-5,-24],[-7,-20],[0,-27],[9,-47],[-5,-85],[-4,-14],[-1,-20],[12,-126]],[[1474,4929],[-10,-65],[-5,-62],[-13,-25],[-33,-40],[-16,-13],[-57,-72],[-31,-52],[-54,-19],[-9,-17],[-50,-53],[-13,-32],[-7,-37],[3,-72],[-8,-36],[-10,-14],[-20,-60]],[[933,4936],[45,28],[142,24],[17,-4],[17,3],[74,-15],[21,-12]],[[1249,4960],[19,3],[15,9],[42,-3],[35,26],[16,19],[15,-3],[11,-8],[7,-17],[14,-16],[51,-41]],[[2189,3841],[-62,-130],[-20,-142],[-15,-205],[-20,-69],[-28,-46],[-29,-26],[-19,-78]],[[1996,3145],[-16,-12],[-10,6],[-15,13],[-46,26],[-37,43],[-19,14],[-22,13],[-18,19],[-41,19],[-18,16],[-15,27],[-35,50],[-13,8],[-10,-3],[-9,-26],[-1,-19],[-6,-24],[-9,-25],[-37,-25],[-21,8],[-6,20],[-11,76],[-12,4],[-15,-12],[-19,-36],[-23,-32],[-17,-40],[-9,2],[-11,-1],[-12,-10],[-6,-14],[0,-25],[6,-23],[3,-33],[-1,-33],[-22,-70],[-19,-17],[-21,-9],[-13,10],[-4,26],[4,31],[-5,15],[-6,-2],[-6,-23],[-13,-32],[-13,-62],[-11,-28],[-12,-46]],[[1130,3287],[19,1],[14,3],[49,47],[27,42],[8,18],[14,11],[25,1],[17,-11],[12,-3],[12,5],[9,1],[14,6],[12,16],[50,54],[15,24],[14,20],[24,55],[7,36],[-7,26],[-14,31],[-8,39],[-17,44],[-52,103],[-19,46],[-10,50],[0,26]],[[1345,3978],[11,19],[39,-7],[31,5],[18,10],[27,44],[16,16],[16,18],[19,18],[44,56],[28,18],[26,8],[22,3],[14,-18],[22,-15],[17,-30],[19,-28],[61,-22],[24,13],[11,27],[5,28],[7,21],[19,-4],[15,-32],[21,-24],[21,-4],[34,22],[16,16],[19,39],[14,37],[19,22],[17,4],[21,-5],[20,5],[21,-4],[18,-21],[24,-59],[11,-98],[15,-53],[32,-79],[9,-33],[1,-50]],[[1164,6532],[-4,-251],[12,-198],[-2,-24],[-1,-32],[0,-25],[3,-21],[-1,-22],[5,-15],[24,-29],[9,-26],[-1,-43],[3,-36],[1,-33],[32,-112],[-1,-20],[-7,-16],[-11,-14],[-8,-13],[-5,-15],[-2,-15],[9,-16],[10,3],[30,62],[8,6],[8,-8],[10,-14],[20,-58],[8,-55],[6,-57],[-4,-28],[-2,-52],[5,-36],[1,-28],[14,-21],[6,-13],[-4,-53],[-6,-39],[-9,-18],[-12,-4],[-15,5],[-13,-1],[-11,-11],[-7,-13],[-4,-15],[-2,-20],[-1,-19],[11,-35],[-1,-20],[-16,-54]],[[546,5523],[0,2],[7,199],[-3,66],[-22,204],[-3,108],[-1,10],[-5,30],[1,-1],[0,4],[0,7],[-1,9],[-2,8],[-7,14],[-1,4],[39,23],[28,21],[21,25],[7,24],[-1,23],[-2,22],[3,19],[9,7],[31,10],[12,7],[35,36],[18,23],[10,25],[21,95],[3,27],[1,31],[4,20],[10,7],[21,-6],[32,-17],[14,4],[5,27],[-1,3]],[[1639,6565],[42,-49],[13,-10],[47,-61],[12,-22],[4,-23],[-4,-22],[-7,-17],[-1,-25],[12,-42],[7,-54],[7,-37],[12,-36],[2,-32],[-1,-29],[-5,-26],[4,-19],[5,-13],[2,-23],[-18,-93],[7,-39],[2,-45],[4,-39],[26,-80],[20,-40]],[[1831,5689],[-45,-5],[-47,-31],[-46,-43],[-9,-34],[-6,-37],[3,-80],[5,-40],[6,-28],[-1,-31],[-4,-28],[-17,-41],[-23,-30],[-15,-37],[-1,-67],[-9,-60],[-49,-67],[-23,-20],[-26,-6],[-31,4],[-11,-20],[1,-23],[-9,-36]],[[1996,3145],[27,-29],[-1,-82],[7,-21],[10,-5],[28,-30]],[[2067,2978],[-9,-46],[-13,-16],[-15,0],[-17,-3],[-7,-11],[-1,-20],[-8,-31],[-13,-23],[-6,-31],[5,-30],[14,-25],[-7,-27],[-15,-6],[-16,0],[-20,-6],[-20,-15],[-20,-24],[-13,-37],[-3,-27],[-4,-25],[-1,-17],[-8,-7],[-10,1],[-9,-11],[-13,-58],[-13,-15],[-6,-26],[1,-9],[-4,-37]],[[1816,2396],[-8,-21],[-11,-22],[-31,-38],[-20,-3],[-16,11],[-12,10],[-16,11],[-29,1],[-61,34],[-20,22],[-41,7],[-94,47],[-6,21],[4,17],[0,28],[-12,9],[-36,-26],[-7,14],[1,26],[-6,21],[-37,-8],[-17,-13],[-21,10],[-17,-15],[-49,3],[-22,73]],[[2622,2593],[-30,-15],[-6,-22],[-3,-46],[3,-30],[-3,-22],[-11,-12],[-12,-11],[-30,-12],[-10,-17],[-6,-24],[3,-23],[0,-24],[-2,-21],[-15,-21],[-31,-34],[0,-18],[12,-10],[40,-5],[15,-10],[4,-13],[-8,-20],[-45,-88],[-22,-60],[-3,-59],[3,-41],[12,-23],[12,-13],[9,-17],[1,-17],[-7,-32],[-38,-48],[-53,-258],[-26,-329]],[[2375,1198],[-5,-15],[0,-2]],[[2370,1181],[-68,-9],[-148,-69],[-167,-51],[-74,-47],[-35,-37],[-56,-151],[0,-1]],[[1822,816],[-2,16],[-4,1],[-19,-13],[-24,-10],[-52,-44],[-18,-3],[-16,3],[-15,0],[-31,-29],[-17,-1],[-16,10],[-10,20],[11,-1],[20,-7],[11,-2],[7,10],[0,20],[-6,11],[-9,-11],[-21,5],[-26,-18],[-48,-46],[-23,-11],[-13,-1],[-21,12],[-24,-18],[-12,-2],[-16,17],[-14,51],[-12,11],[-17,4],[-13,10],[-23,27],[-13,11],[-15,9],[-17,7],[-18,2],[-11,7],[-31,32],[-16,11],[-28,-1],[-8,6],[-12,16],[-8,7],[-8,3],[-10,-8],[-46,-81]],[[1108,848],[29,104],[62,168],[46,81],[28,75],[3,32],[-4,24],[-6,18],[0,23],[11,15],[28,6],[12,12],[15,11],[12,22],[16,12],[16,8],[16,-4],[15,-14],[15,-8],[29,-30],[16,-24],[22,-18],[15,-2],[4,23],[-3,72],[6,9],[31,-33],[19,-5],[9,14],[4,31],[8,28],[10,5],[18,5],[9,10],[-1,21],[0,24],[8,12],[10,14],[10,32],[34,-11],[34,2],[42,18],[19,26],[4,33],[-4,29],[1,36],[-1,25],[-6,27],[4,19],[19,9],[52,2],[8,16],[-2,26],[-10,19],[-23,5],[-11,11],[-14,11],[-7,22],[6,23],[13,15],[15,10],[22,6],[6,9],[1,11],[-2,10],[4,10],[10,5],[11,1],[13,9],[9,17],[7,17],[11,9],[7,31]],[[1918,2129],[32,-21],[11,-23],[33,-44],[22,8],[29,34],[55,41],[13,27],[13,15],[32,-22],[18,5],[5,29],[-3,35],[2,106],[19,8],[26,4],[67,47],[36,42],[34,28],[28,48],[50,195]],[[2440,2691],[48,0],[13,-5],[25,24],[18,28],[42,47],[18,4],[13,-12],[10,-74],[-5,-110]],[[1042,787],[-1,-21],[-10,-10],[-5,4],[-10,17],[-4,9],[-10,-4],[-5,5],[-3,19],[7,6],[7,4],[18,1],[11,-10],[1,-5],[4,-15]],[[1816,2396],[14,-40],[3,-33],[-3,-35],[-8,-33],[-1,-30],[13,-23],[17,-8],[18,7],[17,-3],[25,-21],[5,-19],[2,-29]],[[1108,848],[-4,-7],[-11,-11],[-17,-3],[-19,4],[-4,14],[15,117],[1,56],[-7,50],[-17,46],[-24,40],[-3,15],[-3,18],[-6,12],[-5,9],[-3,12],[-1,28],[-3,28],[-6,25],[-7,23],[-29,58],[-4,17],[-1,11],[-10,34],[-8,37],[-3,12],[-49,107],[-18,103],[11,45],[7,49],[5,258],[16,45],[3,14],[2,10],[3,10],[9,7],[-11,46],[2,69]],[[6982,4337],[-2,9],[-1,27],[-7,21],[-6,26],[-8,25],[-6,38],[-9,27],[-8,29],[-14,36],[-19,15],[-44,12],[-54,25],[-54,-5],[-19,-12],[-20,-9],[-62,-11],[-41,3],[-50,29],[-12,26],[-6,24],[0,27],[2,25],[9,25],[13,27],[15,46],[13,19],[8,21],[4,59],[-5,28],[-8,15],[-33,16],[-24,5],[-25,13],[-14,32],[-7,51],[6,57],[7,38],[7,29],[0,28],[-4,34],[-1,39],[8,42],[0,36],[7,51],[14,26],[13,41],[31,148],[-4,51],[-10,38],[-17,17],[-12,21],[-12,28],[1,54],[63,420],[-6,53],[-9,31],[-1,33],[13,57],[17,37],[20,28],[34,36],[142,103],[65,76],[62,127],[325,462],[105,270],[99,380],[76,413],[50,435],[138,599],[221,557]],[[7936,9976],[23,-92],[34,-141],[35,-140],[34,-140],[35,-140],[35,-141],[34,-140],[35,-140],[34,-140],[35,-140],[34,-141],[35,-140],[34,-140],[35,-140],[34,-141],[35,-140],[35,-140],[25,-104],[-1,-1],[-5,-1],[-1,0],[12,-14],[30,-3],[14,-8],[9,-20],[31,-122],[51,-196],[50,-197],[51,-197],[50,-196],[-17,-15],[-38,-18],[-30,12],[-9,-14],[-9,-3],[-10,-1],[-8,-8],[-16,-27],[-9,-12],[-20,-11],[-27,-28],[-34,-12],[-16,-12],[-30,-31],[-12,-9],[-113,-52],[-52,-42],[-91,-111],[-82,-116],[-21,-40],[-11,-29],[-14,-51],[-27,-53],[-6,-16],[-3,-19],[0,-14],[2,-18],[2,-14],[2,-4],[-6,-27],[-19,-36],[-8,-59],[-19,-40],[-4,-26],[7,-28],[11,-33],[7,-34],[-9,-59],[13,-22],[27,-24],[0,-21],[-6,-104],[3,-63],[8,-61],[6,-9],[4,-8],[1,-8],[-1,-9],[-4,-13],[-1,-8],[0,-11],[-3,-11],[-4,-9],[-6,-3],[-18,-3],[-122,-61],[-7,-10],[-6,-10],[-5,-7],[-7,-4],[-12,-2],[-8,-3],[-64,-43],[-31,-35],[1,-32],[-1,-6],[-2,-2],[-2,0],[-1,-1],[0,-17],[-2,-2],[-5,5],[-4,3],[-6,6],[-7,10],[-7,3],[-11,-13],[-5,-6],[-41,-14],[-64,-61],[-10,-18],[-4,-21],[4,-28],[7,-15],[0,-10],[-17,-11],[-85,-14],[-16,-9],[-13,-13],[-13,-15],[-19,-39],[-16,-2],[-6,-5],[-3,-9],[-9,0],[-41,-32],[-11,-12],[-20,12],[-23,-16],[-21,-24],[-16,-12],[-161,-76],[-176,-58],[-38,-18]],[[2754,3744],[90,0],[19,-16],[14,-22],[-4,-27],[-10,-30],[-6,-29],[-1,-35],[16,-23],[34,-13],[19,-27],[46,-213],[17,-50],[29,-33],[37,-12],[34,-25],[15,-19],[3,-17],[-1,-16],[3,-17],[1,-19],[8,-25],[3,-25],[18,-30]],[[3138,3021],[-12,-121],[-22,-54],[-13,-69],[-30,-47],[-139,-179],[-33,-34],[-25,-19],[-22,-9],[-17,-3],[-16,0],[-28,25],[-13,41],[-25,14],[-112,19],[-9,8]],[[2440,2691],[0,38],[-5,36],[-20,44],[-60,99],[-23,50],[-13,42],[-18,41],[-27,17],[-33,1],[-22,6],[-18,-11],[-13,-5],[-11,-15],[-13,-9],[-22,0],[-24,-7],[-16,-16],[-35,-24]],[[2189,3841],[42,11],[34,24],[34,50]],[[2299,3926],[43,-15],[22,-21],[17,-31],[4,-40],[0,-43],[7,-75],[7,-48],[1,-47],[8,-34],[2,-31],[7,-31],[10,-27],[15,-16],[26,-1],[32,40],[9,20],[13,8],[34,-13],[5,15],[-3,15],[7,15],[15,2],[23,15],[27,23],[28,4],[21,-12],[18,-4],[13,9],[0,25],[-14,45],[1,21],[7,16],[8,-3],[5,-9],[5,-16],[8,-8],[12,10],[6,12],[6,48]],[[3907,7026],[34,-876],[-166,-285]],[[3775,5865],[-677,-217],[-244,-149],[-104,-156],[-92,-14],[-170,47],[-195,178],[-272,111]],[[2021,5665],[-54,57],[-69,9],[-16,-12],[-7,-13],[-44,-17]],[[2272,7725],[20,-1],[136,-1],[46,-16],[131,-132],[37,-21],[171,-31],[201,-35],[153,-27],[196,-35],[133,32],[180,42],[30,-30],[52,-125],[59,-144],[69,-163],[10,-12],[11,0]],[[5260,2807],[-87,3],[-13,-8],[-36,-55],[-8,-5],[-7,-1],[-6,-3],[-7,-11],[-15,32],[-28,9],[-63,-1],[3,-6],[3,-14],[-33,-1],[-93,-48],[4,35],[-13,23],[-18,16],[-12,15],[-6,-10],[-22,18],[-72,31],[-12,-12],[-43,-22],[-14,-5],[-12,-13],[-41,-87],[-36,-33],[-124,-75],[-18,-20],[-66,-122],[-103,-162],[-31,-33],[-59,-32]],[[4172,2210],[-1,1],[-7,73],[-6,37],[-1,56],[-25,141],[-42,155],[8,34],[10,27],[10,36],[-3,48],[1,64],[-11,61],[-29,34],[-111,9],[-55,22],[-52,32],[-57,14],[-62,-14],[-81,-40],[-65,-48],[-93,-27],[-14,35],[-5,38],[-28,91],[-12,53],[-19,40],[-23,-3],[-26,-22],[-71,-46],[-24,-32],[-30,-25],[-37,-21],[-73,-12]],[[2754,3744],[-18,18],[-10,20],[-7,41],[2,79],[4,43],[5,39],[0,18],[-2,12],[11,13],[4,18],[-1,26],[6,57],[19,62],[7,31],[17,62],[112,164],[473,577]],[[3376,5024],[1139,152],[73,-44],[10,-47],[-106,-286],[-35,-74],[-19,-70],[-8,-51],[0,-36],[-24,-124],[-2,-40],[0,-36],[20,-36],[34,-79],[0,-43],[-12,-42],[-131,-195],[-19,-40],[6,-37],[90,-110],[57,-98],[62,-72],[62,-39],[83,-21],[60,-3],[20,-13],[21,-20],[16,-32],[16,-57],[8,-45],[20,-46],[11,-40],[40,-70],[60,-51],[53,-8],[62,2],[69,-31],[74,-60],[18,-37],[12,-31],[7,-28],[2,-25],[4,-24],[23,-70],[6,-29],[2,-19],[0,-12]],[[2299,3926],[1,88],[4,22],[0,27],[-6,11],[-4,9],[2,12],[8,17],[6,29],[11,31],[8,18],[10,17],[10,41],[4,21],[14,17],[40,32],[12,34],[9,46],[-3,34],[2,29],[0,31],[-8,50],[-16,28],[-24,18],[-37,7],[-28,12],[-21,33],[-27,51],[-35,48],[-15,8],[-12,5],[-16,-18],[-10,-26],[-10,-34],[-28,-15],[-26,18],[-28,58],[-27,40],[-19,11],[-18,3],[-13,9],[-3,23],[17,27],[95,116],[23,41],[15,56],[-17,48],[-53,115],[-12,67],[0,100],[-14,135],[-39,139]],[[3775,5865],[-399,-841]],[[1345,3978],[-34,17],[-3,16],[-43,10],[-5,44],[-14,34]],[[1633,4622],[22,0],[17,0],[6,2],[1,6],[-2,14],[3,8],[8,2],[7,7],[3,13],[0,27],[-4,20],[-5,-3],[-5,7],[-1,17],[-1,16],[-6,18],[-1,13],[1,16],[-1,13],[4,14],[11,18],[14,17],[8,12],[2,14],[-1,11],[-2,21],[4,15],[7,12],[4,-12],[6,13],[8,0],[16,-3],[3,10],[0,15],[3,14],[6,7],[4,8],[5,0],[5,-4],[-3,-11],[-1,-13],[7,-6],[12,1],[7,10],[6,12],[-2,12],[-5,13],[-15,17],[-9,19],[-7,11],[-14,11],[-13,8],[-11,-1],[-11,-7],[-8,-7],[-10,-19],[-7,-17],[-3,-8],[-11,0],[-7,-6],[-6,-12],[-15,-10],[-7,-15],[-1,-37],[3,-9],[-2,-7],[-8,-2],[-3,-12],[-6,-15],[-10,-3],[-5,-11],[-8,-4],[-15,-6],[-9,-5],[-1,-12],[4,-17],[7,-10],[6,-5],[8,-10],[11,-23],[1,-15],[-4,-3],[-8,-4],[-6,-11],[-4,-29],[3,-26],[14,-15],[14,-18],[3,-13],[-3,-31],[3,-7]],[[9004,10],[2,-10],[-27,3],[-11,2],[-12,9],[16,11],[15,-2],[9,-8],[8,-5]],[[8786,100],[12,-8],[12,0],[12,-15],[3,-23],[0,-21],[-12,-6],[-12,-3],[-24,17],[-16,25],[-18,8],[3,14],[12,7],[11,-2],[8,6],[9,1]],[[8092,149],[17,-11],[14,1],[23,-7],[35,-2],[24,-18],[45,17],[12,-4],[4,-15],[-10,-7],[-9,-16],[7,-8],[7,-25],[-23,8],[-10,-4],[-13,-8],[-38,13],[-35,3],[-5,12],[-8,22],[-5,0],[-5,1],[-17,-9],[-9,-3],[-3,5],[-10,24],[-1,7],[-5,-2],[-16,12],[-11,-3],[-9,-5],[-5,7],[-3,5],[-27,22],[-4,19],[12,6],[13,3],[19,0],[24,-10],[25,-30]],[[9218,860],[27,-5],[19,3],[20,4],[32,-51],[55,-73],[37,-17],[30,-13],[17,35],[8,35],[22,9],[15,-4],[14,-12],[27,7],[28,-2],[30,30],[11,7],[19,22],[9,18],[21,3],[10,-18],[12,-15],[17,-19],[13,5],[10,10],[4,-3],[8,-17],[6,-13],[15,-1],[19,-6],[7,-23],[13,-8],[28,-22],[33,-12],[32,-33],[17,-9],[40,-44],[21,5],[15,16],[9,-2],[11,-10],[-10,-18],[-24,-12],[-17,-10],[-16,-5],[-9,-11],[-1,-39],[-34,-25],[-28,-11],[-32,-24],[-37,6],[-30,-20],[-7,-12],[-15,-38],[-7,-8],[-21,-16],[-23,-33],[-12,-13],[-24,4],[-37,-5],[-14,4],[-13,-7],[-19,-27],[-18,11],[-13,7],[-24,-11],[-35,-10],[-97,-33],[-36,-7],[-47,9],[-45,24],[-37,17],[-16,29],[-13,28],[-13,29],[-12,33],[-21,13],[-33,39],[-84,92],[-20,32],[-2,8],[7,10],[22,-11],[16,4],[18,18],[4,5],[1,13],[7,26],[3,15],[-6,85],[12,8],[14,5],[34,8],[7,22],[9,22],[8,15],[15,4],[44,-16]],[[6982,4337],[-115,-57],[-87,-14],[-77,-55],[-21,-22],[-60,-90],[-13,-13],[-33,-10],[-47,-44],[-52,-64],[-11,-1],[-15,10],[-21,-8],[-34,-27],[-15,-8],[-7,-2],[-8,0],[-5,30],[-5,16],[-7,-1],[-12,-8],[-51,-8],[-116,-67],[-178,-80],[-95,-93],[-35,-24],[-36,-10],[-17,-15],[-32,-39],[-67,-54],[-32,-39],[-6,-47],[-8,3],[-13,11],[-15,10],[-19,-4],[-17,-15],[-12,-20],[-40,-101],[-15,-51],[-8,-61],[6,-7],[1,-17],[-2,-17],[-2,-9],[-10,-12],[-33,-23],[-7,-9],[-5,-35],[-12,-13],[-14,-5],[-13,-11],[-4,-11],[-2,-12],[0,-33],[-3,-8],[-5,-7],[-6,-10],[-8,-40],[-14,-16],[-31,-23],[-48,-105],[-15,-16],[-4,-2],[-10,-13],[-5,-5],[-5,-1],[-6,1],[-8,1]],[[3907,7026],[107,-8],[26,6],[24,17],[99,100],[92,93],[1,2],[24,55],[54,256],[38,181],[13,45],[79,169],[120,260],[128,275],[73,157],[63,135],[18,22],[104,91],[138,122],[155,136],[139,121],[116,102],[36,32],[75,47],[104,25],[129,32],[130,31],[129,31],[129,31],[130,32],[129,31],[130,31],[129,32],[129,31],[130,31],[129,32],[129,31],[130,31],[129,31],[129,32],[130,31],[127,31],[6,-23]],[[2370,1181],[-6,-25],[-2,-26],[-1,-4],[-19,-86],[-2,-15],[5,-59],[-3,-21],[-16,-8],[-17,5],[-19,13],[-12,19],[1,22],[13,9],[17,-2],[14,2],[6,25],[-3,1],[-13,29],[-1,6],[-1,5],[1,7],[0,5],[-5,2],[-5,-3],[-1,-6],[1,-7],[-1,-3],[-5,-17],[-3,-3],[-4,1],[-8,7],[-5,2],[-18,-2],[-8,-5],[-4,-8],[-5,-23],[-14,-19],[-15,-4],[-10,21],[-11,-11],[-3,-12],[3,-37],[6,10],[5,2],[25,-4],[4,-5],[-2,-18],[-5,-16],[-12,-17],[-13,-10],[-11,4],[-23,16],[-7,8],[-5,12],[0,6],[-2,5],[-10,7],[-20,4],[-16,-10],[-30,-34],[-1,6],[-3,9],[-2,6],[4,14],[-5,20],[-10,20],[-11,14],[-33,21],[-34,3],[-30,-12],[-25,-21],[-42,-63],[-46,-87],[-2,-8],[-6,-18],[-6,-8],[-1,4]],[[4172,2210],[-128,-69],[-205,-76],[-71,-83],[-100,-66],[-14,-5],[-16,4],[-29,16],[-19,0],[-17,-8],[-35,-27],[-16,-5],[-167,0],[-35,10],[-18,17],[-10,6],[-5,-7],[-4,-4],[-21,-12],[-70,-20],[-138,-10],[-14,-6],[-26,-19],[-12,-4],[-34,2],[-15,-2],[-10,-11],[-111,-44],[-27,-25],[-78,-139],[-22,-29],[-11,-21],[-11,-54],[-38,-86],[-25,-41],[-30,-23],[-139,-60],[-32,-30],[-29,-44],[-11,-24],[-4,-13]]],
transform:{scale:[.0012382830080008113,.0006884882329232865],translate:[42.15870201900006,12.11144367200015]}},m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();