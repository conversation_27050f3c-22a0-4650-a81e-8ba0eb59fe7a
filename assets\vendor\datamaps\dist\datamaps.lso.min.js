!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo={type:"Topology",objects:{lso:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Berea"},id:"LS.BE",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Maseru"},id:"LS.MS",arcs:[[4,5,6,7,8,-2]]},{type:"Polygon",properties:{name:"Mohale's Hoek"},id:"LS.MH",arcs:[[9,10,11,-7,12,13,14]]},{type:"Polygon",properties:{name:"Quthing"},id:"LS.QT",arcs:[[15,-10,16]]},{type:"Polygon",properties:{name:"Butha-Buthe"},id:"LS.BB",arcs:[[17,18,19]]},{type:"Polygon",properties:{name:"Leribe"},id:"LS.LE",arcs:[[20,21,-4,22,-19]]},{type:"Polygon",properties:{name:"Mokhotlong"},id:"LS.MK",arcs:[[23,-21,-18,24]]},{type:"Polygon",properties:{name:"Qacha's Nek"},id:"LS.QN",arcs:[[25,-17,-15,26]]},{type:"Polygon",properties:{name:"Thaba-Tseka"},id:"LS.TT",arcs:[[-24,27,-27,-14,28,-5,-1,-22]]},{type:"MultiPolygon",properties:{name:"Mafeteng"},id:"LS.MF",arcs:[[[-29,-13,-6]],[[-12,29,-8]]]}]}},arcs:[[[5186,7373],[-65,-163],[14,-71],[32,-48],[15,-27],[13,-29],[21,-72],[57,-141],[1,-75],[-20,-49],[-32,-40],[-179,-138],[-20,-21],[-34,-56],[-13,-25],[-41,-65]],[[4935,6353],[-120,194],[-18,6],[-22,6],[-9,-17],[-14,-15],[-16,-11],[-89,-31],[-32,-17],[-27,-22],[-48,-60],[-132,-107],[-95,-98],[-42,-26],[-31,8],[-25,29],[-13,35],[-3,44],[9,106],[0,49],[-4,44],[-30,137],[-14,39],[-18,29],[-22,1],[-25,-26],[-77,-117],[-35,-42],[-86,-69],[-78,-44],[-44,-17],[-122,-25],[-49,7],[-38,15],[-248,151],[-67,23],[-46,5],[-42,-1],[-75,-18],[-31,-15],[-22,-18],[-14,-27],[-7,-29],[-10,-32],[-21,-22],[-61,-12],[-67,26],[-122,25],[-63,30],[-103,76],[-32,9],[-29,-15],[-48,-67],[-40,-36],[-50,-22],[-93,-15],[-46,4],[-38,19],[-19,28],[-11,28],[0,22],[2,15],[20,54],[-1,13],[-7,10],[-16,10],[-21,6],[-16,9],[-11,11],[-19,36],[-3,8]],[[2089,6647],[34,11],[41,36],[-7,55],[-25,112],[2,46],[27,42],[42,26],[42,16],[29,17],[236,295],[93,178],[-5,27]],[[2598,7508],[35,0],[109,50],[68,1],[51,-10],[52,-22],[47,13],[155,107],[48,12],[35,15],[30,16],[153,103],[34,2],[35,-19],[63,-48],[36,-20],[43,-10],[69,-4],[49,34],[39,34],[116,184],[39,42],[52,35],[34,57],[26,82],[46,61],[39,20],[41,-5],[47,-20],[51,-35],[52,-45],[51,-60],[93,-80],[35,-41],[29,-51],[25,-57],[33,-51],[38,-28],[43,-14],[88,-16],[40,-12],[41,-21],[47,-37],[42,-41],[71,-90],[23,-38],[19,-38],[39,-52],[137,-38]],[[4935,6353],[-222,-212],[-41,-22],[-52,-22],[-32,-4],[-23,-25],[-19,-40],[-15,-46],[-10,-41],[-4,-45],[4,-47],[-10,-71],[7,-41],[14,-30],[23,-28],[12,-20],[11,-13],[26,-11],[8,-20],[7,-25],[10,-18],[33,-15],[15,-21],[11,-36],[106,-620],[-6,-50],[6,-41],[62,-243]],[[4856,4546],[-167,27],[-93,0],[-127,8],[-101,-32],[-33,-46],[80,-47],[60,-109],[20,-94],[20,-86],[0,-46],[114,-8],[87,23],[47,-15],[-40,-94],[20,-78],[33,-86],[0,-31],[-87,0],[-73,-39],[13,-70],[0,-109]],[[4629,3614],[-67,-78],[-107,-31],[-34,105]],[[4421,3610],[-41,36],[-14,32],[-31,38],[-51,27],[-185,26],[-65,20],[-90,69],[-92,44],[-530,104],[-88,31],[-161,1],[-344,-50],[-46,24],[-7,33],[32,40],[40,40],[21,56],[-3,68],[-199,333],[-43,33],[-43,10],[-130,-5],[-97,26],[-46,43],[-17,50],[2,109],[-11,39],[-20,6],[-24,-16],[-71,-91],[-17,-16],[-24,-13],[-108,-32],[-40,-1],[-32,5],[-62,18],[-29,21],[-18,30],[-2,34],[-8,44],[-20,54],[-105,120],[-29,51],[-16,46],[-51,92],[-13,40],[-13,84],[-17,39],[-22,20],[-24,13],[-28,5],[-21,-1],[-46,-9],[-87,20],[-10,3]],[[1225,5453],[9,10],[27,42],[21,51],[8,54],[22,18],[96,-5],[22,21],[18,86],[45,75],[171,190],[27,21],[16,29],[6,60],[-12,38],[-24,30],[-17,27],[10,34],[37,30],[38,13],[38,21],[40,56],[10,43],[8,111],[12,39],[69,44],[167,56]],[[5187,3149],[-102,-75],[-138,-46],[-178,16],[-93,-15],[-41,-62],[-25,-66],[-61,-40],[-141,-43],[-73,-11],[-213,55],[-36,-2],[-104,-24],[-112,1],[-43,-18],[-34,-28],[-29,-38],[-87,-165],[-16,-62],[0,-70],[33,-119],[6,-57],[-24,-58],[-20,-20],[-49,-33],[-21,-24],[-14,-26],[-22,-59],[-14,-28],[-112,-62],[-66,-81],[-19,-17],[-70,-27],[-73,-4],[-139,31],[-16,14],[-28,48],[-20,14],[-39,-5],[-16,-28],[1,-41],[8,-45],[61,-187],[4,-91],[-47,-92],[-140,-65],[-279,114],[-146,-40],[-28,-43],[-27,-105],[-29,-36],[-34,-4],[-44,11],[-56,-20]],[[2182,1271],[-15,53],[-150,70],[-64,55],[-44,72],[-30,118],[-30,31],[-66,1],[-87,-37],[-29,-5],[-42,7],[-82,23],[-3,1],[-44,5],[6,75],[-91,258],[-8,137],[90,226],[62,63],[3,48],[-39,32],[-63,18],[-91,0],[-51,-13],[-38,16],[-51,79],[-27,68],[-56,227],[-58,96],[-197,169],[-79,94],[-113,273]],[[695,3531],[67,26],[284,138],[116,91],[29,11],[20,-5],[24,-15],[33,-11],[84,7],[36,13],[28,23],[22,24],[39,31],[62,12],[37,-10],[19,-27],[-1,-30],[-71,-199],[-11,-42],[-12,-88],[0,-83],[9,-67],[12,-54],[14,-47],[32,-74],[6,-11],[14,-15],[105,-77],[88,-85],[30,-8],[23,3],[37,21],[28,11],[20,15],[12,16],[16,39],[9,15],[11,14],[171,174],[11,18],[11,25],[7,25],[7,15],[16,14],[21,15],[23,19],[17,19],[11,21],[9,22],[19,77],[9,2],[12,-11],[19,-26],[39,-22],[53,-5],[88,10],[51,-7],[37,-21],[58,-95],[25,-33],[28,-29],[41,-27],[39,0],[46,19],[40,30],[69,3],[60,16],[34,78],[80,46],[67,-62],[94,-54],[87,23],[67,78],[13,62],[-20,102],[73,-8],[74,-94],[47,-70],[27,70],[13,117],[47,63],[73,-47],[67,-78],[107,0],[101,0],[107,-70],[107,-32],[60,-15],[7,109],[86,51]],[[4629,3614],[167,-8],[94,-8],[114,-15],[107,-63],[60,-78],[60,-54],[60,-47],[41,54],[40,47],[-34,94],[-80,124]],[[5258,3660],[80,47],[80,8],[61,16],[0,70],[20,78],[100,39],[80,-39],[74,23],[100,63],[107,70],[94,31],[107,16],[40,54],[117,90]],[[6318,4226],[60,-113],[-1,-102],[-25,-118],[-8,-90],[24,-145],[1,-59],[-18,-56],[-30,-30],[-48,-25],[-141,-58],[-43,-31],[-41,-39],[-51,-77],[-34,-32],[-25,-21],[-23,-9],[-19,-10],[-22,-15],[-90,-76],[-144,-97],[-18,-5],[-11,-7],[-15,18],[-67,121],[-46,45],[-148,33],[-127,-64],[-21,-15]],[[5561,2325],[-70,-133],[-64,-65],[-39,-29],[-50,-76],[-29,-28],[-40,-18],[-120,-18],[-76,-31],[-86,-53],[-63,-72],[-9,-89],[45,-65],[60,-28],[48,-43],[11,-109],[-29,-103],[-54,-65],[-349,-278],[-32,-57],[9,-38],[48,-84],[13,-47],[-5,-39],[-70,-223],[-14,-29],[-33,-44],[-32,-28],[-36,-19],[-34,-25],[-26,-45],[-24,-96],[3,-78],[10,-76],[-1,-94],[-99,44],[-485,42],[-86,28],[-82,42],[-39,31],[-32,33],[-35,27],[-48,17],[-36,-6],[-78,-39],[-41,-7],[-173,23],[-143,45],[-123,79],[-222,255],[-251,193],[-25,40],[-7,43],[-2,43],[-14,43],[-99,141],[-107,107],[-14,47]],[[5187,3149],[32,-109],[27,-82],[-12,-37],[-19,-18],[-79,-6],[-125,-67],[-33,-34],[-19,-36],[-19,-82],[-5,-39],[0,-36],[6,-31],[15,-32],[31,-30],[45,-21],[356,-44],[31,1],[25,5],[25,-12],[19,-12],[71,-100],[2,-2]],[[7501,9095],[-4,-11],[-37,-283],[4,-103],[9,-27],[9,-16],[10,-13],[10,-10],[9,-13],[2,-16],[-4,-22],[-26,-36],[-20,-19],[-112,-52]],[[7351,8474],[-110,-30],[-27,-14],[-21,-19],[-48,-63],[-111,-176],[-22,-49],[-16,-51],[-23,-153],[-11,-39],[-21,-38],[-26,-33],[-35,-31],[-37,-25],[-46,-22],[-50,-17],[-50,-11],[-45,-5],[-47,2],[-42,8],[-183,72],[-34,3],[-30,-4],[-20,-15],[-16,-26],[-52,-107],[-13,-21],[-19,-14],[-32,5],[-33,29],[-37,80],[-11,60],[0,55],[88,312],[3,46],[-7,52],[-19,66],[-10,53],[-3,56],[-8,51],[-19,51],[-55,30],[-33,8],[-67,1],[-44,7],[-35,14],[-33,27],[-39,50],[-38,37],[-59,31],[-44,15],[-47,25],[-145,105],[-54,20],[-49,2],[-35,-9],[-33,-3],[-105,22],[-24,22],[-19,42],[-18,105],[-1,167],[-19,67],[-85,56],[-31,24]],[[4996,9377],[51,5],[83,-37],[95,-11],[95,13],[83,33],[79,74],[111,196],[80,82],[84,36],[572,39],[82,21],[233,163],[62,8],[77,-59],[55,-68],[37,-79],[26,-94],[29,-196],[44,-68],[182,-5],[60,-52],[120,-231],[34,-27],[128,-24],[3,-1]],[[7351,8474],[-20,-96],[-34,-72],[-15,-65],[-31,-469],[-29,-99],[-31,-71],[-61,-71],[-340,-320],[-44,-33],[-39,-16],[-80,-20],[-46,-27],[-46,-43],[-63,-75],[-32,-61],[-15,-59],[-3,-89],[-13,-84]],[[6409,6704],[3,-52],[7,-17],[18,-32],[116,-157],[28,-27],[47,-36],[16,-23],[5,-17],[-13,-17],[-24,-18],[-30,-11],[-123,-11],[-45,4],[-59,22],[-203,115],[-135,52],[-115,26],[-41,3],[-46,18],[-40,39],[-102,211],[-112,326],[-48,98],[-14,48],[-2,47],[2,43],[-3,33],[-16,26],[-27,17],[-30,9],[-65,12],[-40,2],[-42,-7],[-46,-14],[-44,-43]],[[2598,7508],[-15,88],[27,33],[29,-33],[20,8],[38,20],[26,5],[-36,47],[-51,54],[-19,44],[65,19],[52,23],[50,55],[79,118],[16,6],[19,-7],[17,-1],[7,19],[0,36],[5,14],[9,16],[9,25],[4,39],[1,66],[33,39],[65,22],[44,37],[-30,81],[468,-33],[109,51],[39,72],[26,76],[33,63],[60,35],[77,-10],[49,-46],[38,-50],[46,-24],[112,0],[36,11],[5,28],[-1,37],[18,38],[155,212],[182,200],[13,28],[114,171],[54,70],[41,59],[38,-15],[222,23]],[[9339,5414],[-2,0],[-68,12],[-18,-3],[-18,-9],[-97,-76],[-26,-16],[-115,-35],[-40,-20],[-76,-74],[-21,-8],[-38,9],[-31,21],[-56,57],[-32,50],[-46,99],[-45,42],[-65,39],[-123,51],[-129,38],[-38,1],[-32,-8],[-62,-32],[-36,-10],[-41,-2],[-47,3],[-203,-10],[-55,4],[-187,60],[-65,9],[-183,-13],[-29,7],[-16,32],[-2,16],[1,18],[-9,14],[54,53],[34,55],[21,71],[8,98],[-10,198],[7,99],[-22,7],[-18,17],[-30,16],[-67,15],[-49,6],[-43,-1],[-25,-10],[-14,-17],[-13,-21],[-22,-13],[-27,13],[-61,84],[-33,25],[-59,22],[-36,22],[-15,36],[10,30],[41,76],[118,278],[15,64],[-1,50],[-19,47],[-20,33],[-23,19],[-29,-10],[-45,-31],[-236,-207],[-107,-62],[-164,-38]],[[7501,9095],[65,-25],[62,-39],[1,0],[57,-51],[52,-65],[165,-299],[226,-237],[60,4],[97,-28],[89,-43],[37,-48],[1,-94],[24,-61],[55,-38],[92,-25],[142,-66],[338,-367],[136,-42],[164,-4],[125,-54],[21,-275],[46,-58],[64,-50],[57,-64],[31,-82],[45,-168],[114,-159],[42,-104],[4,-29],[-4,-73],[4,-35],[18,-29],[55,-55],[13,-28],[-15,-70],[-76,-111],[-8,-75],[3,-69],[-19,-54],[-34,-42],[-47,-36],[-108,-48],[-123,-39],[-116,-52],[-86,-90],[-30,-101],[-1,-103]],[[8675,4338],[-5,-106],[-25,-127],[-2,-115],[60,-86],[51,-41],[12,-34],[-3,-42],[9,-68],[45,-92],[10,-47],[-26,-41],[-47,-24],[-109,-32],[-50,-23],[-322,-203],[-162,-102],[-478,-318],[-265,-110],[-271,-60],[-369,-142],[-139,1],[-200,78],[-68,8],[-67,-16],[-179,-108],[-101,-37],[-198,29],[-99,-16],[-82,-72],[-34,-67]],[[6318,4226],[69,27],[41,2],[54,-2],[83,-19],[115,-46],[79,-46],[76,-58],[29,-29],[22,-31],[12,-36],[3,-42],[-7,-92],[1,-39],[10,-30],[19,-26],[23,-18],[18,-9],[52,24],[30,32],[26,35],[30,29],[39,19],[39,-2],[39,-13],[39,-4],[41,22],[32,59],[-11,61],[-26,58],[7,2],[38,9],[85,1],[60,-13],[55,-24],[221,-184],[61,-27],[66,-7],[84,11],[66,21],[61,33],[64,72],[29,53],[17,53],[32,294],[23,37],[40,23],[85,-21],[138,-54],[84,-9],[62,16],[2,0]],[[9339,5414],[0,-19],[28,-115],[58,-74],[-67,-47],[-6,-75],[1,-79],[-47,-60],[-316,-99],[-119,-96],[-67,-120],[-52,-136],[-77,-143],[0,-13]],[[5258,3660],[-40,78],[-34,63],[-60,23],[-53,31],[-8,35],[-61,293],[-62,114],[104,128],[-101,109],[-87,12]],[[695,3531],[-339,820],[-1,0],[0,1],[-35,71],[-119,184],[-55,49],[-54,31],[-59,48],[-33,17],[51,119],[1,77],[31,45],[44,0],[38,-57],[28,36],[32,77],[22,21],[40,1],[13,-25],[10,-30],[34,-15],[19,14],[133,143],[22,15],[116,47],[120,13],[42,16],[73,41],[116,25],[87,37],[134,80],[19,21]]],transform:{scale:[.00024339966546654089,.00020882467466746085],translate:[27.002154989000132,-30.658799335999916]}},m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){
return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();