!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo={type:"Topology",objects:{vut:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:"Torba"},id:"VU.TR",arcs:[[[0]],[[1]],[[2]],[[3]],[[4]],[[5]],[[6]]]},{type:"MultiPolygon",properties:{name:"Sanma"},id:"VU.SN",arcs:[[[7]],[[8]],[[9]],[[10]]]},{type:"MultiPolygon",properties:{name:"Tafea"},id:"VU.TF",arcs:[[[11]],[[12]],[[13]]]},{type:"MultiPolygon",properties:{name:"Malampa"},id:"VU.ML",arcs:[[[14]],[[15]],[[16]],[[17]]]},{type:"MultiPolygon",properties:{name:"Penama"},id:"VU.PM",arcs:[[[18]],[[19]],[[20]]]},{type:"MultiPolygon",properties:{name:"Shefa"},id:"VU.SE",arcs:[[[21]],[[22]],[[23]],[[24]],[[25]],[[26]]]}]}},arcs:[[[2973,8499],[182,-41],[26,-9],[23,-14],[-20,-73],[-9,-12],[-16,-9],[-76,-59],[-42,-19],[-32,-8],[-33,-2],[-198,8],[-41,-7],[-43,1],[-36,7],[-30,8],[-21,4],[-13,8],[12,16],[30,28],[-18,76],[7,25],[20,12],[29,8],[71,27],[21,2],[26,-3],[50,1],[26,6],[45,18],[30,5],[30,-4]],[[3123,8912],[16,-3],[14,1],[9,-1],[3,-11],[-8,-6],[-18,1],[-22,1],[-16,-1],[-49,-17],[-53,-12],[-63,-7],[-76,-2],[11,-4],[9,-2],[-1,-5],[-19,-8],[35,-33],[-41,-24],[-62,-3],[-33,35],[-2,29],[-11,18],[-30,15],[-58,20],[-70,49],[-17,28],[5,38],[23,30],[40,23],[54,17],[128,26],[45,0],[42,-12],[53,-27],[85,-74],[37,-12],[-37,-22],[-22,-6],[19,-20],[24,-8],[28,-3],[28,-8]],[[3454,9146],[-59,-6],[-50,1],[-21,-15],[-25,-1],[-5,12],[108,67],[41,16],[56,7],[27,-8],[14,-20],[0,-23],[-11,-16],[-25,-6],[-50,-8]],[[2416,9361],[30,-6],[35,3],[36,12],[-7,-37],[-35,-14],[-55,0],[-67,3],[-31,21],[-24,22],[-1,22],[36,20],[77,10],[49,-13],[0,-19],[-65,-6],[22,-18]],[[498,9500],[-48,-44],[-42,55],[46,11],[44,-22]],[[326,9725],[-52,-9],[-51,6],[-23,8],[-16,8],[-12,10],[-10,13],[46,18],[62,11],[59,-2],[37,-19],[-4,-24],[-36,-20]],[[117,9831],[-16,-4],[-48,12],[-26,14],[-9,20],[2,30],[18,38],[2,15],[-7,11],[-16,7],[-14,8],[-3,17],[100,-13],[21,-6],[16,-15],[12,-31],[13,-12],[0,-9],[-15,-7],[-5,-9],[3,-10],[17,-13],[-17,-3],[-4,-6],[1,-8],[0,-11],[-6,-6],[-8,-5],[-11,-4]],[[2111,6276],[-83,-14],[-59,1],[-41,9],[-82,28],[-44,8],[-83,10],[-36,11],[-50,38],[10,37],[57,27],[95,12],[209,-12],[106,-16],[10,-25],[-35,-43],[21,-40],[5,-31]],[[2039,6478],[-43,-16],[-69,9],[-10,-5],[-9,-3],[-23,-2],[-43,3],[-31,5],[-68,21],[142,67],[56,11],[41,0],[7,-6],[-42,-5],[0,-9],[31,-4],[11,-7],[-4,-9],[-17,-9],[68,-17],[3,-24]],[[1859,7355],[-27,-6],[-25,1],[-23,8],[-66,33],[-13,15],[52,-7],[76,-27],[52,-4],[-26,-13]],[[866,7084],[76,-1],[132,7],[76,-1],[32,1],[18,11],[38,28],[24,11],[27,10],[21,12],[9,20],[17,153],[-6,18],[-16,22],[34,19],[53,15],[41,7],[222,-10],[0,-10],[-53,-4],[-26,-12],[-6,-18],[8,-58],[-11,-14],[-35,-18],[49,-2],[20,-17],[12,-47],[51,-69],[10,-26],[71,23],[37,4],[55,-9],[-14,-25],[53,-112],[-14,-23],[-25,-4],[-10,-5],[30,-21],[14,-3],[47,-8],[20,-7],[11,-8],[29,-31],[-7,-26],[-13,-23],[-18,-8],[-22,19],[-20,0],[20,-46],[53,-71],[69,-65],[71,-28],[25,-6],[6,-14],[-8,-16],[-12,-11],[-21,-8],[-17,1],[-22,4],[-33,3],[-111,0],[-42,-7],[-50,-18],[-112,-62],[-25,-8],[-25,-6],[-28,-3],[-33,-2],[-35,3],[-50,12],[-28,4],[-112,7],[-127,-2],[-113,-13],[-73,-31],[29,-13],[-24,-2],[-63,6],[-37,-11],[-14,-11],[-11,-13],[-21,-12],[-60,-14],[-66,3],[-64,16],[-52,24],[37,26],[1,35],[-31,30],[-103,21],[-42,20],[-66,48],[-32,42],[-19,11],[-78,35],[-13,13],[-9,17],[-43,26],[-10,14],[7,12],[27,28],[15,41],[40,57],[11,29],[-2,64],[-16,33],[-71,46],[-17,28],[-13,58],[-30,53],[2,19],[28,23],[-51,8],[-31,20],[-16,27],[-4,26],[-13,24],[-64,43],[-24,24],[-55,106],[4,22],[21,23],[11,34],[-3,33],[-19,20],[81,76],[-6,34],[11,17],[93,13],[18,11],[14,4],[33,-13],[13,-11],[28,-37],[9,-8],[25,-6],[16,-15],[8,-17],[3,-15],[11,-2],[69,-41],[26,-34],[18,-16],[60,-13],[121,-42],[-29,-14],[13,-32],[54,-68],[29,-64],[13,-70],[-4,-16],[15,-9],[20,-8],[10,-9],[40,-173],[34,-11]],[[9819,6],[-24,-6],[-28,12],[-38,8],[-75,8],[-90,19],[-41,14],[-5,33],[21,35],[36,23],[38,5],[75,6],[81,1],[57,-8],[59,-20],[47,-12],[37,-14],[30,-25],[-31,-35],[-20,-17],[-30,-14],[-33,-7],[-66,-6]],[[8657,842],[-28,-13],[-57,2],[-126,17],[-49,10],[-122,65],[-30,10],[-60,2],[-23,4],[-9,8],[-10,16],[-21,11],[-20,7],[-10,5],[-7,11],[-100,64],[-12,15],[-3,21],[3,35],[17,34],[33,15],[23,8],[0,18],[-13,32],[12,16],[14,12],[21,10],[32,9],[60,8],[114,1],[49,10],[48,-18],[2,-19],[-17,-23],[-11,-31],[40,-104],[26,-20],[62,-12],[74,-8],[60,-12],[96,-23],[46,-15],[62,-11],[-18,-17],[-105,-59],[-20,-61],[-23,-30]],[[7891,2112],[120,-6],[-1,2],[14,3],[21,3],[16,1],[12,-3],[-2,-12],[25,-7],[17,-9],[14,-11],[6,-10],[-35,-1],[-188,-23],[72,-39],[9,-13],[19,-3],[92,-21],[31,-10],[89,-21],[63,-25],[37,-32],[13,-41],[-26,-36],[-59,-17],[-62,-13],[-35,-24],[-60,20],[-262,64],[-35,4],[-36,-7],[-25,0],[-92,12],[-27,6],[-30,13],[-72,23],[-50,11],[-163,25],[-5,23],[48,62],[1,19],[-11,16],[-18,14],[-25,14],[-5,10],[19,100],[18,30],[40,27],[89,29],[117,15],[120,-1],[100,-24],[30,-22],[12,-28],[0,-50],[17,-25],[43,-12]],[[5416,5166],[-62,0],[-40,5],[-25,15],[-36,37],[86,8],[38,1],[36,-9],[35,-22],[0,-21],[-32,-14]],[[5100,5228],[-52,-14],[-47,0],[-21,13],[30,24],[28,52],[34,12],[79,3],[-27,-20],[-3,-49],[-21,-21]],[[5333,5473],[-101,-35],[-313,-6],[-41,-4],[-43,-10],[-42,-5],[-37,6],[-34,13],[-40,11],[-39,5],[-38,-5],[-78,25],[-40,3],[-43,-9],[-29,16],[-98,28],[-47,17],[-42,12],[-52,9],[-31,12],[16,20],[-40,16],[38,18],[73,16],[87,9],[43,14],[29,3],[132,0],[62,14],[110,51],[41,11],[14,9],[46,54],[26,6],[65,8],[58,-30],[47,-46],[58,-87],[12,-43],[56,-25],[212,-51],[21,-12],[4,-21],[-22,-17]],[[2675,5761],[59,-9],[46,-19],[30,-5],[10,6],[-4,12],[-16,25],[12,2],[28,-14],[42,-31],[-3,-12],[-22,-15],[3,-11],[67,-15],[16,-4],[18,0],[10,7],[8,4],[11,3],[12,5],[8,-16],[13,-16],[17,-14],[22,-12],[34,-10],[69,-9],[39,-9],[66,-28],[39,-25],[42,-10],[79,15],[28,-7],[10,-2],[0,-10],[-19,-20],[53,-27],[128,-47],[75,6],[41,-32],[25,-68],[-56,-47],[-5,-25],[45,-15],[3,26],[12,18],[21,13],[33,14],[30,0],[23,-17],[20,-22],[20,-14],[-70,-33],[-23,-5],[-38,-3],[-18,-10],[-15,-14],[-31,-15],[-19,-13],[2,-12],[-1,-11],[-34,-8],[-48,0],[-21,12],[-11,15],[-20,13],[-28,3],[-78,6],[-16,3],[-18,13],[-43,0],[-44,-4],[-27,1],[-56,11],[-66,-11],[-59,-19],[-34,-14],[20,-9],[7,-5],[11,-3],[26,-2],[-29,-18],[-35,-6],[-77,-5],[-63,-28],[-17,1],[-20,7],[-16,0],[-7,-18],[-19,0],[-24,14],[-81,13],[-36,32],[-40,15],[-36,20],[-7,30],[20,0],[12,-9],[19,-8],[23,-6],[27,-4],[-7,22],[-18,28],[-25,26],[-74,45],[-13,15],[-4,21],[2,25],[-2,8],[-14,9],[-19,4],[-19,3],[-9,2],[-9,30],[3,85],[16,18],[-32,67],[-2,8],[2,25],[-6,11],[-14,3],[-14,1],[-6,4],[-16,25],[-106,75],[-47,-13],[-15,-6],[-18,10],[-48,-26],[-136,-41],[-70,29],[-40,7],[-31,-7],[-20,14],[-8,14],[-3,14],[-10,15],[-23,14],[-26,10],[-22,11],[-10,18],[12,33],[89,77],[-12,92],[2,18],[15,11],[5,10],[11,8],[30,3],[91,30],[325,-58],[-4,-49],[177,-103],[-52,-48],[38,-7],[31,-10],[12,-13],[-20,-18],[26,-10],[26,-13],[27,-11]],[[5050,5929],[-67,-2],[-35,9],[-14,20],[-11,56],[-53,84],[-32,107],[-71,78],[-26,50],[-8,54],[14,58],[73,109],[16,111],[65,2],[19,-64],[9,-14],[20,-13],[18,-3],[15,-7],[6,-21],[-23,-86],[4,-23],[41,-44],[0,-42],[8,-20],[70,-89],[23,-72],[14,-20],[35,-32],[11,-25],[8,-55],[-8,-48],[-38,-37],[-83,-21]],[[3969,6644],[-68,-11],[-309,1],[-63,9],[-101,23],[-17,16],[28,24],[361,156],[64,10],[50,4],[144,25],[252,20],[76,-6],[2,-13],[-67,-30],[-39,-21],[-40,-29],[-30,-31],[-12,-29],[-10,-25],[-25,-19],[-196,-74]],[[4926,6799],[-18,-41],[-38,6],[-32,0],[-71,-6],[17,32],[-4,35],[-35,66],[20,48],[-1,149],[-28,31],[-72,215],[5,11],[8,7],[-1,8],[-21,12],[-8,14],[10,17],[23,14],[26,7],[37,-9],[31,-25],[23,-27],[9,-19],[4,-32],[36,-78],[2,-14],[-5,-29],[3,-14],[35,-46],[6,-16],[0,-48],[9,-12],[42,-23],[9,-12],[-9,-95],[16,-20],[-5,-26],[-20,-40],[-3,-40]],[[5361,3761],[14,-9],[52,15],[204,15],[48,-4],[42,-13],[7,-8],[10,-26],[12,-5],[25,-2],[17,-6],[30,-20],[24,-26],[16,-27],[27,-22],[56,-11],[18,-13],[23,-27],[19,-8],[30,-3],[30,2],[26,5],[15,5],[6,-23],[-89,-81],[-23,-35],[-22,-17],[-40,-7],[-76,-7],[-6,-4],[-8,-6],[-14,-7],[-22,-3],[-15,4],[-19,6],[-15,6],[-2,4],[-210,-23],[-23,-1],[-52,14],[-11,10],[-7,14],[-13,4],[-31,-18],[-3,11],[-12,14],[-4,12],[-13,-2],[-1,-1],[0,-2],[-6,-4],[-24,3],[-23,0],[-19,-5],[-15,-8],[19,48],[-19,0],[-30,-18],[-47,-9],[-106,-11],[0,9],[51,17],[66,9],[53,13],[13,29],[-24,-7],[-20,-1],[-13,7],[-10,29],[-13,5],[-19,1],[-24,4],[-47,12],[-39,2],[-37,-4],[-38,-10],[-21,-10],[-40,-30],[-20,-8],[-33,-4],[-20,6],[-8,14],[-42,12],[9,17],[73,69],[35,17],[76,25],[31,15],[118,79],[77,29],[90,7],[15,-8],[11,-15]],[[5010,3732],[-23,-15],[7,43],[92,39],[109,18],[58,-24],[-37,-6],[-66,-17],[-40,-5],[-69,-21],[-31,-12]],[[5480,3859],[-47,-7],[-65,19],[-34,32],[14,39],[33,7],[40,-15],[34,-26],[25,-49]],[[5395,4395],[-31,-1],[-30,7],[15,16],[35,16],[29,9],[115,26],[41,0],[68,-8],[-25,-9],[-76,-19],[-16,-9],[-11,-9],[-16,-8],[-98,-11]],[[6067,4612],[-33,-8],[-48,1],[-31,7],[-8,11],[15,19],[-3,30],[56,21],[76,3],[55,-25],[-79,-59]],[[4927,5116],[14,-7],[8,-7],[9,-3],[21,-12],[52,-52],[29,-12],[6,-10],[7,-21],[13,-23],[25,-13],[23,1],[49,14],[29,4],[26,-2],[23,-6],[20,-6],[13,-5],[31,-21],[20,-22],[26,-17],[54,-7],[9,-7],[13,-16],[28,-17],[53,-7],[157,2],[67,-10],[50,-31],[-39,-8],[7,-19],[17,-22],[-6,-18],[-31,7],[-29,-7],[-157,41],[-48,23],[-49,11],[-57,8],[-45,3],[-123,-12],[-88,-23],[-86,-16],[-121,13],[-37,11],[-88,38],[-16,13],[6,39],[-6,14],[-47,18],[-24,13],[1,12],[12,16],[6,52],[13,22],[19,10],[18,7],[16,9],[6,17],[-1,14],[-5,9],[-14,21],[29,-9],[18,1],[18,5],[27,3],[29,-3]]],transform:{scale:[.0003378755844584569,.0007188951317131781],translate:[166.52051842500012,-20.253106377999913]}},m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();