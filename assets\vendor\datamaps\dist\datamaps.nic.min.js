!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo={type:"Topology",objects:{nic:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Rivas"},id:"lake",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Rio San Juan"},id:"NI.",arcs:[[5,6,-2,7]]},{type:"MultiPolygon",properties:{name:"Atlántico Sur"},id:"NI.AS",arcs:[[[8]],[[9]],[[10]],[[11,-6,12,13,14,15]]]},{type:"Polygon",properties:{name:"Carazo"},id:"NI.CA",arcs:[[16,-4,17,18,19]]},{type:"Polygon",properties:{name:"Granada"},id:"NI.GR",arcs:[[20,21,-5,-17,22,23]]},{type:"Polygon",properties:{name:"Jinotega"},id:"NI.JI",arcs:[[24,25,26,27,28,29]]},{type:"MultiPolygon",properties:{name:"Atlántico Norte"},id:"NI.AN",arcs:[[[30]],[[31]],[[32]],[[33]],[[34]],[[35]],[[36]],[[37]],[[38]],[[39]],[[40]],[[41]],[[42]],[[43]],[[44]],[[45]],[[46]],[[47]],[[48]],[[49]],[[50]],[[51]],[[52]],[[53]],[[54]],[[55]],[[56]],[[57]],[[58]],[[59]],[[60]],[[61]],[[62]],[[63]],[[64]],[[65]],[[66]],[[67]],[[68]],[[69]],[[70]],[[71]],[[72]],[[73]],[[74]],[[75]],[[76]],[[77]],[[78]],[[79]],[[80]],[[81]],[[82]],[[83]],[[84]],[[85]],[[86]],[[87]],[[88]],[[89]],[[90]],[[91]],[[92]],[[93]],[[94]],[[95]],[[96]],[[97]],[[98]],[[99]],[[100]],[[101]],[[102]],[[103]],[[104]],[[105]],[[106]],[[107]],[[108]],[[109]],[[110]],[[111]],[[112]],[[113]],[[114]],[[115]],[[116]],[[117]],[[118]],[[119]],[[120]],[[121]],[[122]],[[123]],[[-16,124,-30,125]]]},{type:"Polygon",properties:{name:"León"},id:"NI.LE",arcs:[[126,127,128,129,130]]},{type:"Polygon",properties:{name:"Managua"},id:"NI.MN",arcs:[[131,-24,132,-19,133,-128,134]]},{type:"Polygon",properties:{name:"Masaya"},id:"NI.MS",arcs:[[-23,-20,-133]]},{type:"Polygon",properties:{name:"Chinandega"},id:"NI.CI",arcs:[[135,136,-130,137]]},{type:"Polygon",properties:{name:"Estelí"},id:"NI.ES",arcs:[[-26,138,-131,-137,139]]},{type:"Polygon",properties:{name:"Madriz"},id:"NI.MD",arcs:[[-27,-140,-136,140,141]]},{type:"Polygon",properties:{name:"Matagalpa"},id:"NI.MT",arcs:[[-15,142,-135,-127,-139,-25,-125]]},{type:"Polygon",properties:{name:"Nueva Segovia"},id:"NI.NS",arcs:[[-28,-142,143]]},{type:"Polygon",properties:{name:"Boaco"},id:"NI.BO",arcs:[[144,-21,-132,-143,-14]]},{type:"Polygon",properties:{name:"Chontales"},id:"NI.CO",arcs:[[-8,-1,-22,-145,-13]]}]}},arcs:[[[4218,2240],[822,-114]],[[5040,2126],[-467,-1122],[79,-85],[175,-75],[96,-47],[262,-113],[167,-66],[110,-76]],[[5462,542],[-45,-19],[-45,6],[-315,136],[-280,122],[-70,30],[-264,114],[-197,86],[-63,41],[-55,58],[-56,34],[-65,-32],[-55,-87],[-35,-90],[-48,-90],[-1,1],[-97,47],[-38,7],[-27,18],[-16,41],[-18,83],[-36,62],[-208,273],[-17,15],[-9,5],[-30,24],[-6,10],[-68,22],[-30,34],[-66,93],[-52,31],[-66,82],[-85,57],[-29,30],[-7,18],[-17,63]],[[2946,1867],[57,121],[12,28],[6,9],[68,53]],[[3089,2078],[46,-14],[15,3],[7,8],[54,32],[7,6],[22,40],[30,77],[6,6],[5,4],[5,1],[3,0],[10,-3],[9,-6],[7,-10],[19,-23],[10,-3],[86,26],[64,21],[724,-3]],[[6004,2661],[39,-159],[29,-63],[16,-16],[13,-15],[4,-15],[-2,-27],[3,-61],[21,-58],[19,-73],[-1,-19],[-3,-26],[-19,-52],[-28,-112],[-5,-45],[-1,-30],[14,-89],[8,-90],[129,-56],[57,-40],[155,-113],[30,-35],[12,-32],[1,-29],[4,-29],[15,-36],[17,-21],[18,-15],[92,-55],[77,-65],[55,-65],[17,-26],[40,-76],[10,-14],[8,-6],[6,0],[7,-2],[7,-4],[13,-8],[5,-3],[11,-3],[15,-7],[5,-1],[5,0],[20,3],[12,0],[6,0],[12,-4],[32,-15],[24,-9],[62,9],[13,-1],[6,-4],[-1,-13],[-1,-7],[1,-6],[3,-5],[21,-26],[19,-19],[11,-6],[8,-1],[16,11],[13,3],[4,2],[9,5],[4,3],[9,7],[8,3],[14,3],[12,-2],[19,-8],[9,-2],[7,1],[4,2],[5,2],[5,3],[4,3],[15,7],[19,3],[10,3],[27,15],[6,-1],[3,-4],[0,-8],[-3,-43],[0,-7],[3,-12],[2,-6],[6,-9],[6,-8],[3,-5],[2,-6],[1,-7],[1,-15],[1,-9],[3,-9],[9,-12],[6,-4],[6,0],[5,2],[4,3],[4,3],[14,0],[22,-2],[77,-17],[15,-6],[4,-3],[7,-8],[35,-54],[29,-55],[50,-64],[34,-30]],[[7738,534],[15,-50],[35,8],[-8,25],[28,5],[42,-10],[32,-20],[-14,2],[-12,-1],[-12,3],[-14,-12],[2,-11],[-1,-12],[2,-99],[18,-82],[-6,-63],[-68,-42],[-128,-36],[-10,-2],[-130,-58],[-40,-55],[-7,-5],[-53,13],[-13,-5],[-27,-23],[-19,-4],[-19,3],[-13,8],[-117,108],[-9,40],[-22,17],[-26,-5],[-23,-27],[-33,9],[-49,-36],[-23,27],[-14,0],[-22,-20],[-23,10],[-36,41],[-23,-4],[-17,3],[-14,2],[-31,1],[-21,-19],[-11,16],[-43,40],[-4,8],[-8,10],[-4,13],[9,18],[8,6],[27,17],[-7,56],[-34,33],[-37,22],[-17,23],[-21,16],[-91,41],[-20,22],[-6,17],[-26,38],[-8,17],[-4,28],[0,24],[-4,17],[-19,11],[-17,-11],[-104,-81],[-19,-9],[-21,3],[-21,9],[-18,12],[-16,17],[-45,64],[-8,7],[-9,6],[-10,5],[-11,3],[-97,51],[-45,16],[-42,-2],[-34,5],[-74,63],[-35,18],[-61,-17],[-144,-112],[-202,-156]],[[5040,2126],[36,71],[4,7],[8,9],[31,17],[39,32],[18,12],[12,6],[9,-5],[5,-1],[9,1],[37,16],[11,8],[7,6],[6,13],[9,13],[14,17],[3,5],[2,5],[3,5],[8,11],[4,6],[1,10],[0,2],[6,16],[29,52],[83,21],[49,38],[129,138],[6,11],[10,4],[10,1],[144,-2],[39,9],[9,0],[16,-6],[8,-1],[30,4],[6,-1],[8,-2],[9,-5],[22,-5],[75,-3]],[[7734,2710],[1,-31],[-13,19],[-15,63],[-4,84],[14,73],[40,32],[3,-1],[2,-1],[1,-5],[-1,-7],[-13,-24],[-13,-38],[-10,-42],[-4,-37],[12,-85]],[[9090,3385],[-57,-72],[-4,51],[19,41],[27,16],[22,-20],[-7,-16]],[[9176,3667],[-1,-25],[-9,12],[-15,8],[-5,9],[8,20],[9,6],[5,-18],[1,-8],[7,-4]],[[8106,5371],[42,-301],[-65,-611],[0,-135],[6,-22],[29,-67],[18,-73],[55,-89],[13,-58],[-5,-71],[-21,-37],[-39,-16],[-63,-2],[-48,-12],[-48,-20],[-46,-9],[-39,25],[15,16],[40,34],[12,15],[1,27],[-7,36],[-12,32],[-43,44],[3,152],[-9,58],[7,4],[3,1],[1,1],[4,8],[12,0],[69,-25],[48,72],[16,104],[-25,71],[0,17],[16,22],[7,31],[3,129],[4,5],[8,8],[9,12],[6,16],[-7,62],[-37,37],[-53,14],[-50,-4],[-40,-27],[-17,-43],[1,-47],[15,-41],[43,-27],[4,-5],[0,-8],[4,-39],[2,-16],[9,-16],[9,-6],[7,-10],[3,-23],[-1,-39],[-4,-16],[-9,-18],[7,-11],[21,-36],[-62,-42],[-103,-101],[-63,-31],[-84,-11],[-36,-13],[-14,-31],[0,-121],[2,-2],[8,5],[16,-1],[24,-6],[15,0],[14,-4],[15,-22],[14,-103],[-8,-50],[-14,-47],[-6,-44],[14,-42],[36,-21],[61,16],[11,-36],[-2,-23],[-10,-45],[-3,-27],[4,-14],[9,21],[31,119],[12,29],[18,13],[38,-7],[8,-17],[-91,-213],[-43,-275],[14,-297],[15,-49],[-13,-10],[-15,-6],[8,47],[-14,40],[-48,71],[-21,58],[17,25],[28,23],[16,53],[-10,40],[-26,45],[-34,39],[-37,20],[-59,-7],[3,-35],[31,-21],[25,31],[15,0],[32,-40],[14,-24],[6,-30],[-8,-40],[-29,-50],[-3,-37],[-11,-44],[-35,-2],[-81,29],[-10,-8],[23,-17],[48,-23],[12,-18],[8,-24],[4,-29],[2,-31],[-4,-21],[-18,-42],[-4,-17],[4,-13],[6,-13],[5,-15],[-3,-15],[-10,-14],[-11,-7],[-10,-4],[-7,-7],[-36,-54],[-12,-9],[-16,-7],[-19,-17],[-17,-21],[-8,-20],[25,-20],[32,-33],[26,-36],[17,-44],[14,-12],[19,-8],[20,-3],[29,3],[2,11],[-8,15],[-3,18],[13,45],[11,24],[17,9],[33,1],[18,-19],[91,-423],[5,-81],[-1,-30],[-3,-18],[-7,-14],[-16,-17],[-4,6],[-27,-6],[-27,-11],[-3,-5],[-15,-8],[-19,-33],[-18,-8],[-24,0],[-19,-4],[-16,-9],[-16,-17],[-20,-72],[-18,-108],[-27,-82],[-41,7],[-12,0],[-62,-92],[-18,-36],[-10,-39],[0,-235],[60,-303],[42,-121],[58,-106],[19,-57],[47,-88],[16,-18],[36,-26],[14,-44]],[[6004,2661],[-10,39],[-14,30],[-2,7],[-2,10],[16,16],[76,37],[-250,139],[-13,24],[-124,364],[-26,138],[-2,77],[12,52],[-6,35],[-69,142],[-12,15],[-14,14],[-13,17],[-14,28],[-11,37],[-8,75],[3,40],[5,28],[11,31],[3,14],[-4,16],[-13,24],[-26,29],[-40,72],[-43,111],[-11,22],[-57,60],[-47,80]],[[5299,4484],[-10,12],[-22,13],[-2,8],[-1,10],[5,36],[-2,43],[-9,29],[-16,37],[-12,16],[-12,12],[-17,11],[-16,29],[-9,4],[-58,51],[-36,23],[-36,-1],[-38,-40],[-10,-27],[-7,-65],[-8,-34],[-6,1],[-16,-12],[-14,-14],[1,-7],[-7,-4],[-26,-29],[20,-37],[2,-35],[-16,-18],[-32,12]],[[4889,4508],[-51,126],[-15,79],[-8,26],[-19,44],[-2,22],[3,22],[7,19],[14,15],[17,10],[28,3],[22,-4],[23,-1],[19,8],[22,23],[7,22],[0,37],[6,11],[20,3],[17,-4],[34,-12],[22,-3],[23,1],[27,6],[54,19],[19,2],[116,-23],[25,-9],[8,10],[24,19],[12,12],[20,35],[14,39],[39,-9],[45,35],[40,46],[29,23],[23,2],[37,12],[27,1],[25,-7],[11,-10],[11,-1],[20,18],[11,5],[9,-2],[5,2],[4,35],[8,23],[2,8],[2,33],[13,78],[8,19],[41,53],[1,1],[13,12],[8,6],[1,1],[-5,1],[-5,0],[-6,-2],[-5,-2],[-21,-15],[-6,-2],[-6,-1],[-6,0],[-6,1],[-5,2],[-5,3],[-4,4],[-6,8],[-6,9],[-28,68],[-12,45],[-3,6],[-15,22],[-8,8],[-4,3],[-5,2],[-5,2],[-5,2],[-4,3],[-14,15],[-5,3],[-4,2],[6,16],[7,12],[84,131]],[[5723,5795],[504,71],[272,2],[39,6],[24,10],[24,30],[20,16],[15,5],[12,1],[15,-2],[11,-3],[12,-7],[13,-10],[23,-8],[19,1],[118,38],[26,4],[16,-4],[5,-12],[37,-137],[8,-17],[11,-14],[13,-3],[16,-10],[48,17],[30,-7],[-6,10],[-10,23],[-10,13],[36,-8],[31,-13],[23,5],[16,49],[13,-11],[9,-1],[18,12],[13,0],[18,-13],[7,10],[-2,24],[-11,26],[21,-4],[8,-6],[29,-3],[158,-7],[38,-7],[20,-10],[15,-10],[20,-10],[34,-11],[15,-9],[7,-10],[-1,-13],[-4,-14],[-7,-14],[-37,-52],[-7,-13],[-4,-14],[-2,-13],[2,-12],[6,-10],[8,-9],[147,-125],[192,-130],[35,-14],[80,5],[98,-7],[36,5]],[[3146,2646],[-9,-24],[-40,-85],[-11,-58],[7,-57],[8,-39],[1,-27],[-5,-21],[-23,-37],[-19,-43],[-8,-72],[3,-33],[5,-21],[34,-51]],[[2946,1867],[-2,6],[-60,32],[-73,82],[-179,92],[-31,27],[-26,35],[-22,73],[-233,208],[-12,17]],[[2308,2439],[0,1],[49,55],[20,14],[15,1],[12,1],[22,7],[11,5],[7,4],[25,27],[20,35],[53,46],[14,16],[38,72],[122,116],[23,41]],[[2739,2880],[21,-30],[179,-84],[57,-31],[25,-42],[5,-5],[104,-36],[16,-6]],[[3577,3440],[13,-45],[3,-35],[3,-18],[10,-14],[17,-14],[31,-15],[53,-53],[104,-171],[146,12]],[[3957,3087],[261,-847]],[[3146,2646],[38,97],[18,47],[7,18],[70,181],[79,202],[-6,36],[-65,86]],[[3287,3313],[164,85],[61,52],[16,7],[15,0],[34,-17]],[[4250,6154],[-108,-82],[-38,-17],[-46,-1],[-41,-32],[-27,-36],[-29,-53],[-34,-68],[-142,-194],[-73,-74],[-21,-14],[-91,-91],[-110,-121],[-43,-35],[-20,-8],[-24,-4],[-71,4],[-23,-5],[-10,-5],[-26,-37],[-67,60],[-30,35],[-28,20],[-18,9],[-26,-1],[-10,0]],[[3094,5404],[-31,43],[-85,59],[-147,171],[-14,24],[-13,32],[23,44],[6,39],[-5,29],[28,96],[-18,100],[1,49],[6,39],[11,35],[16,35]],[[2872,6199],[72,89],[32,33],[31,6],[27,2],[24,-12],[19,25],[76,70],[18,26]],[[3171,6438],[19,26],[22,11],[21,-30],[19,10],[102,10],[33,-3],[22,-14],[16,-18],[19,-11],[26,4],[13,12],[33,54],[44,37],[35,21],[22,30],[-3,138],[6,57],[17,54],[57,112],[8,28],[6,31],[3,109],[12,38],[19,27],[47,33]],[[3789,7204],[1,-1],[4,5],[14,14],[16,37],[4,36],[-11,27],[-29,10],[-8,21],[8,44],[20,70],[2,51],[10,12],[20,-5],[34,-3],[25,14],[40,39],[16,-5],[12,0],[0,11],[3,2],[5,0],[6,2],[-6,25],[68,24],[19,23],[18,32],[43,16],[17,4],[32,6],[36,14],[19,32],[4,37],[11,31],[70,22],[34,22],[50,46],[23,32],[21,45],[16,44],[14,59],[19,37],[23,34],[18,21],[9,1],[21,-3],[8,2],[5,7],[6,17],[14,19],[8,16],[10,13],[19,5],[46,27],[20,5],[74,0],[48,10],[49,18],[40,29],[23,38],[-2,53],[-25,46],[-35,36],[-31,24],[53,48],[2,14],[-4,36],[2,14],[45,56],[7,15],[4,117],[8,64],[16,33],[35,-10],[31,-40],[34,-29],[41,23],[69,80],[12,32]],[[5192,9007],[9,-1],[15,-4],[5,-4],[6,-5],[5,-9],[4,-10],[3,-14],[1,-15],[-11,-34],[-3,-13],[1,-14],[-1,-11],[-4,-24],[-1,-13],[1,-20],[-1,-22],[1,-7],[3,-5],[26,-24],[29,-18],[9,-10],[7,-14],[3,-18],[-3,-23],[-22,-43],[-3,-15],[2,-50],[-2,-12],[-15,-31],[-4,-16],[1,-15],[5,-17],[37,-55],[33,-35],[28,-39],[10,-8],[14,-4],[174,-13],[5,-32],[-3,-56],[-43,-265],[-6,-108],[18,-84],[-4,-28],[-18,-44],[-118,-231],[-248,-459],[-10,-12],[-9,-4],[-186,99],[-7,-2],[-238,-172],[-4,-1],[-157,-25],[-10,-13],[-9,-21],[-17,-55],[-16,-28],[-14,-19],[-20,-15],[-3,-14],[1,-21],[16,-39],[13,-26],[10,-31],[3,-22],[-21,-72],[-1,-42],[1,-12],[-2,-21],[-7,-18],[-21,-21],[-106,-79],[-61,-63],[-11,-30],[-1,-82]],[[9630,7932],[-5,-3],[3,5],[2,-2]],[[9575,7947],[0,-2],[1,1],[1,-4],[-3,-2],[-1,2],[-2,3],[4,2]],[[9599,7952],[0,-4],[-2,2],[2,2]],[[9534,7974],[0,-3],[-6,2],[3,5],[3,-4]],[[9643,7981],[-1,-4],[-5,3],[0,5],[6,-4]],[[9641,7993],[0,-4],[-6,1],[6,3]],[[9520,7996],[0,-2],[1,1],[1,-4],[-3,-2],[0,2],[-1,0],[-2,3],[4,2]],[[9520,8004],[0,-3],[-2,4],[2,-1]],[[9513,8008],[0,-2],[1,0],[1,-4],[-3,-1],[-1,1],[-2,4],[4,2]],[[9484,8031],[-1,-2],[-2,6],[3,-4]],[[9486,8047],[0,-2],[1,0],[1,-4],[-3,-1],[0,1],[-1,0],[-2,4],[4,2]],[[9999,8159],[-7,-7],[-2,10],[9,-3]],[[9143,8174],[-3,-1],[1,4],[2,-3]],[[9141,8196],[0,-8],[-3,1],[3,7]],[[9241,8211],[0,-2],[-5,2],[5,0]],[[9254,8221],[-3,-1],[3,7],[0,-6]],[[9308,8256],[-6,0],[4,8],[2,-8]],[[9193,8267],[1,-1],[1,-4],[-2,-1],[-1,1],[-2,4],[3,1]],[[9993,8268],[-3,0],[1,4],[2,-4]],[[9980,8273],[-2,0],[0,2],[2,2],[2,-2],[-2,-2]],[[9399,8277],[-3,0],[3,4],[0,-4]],[[9184,8281],[-2,0],[-2,3],[3,1],[1,-4]],[[9395,8284],[0,-3],[-2,2],[0,3],[2,-2]],[[9417,8289],[-5,-2],[-1,2],[6,0]],[[9248,8310],[-1,-6],[-3,3],[4,3]],[[9237,8306],[-5,-2],[1,8],[4,-6]],[[9249,8315],[0,-2],[-4,2],[4,0]],[[9237,8325],[-1,-2],[-3,1],[2,5],[2,-4]],[[9232,8336],[-1,-4],[-3,0],[0,2],[4,2]],[[9038,8340],[0,-2],[1,0],[1,-4],[-3,-1],[-1,1],[-2,3],[4,3]],[[9070,8348],[-4,-7],[-2,2],[6,5]],[[9053,8352],[0,-1],[1,0],[0,-4],[-2,-1],[-1,1],[-2,3],[4,2]],[[9038,8354],[-2,-5],[-2,3],[4,2]],[[9061,8348],[-2,0],[-5,5],[6,1],[1,-6]],[[9198,8349],[-3,0],[7,6],[-4,-6]],[[9046,8349],[-2,0],[-1,9],[3,-9]],[[9069,8354],[-2,0],[-2,3],[4,2],[0,-5]],[[9078,8359],[-1,-5],[-4,1],[1,7],[4,-3]],[[9062,8361],[-3,-2],[-1,3],[3,6],[1,-7]],[[9939,8370],[-19,-34],[-5,17],[10,15],[14,2]],[[9020,8369],[-4,-1],[1,6],[3,-5]],[[9013,8370],[-4,-2],[-1,4],[0,4],[6,5],[1,-5],[-2,-6]],[[9912,8392],[6,-23],[-19,6],[13,17]],[[9112,8395],[-2,0],[1,4],[1,-4]],[[9121,8396],[-3,-1],[0,6],[3,-5]],[[9113,8406],[-6,-2],[0,5],[6,-3]],[[9065,8503],[-3,-2],[1,7],[2,-5]],[[9337,8516],[-4,-4],[-1,10],[5,-6]],[[9346,8528],[1,-1],[1,-4],[-2,-1],[-1,1],[-2,3],[3,2]],[[9338,8534],[-4,-5],[-1,8],[5,-3]],[[9043,8567],[-3,0],[1,4],[2,-4]],[[9037,8579],[0,-2],[1,1],[0,-4],[-2,-2],[-1,1],[-2,4],[4,2]],[[9660,8467],[-36,-18],[-103,10],[-9,1],[8,0],[1,0],[4,1],[3,6],[-1,9],[31,20],[13,26],[7,27],[14,21],[32,19],[26,0],[17,-19],[6,-38],[-13,-65]],[[9407,8584],[-20,-8],[0,26],[20,-18]],[[9084,8608],[-2,-1],[1,4],[1,-3]],[[9707,8632],[-2,-3],[-1,5],[3,-2]],[[9685,8632],[-3,0],[0,3],[3,2],[0,-5]],[[9741,8645],[-2,0],[-1,1],[2,3],[1,-4]],[[9717,8655],[-2,-3],[-4,4],[6,-1]],[[9740,8656],[-2,0],[2,3],[0,-3]],[[9737,8661],[0,-5],[-3,4],[3,1]],[[9703,8663],[0,-2],[-2,3],[2,-1]],[[9711,8667],[0,-2],[1,1],[1,-4],[-3,-2],[-1,2],[0,-1],[-2,4],[4,2]],[[9381,8665],[-3,-1],[-2,4],[6,-1],[-1,-2]],[[9697,8667],[-2,-2],[-2,2],[1,6],[4,-1],[-1,-5]],[[9376,8674],[0,-5],[-4,5],[4,0]],[[9354,8687],[0,-1],[1,0],[0,-4],[-2,-1],[-1,1],[-2,3],[4,2]],[[9402,8696],[-6,-4],[-1,3],[7,1]],[[9329,8695],[-4,-4],[-1,7],[5,-3]],[[9362,8707],[-4,-3],[-1,1],[2,3],[3,-1]],[[9484,8733],[-1,0],[-2,6],[5,-1],[-2,-5]],[[9478,8740],[0,-1],[1,0],[0,-4],[-2,-1],[-1,1],[-2,3],[4,2]],[[9464,8751],[1,-2],[1,-4],[-2,-1],[-1,1],[-1,0],[-2,3],[4,3]],[[9490,8747],[-3,0],[1,5],[2,-5]],[[9482,8754],[-2,-3],[-2,8],[4,-5]],[[9097,8776],[1,-1],[1,-4],[-2,-1],[-1,1],[-2,3],[3,2]],[[9956,8889],[-4,0],[2,5],[0,1],[2,-6]],[[9212,8895],[0,-2],[1,1],[0,-4],[-2,-2],[-1,2],[0,-1],[-2,4],[4,2]],[[9953,8901],[0,-2],[1,1],[0,-4],[-1,-1],[-1,-1],[-1,1],[-2,4],[4,2]],[[9965,8898],[-1,-2],[-5,6],[6,-4]],[[9978,8898],[-6,-2],[0,8],[6,-6]],[[9814,8913],[11,-45],[-7,8],[-12,19],[8,18]],[[9660,8916],[0,-1],[1,0],[1,-4],[-3,-1],[-1,1],[-2,4],[4,1]],[[9994,8927],[0,-2],[1,0],[1,-4],[-3,-1],[-1,1],[-2,3],[4,3]],[[9207,8931],[0,-2],[1,1],[0,-4],[-2,-2],[-1,2],[0,-1],[-2,4],[4,2]],[[9961,8931],[0,-2],[1,1],[1,-4],[-3,-2],[0,2],[-1,-1],[-2,4],[4,2]],[[9990,8932],[-3,0],[0,7],[3,-7]],[[9739,8969],[-2,-5],[-3,0],[1,4],[4,1]],[[9788,9e3],[-2,0],[1,5],[3,-1],[-2,-4]],[[9580,9059],[1,-28],[-13,16],[12,12]],[[9846,9062],[0,-1],[1,0],[1,-4],[-3,-1],[-1,1],[-2,3],[4,2]],[[9784,9093],[0,-2],[1,0],[1,-4],[-3,-1],[-1,1],[-2,4],[4,2]],[[9780,9110],[-2,-2],[-10,7],[7,2],[5,-7]],[[9738,9480],[0,-4],[-4,2],[2,6],[2,-4]],[[5723,5795],[-36,62],[-195,288],[-11,23],[-7,8],[-8,3],[-177,-192],[-18,-8],[-30,-3],[-129,3],[-65,-7],[-50,-34],[-25,-42],[-16,-41],[-23,-81],[-24,-58],[-17,-23],[-19,-17],[-56,-30],[-51,-14],[-20,3],[-60,29],[-14,23],[-24,3],[-3,5],[-3,21],[-3,7],[-7,6],[-28,15],[-14,12],[-8,3],[-7,0],[-10,-4],[-12,-1],[-6,0],[-6,1],[-5,2],[-4,4],[-56,60],[-11,10],[-9,6],[-26,12],[-39,44],[-100,155],[-41,106]],[[5192,9007],[3,7],[-51,0],[12,18],[43,47],[-56,45],[-10,23],[30,10],[11,5],[6,14],[10,86],[17,36],[27,29],[4,3],[31,23],[34,29],[90,100],[32,21],[116,25],[52,2],[50,-17],[43,-37],[29,-55],[-6,-6],[-15,-23],[-1,-5],[38,-5],[6,-28],[-2,-31],[40,-31],[20,-39],[29,-79],[36,25],[38,-5],[75,-37],[44,-11],[143,-5],[-8,-17],[-6,-45],[30,11],[21,-14],[20,-20],[30,-10],[22,7],[50,33],[73,29],[36,51],[27,59],[25,44],[19,-23],[14,-60],[15,-12],[30,-3],[47,-13],[22,-1],[40,24],[10,51],[-10,109],[11,27],[24,2],[27,-11],[18,-11],[15,-15],[46,-57],[24,-11],[28,2],[48,16],[11,8],[8,11],[10,10],[18,4],[16,-3],[10,-6],[8,-1],[13,10],[0,17],[-17,20],[-13,21],[-23,54],[21,21],[15,1],[44,-33],[5,-10],[8,-9],[20,-3],[33,1],[10,-6],[4,-18],[33,-4],[193,11],[-10,19],[0,22],[10,53],[24,-15],[31,-39],[25,-8],[-2,10],[-10,22],[14,-7],[8,-3],[7,-5],[11,-17],[24,25],[34,22],[39,14],[36,1],[-15,12],[-10,11],[-14,27],[109,8],[41,18],[-4,36],[24,2],[20,-3],[15,-11],[7,-20],[15,0],[-19,67],[8,26],[36,20],[39,6],[43,-3],[36,-12],[16,-25],[15,0],[8,29],[4,31],[10,25],[48,21],[48,44],[28,10],[-3,8],[-6,16],[-4,8],[29,-10],[11,-6],[-6,31],[3,27],[10,10],[20,-21],[1,35],[3,15],[10,15],[-13,22],[7,12],[19,3],[26,-5],[10,-12],[24,-42],[18,-11],[46,6],[39,27],[31,39],[18,40],[25,-22],[25,-12],[20,-14],[10,-31],[30,22],[33,3],[86,-11],[-23,-13],[-2,-12],[15,-12],[23,-10],[9,1],[86,-1],[51,27],[28,6],[65,-5],[26,5],[-14,-23],[-36,-20],[-18,-21],[-28,39],[-24,-27],[-18,-49],[-15,-27],[-25,-14],[-24,-33],[-17,-39],[-7,-32],[-5,-11],[-25,-28],[-10,-17],[-5,-22],[-4,-50],[-4,-22],[-16,-29],[-24,-21],[-67,-31],[5,14],[5,11],[17,23],[0,-18],[6,13],[4,11],[2,11],[0,16],[-15,-11],[-7,-2],[-17,13],[0,14],[23,13],[23,3],[19,-8],[15,-22],[-2,43],[-20,60],[-6,47],[-12,46],[-28,8],[-31,-14],[-23,-16],[-39,-42],[-86,-128],[-21,-55],[19,23],[7,10],[27,-24],[21,-95],[13,-22],[26,-17],[28,-32],[29,-11],[30,43],[-14,14],[-36,26],[-16,8],[22,14],[22,-8],[21,-23],[16,-31],[-2,53],[15,16],[17,-20],[76,-405],[4,-13],[19,-44],[16,-110],[94,-318],[11,-91],[-10,-79],[-53,-146],[-162,-302],[-70,-98],[-10,-38],[-13,-29],[-93,-75],[-39,-64],[-28,-74],[-46,-215],[-76,-292],[-46,-264],[3,-114],[-8,-55],[0,-44],[-41,-116],[-35,-153],[-21,-298],[3,-55],[14,-33],[-14,-14],[11,-28],[65,-459]],[[2773,4945],[-6,-58],[-23,-229],[-19,-205]],[[2725,4453],[-78,-55],[-27,-31],[-17,-25],[-5,-14],[-2,-11],[0,-9],[-3,-23],[0,-9],[1,-7],[-1,-10],[-2,-15],[-12,-41],[-2,-10],[1,-7],[4,-12],[2,-5],[18,-31],[1,-1],[11,-12],[3,-4],[1,-6],[1,-15],[1,-7],[2,-9],[-3,-7],[-8,-11],[-189,-218],[-131,-153],[-27,-44],[-3,-37],[0,-164],[2,-15],[6,-14],[8,-8],[10,-7],[12,-17],[3,-41],[-25,-65],[-38,-51],[-74,-65],[-10,-7],[-6,0],[-28,7],[-28,12],[-3,1],[-5,0],[-6,-1],[-5,-2],[-29,-12],[-14,-3],[-7,-4],[-5,-4],[-3,-5],[-2,-5],[0,-7],[1,-7],[3,-12],[13,-32],[9,-13],[3,-5],[2,-8],[-1,-6],[0,-6],[-9,-17],[-7,-9],[-48,-41]],[[1980,2981],[-38,54],[-73,149],[-69,218],[-19,35],[-49,49],[-399,284],[-25,36],[-110,74],[-59,74],[-135,71],[-5,53],[22,-34],[39,-30],[44,-21],[43,-9],[-45,57]],[[1102,4041],[43,30],[96,12],[34,18],[33,22],[18,14],[101,122],[40,74],[15,46],[7,15],[38,42],[50,62],[48,60],[38,20],[37,53],[27,61],[14,16],[22,13],[140,22],[28,8],[15,7],[35,25],[24,65],[6,73],[-18,280],[-24,64],[-27,50],[-41,111],[-19,78]],[[1882,5504],[34,16],[19,2],[26,-1],[31,-12],[37,-21],[23,2],[40,10],[129,50],[109,27],[23,-8],[15,-8],[36,-42],[51,-99],[20,-31],[10,-20],[13,-58],[-4,-72],[-43,-89],[-2,-27],[0,-19],[20,-41],[65,-38],[239,-80]],[[3342,4189],[-12,-41],[8,-78],[9,-33],[13,-26],[25,-39],[49,-62],[7,-13],[2,-14],[-4,-46],[0,-39],[3,-16],[6,-12],[171,-188],[4,-8],[-3,-13],[-32,-57],[-11,-64]],[[3287,3313],[-35,10],[-11,7],[-3,1],[-6,1],[-33,-5],[-67,-32],[-57,-11],[-35,-31],[-119,-137],[7,-29],[28,-34],[10,-17],[6,-14],[-3,-52],[-22,-41],[-11,-15],[-12,-13],[-15,-10],[-18,-6],[-16,-2],[-23,8],[-14,7],[-53,48],[-46,-66]],[[2308,2439],[-26,38],[-52,137],[-21,21],[-20,12],[-51,61],[-31,51],[-33,73],[-6,34],[-10,32],[-22,27],[-48,44],[-8,12]],[[2725,4453],[331,-85],[35,-13],[29,-31],[29,-39],[22,-20],[19,-15],[120,-54],[32,-7]],[[1859,5917],[2,-4],[37,-9],[11,-10],[6,-19],[2,-18],[-1,-14],[-3,-12],[-2,-6],[-18,-66]],[[1893,5759],[-20,-21],[-2,-3],[-5,-21],[-6,-72],[22,-138]],[[1102,4041],[-4,5],[-25,23],[-27,9],[-16,16],[-44,97],[-12,0],[19,-86],[-31,32],[-51,75],[-38,42],[-24,12],[-122,110],[-16,19],[-9,24],[-10,40],[-6,-12],[-15,-22],[-6,-12],[-215,289],[-25,61],[9,-11],[8,-7],[23,-14],[0,14],[-9,4],[-17,14],[0,14],[-5,24],[-33,31],[-44,28],[-39,12],[0,-14],[51,-40],[27,-29],[1,-26],[-19,3],[-29,26],[-44,48],[-228,160],[-48,49],[-29,57],[0,68],[37,80],[55,56],[56,41],[41,52],[10,88],[12,0],[-5,-49],[-7,-14],[22,0],[26,-4],[22,-8],[10,-11],[9,-7],[50,-49],[12,-8],[35,-55],[9,-8],[25,-17],[6,-7],[2,-15],[-4,-35],[2,-14],[64,-74],[83,-21],[201,14],[0,17],[-102,-3],[-51,7],[-22,21],[20,37],[94,55],[13,10],[447,33],[29,-4],[45,-32],[23,-1],[11,37],[40,24],[50,36],[22,28],[46,83],[11,10],[29,17],[10,11],[5,23],[-8,9],[-11,5],[-5,15],[17,122],[-3,5],[-15,38],[-4,13],[4,6],[14,65],[14,32],[16,22],[93,94],[26,20],[31,11],[34,-3],[112,-65],[18,-6],[19,5],[14,11]],[[3094,5404],[-74,-116],[-12,-60],[-5,-12],[-9,-13],[-16,-13],[-25,-10],[-50,-13],[-23,-9],[-43,-29],[-15,-17],[-7,-9],[-31,-75],[-11,-83]],[[1893,5759],[164,192],[37,60],[16,40],[80,163],[78,78],[52,10],[20,-8],[29,-16],[83,-46],[83,16],[149,56],[109,6],[37,-14],[7,-15],[20,-51],[15,-31]],[[1859,5917],[14,11],[27,29],[15,30],[4,36],[-12,66],[-4,14],[-14,30],[-12,13],[-14,4],[-11,7],[-8,23],[3,33],[25,64],[3,36],[-63,278],[-1,39],[12,74],[-1,38],[-7,14],[-17,15]],[[1798,6771],[16,15],[15,23],[6,8],[9,7],[43,0],[136,-30],[39,-66],[9,-20],[17,-22],[21,-17],[33,-15],[22,-15],[25,-25],[30,18],[20,22],[44,13],[19,3],[140,-3],[109,-41],[63,1],[81,12],[23,-3],[30,-11],[38,-27],[59,-30],[18,25],[12,66],[6,15],[5,10],[43,20],[38,15],[68,-64],[15,-19],[19,-27],[43,-89],[59,-82]],[[4889,4508],[1,-25],[-2,-22],[-5,-18],[-8,-15],[-13,0],[-10,10],[-18,14],[-17,8],[-8,-8],[-6,-23],[-13,4],[-61,79],[-22,4],[-33,-25],[-13,0],[11,58],[2,7],[-14,12],[-31,3],[-37,23],[-64,26],[-13,8],[-7,22],[-17,23],[-19,18],[-17,7],[-12,3],[-15,-13],[-3,-5],[-3,-9],[-4,-10],[-6,-10],[-4,-3],[-9,-7],[-9,-5],[-15,-5],[-5,-3],[-3,-4],[-2,-5],[-11,-5],[-20,-2],[-125,8],[-6,-2],[-5,-2],[-4,-4],[-3,-5],[-25,-58],[-6,-10],[-8,-4],[-11,-2],[-38,0],[-9,-2],[-18,-10],[-39,-9],[-24,-5],[-23,-23],[-8,-5],[-6,1],[-11,7],[-31,6],[-26,9],[-339,-83],[-31,-18],[-35,-84],[-93,-61],[-10,-9],[-58,-61]],[[1798,6771],[-8,7],[-8,12],[-4,21],[6,124],[2,40],[4,32],[4,14],[9,10],[22,18],[44,26],[41,4],[85,-13],[159,36],[77,5],[65,-41],[35,-12],[28,11],[26,16],[29,5],[30,-37],[12,-10],[18,-7],[14,-2],[101,8],[35,10],[30,16],[22,30],[12,35],[15,69],[31,56],[127,159],[68,122],[20,15],[23,11],[24,14],[23,27],[14,30],[7,22],[10,20],[22,23],[27,16],[149,26],[0,-26],[-21,-49],[5,-62],[138,-115],[42,-65],[13,-14],[28,-11],[18,4],[17,9],[27,2],[43,-22],[23,-40],[19,-48],[31,-43],[43,-18],[45,2],[40,-4],[26,-34],[4,-1]],[[5299,4484],[-24,-58],[-75,-127],[-7,-19],[-5,-18],[-3,-26],[-1,-85],[-5,-26],[-11,-26],[-32,-48],[-21,-25],[-29,-25],[-57,-33],[-28,-6],[-12,0],[-20,3],[-21,-6],[-29,-14],[-114,-91],[-24,-15],[-79,-32],[-106,-14],[-140,-29],[-83,5],[-57,1],[-48,9],[-22,1],[-28,-10],[-73,-42],[-50,-43],[-10,-10],[-15,-25],[-4,-12],[-3,-14],[1,-57],[4,-42],[0,-15],[-4,-16],[-39,-105],[-3,-4],[-2,-4],[-3,-5],[-18,-34],[-9,-22],[-33,-233]]],
transform:{scale:[.0005127547259725954,.0004317920195019543],translate:[-87.6858210929999,10.713481547000114]}},m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();