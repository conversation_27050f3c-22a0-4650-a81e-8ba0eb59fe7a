<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>

  <link rel="stylesheet" href="../assets/vendor/apexcharts/dist/apexcharts.css">
  <style type="text/css">
    .apexcharts-tooltip.apexcharts-theme-light
    {
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  </style>
</head>

<body class="bg-gray-50 dark:bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="lg:ms-65 lg:hidden fixed top-0 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 bg-white border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
    <div class="flex justify-between xl:grid xl:grid-cols-3 basis-full items-center w-full py-2.5 px-2 sm:px-5">
      <!-- Logo & Sidebar Toggle -->
      <div class="flex items-center gap-x-3">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
          <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->

        <div class="lg:hidden">
          <!-- Sidebar Toggle -->
          <button type="button" class="w-7 h-9.5 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13" />
            </svg>
          </button>
          <!-- End Sidebar Toggle -->
        </div>

        <div class="lg:hidden">
          <!-- Templates Dropdown -->
          <div class="hs-dropdown  relative  [--scope:window] [--auto-close:inside] inline-flex">
            <button id="hs-dropdown-preview-navbar" type="button" class="hs-dropdown-toggle  group relative flex justify-center items-center size-8 text-xs rounded-md text-gray-800 hover:bg-gray-100 focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <span class="">
                <svg class=" size-4 shrink-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </span>

              <span class="absolute -top-0.5 -end-0.5">
                <span class="relative flex">
                  <span class="animate-ping absolute inline-flex size-full rounded-full bg-red-400 dark:bg-red-600 opacity-75"></span>
                  <span class="relative inline-flex size-2 bg-red-500 rounded-full"></span>
                  <span class="sr-only">Notification</span>
                </span>
              </span>
            </button>

            <!-- Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-full min-w-90 md:w-125 transition-[opacity,margin] duration opacity-0 hidden z-61 overflow-hidden border border-gray-200 bg-white rounded-xl shadow-xl dark:bg-neutral-800 dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-preview-navbar">
              <!-- Tab -->
              <div class="p-3 pb-0 flex flex-wrap justify-between items-center gap-3 border-b border-gray-200 dark:border-neutral-700">
                <!-- Nav Tab -->
                <nav class="flex gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pmn-item-pro" aria-selected="true" data-hs-tab="#hs-pmn-pro" aria-controls="hs-pmn-pro" role="tab">
                    Pro
                  </button>
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pmn-item-free" aria-selected="false" data-hs-tab="#hs-pmn-free" aria-controls="hs-pmn-free" role="tab">
                    Free
                  </button>
                </nav>
                <!-- End Nav Tab -->

                <!-- Switch/Toggle -->
                <div class="mb-2 flex items-center gap-x-0.5">
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:hidden flex shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="dark">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                    </svg>
                    Switch to Dark
                  </button>
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:flex hidden shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="light">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="4"></circle>
                      <path d="M12 2v2"></path>
                      <path d="M12 20v2"></path>
                      <path d="m4.93 4.93 1.41 1.41"></path>
                      <path d="m17.66 17.66 1.41 1.41"></path>
                      <path d="M2 12h2"></path>
                      <path d="M20 12h2"></path>
                      <path d="m6.34 17.66-1.41 1.41"></path>
                      <path d="m19.07 4.93-1.41 1.41"></path>
                    </svg>
                    Switch to Light
                  </button>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <!-- End Tab -->

              <!-- Tab Content -->
              <div id="hs-pmn-pro" class="" role="tabpanel" aria-labelledby="hs-pmn-item-pro">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (21)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../pro/pricing.html">
                      Purchase
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/dashboard/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Dashboard
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img55.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img55.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img58.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img58.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/video-call/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img61.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img61.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Video Call
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/startup/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img32.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img32.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Startup
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/payment/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img8.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img8.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Payment
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img16.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img16.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Chat
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img21.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img21.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ecommerce/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        E-Commerce
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img46.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img46.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/crm/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img11.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img11.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CRM
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/workspace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img18.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img18.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Workspace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 bg-gray-100 dark:bg-neutral-700 transition" href="../../pro/analytics/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img9.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img9.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Analytics
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/calendars/day.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img14.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img14.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Calendars
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/smart-home/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img35.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img35.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Smart Home
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img52.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img52.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/inbox/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img26.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img26.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Inbox
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/project/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img10.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img10.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Project
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop-marketplace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img29.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img29.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop Marketplace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img49.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img49.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/files/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img12.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img12.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Files
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                      Examples (780<!-- (780) -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                      Templates (21)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->

              <!-- Tab Content -->
              <div id="hs-pmn-free" class="hidden" role="tabpanel" aria-labelledby="hs-pmn-item-free">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (5)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../templates.html">
                      Free download
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img5.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img5.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img2.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img2.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img3.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img3.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../examples.html">
                      Examples (220+<!-- 222 -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../templates.html">
                      Templates (5)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->
            </div>
            <!-- End Dropdown -->
          </div>
          <!-- End Templates Dropdown -->
        </div>
      </div>
      <!-- End Logo & Sidebar Toggle -->

      <div class="lg:hidden h-9.5 flex justify-end items-center gap-x-2">
        <!-- Account Dropdown -->
        <div class="hs-dropdown inline-flex   [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
          <button id="hs-pro-dnad" type="button" class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
            <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
          </button>

          <!-- Account Dropdown -->
          <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnad">
            <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
              <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                <div class="grow">
                  <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                    James Collison
                  </span>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Preline@HS
                  </p>
                </div>
              </a>
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
                Billing
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                Settings
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                  <circle cx="12" cy="7" r="4" />
                </svg>
                My account
              </a>
            </div>
            <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
              <!-- Switch/Toggle -->
              <div class="flex flex-wrap justify-between items-center gap-2">
                <label for="hs-pro-dnaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                <label for="hs-pro-dnaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                  <input data-hs-theme-switch type="checkbox" id="hs-pro-dnaddm" class="peer sr-only">
                  <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                  <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                </label>
              </div>
              <!-- End Switch/Toggle -->
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Customization
                <div class="ms-auto">
                  <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                    New
                  </span>
                </div>
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Manage team
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Sign out
              </a>
            </div>
            <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
              <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M8 12h8" />
                  <path d="M12 8v8" />
                </svg>
                Add team account
              </button>
            </div>
          </div>
          <!-- End Account Dropdown -->
        </div>
        <!-- End Account Dropdown -->
      </div>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN SIDEBAR ========== -->
  <aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
    hs-overlay-open:translate-x-0
    -translate-x-full transition-all duration-300 transform
    w-82 h-full
    hidden
    fixed inset-y-0 start-0 z-60
    bg-white
    lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
    dark:bg-neutral-800" tabindex="-1" aria-label="Sidebar">
    <div class="h-full flex">
      <!-- Nav Sidebar -->
      <div class="w-16 flex flex-col h-full max-h-full">
        <div class="p-4 flex flex-col items-center">
          <!-- Logo -->
          <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
            <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            </svg>
          </a>
          <!-- End Logo -->
        </div>

        <!-- Content -->
        <div class="h-full px-4 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <!-- Nav -->
          <ul class="flex flex-col items-center space-y-1">
            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                  <polyline points="9 22 9 12 15 12 15 22" />
                </svg>
                <span class="sr-only">Dashboard</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Dashboard
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
                <span class="sr-only">Users</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Users
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 9 5 12 1.8-5.2L21 14Z" />
                  <path d="M7.2 2.2 8 5.1" />
                  <path d="m5.1 8-2.9-.8" />
                  <path d="M14 4.1 12 6" />
                  <path d="m6 12-1.9 2" />
                </svg>
                <span class="sr-only">Events</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Events
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2" />
                  <rect width="18" height="18" x="3" y="4" rx="2" />
                  <circle cx="12" cy="10" r="2" />
                  <line x1="8" x2="8" y1="2" y2="4" />
                  <line x1="16" x2="16" y1="2" y2="4" />
                </svg>
                <span class="sr-only">Attributes</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Attributes
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4 text-indigo-600 dark:text-indigo-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="m16 12-4-4-4 4" />
                  <path d="M12 16V8" />
                </svg>
                <span class="sr-only">Upgrade</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Upgrade
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 22v-5" />
                  <path d="M9 8V2" />
                  <path d="M15 8V2" />
                  <path d="M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z" />
                </svg>
                <span class="sr-only">Integrations</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Integrations
              </span>
            </li>
            <!-- End Item -->
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Content -->

        <footer class="hidden lg:block text-center border-t border-gray-200 dark:border-neutral-700">
          <!-- Account Dropdown -->
          <div class="hs-dropdown  inline-flex  [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
            <button id="hs-pro-dsad" type="button" class="w-full flex items-center gap-x-3 text-start py-4 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
            </button>

            <!-- Account Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dsad">
              <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
                <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                  <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                  <div class="grow">
                    <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                      James Collison
                    </span>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      Preline@HS
                    </p>
                  </div>
                </a>
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <line x1="2" x2="22" y1="10" y2="10" />
                  </svg>
                  Billing
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  Settings
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                  My account
                </a>
              </div>
              <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
                <!-- Switch/Toggle -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                  <label for="hs-pro-dsaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                  <label for="hs-pro-dsaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                    <input data-hs-theme-switch type="checkbox" id="hs-pro-dsaddm" class="peer sr-only">
                    <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                    <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                  </label>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Customization
                  <div class="ms-auto">
                    <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                      New
                    </span>
                  </div>
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Manage team
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Sign out
                </a>
              </div>
              <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
                <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                  <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M8 12h8" />
                    <path d="M12 8v8" />
                  </svg>
                  Add team account
                </button>
              </div>
            </div>
            <!-- End Account Dropdown -->
          </div>
          <!-- End Account Dropdown -->
        </footer>

        <!-- Sidebar Close -->
        <div class="lg:hidden absolute top-4 -end-6 z-10">
          <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-e-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="7 8 3 12 7 16" />
              <line x1="21" x2="11" y1="12" y2="12" />
              <line x1="21" x2="11" y1="6" y2="6" />
              <line x1="21" x2="11" y1="18" y2="18" />
            </svg>
          </button>
        </div>
        <!-- End Sidebar Close -->
      </div>
      <!-- End Nav Sidebar -->

      <!-- Secondary Sidebar -->
      <div class="w-72 bg-white border-x border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
        <div class="flex flex-col h-full max-h-full">
          <!-- Quick Actions -->
          <div class="py-3 px-2 border-b border-gray-200 dark:border-neutral-700">
            <button type="button" class="py-1.5 ps-3 pe-1.5 w-full inline-flex items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700/50">
              Quick actions
              <span class="ms-auto flex items-center gap-x-1 py-px px-1.5 border border-gray-200 rounded-md dark:border-neutral-700">
                <svg class="shrink-0 size-2.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
                </svg>
                <span class="text-[11px] uppercase">k</span>
              </span>
            </button>
          </div>
          <!-- End Quick Actions -->

          <!-- Content -->
          <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <!-- Nav -->
            <nav class="hs-accordion-group size-full flex flex-col justify-between" data-hs-accordion-always-open>
              <!-- Body -->
              <div>
                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/index.html">
                      Overview
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/visitors.html">
                      Visitors
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 bg-gray-100 focus:bg-gray-200 dark:bg-neutral-700 dark:focus:bg-neutral-600" href="../../pro/analytics/survey.html">
                      Survey
                      <div class="ms-auto">
                        <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-md text-[10px] leading-4 font-medium bg-white border border-gray-200 text-gray-800 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                          Beta
                        </span>
                      </div>
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/incidents.html">
                      Incidents
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/empty-states.html">
                      Empty Contents
                    </a>
                  </li>
                  <!-- End Link -->
                </ul>

                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li class="hs-accordion active" id="projects-accordion">
                    <button type="button" class="hs-accordion-toggle py-1 px-3 flex justify-center items-center gap-x-1 text-xs text-gray-500 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-500 dark:focus:bg-neutral-700" aria-expanded="true" aria-controls="projects-accordion-sub">
                      <svg class="hs-accordion-active:rotate-90 shrink-0 size-3 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                      Channels
                    </button>

                    <div id="projects-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="projects-accordion">
                      <ul class="hs-accordion-group mt-1 flex flex-col" data-hs-accordion-always-open>
                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/index.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />
                                <path d="m3.3 7 8.7 5 8.7-5" />
                                <path d="M12 22V12" />
                              </svg>
                            </span>
                            PRO
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="18" height="7" x="3" y="3" rx="1" />
                                <rect width="9" height="7" x="3" y="14" rx="1" />
                                <rect width="5" height="7" x="16" y="14" rx="1" />
                              </svg>
                            </span>
                            Examples
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="4" width="20" height="16" rx="2" />
                                <path d="M10 4v4" />
                                <path d="M2 8h20" />
                                <path d="M6 4v4" />
                              </svg>
                            </span>
                            Templates
                          </a>
                        </li>
                        <!-- End Link -->
                      </ul>
                    </div>
                  </li>
                  <!-- End Link -->
                </ul>
              </div>
              <!-- End Body -->

              <!-- Footer -->
              <footer class="p-2 pb-4 flex flex-col gap-y-3">
                <!-- Credits Card -->
                <a class="p-3 group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="flex justify-between items-center gap-x-1">
                    <div class="grow">
                      <p class="font-medium text-sm text-gray-800 dark:text-neutral-200">
                        65/100 credits used
                      </p>
                      <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                        Invite team members
                      </p>
                      <p class="mt-2 flex items-center gap-1 text-xs text-indigo-600 dark:text-indigo-500">
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
                        </svg>
                        Upgrade now
                        <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                          <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                        </svg>
                      </p>
                    </div>

                    <!-- Circular Progress -->
                    <div class="relative size-14">
                      <svg class="size-full -rotate-90" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-100 dark:text-indigo-500/40" stroke-width="4"></circle>
                        <!-- Progress Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-600 dark:text-indigo-500" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="35" stroke-linecap="round"></circle>
                      </svg>
                    </div>
                    <!-- End Circular Progress -->
                  </div>
                </a>
                <!-- End Credits Card -->

                <!-- News Card -->
                <a class="group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 transition duration-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="p-2 w-full h-32 rounded-md">
                    <img class="size-full object-cover rounded-md dark:hidden" src="../assets/img/demo-previews/img32.webp" alt="Card Image">
                    <img class="size-full object-cover rounded-md dark:block hidden" src="../assets/img/demo-previews-dark/img32.webp" alt="Card Image">
                  </div>

                  <div class="p-3">
                    <div class="flex items-center gap-2">
                      <div class="grow">
                        <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                          New
                        </p>
                        <p class="text-wrap font-medium text-sm text-gray-800 group-hover:text-indigo-600 group-focus:text-indigo-600 dark:text-neutral-200 dark:group-hover:text-white dark:group-focus:text-white">
                          Preline Startup Demo
                        </p>
                      </div>

                      <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </a>
                <!-- End News Card -->
              </footer>
              <!-- End Footer -->
            </nav>
            <!-- End Nav -->
          </div>
          <!-- End Content -->
        </div>
      </div>
      <!-- End Secondary Sidebar -->
    </div>
  </aside>
  <!-- ========== END MAIN SIDEBAR ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" class="lg:ps-82 pt-15 lg:pt-0">
    <div class="pt-3.5 pb-10 px-2 sm:px-5 flex flex-col gap-y-5">
      <!-- Go Back -->
      <div class="mt-0.5 flex gap-6">
        <div class="relative flex last:before:hidden before:absolute before:top-1 before:-end-3 before:inline-block before:w-px before:h-3 before:bg-gray-400 dark:before:bg-neutral-600">
          <div>
            <a class="inline-flex items-center gap-x-1 text-start whitespace-nowrap text-sm text-gray-600 rounded-full underline-offset-2 hover:underline hover:decoration-2 focus:outline-hidden focus:underline focus:decoration-2 dark:text-neutral-400" href="#">
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
              Back
            </a>
          </div>
        </div>

        <div>
          <h1 class="text-base/5 font-semibold text-gray-800 dark:text-neutral-200">
            2025 Web Development Trends & Tools Usage Survey
          </h1>
        </div>
      </div>
      <!-- End Go Back -->

      <!-- Card -->
      <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
        <!-- Header -->
        <div class="pb-5 mb-5 border-b border-gray-200 dark:border-neutral-700">
          <div class="flex flex-wrap justify-between items-center gap-3">
            <div class="size-10 bg-gray-200 rounded-lg dark:bg-neutral-700">
              <img class="size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1708443683276-8a3eb30faef2?q=80&w=160&h=160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Category Image">
            </div>

            <div class="grow">
              <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                Cho Muncho
              </h2>

              <!-- List -->
              <ul class="flex flex-wrap items-center whitespace-nowrap gap-1.5">
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Survey
                  </p>
                </li>
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Launched&nbsp;on Mar 25, 2025
                  </p>
                </li>
              </ul>
              <!-- End List -->
            </div>

            <!-- Button Group -->
            <div class="flex flex-wrap items-center gap-2">
              <button type="button" class="py-1.5 px-2.5 inline-flex items-center gap-x-1.5 text-[13px] whitespace-nowrap rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                Preview survey
              </button>

              <button type="button" class="py-1.5 px-2.5 inline-flex items-center gap-x-1.5 text-[13px] whitespace-nowrap rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                  <path d="m15 5 4 4" />
                </svg>
                Edit survey
              </button>

              <!-- Select -->
              <div class="relative inline-block">
                <select id="hs-pro-select-status-with-icons" data-hs-select='{
                    "placeholder": "Filter",
                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 ps-2.5 pe-8 inline-flex shrink-0 justify-center items-center gap-x-1.5 bg-indigo-600 border border-transparent text-[13px] text-white rounded-lg hover:bg-indigo-700 focus:outline-hidden focus:bg-indigo-700 before:absolute before:inset-0 before:z-1 dark:bg-indigo-500 dark:hover:bg-indigo-600 dark:focus:bg-indigo-600",
                    "dropdownScope": "window",
                    "dropdownPlacement": "bottom-right",
                    "dropdownAutoPlacement": true,
                    
                    "dropdownClasses": "mt-2 p-1 space-y-0.5 z-50 w-40 bg-white rounded-xl shadow-xl dark:bg-neutral-950",
                    "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 flex gap-x-3 py-1.5 px-2 text-[13px] text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                    "optionTemplate": "<div class=\"flex items-center w-full\"><div class=\"me-2\" data-icon></div><span data-title></span><span class=\"hidden hs-selected:block ms-auto\"><svg class=\"shrink-0 size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                    "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m6 9 6 6 6-6\"/></svg></div>"
                  }' class="hidden">
                  <option value="">Choose</option>
                  <option selected data-hs-select-option='{
                    "icon": "<svg class=\"shrink-0 size-3 sm:size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M21 12a9 9 0 1 1-6.219-8.56\"/></svg>"}'>
                    In Progress
                  </option>
                  <option data-hs-select-option='{
                    "icon": "<svg class=\"shrink-0 size-3 sm:size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect x=\"14\" y=\"4\" width=\"4\" height=\"16\" rx=\"1\"/><rect x=\"6\" y=\"4\" width=\"4\" height=\"16\" rx=\"1\"/></svg>"}'>
                    Pause
                  </option>
                  <option data-hs-select-option='{
                    "icon": "<svg class=\"shrink-0 size-3 sm:size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M21.801 10A10 10 0 1 1 17 3.335\"/><path d=\"m9 11 3 3L22 4\"/></svg>"}'>
                    Complete
                  </option>
                  <option data-hs-select-option='{
                    "icon": "<svg class=\"shrink-0 size-3 sm:size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"5\" x=\"2\" y=\"3\" rx=\"1\"/><path d=\"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8\"/><path d=\"M10 12h4\"/></svg>"}'>
                    Archive
                  </option>
                </select>
              </div>
              <!-- End Select -->
            </div>
            <!-- End Button Group -->
          </div>
        </div>
        <!-- End Header -->

        <!-- Body -->
        <div>
          <!-- Button Group -->
          <div class="mb-3 md:mb-0 flex flex-wrap justify-end items-center gap-y-2 gap-x-4">
            <div class="inline-flex items-center relative pe-4 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:w-px after:h-3 after:bg-gray-300 after:-translate-y-1/2 dark:after:bg-neutral-600">
              <!-- Button -->
              <button type="button" class="inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 3h6v6" />
                  <path d="M10 14 21 3" />
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                </svg>
                Share survey link
              </button>
              <!-- End Button -->
            </div>

            <div class="inline-flex items-center relative pe-4 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:w-px after:h-3 after:bg-gray-300 after:-translate-y-1/2 dark:after:bg-neutral-600">
              <!-- Button -->
              <button type="button" class="inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                  <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                  <path d="M12 18v-6" />
                  <path d="m9 15 3 3 3-3" />
                </svg>
                Download CSV
              </button>
              <!-- End Button -->
            </div>

            <div class="inline-flex items-center relative pe-4 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:w-px after:h-3 after:bg-gray-300 after:-translate-y-1/2 dark:after:bg-neutral-600">
              <!-- Calendar Dropdown -->
              <div class="hs-dropdown [--auto-close:inside] [--placement:top-right] inline-flex">
                <button id="hs-pro-dnic" type="button" class="inline-flex items-center gap-x-1.5 text-start whitespace-nowrap text-[13px] text-gray-600 rounded-full underline-offset-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-400" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                  <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                    <line x1="16" x2="16" y1="2" y2="6" />
                    <line x1="8" x2="8" y1="2" y2="6" />
                    <line x1="3" x2="21" y1="10" y2="10" />
                    <path d="M8 14h.01" />
                    <path d="M12 14h.01" />
                    <path d="M16 14h.01" />
                    <path d="M8 18h.01" />
                    <path d="M12 18h.01" />
                    <path d="M16 18h.01" />
                  </svg>
                  Duration: Last 30 days
                  <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m6 9 6 6 6-6" />
                  </svg>
                </button>

                <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-79.5 sm:w-159 transition-[opacity,margin] duration opacity-0 hidden z-50 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnic">
                  <!-- Calendar -->
                  <div class="sm:flex">
                    <!-- Calendar -->
                    <div class="p-3 space-y-0.5">
                      <!-- Months -->
                      <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                        <!-- Prev Button -->
                        <div class="col-span-1">
                          <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="m15 18-6-6 6-6" />
                            </svg>
                          </button>
                        </div>
                        <!-- End Prev Button -->

                        <!-- Month / Year -->
                        <div class="col-span-3 flex justify-center items-center gap-x-1">
                          <div class="relative">
                            <select data-hs-select='{
                                "placeholder": "Select month",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                                "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                              }' class="hidden">
                              <option value="0">January</option>
                              <option value="1">February</option>
                              <option value="2">March</option>
                              <option value="3">April</option>
                              <option value="4">May</option>
                              <option value="5">June</option>
                              <option value="6" selected>July</option>
                              <option value="7">August</option>
                              <option value="8">September</option>
                              <option value="9">October</option>
                              <option value="10">November</option>
                              <option value="11">December</option>
                            </select>
                          </div>

                          <span class="text-gray-800 dark:text-neutral-200">/</span>

                          <div class="relative">
                            <select data-hs-select='{
                                "placeholder": "Select year",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                                "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                              }' class="hidden">
                              <option selected>2023</option>
                              <option>2024</option>
                              <option>2025</option>
                              <option>2026</option>
                              <option>2027</option>
                            </select>
                          </div>
                        </div>
                        <!-- End Month / Year -->

                        <!-- Next Button -->
                        <div class="col-span-1 flex justify-end">
                          <button type="button" class="opacity-0 pointer-events-none size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                            <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="m9 18 6-6-6-6" />
                            </svg>
                          </button>
                        </div>
                        <!-- End Next Button -->
                      </div>
                      <!-- Months -->

                      <!-- Weeks -->
                      <div class="flex pb-1.5">
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Mo
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Tu
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          We
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Th
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Fr
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Sa
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Su
                        </span>
                      </div>
                      <!-- Weeks -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                            26
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                            27
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                            28
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                            29
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                            30
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            1
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            2
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            3
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            4
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            5
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            6
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            7
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            8
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            9
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            10
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            11
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            12
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            13
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            14
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            15
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            16
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            17
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            18
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            19
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            20
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            21
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            22
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            23
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            24
                          </button>
                        </div>
                        <div class="bg-gray-100 rounded-s-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                            25
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            26
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            27
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            28
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            29
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            30
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            31
                          </button>
                        </div>
                        <div class="bg-linear-to-r from-gray-100 dark:from-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            1
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            2
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            3
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            4
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            5
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            6
                          </button>
                        </div>
                      </div>
                      <!-- Days -->
                    </div>
                    <!-- End Calendar -->

                    <!-- Calendar -->
                    <div class="p-3 space-y-0.5">
                      <!-- Months -->
                      <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                        <!-- Prev Button -->
                        <div class="col-span-1">
                          <button type="button" class="opacity-0 pointer-events-none size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="m15 18-6-6 6-6" />
                            </svg>
                          </button>
                        </div>
                        <!-- End Prev Button -->

                        <!-- Month / Year -->
                        <div class="col-span-3 flex justify-center items-center gap-x-1">
                          <div class="relative">
                            <select data-hs-select='{
                                "placeholder": "Select month",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                                "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                              }' class="hidden">
                              <option value="0">January</option>
                              <option value="1">February</option>
                              <option value="2">March</option>
                              <option value="3">April</option>
                              <option value="4">May</option>
                              <option value="5">June</option>
                              <option value="6" selected>July</option>
                              <option value="7">August</option>
                              <option value="8">September</option>
                              <option value="9">October</option>
                              <option value="10">November</option>
                              <option value="11">December</option>
                            </select>
                          </div>

                          <span class="text-gray-800 dark:text-neutral-200">/</span>

                          <div class="relative">
                            <select data-hs-select='{
                                "placeholder": "Select year",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                                "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                              }' class="hidden">
                              <option selected>2023</option>
                              <option>2024</option>
                              <option>2025</option>
                              <option>2026</option>
                              <option>2027</option>
                            </select>
                          </div>
                        </div>
                        <!-- End Month / Year -->

                        <!-- Next Button -->
                        <div class="col-span-1 flex justify-end">
                          <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                            <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="m9 18 6-6-6-6" />
                            </svg>
                          </button>
                        </div>
                        <!-- End Next Button -->
                      </div>
                      <!-- Months -->

                      <!-- Weeks -->
                      <div class="flex pb-1.5">
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Mo
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Tu
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          We
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Th
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Fr
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Sa
                        </span>
                        <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                          Su
                        </span>
                      </div>
                      <!-- Weeks -->

                      <!-- Days -->
                      <div class="flex">
                        <div class="bg-linear-to-l from-gray-100 dark:from-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            31
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                            1
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                            2
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                            3
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                            4
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            5
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            6
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            7
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            8
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            9
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            10
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            11
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            12
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            13
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            14
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            15
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            16
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            17
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            18
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            19
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            20
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            21
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            22
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            23
                          </button>
                        </div>
                        <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            24
                          </button>
                        </div>
                        <div class="bg-gray-100 rounded-e-full dark:bg-neutral-800">
                          <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                            25
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            26
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            27
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            28
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            29
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            30
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                            31
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            1
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            2
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            3
                          </button>
                        </div>
                      </div>
                      <!-- Days -->

                      <!-- Days -->
                      <div class="flex">
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            4
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            5
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            6
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            7
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            8
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            9
                          </button>
                        </div>
                        <div>
                          <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                            10
                          </button>
                        </div>
                      </div>
                      <!-- Days -->
                    </div>
                    <!-- End Calendar -->
                  </div>
                  <!-- End Calendar -->
                </div>
              </div>
              <!-- End Calendar Dropdown -->
            </div>
          </div>
          <!-- End Button Group -->

          <!-- Grid -->
          <div class="grid grid-cols-1 2xl:grid-cols-2 gap-y-5 gap-x-10">
            <div class="flex flex-col">
              <!-- Heading -->
              <div class="mb-3">
                <h3 class="mb-0.5 font-medium text-gray-800 dark:text-neutral-200">
                  Responses
                </h3>

                <div class="flex flex-wrap items-center gap-2">
                  <span class="block text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    75
                  </span>
                  <span class="text-sm text-gray-500 dark:text-neutral-500">
                    +20 since yesterday
                  </span>
                </div>
              </div>
              <!-- End Heading -->

              <div class="mt-auto flex flex-col gap-y-2">
                <!-- Subheader -->
                <div class="flex justify-between items-center gap-2">
                  <div>
                    <p class="text-sm text-gray-800 dark:text-neutral-200">
                      <span class="font-semibold">25%</span> to your goal
                    </p>
                  </div>

                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      100
                    </span>
                  </div>
                </div>
                <!-- End Subheader -->

                <!-- Progress -->
                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 75%"></div>
                </div>
                <!-- End Progress -->
              </div>
            </div>
            <!-- End Col -->

            <div class="h-full flex flex-col justify-end">
              <!-- Grid -->
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-y-5 gap-x-10">
                <!-- Item -->
                <div class="relative w-full">
                  <!-- Heading -->
                  <div class="mb-2 flex flex-col">
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Ignored
                    </span>
                    <div>
                      <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                        64
                      </span>
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        / 100
                      </span>
                    </div>
                  </div>
                  <!-- End Heading -->

                  <!-- Segmented Progress -->
                  <div class="flex items-center gap-x-1">
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <!-- End Segmented Progress -->
                </div>
                <!-- End Item -->

                <!-- Item -->
                <div class="relative w-full">
                  <!-- Heading -->
                  <div class="mb-2 flex flex-col">
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dismissed
                    </span>
                    <div>
                      <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                        49
                      </span>
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        / 100
                      </span>
                    </div>
                  </div>
                  <!-- End Heading -->

                  <!-- Segmented Progress -->
                  <div class="flex items-center gap-x-1">
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <!-- End Segmented Progress -->
                </div>
                <!-- End Item -->

                <!-- Item -->
                <div class="relative w-full">
                  <!-- Heading -->
                  <div class="mb-2 flex flex-col">
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Responded
                    </span>
                    <div>
                      <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                        75
                      </span>
                      <span class="text-sm text-gray-500 dark:text-neutral-500">
                        / 100
                      </span>
                    </div>
                  </div>
                  <!-- End Heading -->

                  <!-- Segmented Progress -->
                  <div class="flex items-center gap-x-1">
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-indigo-500" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="shrink-0 size-3 flex flex-col justify-center overflow-hidden bg-gray-100 text-xs text-white text-center whitespace-nowrap rounded-xs transition duration-500 dark:bg-neutral-700" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <!-- End Segmented Progress -->
                </div>
                <!-- End Item -->
              </div>
              <!-- End Grid -->
            </div>
            <!-- End Col -->
          </div>
          <!-- End Grid -->
        </div>
        <!-- End Body -->
      </div>
      <!-- End Card -->

      <!-- Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-5">
        <!-- Card -->
        <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="flex gap-3">
            <div class="mt-0.5 flex shrink-0 justify-center items-center size-8 bg-gray-100 text-gray-800 rounded-md dark:bg-neutral-700 dark:text-neutral-200">
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="5" width="6" height="6" rx="1" />
                <path d="m3 17 2 2 4-4" />
                <path d="M13 6h8" />
                <path d="M13 12h8" />
                <path d="M13 18h8" />
              </svg>
            </div>

            <div class="grow">
              <h2 class="font-medium text-sm sm:text-base text-gray-800 dark:text-neutral-200">
                What frontend framework do you primarily use?
              </h2>

              <!-- List -->
              <ul class="flex flex-wrap items-center whitespace-nowrap gap-1.5">
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Multiple choice
                  </p>
                </li>
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    75 responses
                  </p>
                </li>
              </ul>
              <!-- End List -->
            </div>
          </div>
          <!-- End Header -->

          <div class="mt-5">
            <!-- List of Horizontal Progress Group -->
            <div class="flex flex-col gap-y-3">
              <!-- Progress -->
              <div>
                <!-- Header -->
                <div class="mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      48%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - React
                    </span>
                  </div>

                  <span class="text-xs text-gray-500 dark:text-neutral-500">
                    25 response
                  </span>
                </div>
                <!-- End Header -->

                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="48" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 48%"></div>
                </div>
              </div>
              <!-- End Progress -->

              <!-- Progress -->
              <div>
                <!-- Header -->
                <div class="mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      22%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Vue
                    </span>
                  </div>

                  <span class="text-xs text-gray-500 dark:text-neutral-500">
                    20 response
                  </span>
                </div>
                <!-- End Header -->

                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="22" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 22%"></div>
                </div>
              </div>
              <!-- End Progress -->

              <!-- Progress -->
              <div>
                <!-- Header -->
                <div class="mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      15%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Angular
                    </span>
                  </div>

                  <span class="text-xs text-gray-500 dark:text-neutral-500">
                    13 response
                  </span>
                </div>
                <!-- End Header -->

                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 15%"></div>
                </div>
              </div>
              <!-- End Progress -->

              <!-- Progress -->
              <div>
                <!-- Header -->
                <div class="mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      8%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Svelte
                    </span>
                  </div>

                  <span class="text-xs text-gray-500 dark:text-neutral-500">
                    7 response
                  </span>
                </div>
                <!-- End Header -->

                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="8" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 8%"></div>
                </div>
              </div>
              <!-- End Progress -->

              <!-- Progress -->
              <div>
                <!-- Header -->
                <div class="mb-2 flex justify-between items-center">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      7%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Others
                    </span>
                  </div>

                  <span class="text-xs text-gray-500 dark:text-neutral-500">
                    3 response
                  </span>
                </div>
                <!-- End Header -->

                <div class="flex w-full h-3 bg-gray-100 rounded-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="7" aria-valuemin="0" aria-valuemax="100">
                  <div class="flex flex-col justify-center rounded-sm overflow-hidden bg-indigo-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-indigo-500" style="width: 7%"></div>
                </div>
              </div>
              <!-- End Progress -->
            </div>
            <!-- End List of Horizontal Progress Group -->
          </div>
        </div>
        <!-- End Card -->

        <!-- Card -->
        <div class="p-5 flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="flex gap-3">
            <div class="mt-0.5 flex shrink-0 justify-center items-center size-8 bg-gray-100 text-gray-800 rounded-md dark:bg-neutral-700 dark:text-neutral-200">
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="5" width="6" height="6" rx="1" />
                <path d="m3 17 2 2 4-4" />
                <path d="M13 6h8" />
                <path d="M13 12h8" />
                <path d="M13 18h8" />
              </svg>
            </div>

            <div class="grow">
              <h2 class="font-medium text-sm sm:text-base text-gray-800 dark:text-neutral-200">
                Which CSS framework do you use the most?
              </h2>

              <!-- List -->
              <ul class="flex flex-wrap items-center whitespace-nowrap gap-1.5">
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Multiple choice
                  </p>
                </li>
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    75 responses
                  </p>
                </li>
              </ul>
              <!-- End List -->
            </div>
          </div>
          <!-- End Header -->

          <div class="mt-5 grid grid-cols-1 sm:grid-cols-2 sm:items-center gap-3">
            <!-- Apex Radial Bar Chart -->
            <div id="hs-pro-asdwrb" class="min-h-[223px] mx-auto"></div>

            <div class="flex flex-col gap-3 sm:gap-5">
              <!-- Legend Indicator -->
              <div class="flex gap-2">
                <span class="shrink-0 size-2.5 inline-block bg-indigo-600 rounded-full mt-2"></span>
                <div class="grow">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      60%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Tailwind CSS
                    </span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    39 response
                  </p>
                </div>
              </div>
              <!-- End Legend Indicator -->

              <!-- Legend Indicator -->
              <div class="flex gap-2">
                <span class="shrink-0 size-2.5 inline-block bg-cyan-400 rounded-full mt-2"></span>
                <div class="grow">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      30%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Bootstrap
                    </span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    27 response
                  </p>
                </div>
              </div>
              <!-- End Legend Indicator -->

              <!-- Legend Indicator -->
              <div class="flex gap-2">
                <span class="shrink-0 size-2.5 inline-block bg-gray-200 rounded-full mt-2 dark:bg-neutral-600"></span>
                <div class="grow">
                  <div>
                    <span class="font-semibold text-sm text-gray-800 dark:text-neutral-200">
                      10%
                    </span>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      - Other
                    </span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    9 response
                  </p>
                </div>
              </div>
              <!-- End Legend Indicator -->
            </div>
          </div>
        </div>
        <!-- End Card -->

        <!-- Card -->
        <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
          <div class="p-5">
            <!-- Header -->
            <div class="flex gap-3">
              <div class="mt-0.5 flex shrink-0 justify-center items-center size-8 bg-gray-100 text-gray-800 rounded-md dark:bg-neutral-700 dark:text-neutral-200">
                <svg class="shrink-0 size-4 sm:size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 4h1a3 3 0 0 1 3 3 3 3 0 0 1 3-3h1" />
                  <path d="M13 20h-1a3 3 0 0 1-3-3 3 3 0 0 1-3 3H5" />
                  <path d="M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1" />
                  <path d="M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7" />
                  <path d="M9 7v10" />
                </svg>
              </div>

              <div class="grow">
                <h2 class="font-medium text-sm sm:text-base text-gray-800 dark:text-neutral-200">
                  What’s one tool, framework, or practice you think will define web development in the next 5 years?
                </h2>

                <!-- List -->
                <ul class="flex flex-wrap items-center whitespace-nowrap gap-1.5">
                  <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      Open text
                    </p>
                  </li>
                  <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      38 responses
                    </p>
                  </li>
                </ul>
                <!-- End List -->
              </div>
            </div>
            <!-- End Header -->

            <div class="mt-5">
              <!-- Table Section -->
              <div class="overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                <div class="min-w-full inline-block align-middle">
                  <!-- Table -->
                  <table class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                    <thead>
                      <tr>
                        <th scope="col" class="min-w-40">
                          <div class="pe-4 py-2 text-start">
                            <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                              User
                            </span>
                          </div>
                        </th>

                        <th scope="col" class="min-w-85">
                          <div class="px-4 py-2 text-start">
                            <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                              Response
                            </span>
                          </div>
                        </th>

                        <th scope="col">
                          <div class="px-4 py-2 text-start">
                            <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                              Time
                            </span>
                          </div>
                        </th>
                      </tr>
                    </thead>

                    <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                      <tr>
                        <td class="size-px whitespace-nowrap align-top pe-4 py-2">
                          <div class="w-full flex items-center gap-x-2">
                            <img class="shrink-0 size-7 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
                            <div class="grow">
                              <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                James Collins
                              </span>
                            </div>
                          </div>
                        </td>
                        <td class="size-px align-top px-4 py-2">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            AI-assisted coding will be the norm, especially tools like GitHub Copilot or Cody.
                          </span>
                        </td>
                        <td class="size-px whitespace-nowrap align-top px-4 py-2">
                          <span class="text-xs text-gray-500 dark:text-neutral-500">
                            5 sec ago
                          </span>
                        </td>
                      </tr>

                      <tr>
                        <td class="size-px whitespace-nowrap align-top pe-4 py-2">
                          <div class="w-full flex items-center gap-x-2">
                            <span class="flex shrink-0 justify-center items-center size-7 text-xs font-medium uppercase bg-cyan-500 text-white rounded-full">
                              L
                            </span>
                            <div class="grow">
                              <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                Lewis Clarks
                              </span>
                            </div>
                          </div>
                        </td>
                        <td class="size-px align-top px-4 py-2">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            WebAssembly – it’s opening doors for high-performance web apps
                          </span>
                        </td>
                        <td class="size-px whitespace-nowrap align-top px-4 py-2">
                          <span class="text-xs text-gray-500 dark:text-neutral-500">
                            2 min ago
                          </span>
                        </td>
                      </tr>

                      <tr>
                        <td class="size-px whitespace-nowrap align-top pe-4 py-2">
                          <div class="w-full flex items-center gap-x-2">
                            <img class="shrink-0 size-7 rounded-full" src="https://images.unsplash.com/photo-1601935111741-ae98b2b230b0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2.5&w=320&h=320&q=80" alt="Avatar">
                            <div class="grow">
                              <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                Daniel Hobbs
                              </span>
                            </div>
                          </div>
                        </td>
                        <td class="size-px align-top px-4 py-2">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Design systems and component-driven development will continue to grow.
                          </span>
                        </td>
                        <td class="size-px whitespace-nowrap align-top px-4 py-2">
                          <span class="text-xs text-gray-500 dark:text-neutral-500">
                            10 min ago
                          </span>
                        </td>
                      </tr>

                      <tr>
                        <td class="size-px whitespace-nowrap align-top pe-4 py-2">
                          <div class="w-full flex items-center gap-x-2">
                            <img class="shrink-0 size-7 rounded-full" src="https://images.unsplash.com/photo-1679412330254-90cb240038c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2.5&w=320&h=320&q=80" alt="Avatar">
                            <div class="grow">
                              <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                Brian Warner
                              </span>
                            </div>
                          </div>
                        </td>
                        <td class="size-px align-top px-4 py-2">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Serverless architecture and edge computing will become mainstream
                          </span>
                        </td>
                        <td class="size-px whitespace-nowrap align-top px-4 py-2">
                          <span class="text-xs text-gray-500 dark:text-neutral-500">
                            15 min ago
                          </span>
                        </td>
                      </tr>

                      <tr>
                        <td class="size-px whitespace-nowrap align-top pe-4 py-2">
                          <div class="w-full flex items-center gap-x-2">
                            <span class="flex shrink-0 justify-center items-center size-7 text-xs font-medium uppercase bg-teal-500 text-white rounded-full">
                              O
                            </span>
                            <div class="grow">
                              <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                Ols Shols
                              </span>
                            </div>
                          </div>
                        </td>
                        <td class="size-px align-top px-4 py-2">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            I don't know 🤷
                          </span>
                        </td>
                        <td class="size-px whitespace-nowrap align-top px-4 py-2">
                          <span class="text-xs text-gray-500 dark:text-neutral-500">
                            2 hrs ago
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <!-- End Table -->
                </div>
              </div>
              <!-- End Table Section -->
            </div>
          </div>

          <!-- Footer -->
          <div class="py-3 px-5 border-t border-gray-200 dark:border-neutral-800">
            <!-- Footer -->
            <div class="grid grid-cols-2 items-center gap-y-2 sm:gap-y-0 sm:gap-x-5">
              <p class="text-sm text-gray-800 dark:text-neutral-200">
                <span class="font-medium">38</span>
                <span class="text-gray-500 dark:text-neutral-500">results</span>
              </p>

              <!-- Pagination -->
              <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Previous">
                  <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                  <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1">
                  <span class="min-h-9.5 min-w-9.5 flex justify-center items-center bg-gray-100 text-gray-800 py-2 px-3 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-white" aria-current="page">1</span>
                  <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                  <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">3</span>
                </div>
                <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Next">
                  <span class="sr-only">Next</span>
                  <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </button>
              </nav>
              <!-- End Pagination -->
            </div>
            <!-- End Footer -->
          </div>
          <!-- End Footer -->
        </div>
        <!-- End Card -->

        <!-- Card -->
        <div class="p-5 flex flex-col justify-between bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
          <!-- Header -->
          <div class="flex gap-3">
            <div class="mt-0.5 flex shrink-0 justify-center items-center size-8 bg-gray-100 text-gray-800 rounded-md dark:bg-neutral-700 dark:text-neutral-200">
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z" />
              </svg>
            </div>

            <div class="grow">
              <h2 class="font-medium text-sm sm:text-base text-gray-800 dark:text-neutral-200">
                How easy or difficult do you find setting up a modern web project from scratch?
              </h2>

              <!-- List -->
              <ul class="flex flex-wrap items-center whitespace-nowrap gap-1.5">
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Opinion scale
                  </p>
                </li>
                <li class="inline-flex items-center relative text-xs text-gray-500 pe-2 last:pe-0 last:after:hidden after:absolute after:top-1/2 after:end-0 after:inline-block after:size-[3px] after:bg-gray-400 after:rounded-full after:-translate-y-1/2 dark:text-neutral-500 dark:after:bg-neutral-600">
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    50 responses
                  </p>
                </li>
              </ul>
              <!-- End List -->
            </div>
          </div>
          <!-- End Header -->

          <!-- Body -->
          <div class="mt-5">
            <div class="mb-5 flex justify-center">
              <span class="py-1 px-2.5 inline-flex items-center bg-orange-100 text-orange-800 text-start text-[13px] rounded-full dark:bg-orange-500/10 dark:text-orange-400">
                Avg. 3.75
              </span>
            </div>

            <!-- Grid -->
            <div class="grid grid-cols-5 gap-2 sm:gap-5">
              <!-- Progress Vertical -->
              <div class="flex flex-col justify-center items-center text-center">
                <div class="mb-2">
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    0
                  </span>
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    resp.
                  </span>
                </div>
                <div class="relative w-full sm:w-2/3">
                  <div class="absolute size-full">
                    <div class="size-full inline-flex justify-center items-center">
                      <span class="py-0.5 px-1.5 inline-block font-semibold text-xs bg-white text-gray-800 rounded-full dark:text-neutral-200">
                        0%
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-col flex-nowrap justify-end w-full h-60 bg-gray-100 rounded-md inset-shadow-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    <div class="overflow-hidden bg-indigo-600 dark:bg-indigo-500" style="height: 0%"></div>
                  </div>
                </div>
                <div class="mt-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    1
                  </span>
                </div>
              </div>
              <!-- End Progress Vertical -->

              <!-- Progress Vertical -->
              <div class="flex flex-col justify-center items-center text-center">
                <div class="mb-2">
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    24
                  </span>
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    resp.
                  </span>
                </div>
                <div class="relative w-full sm:w-2/3">
                  <div class="absolute size-full">
                    <div class="size-full inline-flex justify-center items-center">
                      <span class="py-0.5 px-1.5 inline-block font-semibold text-xs bg-white text-gray-800 rounded-full dark:text-neutral-200">
                        72%
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-col flex-nowrap justify-end w-full h-60 bg-gray-100 rounded-md inset-shadow-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="72" aria-valuemin="0" aria-valuemax="100">
                    <div class="overflow-hidden bg-indigo-600 dark:bg-indigo-500" style="height: 72%"></div>
                  </div>
                </div>
                <div class="mt-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    2
                  </span>
                </div>
              </div>
              <!-- End Progress Vertical -->

              <!-- Progress Vertical -->
              <div class="flex flex-col justify-center items-center text-center">
                <div class="mb-2">
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    4
                  </span>
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    resp.
                  </span>
                </div>
                <div class="relative w-full sm:w-2/3">
                  <div class="absolute size-full">
                    <div class="size-full inline-flex justify-center items-center">
                      <span class="py-0.5 px-1.5 inline-block font-semibold text-xs bg-white text-gray-800 rounded-full dark:text-neutral-200">
                        30%
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-col flex-nowrap justify-end w-full h-60 bg-gray-100 rounded-md inset-shadow-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">
                    <div class="overflow-hidden bg-indigo-600 dark:bg-indigo-500" style="height: 30%"></div>
                  </div>
                </div>
                <div class="mt-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    3
                  </span>
                </div>
              </div>
              <!-- End Progress Vertical -->

              <!-- Progress Vertical -->
              <div class="flex flex-col justify-center items-center text-center">
                <div class="mb-2">
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    17
                  </span>
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    resp.
                  </span>
                </div>
                <div class="relative w-full sm:w-2/3">
                  <div class="absolute size-full">
                    <div class="size-full inline-flex justify-center items-center">
                      <span class="py-0.5 px-1.5 inline-block font-semibold text-xs bg-white text-gray-800 rounded-full dark:text-neutral-200">
                        52%
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-col flex-nowrap justify-end w-full h-60 bg-gray-100 rounded-md inset-shadow-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="52" aria-valuemin="0" aria-valuemax="100">
                    <div class="overflow-hidden bg-indigo-600 dark:bg-indigo-500" style="height: 52%"></div>
                  </div>
                </div>
                <div class="mt-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    4
                  </span>
                </div>
              </div>
              <!-- End Progress Vertical -->

              <!-- Progress Vertical -->
              <div class="flex flex-col justify-center items-center text-center">
                <div class="mb-2">
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    2
                  </span>
                  <span class="block text-xs text-gray-500 dark:text-neutral-500">
                    resp.
                  </span>
                </div>
                <div class="relative w-full sm:w-2/3">
                  <div class="absolute size-full">
                    <div class="size-full inline-flex justify-center items-center">
                      <span class="py-0.5 px-1.5 inline-block font-semibold text-xs bg-white text-gray-800 rounded-full dark:text-neutral-200">
                        17%
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-col flex-nowrap justify-end w-full h-60 bg-gray-100 rounded-md inset-shadow-sm overflow-hidden dark:bg-neutral-700" role="progressbar" aria-valuenow="17" aria-valuemin="0" aria-valuemax="100">
                    <div class="overflow-hidden bg-indigo-600 dark:bg-indigo-500" style="height: 17%"></div>
                  </div>
                </div>
                <div class="mt-2">
                  <span class="text-sm text-gray-800 dark:text-neutral-200">
                    5
                  </span>
                </div>
              </div>
              <!-- End Progress Vertical -->
            </div>
            <!-- End Grid -->
          </div>
          <!-- End Body -->

          <!-- Footer -->
          <div class="mt-5 flex justify-between items-center">
            <span class="text-xs text-gray-500 dark:text-neutral-500">
              Difficult
            </span>

            <span class="text-xs text-gray-500 dark:text-neutral-500">
              Easy
            </span>
          </div>
          <!-- End Footer -->
        </div>
        <!-- End Card -->
      </div>
      <!-- End Grid -->
    </div>
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <script src="../assets/vendor/@floating-ui/core/dist/floating-ui.core.umd.min.js"></script>
  <script src="../assets/vendor/@floating-ui/dom/dist/floating-ui.dom.umd.min.js"></script>
  <!-- Required -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>
  <!-- Apexcharts -->
  <script src="../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../assets/vendor/apexcharts/dist/apexcharts.min.js"></script>
  <script src="../assets/vendor/preline/dist/helper-apexcharts.js"></script>

  <!-- JS INITIALIZATIONS -->
  <script>
    (function () {
      buildChart(
        "#hs-pro-asdwrb",
        (mode) => ({
          chart: {
            height: 250,
            width: 250,
            type: "radialBar",
            zoom: {
              enabled: false,
            },
          },
          responsive: [
            {
              breakpoint: 1600,
              options: {
                chart: {
                  height: 220,
                  width: 220
                }
              }
            },
            {
              breakpoint: 600,
              options: {
                chart: {
                  height: 200,
                  width: 200
                }
              }
            },
          ],
          series: [60, 30, 10],
          plotOptions: {
            radialBar: {
              hollow: {
                margin: 5,
                size: '35%'
              },
              track: {
                background: mode === 'dark' ? 'oklch(0.371 0 0)' : 'oklch(0.967 0.003 264.542)',
                strokeWidth: '97%',
                margin: 5,
              },
              dataLabels: {
                show: false
              },
            }
          },
          stroke: {
            lineCap: 'round'
          },
          legend: {
            show: false,
          },
          grid: {
            padding: {
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            },
          },
          states: {
            hover: {
              filter: {
                type: "none",
              },
            },
          },
          tooltip: {
            enabled: false,
          },
        }),
        {
          colors: ["oklch(0.511 0.262 276.966)", "oklch(0.789 0.154 211.53)", "oklch(0.872 0.01 258.338)"],
          stroke: {
            colors: ["rgb(255, 255, 255)"],
          },
        },
        {
          colors: ["oklch(0.585 0.233 277.117)", "oklch(0.715 0.143 215.221)", "oklch(0.439 0 0)"],
          stroke: {
            colors: ["rgb(38, 38, 38)"],
          },
        }
      );
    })();
  </script>
  <script>
    (function () {
      buildChart(
        "#hs-pro-asdwrb1",
        (mode) => ({
          chart: {
            height: 150,
            width: 150,
            type: "radialBar",
            zoom: {
              enabled: false,
            },
          },
          series: [34],
          plotOptions: {
            radialBar: {
              hollow: {
                margin: 5,
                size: '20%'
              },
              track: {
                background: mode === 'dark' ? 'oklch(0.371 0 0)' : 'oklch(0.967 0.003 264.542)',
                strokeWidth: '97%',
                margin: 5,
              },
              dataLabels: {
                show: false
              },
            }
          },
          stroke: {
            dashArray: 4
          },
          legend: {
            show: false,
          },
          grid: {
            padding: {
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            },
          },
          states: {
            hover: {
              filter: {
                type: "none",
              },
            },
          },
          tooltip: {
            enabled: false,
          },
        }),
        {
          colors: ["oklch(0.511 0.262 276.966)"],
          stroke: {
            colors: ["rgb(255, 255, 255)"],
          },
        },
        {
          colors: ["oklch(0.585 0.233 277.117)"],
          stroke: {
            colors: ["rgb(38, 38, 38)"],
          },
        }
      );
    })();
  </script>

</body>
</html>