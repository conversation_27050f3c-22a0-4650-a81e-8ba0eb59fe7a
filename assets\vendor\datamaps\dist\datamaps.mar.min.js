!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo={type:"Topology",objects:{mar:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Tanger - Tétouan"},id:"MA.TO",arcs:[[0,1,2]]},{type:"Polygon",properties:{name:"Fès - Boulemane"},id:"MA.FB",arcs:[[3,4,5]]},{type:"Polygon",properties:{name:"Taza - Al Hoceima - Taounate"},id:"MA.TH",arcs:[[6,-6,7,-1,8]]},{type:"Polygon",properties:{name:"Gharb - Chrarda - Béni Hssen"},id:"MA.GB",arcs:[[-8,9,10,11,-2]]},{type:"Polygon",properties:{name:"Chaouia - Ouardigha"},id:"MA.CO",arcs:[[12,13,14,15,16,17,18,19]]},{type:"Polygon",properties:{name:"Grand Casablanca"},id:"MA.GC",arcs:[[-18,20]]},{type:"Polygon",properties:{name:"Rabat - Salé - Zemmour - Zaer"},id:"MA.RZ",arcs:[[21,-20,22,-11]]},{type:"Polygon",properties:{name:"Meknès - Tafilalet"},id:"MA.MT",arcs:[[23,24,25,26,-13,-22,-10,-5]]},{type:"Polygon",properties:{name:"Tadla - Azilal"},id:"MA.TD",arcs:[[27,28,-14,-27]]},{type:"Polygon",properties:{name:"Oriental"},id:"MA.OR",arcs:[[-24,-4,-7,29]]},{type:"Polygon",properties:{name:"Souss - Massa - Draâ"},id:"MA.SM",arcs:[[30,31,32,33,-28,-26]]},{type:"Polygon",properties:{name:"Marrakech - Tensift - Al Haouz"},id:"MA.MK",arcs:[[-29,-34,34,35,-15]]},{type:"Polygon",properties:{name:"Doukkala - Abda"},id:"MA.DA",arcs:[[-36,36,-16]]},{type:"Polygon",properties:{name:"Laâyoune - Boujdour - Sakia El Hamra"},id:"MA.LB",arcs:[[37,38,39,40]]},{type:"Polygon",properties:{name:"Guelmim - Es-Semara"},id:"MA.GE",arcs:[[41,-41,42,-32]]},{type:"Polygon",properties:{name:"Oued el Dahab"},id:"MA.OD",arcs:[[43,-39]]}]}},arcs:[[[7832,9485],[0,-4],[0,-25],[11,-8],[2,-17],[7,-16],[-7,-19],[-19,-22],[-22,-21],[-25,-18],[-22,-7],[-19,-3],[-12,-1],[-13,-8],[-9,-10],[-28,-14],[-26,-23],[-13,-3],[-16,-12],[-31,-8],[-25,-12],[-32,-11],[-46,-6],[-52,-17],[-38,-35]],[[7397,9165],[-30,6],[-43,-2],[-26,-5],[-19,8],[-25,8],[-19,19],[-26,21],[-14,16],[-16,5],[-29,-6],[-12,2],[-18,1],[-40,40],[-36,22],[-17,13],[-22,6],[-61,11],[-27,7],[-23,11],[-23,1],[-19,4],[-15,6],[-17,8],[-13,-1],[-32,-7],[-37,3],[-3,-1]],[[6735,9361],[27,85],[114,260],[3,13],[1,17],[3,10],[14,27],[13,37],[1,6],[2,6],[8,11],[2,7],[3,32],[5,16],[5,11],[12,11],[22,3],[58,-7],[7,1],[17,15],[8,5],[11,6],[12,2],[7,-3],[11,5],[15,0],[15,-3],[11,-7],[37,20],[10,8],[22,24],[12,9],[13,5],[5,0],[3,-2],[7,-4],[6,0],[3,5],[1,6],[2,1],[4,-1],[6,-16],[7,-14],[9,-12],[14,-12],[-3,-10],[2,-17],[10,-36],[3,-29],[2,-8],[4,-6],[5,-5],[3,-1],[11,2],[3,-1],[1,-4],[-1,-11],[0,-4],[11,-37],[6,-14],[14,-11],[26,-17],[22,-19],[53,-68],[10,-7],[15,-2],[11,-3],[13,-8],[21,-18],[17,-22],[11,-11],[28,-8],[34,-29],[25,-13],[89,-25],[83,-17]],[[8704,8523],[20,-11],[12,-10],[-6,-26],[-32,-42],[-68,-59],[-11,-18],[15,-22],[78,-50],[66,-67],[28,-52],[8,-39],[-32,-25],[-35,-18],[-179,-32],[-71,-3],[-37,4],[-34,-4],[-33,-7],[-49,-40],[-115,-142],[-29,-49],[-31,-40],[-15,-24],[-11,-29]],[[8143,7718],[-44,-2],[-24,4],[-50,-5],[-31,11],[-27,16],[-12,23],[-62,81],[-60,56],[-39,12],[-52,4],[-50,12],[-22,18],[-5,23],[-11,27],[-53,71],[-81,200],[9,29],[51,57],[-4,36],[-17,31],[-28,19],[-89,19],[-18,17],[-3,21],[31,44],[9,16],[-12,28],[-53,86],[-26,59],[-9,15],[-14,-4],[-10,-7],[-17,-2],[-10,9],[-1,16],[-6,25],[-7,24],[-17,22],[-26,36]],[[7253,8865],[11,-6],[13,-2],[39,-1],[23,4],[56,22],[12,3],[12,0],[12,-5],[12,-15],[7,-4],[10,10],[5,1],[6,-1],[51,2],[11,-1],[6,-5],[5,-7],[7,-6],[5,-1],[13,0],[2,1],[8,-9],[-1,-2],[5,-9],[2,-4],[9,-7],[21,0],[23,-19],[15,-20],[19,-31],[19,-21],[42,-23],[36,-13],[19,-20],[5,-21],[-1,-16],[15,-13],[37,-21],[18,1],[46,-6],[9,6],[17,0],[34,-5],[22,9],[23,2],[13,-7],[-3,-9],[2,-14],[33,-43],[17,-30],[27,-39],[-11,-27],[3,-29],[-4,-20],[7,-18],[13,-10],[15,-6],[15,-3],[18,-5],[41,11],[15,-1],[9,-15],[4,-11],[8,2],[25,40],[-4,17],[4,17],[8,14],[49,55],[7,11],[11,7],[72,75],[24,20],[25,13],[24,-1],[21,-7],[16,-12],[12,-14],[24,-14],[151,-36]],[[8253,9503],[1,-5],[4,-25],[-6,-24],[9,-136],[18,-21],[32,-20],[24,-5],[50,-16],[10,-31],[-2,-19],[-6,-20],[0,-15],[-11,-11],[-5,-12],[1,-15],[6,-14],[18,-8],[31,-26],[21,0],[92,65],[21,23],[14,19],[22,17],[47,3],[133,-49],[9,-14],[3,-17],[3,-14],[8,-14],[-8,-7],[-10,3],[-15,1],[-15,-5],[-9,-9],[0,-13],[-15,-47],[-2,-1],[-2,-6],[-3,-3],[-6,-3],[-3,-4],[-1,-6],[-4,-6],[-46,-28],[-9,-9],[-6,-8],[0,-7],[-1,-19],[15,-24],[39,-49],[10,-38],[15,-39],[-8,-35],[-17,-24],[-58,-24],[-5,-21],[10,-34],[58,-111]],[[7253,8865],[-30,38],[-6,6],[-4,7],[-4,10],[14,12],[75,49],[16,26],[34,25],[12,11],[20,8],[13,11],[8,17],[4,21],[-1,36],[-7,23]],[[7832,9485],[1,0],[74,-20],[18,-1],[12,3],[37,21],[86,14],[53,21],[53,10],[27,11],[6,-8],[-2,-3],[-8,-2],[-3,-3],[3,-4],[4,-4],[9,-12],[9,-4],[13,-1],[18,-1],[11,1]],[[7253,8865],[-21,-47],[4,-12],[-1,-12],[-12,-9],[-64,-25],[-22,2],[-26,-1],[-19,2],[-15,6],[-5,16],[-11,8],[-9,10],[-29,14],[-7,-7],[-11,-8],[-8,-16],[-20,-13],[-14,-21]],[[6963,8752],[-20,-8],[-15,1],[-20,-2],[-28,2],[-33,6],[-40,18],[-43,11],[-47,3],[-30,0],[-23,4],[-22,-7],[-25,1],[-28,-15],[-27,-5],[-19,-7],[-20,7],[-25,4],[-23,14],[-15,0],[-21,18]],[[6439,8797],[34,67],[69,95],[6,12],[11,27],[14,21],[135,259],[27,83]],[[6628,8146],[10,-29],[28,-20],[23,-11],[75,-51],[44,-45],[-6,-20],[-5,-13],[-2,-26],[10,-11],[32,0],[18,-9],[13,-28],[-4,-15],[1,-29]],[[6865,7839],[-14,-22],[-18,-9],[-77,-23],[-48,-9],[-179,14],[-20,-11],[-19,-4],[-13,-10],[-12,3],[-15,0],[-13,3],[-39,1],[-32,-6],[-12,3],[-55,-31],[-21,-5],[-10,-20],[-18,-75],[-29,-65],[-35,-42]],[[6186,7531],[-43,7],[-66,41],[-22,12],[-22,18],[-47,11],[-82,3],[-15,5],[-12,2],[-16,10],[-65,84],[-50,38],[-27,15],[-22,23],[-38,45],[-13,22]],[[5646,7867],[-8,24],[-8,5],[-12,3],[-7,12],[-5,11],[0,16],[-7,15],[0,25],[-7,61],[-26,58],[8,17],[9,15],[17,8],[16,5],[11,9],[3,10],[7,12],[13,10],[9,30],[0,32],[-6,23],[-20,34],[-1,1]],[[5632,8303],[31,13],[29,7],[15,6],[4,4]],[[5711,8333],[1,-1],[14,-8],[32,-6],[16,-6],[23,-5],[42,-21],[18,4],[39,-1],[8,-9],[-21,-19],[-2,-15],[6,-10],[14,-4],[10,4],[10,7],[21,28],[56,36],[21,37],[-1,18],[-2,14],[0,11],[-1,11],[3,21],[5,13],[12,15],[5,15],[8,10],[4,12],[0,2]],[[6052,8486],[46,28],[24,20],[22,11],[26,9],[22,3]],[[6192,8557],[1,-1],[24,-26],[12,-8],[12,-22],[11,-26],[6,-24],[20,-45],[19,-8],[59,-21],[16,-7],[26,-6],[3,-13],[-7,-16],[-10,-12],[-4,-19],[9,-17],[0,-13],[-4,-7],[-8,-57],[9,-32],[19,-15],[28,-13],[74,-21],[36,-17],[32,7],[53,28]],[[5711,8333],[10,12],[16,6],[41,12],[25,12],[12,3],[12,6],[10,11],[10,9],[13,-3],[7,4],[6,0],[6,-3],[7,-1],[6,2],[11,7],[18,3],[13,5],[12,6],[8,6],[21,25],[18,8],[18,20],[12,4],[-4,-5],[12,-4],[14,3],[7,5]],[[6963,8752],[22,-12],[8,-8],[14,-26],[1,-15],[11,-16],[15,-17],[7,-18],[-17,-47],[6,-13],[10,-12],[5,-15],[11,-11],[7,-17],[-9,-48],[-18,-29],[-13,-39],[0,-15],[2,-22],[-3,-23],[-8,-22],[2,-17],[-4,-19],[1,-16],[-1,-17],[0,-12],[4,-13],[7,-10],[-12,-9],[-17,-6],[-72,3],[-17,-4],[-13,-7],[-16,-1],[-35,10],[-12,8],[-16,5],[-16,2],[-30,10],[-8,10],[1,13],[-2,12],[-1,15],[-13,10],[-42,14],[-17,2],[-15,-5],[-10,-25],[-19,-35],[-2,-13],[-4,-19],[4,-15],[6,-31],[-17,-21]],[[6192,8557],[11,4],[20,16],[22,7],[66,49],[65,65],[56,85],[7,14]],[[8143,7718],[38,-54],[17,-32],[41,-57],[27,-52],[9,-34],[25,-15],[8,-29],[24,-22],[7,-25],[17,-8],[36,7],[27,12],[18,11],[25,0],[30,-8],[21,-8],[28,-6],[29,6],[29,2],[25,-7],[-12,-34],[-1,-58],[7,-44],[30,-64],[24,-18],[-4,-35],[-1,-38]],[[8667,7108],[-36,-10],[-183,-31],[-24,-2],[-26,6],[-43,-21],[-9,-178],[-46,-3],[-34,-24],[-8,-9],[-3,-13],[4,-52],[-1,-16],[-13,-21],[-5,-13],[3,-12],[7,-7],[10,0],[12,4],[18,17],[9,4],[11,-2],[9,-9],[17,-26],[11,-10],[23,-11],[7,-6],[6,-13],[3,-12],[1,-13],[34,-52],[-35,-53],[-31,-29],[4,-43],[5,-44],[-118,-58],[-105,-24],[-96,-4],[-75,-20],[-61,-33],[-70,-87],[-87,-68],[-92,-38]],[[7660,6072],[-97,62],[-66,43],[-50,39],[-32,11],[-36,30],[-5,43],[12,30],[2,34],[-24,25],[-22,31],[-11,26],[-21,22],[-19,14],[-2,33],[2,24],[17,15],[19,48],[-5,35],[4,43],[44,131],[13,77],[-25,42],[-96,63],[-30,30],[-37,13],[-32,7],[-47,-10],[-34,1],[-26,9],[14,46],[58,66],[31,51],[-11,44],[-44,22],[-92,-28]],[[7012,7244],[-14,38],[-19,20],[5,30],[19,31],[46,27],[68,51],[27,11],[25,8],[50,2],[4,11],[-18,39],[-4,24],[-14,25],[12,35],[19,20],[23,17],[2,10],[-53,14],[-82,51],[-76,28],[-78,69],[-89,34]],[[7012,7244],[-34,-42],[-122,-80],[-195,-91],[-40,-25],[-31,-7],[-139,-55],[-29,-43],[-34,-27],[-54,-14],[-59,0],[-40,13],[-38,8],[-23,16],[-26,15]],[[6148,6912],[23,79],[-18,14],[-49,51],[-17,28],[10,26],[28,17],[90,18],[25,30],[8,36],[-19,67],[-25,60],[-10,60],[3,45],[-3,33],[3,27],[-11,28]],[[8253,9503],[22,2],[14,6],[6,12],[5,19],[13,13],[17,6],[16,-2],[12,-8],[27,-22],[13,-8],[47,-7],[17,-8],[7,0],[8,0],[7,0],[7,-2],[12,-5],[6,-2],[32,-3],[27,5],[78,26],[13,7],[28,27],[7,4],[5,0],[6,-3],[7,-2],[8,1],[19,18],[25,55],[16,21],[-2,7],[4,6],[5,2],[8,-3],[3,-6],[-4,-12],[3,-8],[-6,-21],[8,-27],[1,-3],[-12,-11],[2,-19],[13,-13],[19,6],[9,-11],[-6,-7],[-2,-10],[-1,-12],[2,-22],[3,-9],[6,-8],[9,-10],[16,-11],[22,-7],[45,-4],[-27,20],[-48,50],[-9,15],[3,5],[8,-6],[31,-30],[4,-7],[30,-28],[27,-14],[32,-10],[65,-8],[29,2],[15,3],[10,6],[13,14],[12,8],[13,3],[59,-21],[14,-2],[15,-4],[17,-9],[16,-5],[1,-27],[6,-18],[11,-14],[19,-7],[24,-15],[19,-17],[21,-12],[28,-2],[8,-6],[3,-8],[0,-10],[3,-9],[9,-9],[33,-19],[21,-18],[66,-38],[12,-11],[-3,-5],[-8,-6],[-15,-31],[-33,-46],[-5,-12],[75,-70],[23,-7],[7,-3],[-67,-74],[24,-26],[16,-31],[45,-127],[2,-9],[1,-9],[-1,-14],[-30,-111],[-2,-32],[6,-34],[7,-20],[-1,-8],[-5,-10],[-6,-8],[-7,-5],[-6,-7],[-3,-11],[4,-11],[9,-6],[22,-7],[11,-8],[7,-8],[28,-62],[3,-23],[-8,-19],[-10,-12],[-11,-39],[-9,-17],[-6,-18],[-1,-67],[6,-23],[32,-29],[11,-19],[9,-22],[13,-17],[16,-14],[18,-12],[11,-10],[4,-14],[-1,-16],[-2,-15],[-1,-7],[-3,-7],[-3,-5],[-5,-5],[-27,-18],[85,-132],[21,-16],[39,-14],[175,-125],[10,-16],[-36,-38],[-21,-14],[-23,-9],[-26,-4],[-10,-5],[-10,-12],[-7,-12],[-8,-25],[-10,-63],[-1,-9],[0,-5],[-1,-6],[-4,-7],[-4,-4],[-10,-8],[-3,-4],[3,-11],[10,0],[23,8],[12,0],[13,-3],[11,-9],[3,-14],[-13,-25],[-24,-5],[-47,2],[-6,0],[-15,1],[-23,1],[-32,1],[-38,2],[-43,2],[-48,2],[-51,2],[-54,3],[-55,2],[-55,2],[-54,3],[-52,2],[-49,2],[-45,2],[-40,2],[-86,4],[-112,-30],[-116,-9],[-36,-19],[43,-108],[26,-67],[-109,-14],[-99,-29]],[[7660,6072],[-66,-34],[-52,-38],[-70,-58],[-66,-44],[-61,-62],[-44,-82],[-57,-87],[-65,-82],[-61,-19],[-53,19],[-22,63],[-78,-10],[-75,-14],[-78,0],[-92,0],[-44,-5]],[[6676,5619],[-83,180],[-12,39],[6,25],[42,64],[2,20],[-24,9],[-36,7],[-18,24],[-18,67],[-3,30],[18,50],[11,23],[-35,44],[-49,36],[-34,13],[-52,-12],[-28,-12],[-20,-35],[-22,-18],[-31,-4],[-47,-1],[-41,14],[-48,2],[-26,-25],[-26,-44],[-18,-38],[-22,-32],[-66,13],[-23,10],[-23,1],[-22,4],[-55,-8],[-32,-18],[-27,-19],[-24,-25],[-101,0],[-29,-9],[-19,-27],[-14,-35],[-22,-20],[-28,-11],[-35,18],[-39,0],[-50,-7],[-43,-29],[-91,-4],[-38,12],[-45,-2],[-16,-25],[-11,-21],[3,-21],[8,-15],[-15,-18],[-4,-18],[2,-20],[2,-123],[10,-40],[17,-37],[4,-21],[-16,-11],[-21,-5],[-22,-10],[-85,36],[-67,-5],[-60,-17],[-28,-33],[-16,-31],[-14,3],[-11,13],[-19,-1],[-54,-11],[-33,-11],[-36,-4],[-30,4],[-29,-1],[-46,-31],[-30,-5],[-30,10],[-29,12],[-38,-14],[-16,-19],[-10,-29],[-28,-31],[-32,5],[-32,-8],[-31,-16],[-46,-41],[-30,-2],[-25,16],[-14,14],[-18,6],[-16,4],[-44,-2],[-41,21],[-1,0]],[[4138,5336],[33,47],[16,20],[12,9],[24,9],[10,10],[15,23],[16,20],[7,12],[3,13],[3,5],[22,16],[3,5],[6,14],[26,37],[4,8],[1,15],[4,15],[5,14],[7,10],[30,27],[5,8],[3,11],[26,34],[82,85],[64,109],[39,89],[6,26],[11,88],[11,46],[2,13],[-3,19],[-7,9],[-9,6],[-11,11],[-7,12],[-16,34],[-5,23],[-5,3],[-7,1],[-7,3],[-6,5],[-12,18],[-44,29],[-14,1],[-14,3],[-9,10],[1,26],[13,26],[17,25],[12,22],[4,16],[0,8],[-2,7],[-5,6],[0,4],[2,5],[1,8],[-2,62]],[[4499,6576],[34,-24],[26,-44],[23,-22],[29,0],[36,18],[24,18],[63,-4],[73,4],[59,-24],[70,-1],[47,7],[28,11],[25,21],[65,38],[25,8],[18,-11],[10,-17],[4,-24],[12,-11],[15,1],[36,8],[45,5],[148,0],[87,25],[36,15],[52,-8],[29,1],[28,20],[28,45],[34,38],[41,31],[43,20],[31,6],[24,17],[214,95],[36,28],[21,24],[19,12],[11,10]],[[4499,6576],[-3,74],[-2,12],[-10,25],[7,16],[17,98],[1,39],[-3,16],[-6,9],[-8,6],[-8,10],[19,23],[15,24],[14,42],[7,9],[12,10],[4,5],[4,13],[8,8],[4,8],[8,12],[4,6],[8,55],[5,11],[58,59],[25,33],[5,5],[9,6],[5,6],[5,6],[5,12],[3,5],[16,15]],[[4727,7254],[2,-1],[24,-14],[21,-8],[31,-5],[93,18],[81,-14],[21,-24],[9,-25],[24,-27],[70,-23],[36,1],[74,-4],[72,-11],[40,4],[33,15],[73,149],[36,46],[7,39],[-8,21],[-72,40],[-20,29],[-16,32],[0,31],[25,24],[22,13],[26,22],[26,11],[29,22],[29,16],[35,27],[32,36],[12,40],[-2,35],[16,66],[12,18],[26,14]],[[4727,7254],[58,57],[7,14],[5,10],[7,28],[1,11],[4,8],[22,27],[9,19],[5,21],[3,71],[-2,4],[-12,5],[-3,5],[2,15],[4,11],[16,31],[5,13],[1,14],[-5,11],[-5,7],[-6,10],[-3,12],[-1,11],[12,21],[93,73],[71,79],[60,40],[22,18],[70,89],[79,81],[5,8],[0,9],[0,14],[6,9],[51,57],[5,3],[6,-2],[6,-6],[5,-1],[16,0],[11,3],[23,11],[22,17],[37,42],[22,17],[26,10],[86,18],[59,24]],[[3102,3154],[-3,-4],[-12,-48],[0,-35],[-13,-27],[-18,-69],[-25,-62],[-19,-83],[-19,-69],[-25,-110],[-25,-103],[-31,-97],[-25,-62],[-19,-34],[-44,-42],[-38,-27],[-43,-35],[-50,-34],[-69,-41],[-57,-36],[-23,-15]],[[2544,2121],[-73,15],[-54,4],[-52,-3],[-50,-10],[-206,-71],[-26,-6],[-26,3],[-53,20],[-87,23],[-68,13],[-52,3],[-103,-9],[-50,-12],[-128,-32],[-26,-1],[-29,10],[-22,23],[-19,29],[-26,21],[-66,24],[-68,18],[-8,3]],[[1252,2186],[8,16],[15,18],[15,13],[16,8],[12,10],[7,14],[6,45],[15,41],[6,32],[8,19],[2,10],[-1,11],[-2,10],[-1,11],[4,11],[-4,4],[4,10],[3,11],[1,11],[1,11],[-2,11],[-6,15],[-1,9],[0,61],[-4,17],[19,80],[4,11],[19,70],[29,40],[8,7],[5,7],[19,45],[4,31],[1,4],[6,12],[4,22],[25,64],[47,66],[17,30],[3,9],[2,14],[10,31],[5,10],[-2,11],[6,38],[-2,11],[-6,20],[2,7],[5,8],[6,20],[4,8],[35,45],[6,4],[30,7],[34,18],[51,51],[9,11],[6,12],[8,10],[14,7],[69,7],[12,6],[65,33],[114,81],[76,49],[37,41],[38,76],[13,36],[12,56],[18,40],[18,87],[12,27],[52,75],[0,4],[-4,6],[0,4],[8,5],[34,78],[20,70],[2,15],[3,12],[12,27],[2,12],[0,3],[4,12],[7,7],[19,9],[20,17],[23,7],[10,12],[26,42],[15,50],[7,15],[10,9],[12,7],[59,22],[113,19],[91,-2],[111,36],[171,29],[7,5],[14,14],[3,2],[11,2],[140,60]],[[3273,4680],[2,-2],[46,-94],[27,-39],[68,-124],[6,-30],[-79,-36],[-80,-49],[-9,-3],[-71,-60],[-67,-93],[-75,-222],[-68,-143],[-32,-46],[-109,-112],[-26,-45],[-20,-52],[-7,-27],[-3,-29],[2,-24],[8,-25],[40,-108],[32,-164],[244,1]],[[6676,5619],[-44,-5],[-92,-24],[-87,-9],[-52,-49],[-110,44],[-70,5],[-48,-5],[-70,-29],[-57,-58],[-71,4],[-13,-5],[-14,-1],[-14,0],[-27,5],[-29,1],[-22,-9],[-38,-37],[-10,-8],[-30,-15],[-38,-35],[-66,-43],[-57,-52],[-21,-15],[-71,-30],[-42,-28],[-42,-38],[-10,-6],[-22,-10],[-9,-7],[-22,-37],[-8,-8],[-28,-15],[-28,-22],[-80,-42],[-12,-10],[-7,-13],[-2,-19],[0,-31],[0,-41],[0,-42],[0,-41],[0,-41],[0,-42],[0,-41],[0,-41],[0,-41],[0,-42],[0,-41],[0,-41],[0,-42],[0,-41],[0,-41],[0,-42],[0,-41],[-44,0],[-40,0],[-1,0],[-1,-1],[4,-32],[18,-57],[7,-49],[-10,-30],[-8,-38],[4,-37],[14,-39],[12,-42],[0,-27],[-25,-21],[-59,-11],[-71,-10],[-51,0],[-78,7],[-48,-1],[-42,0],[-38,-7],[-46,-26],[-52,-42],[-65,-55],[-39,-34],[-51,-7],[-52,0],[-50,27],[-33,14],[-21,-1],[-35,-20],[-42,-13],[-39,0],[-64,28],[-78,41],[-45,20],[-65,7],[-64,14],[-46,-7],[-58,0],[-77,-28],[-65,-20],[-71,-21],[-80,-19],[19,-61],[28,-34],[0,-42],[-13,-35],[-39,-34],[-44,-44],[-26,-34],[-26,-48],[-19,-28],[-34,-45],[-29,-56],[-9,-35],[-12,-41],[-23,-12],[-79,-11],[-50,-14],[-44,-13],[-16,-24]],[[3273,4680],[186,80],[20,16],[15,19],[25,40],[10,10],[11,10],[9,11],[4,14],[6,11],[37,25],[29,35],[5,10],[26,15],[5,6],[5,9],[3,5],[22,16],[2,3],[13,21],[17,18],[10,6],[178,89],[43,28],[51,25],[24,16],[77,70],[32,48]],[[2544,2121],[-27,-19],[-43,-48],[-38,-69],[-25,-55],[-44,-90],[-31,-48],[-19,-27],[-50,-28],[-56,-21],[-63,-27],[-50,-28],[-69,-27],[-43,-28],[-32,-41],[-25,-48],[-31,-70],[-25,-75],[-12,-49],[-38,-165],[-13,-96],[-12,-62],[-19,-76],[-12,-118],[0,-96],[-13,-55],[-6,-42],[-31,-48],[-25,-34],[-44,-48],[-38,-28],[-12,-28],[-38,-34],[-37,-55],[-32,-34],[7,-28],[6,-48],[-19,-49],[-19,-55],[-50,-69],[-56,-34],[-81,-7],[-113,0],[-88,7],[-106,0],[-94,13],[-87,14],[-107,7],[-75,0],[-94,-13],[-244,0],[-93,-8],[-138,-27],[-40,-7],[1,17],[26,102],[2,30],[-2,26],[2,16],[10,19],[2,8],[1,8],[0,8],[-2,4],[-8,7],[-3,4],[4,10],[3,21],[12,28],[5,28],[5,16],[65,138],[23,24],[3,6],[1,5],[7,16],[4,4],[10,7],[4,4],[10,24],[7,10],[15,6],[4,3],[4,4],[7,0],[5,-2],[5,-4],[7,-8],[69,28],[12,10],[13,16],[9,17],[22,80],[2,12],[6,7],[31,32],[8,11],[8,44],[2,58],[3,15],[7,25],[5,11],[13,18],[0,7],[-2,6],[-2,4],[0,8],[1,5],[6,13],[1,4],[3,2],[14,2],[15,4],[20,18],[21,25],[6,13],[4,14],[3,15],[1,17],[-6,16],[-12,2],[-26,-7],[5,13],[11,22],[5,8],[30,28],[3,5],[46,110],[8,9],[6,4],[8,8],[28,48],[9,47],[7,13],[9,13],[22,41],[31,35],[2,4],[10,8],[4,5],[4,7],[3,14],[2,8],[10,15],[10,13],[8,13],[3,17],[1,31],[-2,13],[-8,15],[2,4],[2,6],[-7,-6],[-1,-8],[1,-9],[-1,-10],[-5,-5],[-16,-12],[-6,-3],[-8,0],[-5,2],[-6,-2],[-10,-9],[-7,-8],[-4,-8],[-10,-23],[-8,-15],[-1,-5],[-2,-8],[-6,-14],[-1,-6],[-5,-8],[-30,-27],[-9,7],[4,11],[14,17],[3,10],[23,44],[39,54],[4,11],[9,12],[10,11],[45,40],[21,7],[27,17],[8,9],[20,2],[17,10],[13,16],[10,15],[26,30],[17,26],[24,18],[10,10],[4,12],[6,8],[57,48],[34,37],[43,66],[16,19],[20,18],[12,8],[12,6],[13,4],[27,5],[14,7],[21,17],[-1,16],[2,4]]],
transform:{scale:[.0015983342188564096,.001450799885130351],translate:[-17.01374332534519,21.41997109758168]}},m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();