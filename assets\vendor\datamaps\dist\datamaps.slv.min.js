!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo={type:"Topology",objects:{slv:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Ahuachapán"},id:"SV.AH",arcs:[[0,1,2]]},{type:"Polygon",properties:{name:"Cabañas"},id:"SV.CA",arcs:[[3,4,5,6,7]]},{type:"Polygon",properties:{name:"Cuscatlán"},id:"SV.CU",arcs:[[-6,8,9,10,11]]},{type:"Polygon",properties:{name:"La Libertad"},id:"SV.LI",arcs:[[12,13,14,15,16,17]]},{type:"Polygon",properties:{name:"La Paz"},id:"SV.PA",arcs:[[18,19,-14,20,-10]]},{type:"Polygon",properties:{name:"Sonsonate"},id:"SV.SO",arcs:[[-16,21,-2,22]]},{type:"Polygon",properties:{name:"San Salvador"},id:"SV.SS",arcs:[[-11,-21,-13,23]]},{type:"MultiPolygon",properties:{name:"La Unión"},id:"SV.UN",arcs:[[[24]],[[25]],[[26,27,28]]]},{type:"Polygon",properties:{name:"Morazán"},id:"SV.MO",arcs:[[-28,29,30]]},{type:"Polygon",properties:{name:"San Miguel"},id:"SV.SM",arcs:[[-27,31,32,33,-4,34,-30]]},{type:"Polygon",properties:{name:"San Vicente"},id:"SV.SV",arcs:[[-34,35,36,-19,-9,-5]]},{type:"Polygon",properties:{name:"Usulután"},id:"SV.US",arcs:[[37,-36,-33]]},{type:"Polygon",properties:{name:"Chalatenango"},id:"SV.CH",arcs:[[-7,-12,-24,-18,38,39]]},{type:"Polygon",properties:{name:"Santa Ana"},id:"SV.SA",arcs:[[-39,-17,-23,-1,40]]}]}},arcs:[[[1514,6887],[35,-27],[75,-86],[15,-24],[7,-16],[5,-17],[9,-63],[4,-19],[6,-17],[15,-26],[17,-24],[15,-28],[0,-2],[9,-30],[4,-19],[-12,-139],[-83,-519]],[[1635,5831],[-73,-57],[-91,-46],[-50,-14],[-12,-14],[-9,-22],[-4,-50],[-1,-33],[10,-145],[7,-45],[8,-36],[9,-35],[4,-18],[-3,-27],[-84,-235],[-7,-42],[-2,-31],[34,-202],[5,-43],[-6,-31],[-12,-37],[-32,-68],[-12,-42],[-6,-33],[-3,-72],[-3,-19],[-53,-175],[-7,-37],[-24,-173],[-21,-15],[-34,-9],[-124,14],[-26,9],[-8,11],[-30,52],[-54,115],[-8,12],[-17,22],[-10,8],[-11,8],[-15,3],[-17,0],[-23,-8],[-14,-9],[-12,-10],[-10,-13],[-12,-18],[-31,-67],[-66,-190],[-9,-29]],[[666,3935],[-279,234],[-297,262],[-88,524],[-2,122],[10,126],[22,123],[33,113],[49,93],[114,110],[50,75],[39,109],[27,113],[35,109],[61,99],[397,509],[88,161],[44,53],[183,127],[61,8],[75,-36],[107,-153],[61,-45],[60,60],[2,43],[-4,13]],[[6681,5382],[-8,-13],[-18,-30],[26,-97],[-6,-92],[-29,-77],[-45,-52],[9,-108],[-38,-108],[-38,-135],[0,-2]],[[6534,4668],[-67,-83],[-39,19],[-104,24],[-15,9],[-8,14],[-5,16],[-4,19],[-5,42],[-6,18],[-12,11],[-28,14],[-10,8],[-28,31],[-44,28],[-10,9],[-75,103],[-10,9],[-45,15],[-57,6],[-27,9],[-132,-3],[-29,5],[-20,10],[-59,78],[-14,-2],[-15,-14],[-20,-50],[-14,-26],[-14,-18],[-25,4],[-29,14],[-27,-14],[-129,-104],[-40,-23],[-28,-9],[-43,26],[-24,11],[-27,2],[-11,-29],[-8,-24],[-6,-223]],[[5221,4600],[-142,72],[-38,39],[-4,19],[-26,75],[-40,78],[-17,41],[-10,35],[-5,50],[-6,30],[-20,67],[-5,32],[-4,35],[-14,61],[-20,41],[-34,42],[-26,49],[-80,209],[-20,67],[-10,49],[2,24],[3,21],[4,18],[7,17],[6,14],[9,12],[55,58],[15,24],[14,27],[12,34],[5,17],[61,176]],[[4893,6133],[122,7],[108,-19],[56,26],[63,44],[254,152],[49,42],[28,39],[31,77],[86,76],[190,53]],[[5880,6630],[-1,-14],[25,18],[106,29],[105,-12],[313,-184],[182,-25],[40,-31],[37,-66],[-4,-25],[-1,-18],[-2,-4],[-4,-6],[-1,1],[-17,4],[5,-18],[21,-85],[-3,-91],[-14,-94],[-9,-115],[12,-74],[44,-204],[3,-124],[-36,-110]],[[5221,4600],[-75,-368],[-14,-109],[-10,-36],[-9,-26],[-81,-145]],[[5032,3916],[-28,-31],[-23,-15],[-52,-11],[-11,-6],[-9,-10],[-7,-14],[-9,-38],[-6,-14],[-9,-8],[-8,-4],[-19,-5],[-14,-2],[-78,46],[-72,70],[-181,123]],[[4506,3997],[-4,234],[-9,38],[-7,42],[-20,46],[-72,250],[-6,37],[7,168],[-4,34],[-7,22],[-11,6],[-16,20],[-17,33],[-52,140],[-8,11],[-11,8],[-12,4],[-26,3],[-29,42],[-38,80],[-110,316],[-11,59],[7,14],[30,24],[11,14],[8,16],[7,29],[-3,17],[-26,59],[-86,258],[-39,148],[-8,58],[-5,67],[3,12],[6,11],[8,11],[7,13],[11,31],[4,19],[3,21],[3,46],[6,43],[2,31],[0,8],[1,15],[-4,75],[-12,128],[-2,34],[4,24],[20,75],[18,92],[2,76]],[[4019,7059],[73,69],[13,0],[14,-3],[52,-48],[107,-71],[20,-20],[30,-49],[70,-91],[6,-15],[19,-60],[91,-197],[7,-22],[2,-12],[0,-2],[-3,-33],[0,-21],[1,-20],[3,-19],[7,-36],[11,-42],[22,-59],[18,-32],[16,-18],[88,-77],[75,12],[64,-45],[68,-15]],[[3499,6943],[-4,-262],[-22,-167],[-2,-42],[3,-27],[25,-36],[10,-27],[12,-40],[21,-88],[12,-42],[11,-28],[18,-21],[19,-17],[34,-19],[12,-1],[38,6],[13,-1],[13,-3],[11,-5],[11,-8],[14,-14],[15,-23],[22,-21],[6,-16],[2,-13],[-11,-51],[-5,-43],[-2,-10],[-3,-5],[-31,1],[-11,-5],[-8,-14],[-4,-25],[-21,-433],[-6,-44],[-4,-18],[-9,-11],[-21,-14],[-9,-10],[-8,-12],[-13,-29],[-8,-38],[-5,-37],[-8,-135],[-9,-60],[-13,-57],[-8,-72],[-4,-124],[-4,-33],[-3,-20],[-29,-98],[-18,-97],[2,-31],[7,-17],[11,-5],[13,-2],[21,-49],[-4,-77],[3,-74],[4,-20],[6,-16],[7,-15],[15,-22],[8,-19],[5,-26],[8,-60],[6,-34],[8,-23],[9,-12],[59,-58],[7,-9],[18,-29],[28,-67],[4,-30],[-1,-24],[-14,-46],[-10,-43],[1,-24],[9,-17],[31,-28],[5,-19],[-2,-17],[-6,-15],[-9,-12],[-5,-45],[-4,-72],[8,-322],[-2,-24],[-26,-123],[-9,-67],[-1,-37],[2,-28],[66,-196],[23,-93],[7,-14],[9,-10],[12,-3],[13,2],[23,10],[43,26],[25,6],[15,-4],[11,-6],[30,-25]],[[4007,2569],[14,-126],[13,-33],[10,1],[17,-2],[23,-9],[40,-31],[14,-26],[3,-22],[-10,-34],[-20,-45],[-6,-16],[-6,-14],[-32,-105],[-12,-47],[-12,-71],[0,-1]],[[4043,1988],[-198,222],[-280,228],[-303,132],[-141,0],[-108,46],[-586,-9],[-251,115],[-89,32]],[[2087,2754],[11,49],[44,183],[22,66],[104,168],[19,38],[11,32],[5,93],[8,65],[10,38],[9,25],[8,15],[9,11],[90,165],[20,51],[11,37],[1,26],[-5,171],[7,90],[12,93],[9,42],[9,29],[8,13],[17,18],[9,5],[29,5],[16,13],[17,24],[49,110],[22,38],[61,65],[26,34],[30,48],[11,31],[6,29],[-2,21],[-4,19],[-6,16],[-7,13],[-8,11],[-10,9],[-47,27]],[[2718,4790],[19,217],[72,362],[8,165],[-4,140],[6,208],[5,43],[6,15],[9,11],[10,5],[11,-2],[10,-6],[12,3],[13,16],[33,97],[9,9],[12,2],[13,-1],[16,2],[21,17],[9,21],[4,24],[7,149],[-1,24],[7,113],[45,430]],[[3070,6854],[18,53],[13,53],[34,24],[49,17],[38,38],[40,56],[43,-16],[74,-78],[68,-17],[13,-8],[39,-33]],[[5032,3916],[31,-153],[2,-127],[-5,-132],[4,-53],[8,-31],[13,-3],[13,1],[13,3],[99,57],[17,1],[19,-5],[32,-23],[11,-25],[102,-399],[3,-22],[2,-23],[-1,-50],[-5,-44],[-32,-159],[-2,-18],[-4,-19],[-3,-24],[0,-12],[0,-3],[-17,-170],[0,-35],[6,-135],[5,-45],[6,-32],[27,-55],[25,-36],[36,-40],[28,-50],[28,-64],[9,-40],[4,-33],[-1,-24],[-6,-44],[-3,-19],[-6,-17],[-25,-36],[-7,-13],[-5,-17],[-3,-20],[-2,-23],[-1,-25],[19,-318],[-1,-44],[-6,-17],[-6,-14],[-8,-15],[-9,-11],[-38,-34],[-30,-23],[-19,-19],[-16,-24],[-7,-15],[-5,-16],[-5,-19],[-4,-19],[-3,-22],[-10,-234],[0,-1]],[[5299,835],[-964,823],[-292,330]],[[4007,2569],[9,66],[12,14],[30,61],[21,82],[35,193],[9,84],[2,54],[-8,38],[-31,103],[-12,63],[-8,67],[2,36],[6,24],[9,10],[11,3],[13,0],[13,-3],[66,-39],[13,-4],[12,-1],[21,29],[124,290],[150,258]],[[2087,2754],[-29,10],[-262,159],[-138,-53],[-391,-9],[-63,80],[-42,286],[-16,178],[-51,78],[-85,97],[-335,348],[-9,7]],[[1635,5831],[96,-44],[37,2],[23,11],[12,3],[13,0],[19,-17],[24,-33],[67,-119],[13,-31],[13,-56],[9,-22],[13,-27],[25,-42],[18,-55],[5,-40],[-1,-22],[-3,-21],[-6,-18],[-6,-13],[-16,-24],[-7,-15],[-5,-18],[-2,-19],[6,-17],[13,-15],[26,-9],[19,-35],[10,-26],[4,-24],[5,-18],[7,-15],[10,-13],[13,-10],[19,-7],[30,13],[15,14],[9,18],[3,18],[0,70],[5,43],[14,54],[10,34],[8,14],[8,11],[11,5],[28,-23],[40,-43],[87,-119],[55,-94],[25,-114],[6,-16],[6,-15],[53,-33],[197,-69]],[[3499,6943],[30,-8],[17,16],[53,71],[32,20],[86,-69],[57,-1],[26,96],[58,-84],[47,-21],[114,96]],[[9983,151],[16,-79],[-35,-35],[-53,35],[-36,-30],[1,84],[-22,84],[8,99],[-3,99],[30,39],[57,-34],[35,-144],[2,-118]],[[9734,482],[-27,-50],[-54,89],[-13,124],[24,45],[35,5],[33,-35],[27,-114],[-25,-64]],[[8354,109],[0,1],[23,609],[6,60],[11,76],[8,-7],[34,-46],[36,-39],[43,-28],[14,-5],[13,-1],[20,11],[9,18],[54,324],[3,48],[-6,45],[-2,32],[53,559],[-1,19],[-4,19],[-5,19],[-38,113],[-5,19],[-4,20],[-12,136],[-2,200],[3,50],[7,31],[7,11],[18,19],[11,15],[12,18],[15,34],[4,27],[0,25],[-16,112],[-2,235],[9,98],[27,66]],[[8697,3052],[128,251],[30,84],[7,146],[95,449],[25,209],[12,181],[1,148],[-4,47],[-3,19],[-4,19],[-6,17],[-19,44],[-5,17],[-3,21],[-1,25],[17,472],[-8,219],[-18,127],[-13,152]],[[8928,5699],[23,-9],[61,-72],[29,-15],[18,17],[54,75],[32,20],[41,-4],[81,-31],[39,5],[40,52],[35,79],[41,64],[62,4],[39,-56],[40,-204],[34,-92],[21,-24],[58,-41],[25,-26],[65,-103],[117,-184],[73,-54],[-37,-113],[-84,-352],[-20,-127],[26,-129],[-20,-83],[-41,-72],[-36,-96],[-15,-137],[3,-288],[-9,-142],[-99,-438],[-24,-215],[43,-178],[42,-28],[88,54],[60,-43],[32,-68],[21,-93],[7,-104],[-9,-100],[-72,-148],[-325,-273],[-3,1],[-32,35],[-21,64],[-31,167],[-116,-370],[-31,-218],[76,-97],[49,-34],[137,-241],[78,-97],[7,-45],[0,-94],[-34,-166],[-82,-119],[-307,-284],[-83,-122],[-15,-124],[95,-121],[-145,-62],[-569,72],[-106,37]],[[8697,3052],[-89,28],[-43,-6],[-161,-91],[-31,-6],[-20,6],[-6,13],[-7,21],[-4,16],[-10,21],[-13,23],[-61,67],[-9,12],[-6,16],[-5,17],[-12,139],[-6,41],[-15,52],[-18,42],[-15,18],[-16,9],[-159,-24],[-24,-9],[-21,-15],[-33,-36],[-23,-12],[-7,9],[0,13],[5,17],[24,60],[4,19],[1,20],[-6,20],[-10,17],[-29,35],[-10,14],[-6,15],[-4,18],[-3,21],[-2,24],[-9,30],[-15,37],[-39,53],[-15,27],[-7,47],[-14,38],[-53,87],[-21,45],[-13,36],[-7,196],[1,20],[4,17],[7,14],[136,179],[19,19],[32,21],[9,11],[8,12],[7,15],[3,23],[0,28],[-7,46],[-11,31],[-10,19],[-156,195],[-23,39],[-15,29],[-3,19],[-3,22],[-2,75],[3,44],[3,14],[2,13],[5,15],[13,29],[7,12],[9,10],[7,14],[6,16],[5,17],[4,19],[3,22],[-1,47],[-13,65],[-16,58],[-7,34],[-2,26],[-7,62],[-48,209],[-23,58]],[[7600,5910],[17,53],[27,55],[49,39],[37,-11],[26,11],[20,107],[-7,21],[-37,62],[-9,28],[-4,153],[2,27],[41,28],[62,-12],[9,-4],[100,-41],[66,-3],[215,47],[109,-11],[48,-72],[60,-289],[60,-131],[76,-127],[68,-148],[34,-192],[188,172],[62,30],[9,-3]],[[8354,109],[-28,11],[-28,0],[-30,-22]],[[8268,98],[-253,539],[-11,8],[-12,4],[-13,2],[-112,-32],[-10,4],[-8,15],[-1,39],[3,25],[4,25],[30,104],[-2,21],[-9,25],[-28,27],[-15,22],[-10,29],[-1,115],[-1,22],[-4,25],[-7,22],[-14,28],[-17,4],[-13,-3],[-39,-38],[-21,-17],[-12,-4],[-11,-2],[-12,4],[-10,7],[-9,10],[-16,25],[-9,10],[-10,8],[-12,1],[-10,-1],[-12,15],[-17,30],[-37,80],[-21,32],[-19,19],[-81,17],[-22,11],[-31,25],[-9,10],[-7,11],[-29,68],[-42,122],[-22,84],[-22,139],[-6,60],[-2,46],[0,50],[-2,24],[-6,20],[-17,10],[-19,-13],[-8,1],[-5,9],[-5,24],[-13,144],[-1,50],[-7,104],[-1,23],[1,23],[4,20],[6,14],[8,5],[6,-5],[5,-7],[5,-13],[7,-9],[8,-1],[6,12],[3,20],[2,48],[-1,48],[2,23],[3,20],[6,19],[6,14],[22,37],[1,3],[5,19],[2,17],[-2,30],[-17,102],[-3,41],[0,32],[8,61],[0,18],[-5,29],[-50,194],[-3,21],[-2,22],[-1,46],[2,23],[4,20],[4,17],[7,15],[10,9],[22,14],[9,8],[7,14],[6,16],[4,20],[2,20],[-2,28],[-6,32],[-12,55],[-8,65],[1,23],[2,23],[19,101],[1,22],[-14,20],[-26,14],[-100,23],[-32,20],[-36,47],[-28,49],[-35,100],[-25,59],[-49,5],[-169,-35]],[[6720,4116],[-48,135],[-127,234],[-11,183]],[[6681,5382],[110,10],[77,86],[75,58],[167,68],[39,-4],[66,-48],[30,-6],[5,23],[0,46],[3,48],[13,28],[27,3],[33,-10],[33,-19],[26,-20],[29,97],[45,30],[50,7],[45,28],[32,58],[14,45]],[[6720,4116],[-259,-347],[-24,-137],[-61,-154],[-83,-97],[-91,38],[-119,-270],[-6,-48],[-67,-21],[-24,-61],[-10,-236],[-35,-230],[-188,-658],[-17,-82],[-10,-98],[-6,-241],[-10,-53],[-72,-129],[-39,-105],[-25,-85],[-38,-58],[-81,-22],[-49,-34],[-32,-78],[-16,-110],[0,-15],[0,-1]],[[5358,784],[-59,51]],[[8268,98],[-39,-29],[-357,-55],[-382,107],[-100,-59],[-52,33],[-60,25],[0,48],[93,11],[6,120],[-50,148],[-75,97],[16,-60],[50,-126],[19,-79],[-149,9],[-73,-14],[-63,-48],[40,-32],[32,9],[39,21],[58,2],[0,-58],[-96,-73],[-106,6],[-82,93],[-26,191],[41,-33],[16,-20],[56,106],[0,48],[-71,59],[-77,201],[-52,59],[14,-113],[23,-92],[12,-81],[-18,-81],[-60,63],[-175,80],[-75,74],[-69,224],[-31,36],[-49,8],[-136,50],[-61,0],[-167,-58],[-233,-11],[-51,-42],[27,-41],[31,-29],[30,-9],[27,22],[117,-50],[331,66],[37,-11],[53,-53],[29,-50],[52,-115],[34,-48],[0,-58],[-77,24],[-56,20],[-123,67],[0,-53],[339,-128],[176,-105],[80,-143],[-29,-93],[-74,23],[-82,79],[-55,75],[-61,57],[-1066,364],[-128,109],[-2,2]],[[3070,6854],[-49,-25],[-62,0],[-61,22],[-46,77],[-13,167],[79,499],[78,-10],[10,7],[10,10],[8,14],[10,25],[48,161],[8,13],[8,11],[10,8],[11,5],[11,3],[43,5],[13,4],[11,6],[10,7],[10,10],[8,11],[15,26],[80,204],[7,36],[2,29],[-4,86],[1,156],[153,114],[37,74],[16,157],[53,281],[-75,88],[-20,48],[-9,13],[-62,59],[-14,22],[-9,17],[-19,56],[-13,59],[-6,15],[-27,55],[-157,269]],[[3174,9748],[159,-47],[128,-111],[211,-82],[105,-136],[190,-104],[39,65],[60,210],[33,62],[36,19],[24,-9],[21,-34],[23,-59],[21,-71],[9,-77],[-4,-80],[-16,-76],[47,-74],[141,-30],[66,-61],[26,-96],[17,-238],[18,-70],[105,-145],[38,-84],[37,-115],[16,-97],[2,-37],[4,-63],[14,-83],[36,-48],[40,16],[49,53],[58,45],[64,-2],[102,-120],[87,-199],[62,-232],[27,-220],[72,20],[181,-25],[50,35],[42,48],[36,7],[30,-88],[-4,-132],[-24,-128],[-3,-109],[65,-78],[88,-22],[20,-12],[29,-47],[21,-52],[9,-65],[-1,-40]],[[1514,6887],[-26,113],[-4,59],[11,64],[177,571],[83,165],[104,147],[108,91],[444,194],[26,48],[0,121],[-14,32],[-52,48],[-18,33],[-8,50],[-7,119],[-5,36],[-24,84],[-12,29],[-24,42],[-29,23],[-86,21],[-26,110],[25,65],[41,54],[26,76],[-12,91],[-30,77],[-17,87],[26,123],[59,80],[61,-9],[45,-84],[12,-144],[56,74],[102,225],[28,22],[45,35],[66,-15],[46,-45],[47,-32],[65,25],[94,177],[42,30],[5,-150],[22,59],[4,18],[120,-159],[64,-19]]],
transform:{scale:[.00024218276827683255,.0001286864520452029],translate:[-90.11477901199996,13.158636786000116]}},m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();