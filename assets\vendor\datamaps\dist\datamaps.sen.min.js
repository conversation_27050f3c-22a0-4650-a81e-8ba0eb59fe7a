!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo={type:"Topology",objects:{sen:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Sédhiou"},id:"SN.KD",arcs:[[0,1,2,3,4,5]]},{type:"Polygon",properties:{name:"Kédougou"},id:"SN.TC",arcs:[[6,7,8]]},{type:"Polygon",properties:{name:"Kaffrine"},id:"SN.",arcs:[[9,10,11,12,13,14,15]]},{type:"Polygon",properties:{name:"Saint-Louis"},id:"SN.SL",arcs:[[16,17,18]]},{type:"Polygon",properties:{name:"Dakar"},id:"SN.DK",arcs:[[19,20]]},{type:"Polygon",properties:{name:"Diourbel"},id:"SN.DB",arcs:[[21,-15,22,23]]},{type:"MultiPolygon",properties:{name:"Fatick"},id:"SN.FK",arcs:[[[24,25]],[[-14,26,27,28,-23]]]},{type:"Polygon",properties:{name:"Kaolack"},id:"SN.",arcs:[[29,-26,30,-27,-13]]},{type:"Polygon",properties:{name:"Louga"},id:"SN.LG",arcs:[[31,-16,-22,32,33,-18]]},{type:"Polygon",properties:{name:"Matam"},id:"SN.SL",arcs:[[34,-10,-32,-17,35]]},{type:"Polygon",properties:{name:"Thiès"},id:"SN.TH",arcs:[[-24,-29,36,-21,37,-33]]},{type:"Polygon",properties:{name:"Kolda"},id:"SN.KD",arcs:[[38,-8,39,-1,40]]},{type:"MultiPolygon",properties:{name:"Ziguinchor"},id:"SN.ZG",arcs:[[[41,-3]],[[42,-5]]]},{type:"Polygon",properties:{name:"Tambacounda"},id:"SN.TC",arcs:[[-9,-39,43,-11,-35,44]]}]}},arcs:[[[3562,2410],[-5,-100],[-14,-87],[-19,-87],[10,-80],[57,-54],[76,-20],[24,-53],[33,-121],[5,-73],[5,-54],[52,-47],[57,-53],[-4,-61],[4,-80],[34,-53],[28,-67],[0,-81],[0,-107],[-24,-73],[-14,-67],[13,-140]],[[3880,852],[-79,0],[-47,-9],[-42,-27],[-94,-93],[-99,-62],[-86,-88],[-5,-4],[-410,-264],[-56,-16],[-280,22]],[[2682,311],[0,1],[-7,13],[-50,102],[-16,49],[-30,59],[-23,68],[-5,60],[2,13]],[[2553,676],[25,-28],[14,-21],[19,-5],[104,-2],[10,-10],[21,-30],[8,-7],[14,3],[42,14],[18,16],[18,30],[23,18],[34,-20],[9,11],[11,7],[10,0],[22,-22],[10,2],[8,5],[9,-3],[13,-20],[11,-39],[8,-17],[11,-14],[11,-9],[15,-6],[20,-1],[18,5],[74,42],[30,42],[50,89],[19,19],[16,12],[12,16],[4,31],[-9,14],[-18,11],[-10,12],[14,18],[-16,52],[-14,69],[-6,71],[14,57],[19,16],[23,1],[25,-3],[22,3],[22,14],[16,16],[10,21],[7,27],[34,-44],[30,-13],[21,25],[4,63],[-25,-36],[-24,1],[-25,15],[-26,5],[-24,-15],[-40,-41],[-30,-8],[-16,0],[-24,-5],[-22,-16],[-10,-33],[0,-133],[18,-85],[3,-24],[1,-37],[-1,-18],[-5,-15],[-39,-47],[-13,-22],[-82,-103],[-21,-18],[-19,-2],[-17,11],[-15,16],[10,69],[-50,30],[-115,26],[-78,-68],[-29,-10],[-5,7],[-7,17],[-12,16],[-19,7],[-20,-5],[-28,-21],[-19,-5],[-19,7],[-25,17],[-40,38],[-2,11],[2,16],[1,15],[-6,6],[-22,4],[-11,5],[-7,5],[-11,19],[-22,61],[-6,7],[-7,5],[-6,7],[-7,49],[-8,26]],[[2458,965],[0,1],[-2,19],[3,32],[7,20],[13,141],[4,14],[8,15],[18,23],[4,12],[3,15],[2,47],[6,23],[-8,39],[-2,24],[1,26],[73,161],[66,186],[0,17],[-9,16],[-11,8],[-12,5],[-11,8],[-2,19],[8,27],[28,43],[16,36]],[[2661,1942],[43,-1],[61,0],[11,11],[3,162],[0,36],[3,161],[6,33],[19,14],[113,15],[73,32],[21,0],[109,-14],[27,3],[49,23],[37,28],[38,20],[51,-4],[177,-51],[60,0]],[[9151,2454],[4,-25],[14,-5],[43,-29],[13,-13],[6,-17],[10,-44],[7,-15],[20,-22],[9,-2],[9,13],[68,71],[4,44],[10,47],[16,38],[25,18],[23,-5],[12,-18],[9,-22],[16,-18],[16,-2],[31,9],[41,2],[8,-10],[-2,-33],[4,-22],[18,-3],[20,7],[12,8],[0,-50],[12,-44],[40,-77],[37,-44],[6,-22],[-2,-25],[-21,-41],[-6,-25],[5,-41],[16,-28],[19,-24],[15,-30],[1,-80],[8,-37],[29,-9],[22,-19],[19,-20],[21,-17],[26,-9],[17,-10],[-2,-16],[-9,-17],[-3,-13],[23,-45],[9,-27],[6,-15],[6,-7],[15,-9],[-3,-10],[-9,-10],[-5,-9],[-1,-19],[-3,-45],[-5,-23],[25,-6],[19,11],[12,26],[4,39],[14,-20],[7,-23],[4,-26],[3,-59],[-5,-14],[-11,-9],[-14,-15],[-7,-1],[-10,8],[-12,5],[-10,-7],[-1,-14],[7,-13],[10,-11],[3,-12],[-6,-24],[-10,-23],[-6,-24],[6,-37],[-3,-10],[-5,-9],[-4,-13],[1,-14],[6,-5],[8,-2],[4,-2],[2,-2],[6,-10],[7,-16],[5,-20],[4,-23],[1,-27],[-3,-25],[-12,-16],[0,-11],[17,-42],[3,-21],[-13,-30],[-22,-5],[-22,-10],[-17,-60],[-13,-12],[-10,-17],[2,-34],[6,-5],[12,-7],[13,-12],[8,-23],[0,-24],[-8,-72],[1,-24],[4,-16],[2,-16],[-4,-27],[-33,-21],[-12,-15],[18,-8],[12,-11],[18,-45],[14,-16],[-1,17],[9,24],[12,7],[10,-36],[4,-20],[10,-37],[3,-20],[13,28],[3,13],[21,-40],[-3,-53],[-11,-60],[-3,-61],[-45,3],[-107,53],[-54,7],[-140,-14],[-68,-18],[-125,-64],[-61,-14],[-133,8],[-33,10],[-98,57],[-3,4],[-3,2],[-11,0],[-7,-4],[-18,-17],[-62,-47],[-21,-6],[-35,5],[-98,45],[-40,-2],[-29,-20],[-54,-67],[-60,-47],[-274,-98],[-27,18],[-46,98],[-29,29],[-70,35],[-34,11],[-28,2],[-28,-8],[-31,-17],[-48,-36],[-12,-5],[-2,0],[-23,1],[-6,8],[-1,16],[-9,22],[-46,70],[-26,30],[-32,18],[-33,5],[-105,-8],[-34,6],[-9,19],[-7,22],[-26,17],[-48,5],[-16,9],[-17,21],[-3,20],[2,15],[1,9],[-40,49],[-64,45],[-59,-1],[-27,-91],[-5,-44],[-19,-22],[-27,-3],[-29,16],[-31,-5],[-28,4],[-24,18],[-19,37],[-7,44],[6,38],[24,77],[6,39],[2,46],[-7,43],[-21,31],[-25,5],[-28,-6],[-30,-1],[-29,20],[-15,-25],[-13,12],[-11,2],[-11,-3],[-15,0],[-20,-8],[-7,-2],[-7,5],[-16,23],[-9,6],[-12,-1],[-27,-10],[-14,-3],[-15,3],[-20,18],[-13,5],[-15,-2],[-48,-14],[-8,-3],[-4,-7],[-4,-5],[-10,-1],[-8,4],[-14,13],[-7,5],[-5,2]],[[6781,785],[5,35],[-13,44],[-21,14],[-10,-2],[-8,-6],[-13,-6],[-17,-5],[-37,0],[-21,5],[-17,8],[-21,15],[-13,13],[-11,19],[-3,14],[2,12],[8,20],[3,11],[3,84],[-1,14],[-5,14],[-4,20],[-11,17],[-4,16],[-1,14],[2,11],[4,11],[9,20],[4,10],[1,13],[-5,9],[-8,9],[-1,7],[6,2],[8,-2],[10,1],[6,5],[3,15],[-7,16],[-6,55],[-7,8],[-22,21],[-15,24],[-6,18],[-5,36],[-8,16],[-3,12],[2,10],[5,9],[4,13],[-8,71],[-2,8],[-8,5],[-10,3],[-10,7],[-4,9],[2,10],[5,10],[2,14],[-3,8],[-12,16],[-12,22],[-18,26],[-25,21],[-3,11],[0,14],[1,14],[-1,15],[-6,8],[-8,6],[-9,11],[-22,33],[-12,34],[-2,21],[-2,38],[-7,18],[-1,14],[1,13],[3,13],[0,36],[1,17],[-2,49],[-3,15],[-6,10],[-7,7]],[[6354,2131],[-1,20],[38,25],[1,121],[28,27],[-6,-38],[10,-17],[19,-7],[16,4],[9,27],[8,14],[24,16],[33,12],[20,-9],[-10,-49],[20,11],[14,54],[15,13],[66,0],[63,-17],[20,12],[17,14],[15,-3],[15,-37],[-16,-37],[-13,-20],[-7,-19],[1,-35],[7,-18],[4,-17],[1,-19],[5,-9],[13,1],[10,1],[5,-8],[4,-14],[11,-10],[11,-7],[8,-9],[1,-11],[-6,-21],[10,-22],[6,-31],[-7,-32],[-3,-29],[21,-25],[-66,-32],[0,-14],[33,-21],[6,-56],[-3,-69],[8,-58],[31,-50],[32,-11],[71,28],[46,24],[24,0],[48,-6],[43,-20],[28,-34],[29,0],[14,34],[14,20],[34,0],[38,26],[28,20],[91,0],[33,-20],[29,-40],[24,0],[38,-53],[43,-40],[33,-14],[14,34],[0,26],[15,20],[28,0],[24,14],[5,40],[14,20],[34,-7],[23,-13],[24,7],[0,40],[15,40],[38,33],[23,41],[-9,53],[0,54],[24,26],[33,7],[33,0],[29,20],[19,20],[24,-20],[24,-20],[38,-7],[52,20],[29,27],[19,47],[5,47],[19,33],[33,7],[10,34],[24,26],[42,14],[39,-14],[23,-40],[19,-20],[19,7],[10,40],[14,27],[10,53],[19,14],[52,-14],[43,0],[24,40],[-10,54],[-47,80],[-10,40],[24,34],[0,53],[38,20],[48,-40],[43,-47],[47,-33],[48,-40],[57,-20],[43,-20],[10,-47],[9,-54],[34,0],[42,40],[43,67],[58,20],[94,24]],[[4341,5317],[30,-36],[43,-28],[264,-107],[26,-14],[10,-15],[3,-28],[0,-26],[-3,-35]],[[4714,5028],[-2,-429],[37,-149],[21,-42],[9,-40],[7,-173],[15,-101],[3,-45],[0,-38],[-15,-94],[-7,-18],[-6,-12],[-16,-11],[-14,-7],[-7,-5],[-2,-4],[-6,-40],[1,-41],[-4,-27],[-3,-17],[-4,-11],[-96,-125],[-38,-42],[-8,-6],[-74,-25],[-16,-11],[-10,-2],[-21,-1],[-19,-5],[-17,-9],[-22,-33],[-20,-37],[-29,-70],[-6,-29]],[[4345,3329],[-31,34],[-60,27],[-164,10],[-96,50],[-35,3],[-118,-60],[-123,-107],[-34,-12],[-14,15],[-31,57],[-16,20],[-70,15],[-75,-39]],[[3478,3342],[-83,53],[-47,0],[-65,8],[-111,66],[-36,-74],[-29,-66],[-30,-17],[-41,25],[-47,66],[-24,66],[0,58],[18,58],[-6,58],[-71,58],[-59,83],[-17,49],[-6,58],[-24,42],[-70,16],[0,116],[0,149],[17,83],[42,124],[29,99],[2,220]],[[2820,4740],[43,46],[38,63],[64,101],[42,5],[37,27],[76,84],[34,38],[37,10],[79,16],[87,21],[60,21]],[[3417,5172],[49,74],[35,209]],[[3501,5455],[130,-107],[82,-42],[247,-68],[56,-7],[38,12],[31,17],[144,120],[28,18],[26,9],[20,-22],[38,-68]],[[6197,8732],[-68,-192],[-165,-91],[-24,-83],[-182,-223],[-259,-290],[-242,-256],[-65,66],[-124,83],[-135,99],[-71,133]],[[4862,7978],[-82,165],[-183,174],[-85,-8],[-130,95],[-28,13],[-929,2],[-12,-152],[-255,-24],[-18,15],[-334,519],[-33,43],[-21,17],[-10,-17],[-6,-8],[-27,-26],[-31,-22],[-7,-7],[-5,-8],[-7,-23],[-16,-39],[-12,-18],[-10,-11],[-307,-234],[0,-1],[-8,-6],[-57,-64],[-11,-17],[-4,-9],[-4,-11],[-5,-23],[-4,-11],[-8,-9],[-70,-50],[-17,-18],[-10,-28],[-21,-75],[-7,-16],[-12,-18],[-23,-5],[-25,-2],[-28,-6],[-23,-11],[-35,-35],[-30,-14],[-31,-7],[-263,-8],[-2,0]],[[1616,7980],[-3,7],[37,85],[16,126],[4,248],[10,27],[22,100],[28,92],[7,155],[12,45],[24,28],[60,20],[25,22],[10,23],[7,27],[67,386],[8,91],[12,43],[23,42],[30,40],[34,29],[37,10],[54,-14],[18,-2],[19,4],[16,11],[48,46],[19,12],[19,5],[21,-6],[15,-12],[12,-17],[19,-40],[28,-39],[33,-22],[36,-6],[39,8],[12,11],[11,8],[12,3],[14,-4],[21,-12],[10,-1],[12,3],[64,39],[23,4],[57,-19],[14,0],[40,10],[26,-2],[73,-33],[53,-4],[14,-5],[38,-25],[25,-9],[25,2],[24,11],[23,19],[36,45],[19,16],[25,6],[69,-20],[24,3],[20,13],[15,20],[40,77],[20,24],[23,14],[25,-3],[15,-17],[10,-23],[11,-21],[20,-12],[24,1],[22,8],[69,38],[25,8],[52,2],[77,-16],[26,1],[175,37],[34,18],[10,9],[7,13],[1,17],[-5,15],[-27,34],[-9,31],[3,29],[14,23],[23,8],[24,-9],[14,-22],[13,-27],[17,-21],[12,-6],[12,2],[11,7],[10,10],[8,14],[4,15],[5,31],[10,22],[17,15],[36,20],[19,-13],[21,-30],[2,-4],[12,-34],[17,-28],[30,-7],[49,16],[278,-27],[18,3],[55,20],[16,0],[15,-5],[45,-29],[18,-5],[18,1],[68,26],[17,1],[66,-19],[18,0],[17,2],[17,7],[16,10],[43,34],[15,9],[32,2],[103,-55],[13,-11],[6,-17],[0,-25],[-9,-44],[3,-17],[15,-9],[62,7],[16,-10],[6,-18],[2,-40],[8,-18],[17,-11],[39,-4],[18,-4],[14,-11],[27,-49],[12,-14],[38,-37],[55,-87],[97,-119],[54,-42],[11,-14],[28,-46],[14,-13],[32,-17],[15,-11],[10,-15],[1,-16],[-10,-35],[-3,-20],[3,-14],[16,-29],[7,-20],[1,-19],[-8,-41],[6,-37],[27,-20],[64,-25],[13,-11],[26,-27],[11,-15],[8,-19],[13,-61],[8,-22],[10,-20],[13,-17],[15,-12],[14,-5],[13,2],[13,6],[26,21],[27,27],[11,17],[28,56],[13,17],[13,10],[15,6],[15,1],[15,-3],[-3,-29],[-9,-38],[-8,-30],[6,-24]],[[629,5261],[-48,91],[-47,59],[-28,26],[-31,20],[-36,8],[-26,10],[-69,50],[-10,17],[-54,-1],[-47,8],[-40,-4],[-37,-36],[14,-22],[19,-8],[-12,-33],[-21,-13],[6,-8],[10,-16],[5,-7],[-10,-33],[-5,-2],[-6,-14],[-21,19],[-109,185],[-26,33],[268,98],[79,88],[228,132]],[[575,5908],[2,-3],[33,-102],[10,-42],[1,-12],[1,-54],[6,-21],[25,-59],[5,-20],[2,-21],[-30,-311],[-1,-2]],[[2279,6072],[52,62],[38,13],[398,4],[25,-7],[-1,-20],[1,-30],[2,-14],[3,-12],[5,-10],[6,-6],[7,-5],[12,-7],[5,-5],[5,-8],[10,-34],[4,-10],[6,-8],[7,-6],[41,-24],[27,-26],[5,-9],[4,-10],[2,-13],[2,-30],[7,-30],[29,-31],[42,18],[86,4],[45,-6],[7,-5],[16,-15],[13,-11],[24,-11],[15,-3],[108,13],[21,-2],[14,-5],[6,-9],[4,-11],[8,-137],[11,-50],[42,-63],[58,-48]],[[3417,5172],[-22,80],[-38,42],[-215,117],[-53,31],[-37,53],[-30,6],[-46,-22],[-34,-5],[-80,80],[-7,-7],[-21,-14],[-8,-7],[-6,-10],[-4,-10],[-3,-12],[-7,-194],[-5,-26],[-4,-12],[-4,-9],[-88,-119],[-7,-7],[-40,-26],[-55,-18],[-136,-20],[-23,0],[-177,43],[-23,10],[-16,9],[-18,25],[-21,19],[-15,11],[-54,24],[-54,8],[-17,-1],[-85,-25],[-11,0],[-12,3],[-14,11],[-9,9],[-13,16],[-7,8],[-16,4],[-138,11],[-72,-9],[-21,-10],[-20,-42]],[[1631,5187],[-34,14],[-82,50],[-19,17],[-5,8],[-4,11],[-2,12],[0,13],[7,16],[11,21],[24,35],[9,22],[4,20],[-1,14],[-3,13],[-4,10],[-5,9],[-7,6],[-16,10],[-6,7],[-5,8],[-3,10],[1,20],[2,18],[5,25],[1,10],[-1,10],[-4,10],[-5,7],[-6,8],[-7,6],[-16,9],[-6,7],[-4,11],[-2,14],[-3,26],[1,13],[4,24],[1,57],[-2,14],[-3,12],[-5,9],[-6,7],[-8,4],[-15,5],[-7,6],[-9,12],[-3,7],[-2,10],[8,51],[10,36],[15,33],[30,22],[391,153],[7,-4],[4,-5],[4,-5],[19,-16],[15,-7],[11,-3],[15,0],[23,4],[13,5],[7,3],[2,2],[1,1],[3,1],[6,0],[8,-3],[55,-60],[7,-6],[27,-14],[10,-8],[6,-11],[3,-12],[1,-13],[1,-12],[4,-12],[4,-9],[7,-7],[8,-5],[11,-4],[14,-3],[21,2],[19,7],[78,55],[14,14],[6,28]],[[2355,2917],[-66,0],[-101,1],[-100,1],[-101,0],[-101,1],[-101,0],[-101,1],[-101,0],[-1,1],[-16,33],[-8,24],[1,64],[28,36],[41,28],[41,45],[-9,6],[-16,17],[-9,6],[20,27],[15,35],[-2,28],[-33,5],[4,-36],[-8,-44],[-17,-31],[-24,2],[-11,-48],[-25,-32],[-32,-12],[-31,13],[-29,41],[-7,42],[13,90],[4,56],[4,23],[15,30],[57,93],[10,33],[10,-9],[25,-16],[9,-8],[-3,10],[-9,38],[57,14],[3,-45],[11,-43],[19,-25],[23,6],[-12,52],[-3,57],[-7,46],[-24,16],[8,31],[36,212],[2,37],[-35,-71],[-15,-45],[-13,-94],[-17,-57],[-21,-37],[-22,8],[-12,0],[-16,-30],[-29,-12],[-33,-6],[-32,-14],[-17,-20],[-6,-19],[-3,-19],[-8,-21],[-9,-11],[-18,-15],[-11,-12],[-16,-42],[-13,-16],[-16,11],[-5,22],[0,28],[-3,24],[-15,11],[-21,20],[-8,45],[2,48],[11,28],[-10,32],[-7,35],[-5,73],[1,39],[6,22],[10,15],[37,41],[19,10],[21,1],[48,-7],[23,-10],[15,-18],[-2,-30],[23,28],[18,33],[22,19],[37,-18],[0,50],[12,22],[21,14],[22,24],[-20,-10],[-22,-6],[-20,6],[-16,24],[73,63],[27,32],[42,71],[23,27],[52,21],[25,44],[20,10],[13,-9],[13,-16],[14,-10],[16,11],[10,10],[26,17],[1,1]],[[1890,4234],[104,-4],[9,-2],[9,-7],[9,-10],[47,-41],[22,-26],[11,-18],[11,-25],[7,-50],[1,-29],[-2,-25],[-5,-17],[-17,-34],[-6,-17],[1,-19],[33,-58],[21,-25],[10,-23],[18,-156],[1,-18],[-2,-17],[-5,-18],[-8,-17],[-18,-33],[-24,-28],[-9,-14],[-4,-16],[-2,-16],[-1,-33],[2,-19],[6,-20],[16,-23],[51,-59],[26,-21],[12,-12],[10,-19],[7,-30],[7,-74],[6,-33],[9,-22],[14,-23],[52,-65],[22,-40],[9,-45],[5,-15],[0,-1]],[[2820,4740],[-51,-44],[-121,5],[-79,5],[-34,-21],[-30,-42],[-34,-16],[-138,32],[-8,10],[0,12],[-15,2],[-83,-10],[-98,-29],[-52,-7],[-16,-11],[-19,-21],[-44,-72],[-82,-166],[-16,-77],[-23,-43]],[[1877,4247],[-77,4],[-44,23],[3,49],[-26,-18],[-10,-33],[5,-35],[19,-23],[-21,-15],[-16,2],[-17,8],[-19,5],[-13,-7],[-6,-17],[-3,-21],[-5,-18],[-42,-101],[-15,-24],[-16,-10],[-35,-12],[-20,-17],[-14,-26],[-20,-52],[-17,-23],[-36,-17],[-35,10],[-35,16],[-39,5],[38,94],[2,16],[27,11],[45,69],[-22,-9],[-21,-44],[-18,-11],[-36,-1],[-14,-5],[-12,-10],[19,-30],[-1,-20],[-28,-44],[-5,-18],[-4,-32],[-8,-20],[-32,-50],[-7,-20],[-8,-64],[8,-216],[-10,0],[-10,437],[-8,66],[-27,110],[-14,39]],[[1177,4148],[13,18],[45,21],[8,11],[2,12],[-4,9],[-6,10],[-4,11],[-1,19],[5,11],[42,48],[7,18],[0,14],[-5,9],[-7,7],[-6,7],[-5,10],[23,131],[82,322],[5,4],[8,3],[54,5],[9,3],[8,5],[6,7],[6,8],[16,27],[13,14],[8,5],[17,8],[14,12],[27,38],[42,77],[9,19],[11,47],[12,69]],[[3478,3342],[-69,-69],[-50,-75],[-18,-39],[-17,-48],[-13,-50],[-2,-15],[-4,-31],[-3,-91],[-26,-11],[-79,0],[-101,0],[-101,1],[-101,1],[-101,0],[-100,1],[-101,0],[-101,1],[-101,0],[-35,0]],[[1890,4234],[12,12],[-25,1]],[[4862,7978],[-129,-166],[-65,-58],[-83,-41],[-47,-25],[-12,-414],[-58,-124],[271,-190],[135,25],[-53,-314],[-29,-249],[69,-47],[-128,-52],[-183,-33],[-112,-58],[47,-223],[71,-215],[-59,-166],[-71,-66],[-23,-182],[-62,-63]],[[2279,6072],[-35,25],[-37,20],[-8,5],[-8,9],[-9,12],[-11,23],[-5,17],[-4,16],[-5,41],[-3,12],[-4,12],[-9,19],[-47,78],[-9,20],[-16,55],[-3,23],[-2,19],[1,20],[2,9],[3,59],[-4,30],[-7,24],[-6,15],[-5,9],[-14,43],[-5,9],[-6,4],[-9,-4],[-15,-15],[-6,-5],[-8,-1],[-7,5],[-6,13],[-5,30],[-10,108],[-2,12],[-3,10],[-7,8],[-9,6],[-16,2],[-9,-1],[-24,-8],[-20,-2],[-76,26],[-12,0],[-11,-4],[-7,-6],[-6,-8],[-5,-8],[-14,-28],[-16,-26],[-18,-36],[-11,-17],[-25,-44],[-10,-35],[-4,-25],[-6,-22],[-9,-19],[-73,-105],[-9,-19],[-21,-34],[-6,-7],[-7,-6],[-8,-2],[-8,2],[-8,7],[-7,12],[-8,19],[-2,37],[2,10],[3,7],[3,9],[3,9],[1,10],[-2,13],[-5,11],[-11,18],[-9,9],[-26,17],[-14,12],[-7,7],[-5,8],[-20,36],[-10,29],[-5,11],[-6,9],[-9,9],[-20,11],[-13,5],[-9,25],[-5,44],[8,96],[12,53],[29,79],[8,44],[0,15],[-1,13],[-8,26],[-7,17],[-49,53],[-8,6]],[[1300,7231],[107,193],[212,484],[8,23],[-3,24],[-8,25]],[[7869,6354],[4,-17],[170,-309],[12,-37],[7,-53],[0,-17],[-3,-20],[-124,-428],[-133,-277],[-20,-30],[-20,-14],[-244,-42],[-70,-2],[-438,111],[-18,-8],[-13,-9],[-426,-419],[-18,-11],[-444,-10],[-80,17],[-707,366],[-27,21],[-29,31],[-15,43],[-71,90],[-67,-29],[-24,-17],[-30,-35],[-48,-79],[-78,-75],[-201,-67]],[[6197,8732],[7,-7],[17,-9],[8,-7],[1,-4],[3,-8],[2,-3],[4,-3],[10,-6],[4,-3],[11,-13],[4,-2],[11,-3],[10,4],[45,32],[9,3],[31,0],[6,1],[12,5],[49,29],[86,25],[22,12],[9,4],[9,0],[11,-3],[3,-32],[-28,-69],[-3,-35],[17,-16],[59,32],[25,-1],[8,-12],[13,-31],[8,-13],[44,-41],[10,-16],[4,-18],[2,-40],[2,-18],[23,-76],[10,-24],[12,-22],[15,-20],[41,-42],[9,-18],[4,-19],[-1,-19],[-7,-39],[0,-18],[4,-16],[7,-13],[29,-47],[15,-35],[7,-39],[1,-44],[-4,-19],[-5,-20],[-4,-19],[2,-19],[9,-16],[11,-12],[25,-20],[13,-14],[11,-16],[18,-37],[7,-18],[2,-17],[-3,-17],[-24,-52],[-6,-19],[-2,-20],[3,-19],[15,-57],[24,1],[49,12],[24,-2],[11,-6],[30,-30],[12,-6],[38,-14],[20,-16],[17,-22],[12,-27],[4,-32],[-9,-86],[0,-20],[3,-19],[6,-21],[9,-10],[50,-17],[23,4],[24,21],[22,23],[24,18],[25,5],[26,-16],[10,-17],[5,-20],[7,-63],[5,-16],[16,-29],[8,-17],[3,-18],[0,-76],[6,-37],[14,-35],[19,-29],[97,-90],[21,-30],[10,-37],[-9,-32],[-26,-6],[-56,8],[-16,-17],[9,-28],[37,-51],[15,-15],[14,-7],[16,0],[34,9],[17,0],[17,-6],[15,-14],[8,-16],[4,-17],[0,-19],[-8,-58],[1,-18],[5,-20],[2,-6],[41,-18],[12,-17],[48,-63],[44,-26],[1,1]],[[1177,4148],[-16,42],[-14,24],[-21,14],[-43,13],[-16,20],[-8,33],[-14,123],[-11,39],[-13,28],[-30,43],[-14,11],[-11,3],[-7,9],[-3,30],[2,23],[9,45],[1,26],[-7,38],[-20,40],[-51,71],[-116,75],[-18,26],[-9,30],[-58,118],[-42,157],[-18,32]],[[575,5908],[15,9],[55,61],[258,420],[52,105],[113,171],[226,546],[6,11]],[[5932,2732],[21,13],[15,8],[18,3],[9,2],[6,8],[4,10],[2,12],[5,10],[6,6],[8,3],[17,-3],[18,0],[8,-4],[4,-8],[-1,-14],[-6,-23],[0,-22],[-1,-7],[-1,-4],[-7,-11],[-4,-9],[-3,-11],[0,-13],[4,-13],[11,-12],[10,-2],[8,3],[14,12],[9,4],[9,3],[10,-1],[9,-3],[24,-12],[17,-7],[5,-4],[2,-6],[-4,-12],[-4,-9],[-30,-40],[-4,-9],[-4,-11],[-2,-12],[0,-13],[0,-13],[0,-14],[-2,-13],[-5,-10],[-4,-9],[-5,-8],[-5,-9],[-2,-10],[4,-11],[10,-8],[10,-4],[11,-2],[10,0],[10,1],[40,10],[21,1],[9,-4],[5,-7],[0,-15],[-4,-11],[-14,-28],[-2,-9],[3,-11],[10,-9],[8,-7],[6,-10],[1,-18],[-2,-14],[-4,-25],[1,-13],[7,-13],[15,-16],[24,-11],[7,-7],[5,-12],[2,-19],[3,-17],[5,-14],[12,-12],[10,-2],[20,0]],[[6781,785],[-73,27],[-525,26],[-576,4],[-423,2],[-153,1],[-575,4],[-131,0],[-445,3]],[[3562,2410],[53,13],[53,27],[46,43],[34,59],[10,64],[6,141],[23,50],[36,52],[34,49],[37,20],[45,-40],[73,-94],[80,-80],[1,0],[0,-1],[106,-74],[55,-20],[62,-7],[25,-10],[54,-45],[30,-14],[33,-8],[18,-15],[32,-63],[46,-55],[54,-32],[59,-10],[60,10],[53,18],[18,-9],[47,-59],[15,-19],[22,-17],[23,-10],[51,-3],[22,-8],[18,-26],[12,-23],[16,-19],[17,-16],[59,-42],[43,-16],[43,-5],[93,11],[88,-23],[47,2],[35,25],[78,80],[25,15],[28,0],[70,35],[65,1],[65,24],[123,14],[80,49],[46,96],[7,117],[-39,108],[-17,18],[-36,30],[-12,14]],[[2682,311],[-16,2],[-129,-13],[-31,8],[-101,47],[-58,7],[-123,-30],[-11,-6],[-7,-8],[-7,-4],[-14,3],[-35,19],[-17,5],[-17,-5],[-112,-84],[-130,-96],[-49,-28],[-82,-1],[-85,-29],[-30,-2],[-220,21],[-60,-20],[-37,-36],[-10,29],[-8,30],[-7,47],[-10,15],[-25,24],[-37,50],[-17,30],[-7,29],[0,95],[4,21],[16,20],[3,20],[12,24],[81,67],[35,2],[51,-6],[54,46],[18,20],[9,19],[7,19],[11,22],[62,62],[30,-48],[23,-86],[22,-20],[1,36],[17,27],[28,12],[32,-10],[55,-43],[49,-18],[54,-35],[28,-3],[32,21],[39,66],[28,22],[38,-2],[32,-17],[32,-4],[38,31],[18,11],[13,-20],[10,-29],[9,-17],[126,65],[29,10],[39,3],[25,10],[22,21],[24,17],[35,-2],[29,-21],[43,-47]],[[2458,965],[-10,0],[0,-17],[-2,-9],[-18,-47],[-7,-14],[-6,-78],[-46,-40],[-66,-16],[-17,-10],[-34,-30],[-10,-6],[-17,3],[-39,14],[-62,0],[-18,-6],[-31,-19],[-17,-6],[-20,0],[-52,14],[-18,-5],[-22,-21],[-15,-5],[-6,-11],[-8,-25],[-11,-25],[-14,-16],[-67,30],[-54,42],[-25,29],[-11,31],[-6,5],[-15,7],[-15,10],[-8,16],[3,23],[10,20],[15,14],[16,5],[0,17],[-25,1],[-15,17],[-9,28],[-6,32],[-11,-188],[-12,0],[-22,40],[-59,71],[-8,45],[-19,-21],[-12,-26],[-15,-21],[-26,-9],[-19,6],[-13,15],[-7,25],[1,31],[23,59],[33,45],[21,52],[-10,80],[-7,-62],[-4,-17],[-9,-20],[-8,-10],[-7,-7],[-33,-41],[-13,-23],[-7,-30],[-2,-48],[12,-81],[-4,-37],[-49,-29],[-71,-82],[-54,-26],[-30,24],[-13,59],[-3,78],[9,80],[26,80],[42,45],[57,-26],[2,34],[-23,25],[-34,9],[-28,-13],[-34,-57],[-18,-18],[-21,6],[10,19],[3,19],[-3,20],[-10,19],[36,67],[7,34],[-8,40],[-13,0],[-7,-25],[-11,-11],[-10,8],[-5,35],[4,27],[10,30],[58,118],[6,29],[-2,29],[-8,54],[-1,26],[3,47],[16,95],[4,61],[-3,37],[-7,19],[3,53],[6,25],[10,19],[26,35],[5,22],[24,55],[56,18],[71,-1],[268,-4],[246,-3],[360,-4],[211,-3],[105,-1]],[[5932,2732],[-3,4],[-9,19],[-10,43],[-8,20],[-28,30],[-39,28],[-41,19],[-34,9],[-22,-5],[-33,-27],[-19,-12],[-17,-3],[-28,3],[-18,-1],[-32,-11],[-76,-53],[-93,-29],[-31,-18],[-90,-77],[-95,-47],[-29,-10],[-29,6],[-39,25],[-118,116],[-13,13],[-17,25],[-10,30],[-22,99],[-15,44],[-22,38],[-32,34],[-69,45],[-67,7],[-66,-24],[-100,-77],[-20,-9],[-22,12],[-67,55],[-11,17],[-32,148],[-14,42],[-21,39],[-26,30]],[[7869,6354],[17,3],[51,21],[25,5],[15,-7],[13,-17],[21,-37],[89,-72],[29,-29],[17,-14],[38,-12],[17,-13],[14,-21],[8,-23],[38,-182],[1,-24],[22,-18],[55,-89],[29,-33],[102,-66],[90,-96],[12,-67],[33,-39],[9,-32],[13,-26],[25,-15],[26,-7],[18,-1],[-9,-28],[-7,-16],[-3,-16],[4,-25],[11,-13],[16,-5],[13,-14],[-2,-38],[-9,-20],[-31,-42],[-12,-23],[-4,-24],[-1,-27],[-5,-25],[-12,-15],[-30,-25],[-10,-40],[3,-47],[20,-100],[4,-46],[-3,-99],[21,-6],[151,-76],[11,-30],[-7,-29],[-24,-12],[14,-31],[15,-28],[20,-23],[64,-32],[23,-21],[16,-33],[60,-192],[3,-51],[-12,-92],[-36,-175],[0,-95],[16,-59],[26,-36],[31,-31],[28,-47],[10,-43],[0,-46],[-10,-93],[-26,-102],[-54,-76],[-139,-107],[23,-26],[21,-45],[34,-89],[20,-82],[36,-77],[30,-46],[94,-94],[10,-16],[14,-34],[10,-15],[17,-11],[15,-3],[13,-7],[11,-27],[-4,-40],[-23,-123]]],
transform:{scale:[.0006158880462046191,.00043862174307430336],translate:[-17.536040818999936,12.305606588000103]}},m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();