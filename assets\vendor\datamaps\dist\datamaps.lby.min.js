!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo={type:"Topology",objects:{lby:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Ghadamis"},id:"LY.GD",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Al Jufrah"},id:"LY.JU",arcs:[[4,5,6,7,8,9,10,11]]},{type:"Polygon",properties:{name:"Al Kufrah"},id:"LY.KF",arcs:[[12,-7,13,14]]},{type:"Polygon",properties:{name:"Al Marqab"},id:"LY.MB",arcs:[[15,16,17,18,19]]},{type:"Polygon",properties:{name:"Ash Shati'"},id:"LY.SH",arcs:[[-10,20,21,22,23,-2,24]]},{type:"Polygon",properties:{name:"Ghat"},id:"LY.GT",arcs:[[25,26,27,-23]]},{type:"Polygon",properties:{name:"Murzuq"},id:"LY.MQ",arcs:[[-13,28,-27,29,30,-8]]},{type:"Polygon",properties:{name:"Misratah"},id:"LY.MI",arcs:[[31,-12,32,-16,33]]},{type:"Polygon",properties:{name:"Sabha"},id:"LY.SB",arcs:[[-9,-31,34,-21]]},{type:"Polygon",properties:{name:"Al Jifarah"},id:"LY.JI",arcs:[[-18,35,36,37]]},{type:"Polygon",properties:{name:"An Nuqat al Khams"},id:"LY.NQ",arcs:[[38,-4,39]]},{type:"Polygon",properties:{name:"Az Zawiyah"},id:"LY.ZA",arcs:[[-37,40,-39,41,42]]},{type:"Polygon",properties:{name:"Mizdah"},id:"LY.MZ",arcs:[[-33,-11,-25,-1,-41,-36,-17]]},{type:"Polygon",properties:{name:"Tajura' wa an Nawahi al Arba"},id:"LY.TN",arcs:[[-19,-38,-43,43]]},{type:"Polygon",properties:{name:"Al Marj"},id:"LY.HZ",arcs:[[44,45,46,47]]},{type:"Polygon",properties:{name:"Al Jabal al Akhdar"},id:"LY.JA",arcs:[[48,49,-45,50]]},{type:"Polygon",properties:{name:"Ajdabiya"},id:"LY.AJ",arcs:[[-46,-50,51,52,53,-14,-6,54,55,56]]},{type:"Polygon",properties:{name:"Benghazi"},id:"LY.BA",arcs:[[-57,57,-47]]},{type:"Polygon",properties:{name:"Surt"},id:"LY.SR",arcs:[[-55,-5,-32,58]]},{type:"Polygon",properties:{name:"Al Qubbah"},id:"LY.QB",arcs:[[59,-52,-49,60]]},{type:"Polygon",properties:{name:"Al Butnan"},id:"LY.BU",arcs:[[-53,-60,61]]},{type:"Polygon",properties:{name:"Wadi al Hayaa"},id:"LY.GT",arcs:[[-35,-30,-26,-22]]}]}},arcs:[[[1623,9361],[0,-87],[-14,-74],[-11,-92],[11,-78],[10,-54],[-12,-73],[-3,-87],[8,-73],[-5,-92],[10,-65],[27,-59],[31,-58],[11,-5],[-60,-109],[-85,-125],[-51,-83],[-33,-35],[-6,-41],[17,-94],[-3,-129],[5,-149],[19,-163],[25,-169],[25,-155],[0,-204],[50,-87],[56,-80],[15,-61],[19,-132]],[[1679,6648],[-1,-2],[-11,-17],[-22,-29],[-68,16],[-41,43],[-64,-13],[-22,-2],[-60,10],[-24,-14],[-42,-28],[-40,-1],[-84,36],[-29,21],[-81,14],[-40,53],[-9,54],[-18,41],[-36,-12],[-46,-31],[-12,-10],[-35,-30],[-56,-26],[-56,-25],[-55,-26],[-41,-8],[-91,52],[-59,23],[-29,-8],[-56,-18],[-50,-17],[-33,-18],[-27,-11]],[[341,6665],[15,123],[-2,138],[-14,112],[-50,175],[-50,175],[-74,142],[-81,122],[-33,39],[-37,45],[-15,24],[147,82],[141,75],[18,5],[46,3],[16,9],[79,102],[66,108],[58,65],[39,81],[9,29],[1,25],[-16,51],[-3,26],[4,28],[-1,13],[-20,42],[-19,77],[-47,125],[-2,13],[7,48],[10,16],[40,45],[43,74],[32,26],[71,-1],[35,14],[10,8],[9,9],[8,11],[10,26],[27,24],[8,25],[5,58],[15,15],[11,-2],[12,-6],[11,-4],[13,3],[21,17],[22,14],[21,21],[26,58],[17,18],[194,92],[165,77],[44,29],[8,7]],[[1411,9441],[5,-3],[37,-32],[32,-34],[36,-13],[102,2]],[[4494,7243],[10,7],[75,-20],[121,7],[67,28],[81,-6],[68,-61],[94,-27],[60,-21],[61,55],[47,27],[48,0],[53,-47],[13,-55],[27,-68],[54,-61],[53,-68],[26,-82],[27,-102],[66,-68],[87,-62],[100,-28],[73,-24],[0,13]],[[5805,6580],[138,-48],[180,-55],[0,-2],[106,-8],[-2,-975]],[[6227,5492],[0,-369],[-1,-368]],[[6226,4755],[-26,13],[-67,24],[-85,23],[-99,20],[-71,19],[-63,7],[-65,62],[-58,70],[-73,34],[-89,-23],[-69,-37],[-29,-38],[-82,-62],[-51,-16],[-82,-7],[-46,-30],[-53,-24],[-92,36],[-108,24],[-95,21],[-37,52],[-50,37],[-48,30],[-67,73],[-33,75],[4,75],[-34,52],[-38,22],[-56,30],[-31,14],[-28,39],[-6,61],[2,54],[5,53]],[[4406,5538],[1,6],[-15,50],[-41,24],[-40,39],[-42,24],[-24,25],[-20,57],[-18,61],[-60,93],[-46,71],[0,22]],[[4101,6010],[1,16],[21,55],[25,71],[15,59],[-9,60],[-55,57],[-45,13],[-83,29],[-52,3],[-80,-8],[-82,-48],[-80,-9],[-66,11],[-15,53],[-1,58],[-21,81],[-75,83],[-113,74],[-47,38],[-37,33],[-37,58],[-29,58],[-9,80],[5,90],[2,72]],[[3234,7097],[55,16],[79,59],[50,32],[26,36],[4,38],[-1,59],[-12,51],[-1,55],[17,40],[37,52],[20,39]],[[3508,7574],[29,-17],[48,-47],[48,-53],[94,-33],[61,-12],[55,-20],[74,1],[40,-20],[21,-20],[41,-67],[47,-54],[88,-13],[40,35],[61,34],[60,21],[88,-26],[61,-20],[30,-20]],[[6237,1730],[0,57],[0,8],[0,8],[0,8],[0,7],[0,8],[0,8],[0,7],[0,8],[-1,363],[-2,363],[-1,364],[-1,363],[-2,363],[-1,363],[-2,364],[-1,363]],[[6227,5492],[214,1],[214,1],[275,2],[276,3],[378,-6],[378,-5],[368,3],[368,2],[369,7],[368,7],[233,6],[220,6],[1,0]],[[9889,5519],[0,-139],[0,-142],[0,-214],[0,-213],[0,-213],[0,-213],[0,-213],[0,-214],[0,-213],[0,-213],[0,-213],[0,-214],[0,-213],[0,-213],[0,-213],[0,-214],[0,-213],[0,-213],[0,-91],[0,-91],[0,-91],[0,-91],[0,-91],[0,-91],[0,-91],[0,-91],[0,-91],[-1,-91],[0,-91],[0,-91],[0,-70],[0,-69],[0,-70],[0,-69],[0,-82],[0,-4],[0,-1],[-1,-2],[0,-1],[-1,-1],[-1,0],[-2,0],[-1,0],[-2,-1],[-63,0],[-92,1],[-77,0],[-78,0],[-85,0],[-71,0],[-54,0],[-101,0],[0,-91],[0,-92],[0,-91],[0,-91],[-78,45],[-78,44],[-78,45],[-78,44],[-77,45],[-78,44],[-78,45],[-78,45],[-78,44],[-78,45],[-77,44],[-78,45],[-78,44],[-78,45],[-78,44],[-77,45],[-78,45],[-78,44],[-78,45],[-78,44],[-78,45],[-77,44],[-78,45],[-78,45],[-78,44],[-78,45],[-78,44],[-77,45],[-78,44],[-78,45],[-78,45],[-78,44],[-77,45],[-78,44],[-78,45],[-78,44],[-78,45],[-78,44],[-64,37]],[[3446,9464],[0,-7],[-8,-17],[-13,-52],[6,-38],[17,-40],[-2,-43],[-25,-30],[-21,-60],[-10,-65],[-41,-6],[-83,-10],[-66,23],[-51,51],[-102,70],[-36,-41],[-46,-9],[-20,2],[-39,18],[-41,-1],[-36,-14],[-62,-18],[-39,-6],[-99,3],[-39,-1]],[[2590,9173],[-3,43],[-18,67],[-40,51],[-31,34]],[[2498,9368],[-9,12]],[[2489,9380],[71,22],[22,14],[10,38],[32,68],[47,14],[30,10],[82,37],[10,1],[56,6],[3,13],[49,52],[40,35]],[[2941,9690],[23,-10],[116,-20],[7,-2],[11,-10],[4,-3],[10,-4],[23,-23],[35,-21],[54,-47],[29,-30],[13,-5],[5,-1],[97,-16],[10,-5],[9,-9],[28,-15],[13,-4],[18,-1]],[[4101,6010],[-146,14],[-93,-35],[-39,-21],[-114,-16],[-66,-8],[-93,-42],[-45,-83],[-79,-49],[-66,-42],[-60,-43],[-26,-41],[-39,-35],[-66,-8],[-210,-24]],[[2959,5577],[-225,44],[-174,57],[-100,18],[-112,-30],[-151,-101],[-123,-99],[-169,-136],[-188,-137],[-169,-122],[-148,-102],[-84,-58]],[[1316,4911],[-10,102],[-23,95],[-40,191],[-24,149],[-17,102],[-19,171],[-36,80],[-55,39],[-81,31],[-147,28],[-121,36],[-122,35],[-122,56],[-90,54],[-3,1]],[[406,6081],[2,6],[1,29],[-92,250],[-8,43],[32,256]],[[1679,6648],[20,38],[80,-23],[30,-4],[76,-33],[46,-5],[50,-3],[39,-8],[6,-56],[55,-29],[62,3],[44,46],[21,51],[37,33],[36,23],[54,34],[71,39],[65,75],[58,82],[37,59],[38,53],[32,52],[30,64],[50,66],[39,47],[34,32],[70,59],[54,16],[52,-11],[37,-23],[82,-40],[67,-48],[52,-58],[33,-42],[-2,-40]],[[1316,4911],[-14,-93],[-23,-109],[-8,-140],[19,-82],[41,-63]],[[1331,4424],[-42,-118],[5,-144],[-2,-151],[-2,-138],[77,-148],[16,-96],[-9,-100]],[[1374,3529],[-200,70],[-150,53],[-120,42],[-14,3],[-14,-2],[-69,-27],[-74,-29],[-25,-2],[-12,4],[-82,71],[-12,14],[-8,25],[-11,68],[-12,20],[-93,58],[-8,19],[-2,102],[-2,103],[-3,96],[-8,47],[-25,46],[-96,141],[-77,114],[-96,141],[-89,129],[-14,40],[15,35],[9,12],[11,29],[22,21],[5,11],[3,12],[1,15],[222,110],[12,15],[26,94],[-1,15],[-7,21],[1,25],[16,78],[-2,11],[-10,8],[-28,17],[-7,6],[-6,15],[-13,76],[-53,195],[0,12],[3,12],[11,29],[8,43],[9,15],[27,31],[5,14],[-6,15],[-9,17],[-2,15],[15,12],[18,10],[10,14],[43,146]],[[6237,1730],[-13,8],[-78,45],[-78,44],[-78,45],[-78,44],[-78,45],[-77,44],[-78,45],[-78,45],[-78,44],[-78,45],[-78,44],[-77,45],[-78,44],[-78,45],[-78,45],[-78,44],[-77,45],[-78,44],[-78,45],[-78,44],[-78,45],[-78,44],[-77,45],[-78,45],[-78,44],[-57,33],[-13,-2],[-28,-15],[-37,-19],[-36,-19],[-36,-19],[-37,-19],[-36,-19],[-36,-19],[-37,-19],[-36,-19],[-36,-19],[-37,-20],[-36,-19],[-36,-19],[-37,-19],[-36,-19],[-36,-19],[-37,-19],[-11,-6],[-108,-63],[-108,-64],[-108,-63],[-107,-63],[-40,-23],[-10,-1],[-9,5],[-29,27],[-79,75],[-78,76],[-79,75],[-79,75],[-36,34],[-73,44],[-64,17],[-56,14],[-56,15],[-55,14],[-56,15],[-55,14],[-56,14],[-56,15],[-55,14],[-56,14],[-55,15],[-56,14],[-56,15],[-55,14],[-56,14],[-55,15],[-56,14],[-48,104],[-44,96],[-71,153],[-1,2],[-45,98],[-44,95],[-16,22],[-21,12],[-26,9]],[[1331,4424],[123,74],[113,-58],[53,-18],[118,11],[72,9],[65,57],[64,57],[86,-17],[78,9],[52,43],[59,22],[104,38],[151,11],[65,50],[64,84],[45,42],[118,31],[167,63]],[[2928,4932],[1,-1],[125,17],[72,29],[111,50],[72,77],[65,76],[65,70],[52,56],[92,36],[80,22],[112,29],[119,15],[119,22],[66,15],[327,93]],[[4316,8596],[-3,-7],[-11,-26],[1,-122],[28,-108],[41,-155],[28,-142],[21,-135],[48,-135],[54,-149],[21,-116],[21,-95],[-14,-74],[-57,-89]],[[3508,7574],[7,14],[42,43],[30,56],[2,84],[-8,92],[-13,111],[-53,39],[-4,2],[-49,24],[-139,38],[-69,50],[-59,25],[-92,49],[-34,85],[-58,143],[-83,154],[-11,12],[-34,22],[-40,24],[-69,85],[-36,51],[-20,77],[-29,67],[-41,79],[-23,107],[-35,66]],[[3446,9464],[98,-4],[128,-22],[29,-11],[14,-2],[8,-4],[24,-31],[23,-14],[8,-8],[6,-27],[5,-14],[14,-27],[27,-36],[3,-12],[-1,-15],[-6,-26],[-1,-30],[-1,-17],[3,-53],[6,-27],[76,-193],[15,-27],[68,-95],[41,-44],[47,-36],[178,-82],[58,-11]],[[2928,4932],[-1,74],[25,83],[72,70],[38,83],[-7,41],[-34,41],[-27,54],[4,96],[-14,75],[-25,28]],[[2498,9368],[-39,-4],[-22,-14],[-66,-10],[-55,-1],[-90,-13]],[[2226,9326],[6,56],[-4,51],[47,112],[18,31],[27,49],[66,34],[2,4]],[[2388,9663],[55,-53],[6,-60],[10,-78],[-1,-51],[31,-41]],[[2071,9730],[-12,-10],[-33,-104],[-25,-41],[-79,-51],[-32,-42],[-49,-23],[-54,-26],[-16,-38],[-148,-34]],[[1411,9441],[13,12],[11,23],[-2,31],[-15,26],[-42,41],[-13,28],[0,40],[9,77],[-5,76],[12,50],[0,40],[1,12],[18,69],[0,33],[13,-3],[22,-8],[9,-6],[8,-19],[10,-8],[20,-9],[76,-12],[24,-8],[-13,13],[-41,13],[-11,14],[30,-17],[68,-22],[148,-91],[26,-27],[24,-7],[40,-26],[50,-24],[28,-8],[142,-14]],[[2226,9326],[-53,-7],[-30,-7],[-24,-6],[-48,15],[-53,14],[-55,23],[-69,-5],[-94,2],[-177,6]],[[2071,9730],[99,-11],[59,5],[158,45],[18,8]],[[2405,9777],[0,-5],[0,-5],[5,-59],[-22,-45]],[[2405,9777],[8,3],[20,16],[11,7],[14,2],[44,2],[59,-10],[8,-4],[6,-5],[125,-65],[28,-7],[108,4],[27,-4],[53,-15],[25,-11]],[[7605,9706],[2,-3],[5,-1],[5,3],[6,1],[1,0],[5,-9],[40,-51],[2,-47],[-46,-46],[-36,-43],[2,-61],[19,-74],[39,-72],[22,-71],[40,-45],[68,-52],[18,-68],[4,-97],[-5,-71],[-26,-64],[-2,-84],[25,-88],[16,-34],[20,-18],[3,-158]],[[7832,8453],[-39,-16],[-162,-8],[-50,2]],[[7581,8431],[-19,176],[-29,58],[-12,75],[-5,61],[-43,41],[-35,-9],[-75,-6],[-82,12],[-48,-2],[-52,-23],[-85,15],[-27,37],[-26,78],[-33,58],[-39,82],[-26,78],[1,54],[29,80],[46,63],[56,50],[29,46],[9,81],[-2,11]],[[7113,9547],[139,59],[87,58],[73,36],[29,7],[120,-8],[28,3],[16,4]],[[8002,9803],[-4,-9],[-2,-45],[-5,-60],[16,-55],[19,-40],[5,-54],[-8,-40],[-19,-54],[-8,-53],[-11,-24],[12,-64],[1,-64],[15,-68],[-3,-117],[1,-75],[-6,-81],[-15,-40],[0,-118],[-12,-53],[-19,-78],[122,-118]],[[8081,8493],[-89,-9],[-112,-11],[-48,-20]],[[7605,9706],[37,10],[25,14],[97,86],[9,4],[31,3],[28,7],[17,-14],[27,-11],[31,-7],[26,-2],[69,7]],[[8081,8493],[92,18],[49,-42],[76,-69],[35,-73],[67,-47],[45,-8],[63,-19],[62,1],[51,5]],[[8621,8259],[1,0],[1,0],[1,0],[13,-5],[13,-6],[13,-5],[13,-6],[1,-480],[1,-481],[1,-480],[1,-481],[299,-7],[299,-7],[299,-7],[299,-7],[6,-2],[6,-1],[1,0]],[[9889,6284],[0,-57],[0,-257],[0,-29],[0,-111],[0,-246],[0,-65]],[[5805,6580],[0,273],[9,211],[41,150],[76,108],[21,109],[-12,122],[8,122],[22,142],[18,128]],[[5988,7945],[85,-56],[27,-11],[56,-9],[56,-1],[59,7],[33,9],[29,9],[51,25],[70,47],[13,6],[29,5],[13,7],[83,65],[104,126],[28,48],[15,22],[23,18],[11,11],[40,85],[30,81],[3,31],[-3,94],[-4,5]],[[6839,8569],[8,3],[-2,7],[-2,3],[-7,7],[-2,7],[35,1],[70,-22],[27,-42],[47,-63],[67,-46],[44,-14],[91,5],[124,24],[242,-8]],[[6839,8569],[-8,14],[-16,48],[-13,20],[-28,29],[-10,15],[-42,119],[-4,30],[-1,33],[-2,12],[-11,27],[-4,14],[-2,30],[2,33],[9,61],[8,28],[-1,7],[-2,8],[-2,8],[3,7],[8,11],[57,120],[15,22],[120,119],[10,16],[30,35],[13,8],[25,11],[11,9],[9,9],[92,72],[8,3]],[[4316,8596],[140,-25],[233,-2],[134,-30],[138,-47],[107,-24],[23,1],[5,-3],[5,-3],[23,-12],[18,-17],[6,-4],[6,-3],[93,-27],[49,-24],[53,-18],[46,-8],[8,-8],[17,-26],[12,-10],[12,-7],[57,-12],[100,-38],[20,-12],[18,-17],[55,-65],[154,-115],[35,-40],[19,-17],[41,-22],[27,-6],[12,-6],[6,-4]],[[8799,9294],[-1,-5],[-20,-64],[-31,-87],[-32,-113],[-8,-131],[-22,-114],[-54,-80],[-18,-124],[-1,-112],[9,-54],[0,-151]],[[8002,9803],[69,7],[3,3],[7,10],[3,3],[14,2],[7,1],[7,-3],[5,-5],[1,-3],[0,-5],[3,-8],[20,-20],[25,-5],[58,0],[25,-8],[62,-46],[12,-8],[11,-4],[12,-3],[29,-2],[147,-44],[34,-22],[12,-3],[7,-1],[21,-9],[8,-1],[22,1],[14,-4],[26,-13],[29,-5],[12,-5],[8,-7],[2,-11],[-4,-7],[-11,-13],[-2,-7],[6,-45],[6,-11],[9,-9],[13,-8],[-5,-14],[-8,-46],[-12,5],[2,7],[-1,6],[-4,6],[-6,6],[-10,-60],[1,-15],[10,-8],[38,-14],[14,-3],[10,-6],[13,-14],[10,-16],[8,-22],[5,-3]],[[8799,9294],[4,-2],[10,-3],[7,-4],[5,-9],[6,-15],[6,-6],[-1,12],[-7,33],[12,-5],[27,0],[6,-3],[18,-14],[4,-3],[79,-7],[87,2],[108,-25],[100,-40],[-15,-15],[-3,-5],[5,-4],[17,-6],[39,-31],[15,-4],[119,1],[168,-11],[56,17],[14,10],[8,3],[25,-5],[5,1],[5,3],[5,1],[5,-3],[2,-2],[8,-4],[9,-3],[12,-6],[21,-4],[26,-10],[73,-15],[23,-14],[10,-24],[-6,-29],[-1,-16],[4,-13],[22,-24],[33,-48],[3,-13],[10,-21],[12,-21],[-2,-3],[-1,-2],[-40,-31],[-17,-25],[-24,-62],[-102,-84],[-8,-23],[0,-27],[6,-61],[1,-60],[15,-78],[4,-15],[22,-45],[8,-38],[28,-58],[8,-30],[-4,-25],[-9,-25],[-8,-29],[-7,-40],[-19,-51],[-2,-14],[-6,-25],[-12,-25],[-37,-56],[-19,-20],[-17,-21],[-6,-11],[-10,-25],[-27,-47],[-10,-26],[0,-28],[6,-16],[18,-32],[54,-137],[3,-16],[-1,-15],[-11,-38],[5,-31],[32,-55],[9,-30],[0,-17],[-6,-53],[2,-29],[6,-25],[52,-128],[11,-39],[5,-42],[0,-91],[0,-122],[0,-292],[0,-288]]],transform:{scale:[.0015871303920392112,.0013686470202748371],translate:[9.286543823000073,19.496123759000028]}},m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",
m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();