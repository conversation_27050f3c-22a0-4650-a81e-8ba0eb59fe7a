!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo={type:"Topology",objects:{tun:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:null},id:"-99",arcs:[[[0]],[[1]]]},{type:"Polygon",properties:{name:"Tozeur"},id:"TN.TO",arcs:[[2,3,4]]},{type:"Polygon",properties:{name:"Manubah"},id:"TN.MN",arcs:[[5,6,7,8,9,10]]},{type:"Polygon",properties:{name:"Béja"},id:"TN.BJ",arcs:[[-9,11,12,13,14,15]]},{type:"Polygon",properties:{name:"Ben Arous (Tunis Sud)"},id:"TN.BA",arcs:[[16,17,-7,18,19]]},{type:"Polygon",properties:{name:"Bizerte"},id:"TN.BZ",arcs:[[-10,-16,20]]},{type:"Polygon",properties:{name:"Jendouba"},id:"TN.JE",arcs:[[21,22,23,-14]]},{type:"Polygon",properties:{name:"Nabeul"},id:"TN.NB",arcs:[[24,25,-17,26]]},{type:"Polygon",properties:{name:"Tunis"},id:"TN.TU",arcs:[[-19,-6,27]]},{type:"Polygon",properties:{name:"Le Kef"},id:"TN.KF",arcs:[[28,29,-23,30]]},{type:"Polygon",properties:{name:"Kassérine"},id:"TN.KS",arcs:[[31,32,33,34,-29]]},{type:"Polygon",properties:{name:"Gabès"},id:"TN.GB",arcs:[[35,36,37,38,39,40]]},{type:"Polygon",properties:{name:"Gafsa"},id:"TN.GF",arcs:[[-40,41,-5,42,-34,43]]},{type:"Polygon",properties:{name:"Sidi Bou Zid"},id:"TN.SZ",arcs:[[44,45,-41,-44,-33,46]]},{type:"MultiPolygon",properties:{name:"Sfax"},id:"TN.SF",arcs:[[[47]],[[48]],[[49,-36,-46,50,51]]]},{type:"Polygon",properties:{name:"Siliana"},id:"TN.SL",arcs:[[-13,52,53,-47,-32,-31,-22]]},{type:"Polygon",properties:{name:"Mahdia"},id:"TN.MH",arcs:[[54,55,-52,56,57]]},{type:"Polygon",properties:{name:"Monastir"},id:"TN.MS",arcs:[[-55,58,59]]},{type:"Polygon",properties:{name:"Kairouan"},id:"TN.KR",arcs:[[-57,-51,-45,-54,60,61]]},{type:"Polygon",properties:{name:"Sousse"},id:"TN.SS",arcs:[[62,-59,-58,-62,63,-25]]},{type:"Polygon",properties:{name:"Zaghouan"},id:"TN.ZA",arcs:[[-18,-26,-64,-61,-53,-12,-8]]},{type:"MultiPolygon",properties:{name:"Médenine"},id:"TN.ME",arcs:[[[64,65,-38,66]],[[67]]]},{type:"Polygon",properties:{name:"Kebili"},id:"TN.KB",arcs:[[-66,68,69,-3,-42,-39]]},{type:"Polygon",properties:{name:"Tataouine"},id:"TN.TA",arcs:[[70,-69,-65]]}]}},arcs:[[[8161,9436],[23,-17],[-16,-1],[-24,2],[-8,-1],[-1,1],[-17,-1],[-13,12],[8,6],[13,5],[3,4],[13,3],[7,-6],[12,-7]],[[3617,9983],[3,-16],[-21,-2],[-31,5],[-27,-5],[-26,-7],[-37,8],[26,10],[31,8],[39,6],[16,9],[10,-1],[7,-4],[0,-6],[10,-5]],[[2912,5280],[-197,-165],[-134,-77],[-1508,-567],[-473,-109],[-41,-12]],[[559,4350],[0,6],[-37,53],[-187,153],[-156,127],[-21,35],[-25,71],[-70,64],[-17,23],[-34,75],[-12,54],[51,137],[7,100],[34,37],[53,26],[99,48],[60,36],[67,33],[95,22],[156,11],[46,12],[31,17],[22,22],[92,162],[48,48],[96,32],[212,43],[102,34],[233,49],[20,9]],[[1524,5889],[11,-5],[19,-25],[1,-14],[-40,-71],[0,-10],[5,-8],[86,-39],[10,-6],[11,-11],[-1,-6],[-7,-4],[-12,-3],[-118,-7],[-11,-1],[-11,-2],[-11,-5],[-87,-49],[-13,-10],[-15,-14],[1,-8],[7,-5],[180,-56],[45,-19],[21,-11],[37,-24],[29,-25],[20,-13],[11,-5],[11,-3],[11,-2],[91,-4],[184,-30],[15,-3],[42,-15],[32,-16],[10,-7],[6,-6],[10,-20],[22,-32],[12,-9],[11,-5],[537,15],[93,-3],[133,-13]],[[6995,9109],[-11,-3],[-344,-50],[-304,-74],[-103,-19],[-46,-11],[-22,-7],[-2,-5],[4,-4],[5,-3],[8,-3],[19,-6],[8,-3],[4,-3],[2,-6],[2,-5],[3,-5],[6,-5],[9,-5],[44,-14],[21,-9],[7,-5],[5,-5],[2,-7],[-1,-5],[-7,-14]],[[6304,8833],[-122,-56],[-45,-15],[-37,-4]],[[6100,8758],[-10,10],[-7,4],[-9,5],[-9,2],[-10,1],[-10,0],[-21,-2],[-13,-5],[-15,-7],[-40,-33],[-40,-43],[-24,-10],[-74,-10]],[[5818,8670],[-26,25],[-96,64],[-80,75],[-18,22],[-5,11],[-5,5],[-8,4],[-14,3],[-65,3],[-139,21],[-286,121]],[[5076,9024],[97,37],[213,43],[128,14],[24,5],[16,5],[29,24],[33,17],[43,17],[23,7],[17,4],[90,3],[49,-3],[47,-7],[130,-27],[17,-2],[36,-1],[32,0],[21,4],[14,4],[8,4],[7,5],[4,4],[19,23],[39,34],[20,13],[85,79],[16,20],[6,5],[8,6],[21,12],[16,11],[24,14],[18,6],[17,4],[128,6],[117,18],[16,1]],[[6704,9433],[22,-15],[8,-13],[-15,-17],[-17,20],[-105,-47],[19,-67],[93,-69],[112,-52],[91,-19],[26,-9],[57,-36]],[[5818,8670],[-145,-55],[-45,-22],[-10,-10],[-5,-8],[-72,-47],[-30,-33]],[[5511,8495],[-148,-7],[-54,-6],[-40,-7],[-106,-26],[-22,-4],[-20,-2],[-13,1],[-16,2],[-22,5],[-15,6],[-55,25],[-22,7],[-33,8],[-23,4],[-30,0],[-141,-4],[-73,-6],[-22,-5],[-20,-7],[-9,-4],[-29,-19],[-98,-79],[-20,-2],[-30,3],[-127,31],[-44,14],[-16,3],[-19,2],[-33,0],[-35,-3],[-142,-23],[-25,-2],[-59,18],[-200,86]],[[3750,8504],[46,12],[12,4],[9,4],[5,5],[1,5],[3,40],[4,12],[4,5],[5,5],[16,10],[39,20],[8,5],[-3,16],[-14,25],[-82,99],[-19,11],[-10,8],[-7,9],[-5,25],[16,69],[-1,6],[-4,6],[-14,7],[-23,9],[-26,7],[-11,2],[-140,11],[-13,2],[-13,4],[-12,4],[-9,5],[-4,6],[5,8],[6,6],[7,6],[4,6],[4,8],[1,12],[3,8],[5,6],[6,5],[10,5],[90,33],[10,5],[6,5],[-2,9],[-9,12],[-47,34],[-92,47],[-7,6],[-4,7],[7,10],[8,4],[10,2],[6,1],[45,8],[9,3],[-16,14],[-105,50],[-1,1]],[[3467,9283],[19,4],[54,23],[76,42],[75,65],[33,9],[24,6],[68,33],[26,7],[74,13]],[[3916,9485],[4,-6],[5,-47],[-4,-21],[-14,-29],[-3,-19],[2,-8],[5,-8],[10,-12],[11,-6],[12,-4],[11,-1],[152,2],[16,-3],[17,-5],[31,-12],[14,-7],[11,-7],[9,-11],[23,-34],[59,-58],[32,-39],[13,-10],[12,-8],[81,-34],[23,-7],[23,-5],[88,-13],[29,-9],[25,-9],[8,-7],[6,-6],[0,-6],[-2,-11],[-3,-9],[-5,-8],[-9,-12],[-5,-6],[-5,-6],[-5,-5],[-14,-18],[1,-8],[6,-13],[19,-25],[15,-13],[14,-8],[32,-10],[12,-2],[22,-2],[10,0],[11,1],[12,1],[11,3],[23,8],[236,111],[22,9],[24,6],[27,5]],[[7190,8887],[-3,-7],[-54,-86],[-93,-84],[-5,-13],[5,-10],[10,-33],[-45,-121]],[[7005,8533],[-57,-3],[-22,-2],[-23,-4],[-91,-24],[-10,-4],[-14,0],[-13,2],[-20,14],[-6,8],[-2,7],[3,5],[2,5],[-21,13],[-222,92],[-7,6],[-67,22],[-98,21],[-43,7],[-29,2],[-25,-5],[-21,-2],[-14,1],[-15,4],[-26,11],[-15,8],[-49,41]],[[6304,8833],[28,-4],[25,-2],[33,0],[55,4],[13,4],[15,6],[37,21],[14,6],[19,1],[39,-3],[22,0],[29,2],[16,1],[17,2],[21,6],[32,15],[33,12],[15,13],[30,48],[-6,7]],[[6791,8972],[8,3],[37,30],[53,-52],[64,-40],[87,-24],[116,-6],[34,4]],[[3916,9485],[195,36],[30,8],[52,37],[44,11],[323,2],[94,16],[59,29],[35,10],[33,-6],[25,-7],[27,3],[26,5],[72,8],[57,12],[52,15],[35,16],[50,-13],[45,4],[90,28],[51,12],[42,5],[108,1],[54,9],[30,3],[25,-7],[20,-9],[25,-4],[209,-3],[1,-4],[5,-13],[1,-61],[-76,-28],[-93,-18],[-51,-33],[8,-11],[38,-37],[13,-8],[14,-5],[15,-19],[20,-4],[107,-7],[50,4],[54,20],[30,19],[27,25],[-1,22],[-156,29],[-24,-2],[-50,-7],[-28,0],[0,9],[60,18],[23,3],[15,6],[11,7],[8,4],[37,2],[238,-24],[44,2],[131,26],[21,-2],[39,-27],[173,-12],[79,-32],[182,-29],[47,-18],[-78,-9],[-37,-7],[-34,-11],[-13,22],[-68,4],[-79,-9],[-44,-17],[18,-22],[64,-11],[75,-2],[47,6],[17,-12]],[[3750,8504],[-39,5],[-41,2],[-47,-4],[-104,-18]],[[3519,8489],[-213,-33],[-585,-13],[-22,-2],[-93,-16],[-220,-51],[-50,-1],[-153,6],[-39,-2]],[[2144,8377],[6,19],[4,56],[-5,26],[-21,25],[-37,11],[-47,6],[-47,11],[-212,12],[-101,19],[5,47],[58,31],[71,21],[158,26],[296,81],[55,28],[56,48],[21,48],[-49,28],[-86,20],[16,22],[75,16],[95,5],[92,-2],[84,7],[170,31],[44,14],[2,17],[-84,58],[-9,14],[2,16],[-8,36],[1,0],[45,9],[174,32],[60,5],[209,0],[55,9],[101,38],[74,16]],[[7466,8369],[-13,12],[-13,13],[-5,3],[-5,2],[-8,3],[-14,0],[-19,-2],[-32,-7],[-18,-5],[-17,-3],[-16,0],[-25,6],[-16,5],[-49,5],[-71,-6],[-80,-6]],[[7065,8389],[-15,1],[-8,1],[-11,3],[-10,4],[-8,5],[-6,6],[-7,28],[5,96]],[[7190,8887],[50,-1],[124,29],[76,8],[47,22],[20,38],[16,32],[35,55],[44,18],[52,4],[113,-3],[103,9],[95,17],[122,39],[76,35],[22,5],[9,13],[8,7],[141,51],[20,11],[6,15],[-9,10],[-4,9],[22,14],[53,11],[124,6],[94,32],[51,4],[43,-10],[23,-25],[-4,-11],[-25,-17],[-6,-14],[5,-15],[24,-20],[6,-12],[9,-10],[42,-28],[16,-14],[5,-13],[5,-29],[6,-14],[12,-10],[16,-13],[18,-10],[13,-4],[14,-7],[29,-21],[-13,-23],[-34,-21],[-98,-18],[-134,-51],[-72,-60],[-105,-69],[-172,-144],[-132,-148],[-63,-38],[-203,-24],[-236,-61],[-86,11],[-84,-28],[-53,-37]],[[6995,9109],[25,-16],[-13,-16],[-56,-25],[-24,-28],[-26,-11],[-33,-2],[-32,12],[18,21],[-23,6],[-42,-3],[-79,-12],[-24,-8],[-42,-27],[-13,-20],[35,-7],[77,-1],[32,-7],[16,7]],[[3727,7484],[-121,4],[-421,81],[-200,26],[-23,1],[-21,-4],[-26,-7],[-56,-20],[-23,-10],[-15,-8],[-6,-5],[-10,-11],[-8,-13],[-5,-12],[-11,-56],[-6,-18],[-7,-9],[-12,-9],[-8,-4],[-52,-3],[-113,3],[-28,4],[-107,26],[-12,2],[-12,1],[-21,0],[-22,-3],[-28,-8],[-87,-30],[-43,-18],[-31,-6],[-73,-3],[-44,-3]],[[2075,7372],[-9,42],[-42,45],[-120,89],[-29,48],[-10,58],[14,109],[52,86],[9,16],[8,75],[52,101],[27,22],[23,26],[-7,25],[-18,25],[-11,27],[11,57],[116,147],[3,7]],[[3519,8489],[114,-78],[30,-29],[3,-6],[1,-10],[-3,-11],[-4,-5],[-12,-10],[-8,-5],[-8,-8],[-5,-9],[-2,-18],[3,-10],[5,-8],[25,-19],[63,-35],[48,-36],[10,-10],[7,-9],[3,-11],[0,-20],[-5,-28],[1,-10],[4,-18],[8,-10],[10,-7],[33,-10],[150,-35],[18,-8],[21,-12],[39,-27],[14,-14],[7,-11],[-2,-25],[-14,-37],[-17,-30],[-13,-15],[-14,-10],[-58,-30],[-35,-24],[-10,-10],[-8,-20],[-6,-12],[-9,-11],[-7,-6],[-8,-5],[-87,-34],[-9,-4],[-8,-7],[-3,-4],[-3,-5],[-3,-11],[0,-54],[-10,-51],[-4,-10],[-34,-53]],[[3727,7484],[151,-98],[47,-24],[10,-2],[10,0],[12,0],[11,1],[33,7],[11,1],[11,1],[10,-1],[96,-20],[24,-7],[15,-7],[29,-31],[18,-14],[14,-5],[14,-4],[61,-5],[51,-11],[88,-25],[20,-7],[19,-10],[14,-9],[17,-15],[6,-10],[3,-9]],[[4522,7180],[-6,-24],[-9,-19],[-10,-9],[-13,-9],[-28,-14],[-33,-14],[-84,-23],[-25,-8],[-21,-10],[-10,-7],[-9,-9],[-28,-38],[-8,-6],[-13,-8],[-38,-15],[-24,-7],[-13,-7],[-14,-9],[-38,-44],[-12,-20],[-2,-6],[0,-5],[0,-5],[2,-5],[3,-4],[4,-3],[7,-4],[21,-6],[8,-3],[184,-26],[26,-6],[11,-4],[8,-5],[7,-5],[4,-6],[2,-6],[-1,-15],[-21,-57],[-1,-17],[3,-24],[5,-14],[10,-13],[21,-22],[2,-4],[2,-3],[0,-4],[-1,-5],[-2,-4],[-3,-5],[-10,-6],[-19,-8],[-63,-17],[-123,-17],[-96,-23],[-5,-1],[-8,0],[-21,1],[-41,5],[-14,-2],[-15,-6],[-23,-16],[-18,-16],[-4,-5],[-20,-19],[-15,-11],[-80,-42],[-15,-10],[-8,-9],[3,-5],[5,-6],[8,-5],[9,-5],[10,-4],[34,-9],[9,-4],[3,-4],[-22,-9],[-600,-159]],[[3246,6182],[-72,-18],[-46,-8],[-111,-7],[-21,-3],[-250,-58],[-25,-8],[-21,-9],[-6,-4],[-4,-5],[-14,-25],[-8,-10],[-21,0],[-34,6],[-170,45],[-34,12],[-268,40],[-49,13],[-30,12],[-35,8],[-90,5],[-30,1]],[[1907,6169],[19,12],[7,19],[-46,138],[-4,51],[25,53],[58,73],[43,100],[32,49],[116,58],[148,104],[24,27],[-84,30],[-102,15],[-92,20],[-57,49],[-16,56],[5,50],[27,48],[88,97],[1,40],[-24,114]],[[5469,5496],[29,-22],[21,-9],[23,-6],[42,-7],[29,-3],[11,0],[54,2],[43,4],[22,2],[41,-1],[22,-3],[18,-5],[19,-11],[30,-22],[38,-17],[46,-16],[43,-10],[55,-4],[20,0],[13,2],[10,4],[4,4],[1,5],[-1,16],[2,7],[4,5],[10,6],[9,0],[7,0],[8,-3],[52,-22]],[[6194,5392],[-11,-3],[23,-24],[9,-27],[-1,-57],[8,-25],[36,-53],[8,-25],[23,-45],[55,-51],[225,-150],[24,-11],[34,-9],[351,-163],[92,-25],[192,-40],[34,-3]],[[7296,4681],[-7,-3],[-52,-18],[-153,-88],[-17,-12],[-10,-11],[-136,-101],[-52,-49],[-17,-9],[-14,-4],[-163,-23],[-78,-16],[-282,-109],[-27,-5],[-181,-19],[-73,-15],[-20,-2],[-43,-1],[-134,6],[-45,-4],[-103,-15],[-23,-2],[-22,1],[-65,4],[-44,1]],[[5535,4187],[-99,49],[-33,21],[-98,86],[-33,22],[-193,83],[-8,8],[-5,8],[0,12],[4,8],[6,7],[58,43],[5,5],[16,20],[6,10],[1,6],[-3,7],[-9,8],[-21,15],[-117,57],[-12,3],[-11,2],[-11,-1],[-33,-3],[-12,0],[-11,2],[-11,2],[-11,4],[-9,5],[-8,5],[-34,29],[-15,11],[-9,5],[-22,8],[-25,6],[-108,21],[-10,3],[-10,5],[-8,4],[-129,105],[-65,65],[-39,49],[-30,51],[-20,61],[-3,59],[15,55],[6,9],[11,10],[8,4],[10,5],[44,18],[93,32],[43,20],[7,4],[4,5],[-3,6],[-10,6],[-112,31],[-13,6],[-42,21],[-24,17],[-16,24]],[[4377,5436],[151,30],[51,6],[276,-16],[25,0],[19,2],[101,25],[45,7],[44,11]],[[5089,5501],[73,28],[32,9],[98,16],[26,1],[19,1],[23,-5],[49,-14],[23,-9],[9,-5],[8,-5],[11,-11],[9,-11]],[[4377,5436],[-326,-25],[-38,2],[-43,6],[-22,-2],[-96,-17],[-54,-12],[-11,-5],[-8,-5],[-6,-5],[-5,-5],[-3,-5],[-3,-10],[-1,-30],[-2,-5],[-5,-6],[-7,-5],[-9,-4],[-8,-3],[-8,-1],[-16,-1],[-153,8],[-95,11],[-17,0],[-22,-1],[-86,-18],[-421,-18]],[[1524,5889],[101,41],[58,17],[19,9],[11,9],[17,24],[16,11],[88,26],[18,15],[-63,46],[7,21],[29,21],[79,38],[3,2]],[[3246,6182],[396,-73],[236,-1],[252,-21],[151,-37],[19,-4],[15,-1],[11,1],[21,3],[34,8],[17,3],[23,2],[11,-2],[7,-5],[1,-5],[-3,-6],[-58,-69],[-5,-10],[-1,-5],[-1,-6],[2,-7],[4,-11],[15,-18],[9,-8],[28,-17],[51,-26],[66,-26],[23,-7],[17,-5],[155,-26],[19,-6],[21,-11],[36,-26],[15,-8],[20,-9],[63,-23],[17,-5],[116,-26],[28,-9],[17,-8],[3,-5],[3,-5],[-11,-161]],[[4834,7190],[66,-40],[19,-17],[0,-5],[0,-4],[-12,-43],[-11,-16],[-15,-42],[0,-4],[2,-4],[3,-5],[7,-5],[11,-7],[184,-79],[17,-4],[43,-4],[26,-1],[24,0],[11,2],[14,-1],[15,-3],[151,-72],[7,-5],[21,-20],[6,-4],[9,-6],[23,-11],[19,-6],[23,-6],[69,-12],[180,-16],[219,-32],[12,-2],[12,0],[11,1],[121,20],[15,-4],[15,-11],[86,-139],[13,-11],[46,-30]],[[6296,6542],[-191,-197],[-281,-214],[-55,-54],[-17,-20],[-7,-12],[-6,-19],[0,-14],[14,-28],[4,-4],[5,-5],[6,-3],[9,-2],[265,-14],[52,-7],[199,-46],[10,-4],[12,-7],[3,-6],[0,-6],[-8,-24],[-13,-24],[-7,-8],[-7,-7],[-8,-4],[-9,-5],[-10,-4],[-117,-28],[-22,-8],[-37,-20],[-11,-4],[-12,-2],[-11,-1],[-55,0],[-23,-1],[-11,-1],[-20,-5],[-16,-5],[-91,-37],[-23,-7],[-48,-12],[-147,-23],[-68,-17],[-31,-14],[-23,-12],[-9,-8],[-3,-7],[5,-4],[9,-5],[41,-16],[16,-11],[13,-10],[17,-19],[5,-8],[3,-8],[3,-15],[-5,-8],[-8,-5],[-10,-2],[-10,-1],[-10,0],[-10,1],[-20,3],[-48,12]],[[4522,7180],[130,17],[46,0],[136,-7]],[[8853,6040],[-99,-36],[-47,5],[-48,11],[-81,19],[-48,14],[24,9],[32,12],[280,-1],[23,-16],[-36,-17]],[[9305,6199],[59,-29],[-19,-18],[-238,-47],[-124,-34],[-45,-9],[-21,24],[34,18],[41,18],[44,30],[6,19],[-2,8],[2,13],[19,5],[5,-13],[37,-11],[59,5],[5,30],[-17,16],[19,19],[29,12],[20,15],[22,15],[41,-11],[70,-5],[12,-13],[-64,-10],[-9,-8],[-15,-9],[-6,-16],[22,-4],[14,-10]],[[8657,6650],[16,-39],[-11,-42],[-82,-47],[-95,-22],[-69,-36],[18,-31],[-20,-29],[-1,-54],[-100,-34],[-21,-10],[-16,-6],[-10,-9],[27,-27],[10,-18],[-89,-19],[-58,-20],[-79,-40],[-38,-36],[-48,-20],[-23,-25],[-42,-22],[-40,-12],[-163,-24],[-23,-7],[-9,-9],[-4,-14],[-12,-17],[-18,-14],[-4,-27],[-17,-15],[-5,-25],[-37,-16],[-55,-13],[-228,-16],[-84,-21],[-130,-100],[-118,-14],[-25,1],[-22,4],[-28,2],[-29,-5],[-15,-7],[-10,-9],[-52,-32],[-23,-10],[-32,-4],[-116,-23],[-49,-18],[-98,-18],[-60,-30],[-30,-42],[-88,-82],[-52,-39],[-34,-6],[-21,-10],[-1,0]],[[6296,6542],[19,17],[47,33],[128,68],[96,39],[52,16],[31,5],[61,3]],[[6730,6723],[282,-22],[20,-3],[12,-3],[69,-23],[20,-4],[14,-1],[19,0],[34,4],[272,59],[48,16],[41,18],[9,6],[5,4],[2,3],[1,3],[-2,9],[1,5],[2,6],[5,4],[7,4],[45,13],[11,4],[7,4],[5,5],[6,10],[5,4],[7,3],[9,2],[11,1],[218,-29],[31,-1],[24,7],[12,4],[11,5],[9,5],[16,11],[38,38],[11,1],[16,-4],[29,-16],[15,-12],[9,-9],[19,-48],[9,-15],[33,-42],[13,-11],[14,-6],[18,-4],[35,-1],[19,2],[14,4],[31,20],[10,4],[13,1],[17,-4],[28,-15],[26,-16],[68,-54],[35,-9],[115,-10],[4,0]],[[5511,8495],[35,-30],[11,-26],[-1,-9],[-2,-8],[-26,-35],[-19,-16],[-33,-21],[-50,-26],[-48,-21],[-160,-45],[-60,-11],[-13,-3],[-19,-8],[-4,-7],[3,-6],[9,-5],[89,-39],[25,-9],[27,-7],[110,-13],[121,-20],[23,-7],[10,-4],[11,-9],[9,-14],[13,-30],[4,-14],[0,-10],[-6,-12],[-8,-10],[-13,-11],[-45,-28]],[[5504,7981],[-534,-200],[-33,-17],[-18,-12],[0,-6],[1,-5],[18,-33],[3,-11],[1,-12],[-2,-11],[-3,-6],[-36,-42],[-3,-11],[2,-10],[2,-4],[3,-4],[4,-4],[14,-11],[19,-10],[16,-8],[8,-5],[5,-7],[-1,-12],[-8,-6],[-9,-3],[-62,-6],[-46,-9],[-103,-30],[-26,-4],[-26,-1],[-26,0],[-31,3],[-170,26],[-16,0],[-18,-2],[-27,-6],[-10,-6],[-4,-6],[3,-6],[5,-5],[5,-5],[329,-183],[97,-85],[3,-4],[4,-12]],[[7641,7209],[285,-43],[167,-15],[92,-2],[25,1],[17,2],[7,4],[20,22],[27,20],[21,11],[38,16],[54,18],[55,15],[203,34]],[[8652,7292],[2,-10],[23,-30],[37,-20],[112,-16],[-67,-28],[-17,-37],[-13,-34],[10,-59],[-30,-28],[12,-50],[58,-43],[87,-50],[51,-18],[61,-13],[34,-8],[-4,-26],[-69,-1],[-48,-17],[-47,-44],[-72,-27],[-35,-31],[-33,-33],[-47,-19]],[[6730,6723],[4,52],[-3,18],[-4,5],[-10,11],[-58,39],[-11,9],[-6,9],[-2,5],[0,3],[0,13],[7,37],[-1,14],[-6,22],[-5,10],[-8,9],[-19,19],[-9,11],[-3,5],[5,10],[10,16],[75,86],[28,20],[51,29],[11,10],[2,10],[-2,11],[-37,45],[-1,5],[3,7],[7,6],[15,11],[48,14],[107,16]],[[6918,7310],[-18,-32],[-3,-11],[1,-7],[3,-7],[6,-7],[9,-5],[10,-5],[58,-20],[29,-16],[13,-3],[17,-2],[78,9],[22,-1],[87,-11],[14,-3],[11,-4],[9,-5],[7,-6],[5,-8],[4,-25],[4,-7],[8,-6],[12,-4],[142,-29],[15,-1],[14,0],[14,3],[12,6],[12,9],[12,15],[8,7],[10,7],[60,15],[11,8],[5,4],[22,41]],[[7641,7209],[-22,10],[-58,10],[-15,1],[-13,-2],[-9,-3],[-7,-5],[-3,-3],[-9,-13],[-5,-5],[-14,-10],[-16,-5],[-19,-1],[-50,2],[-10,1],[-11,3],[-11,3],[-10,4],[-7,5],[-7,6],[-1,2],[0,9],[4,15],[6,7],[10,4],[78,15],[22,7],[9,6],[6,6],[-1,10],[-6,6],[-23,15],[0,6],[7,5],[21,8],[14,3],[14,2],[54,6],[88,14],[39,11],[20,8],[12,10],[10,14],[16,45],[0,10],[-8,42],[-1,26],[1,6],[23,20],[97,54],[6,3]],[[7862,7602],[6,-2],[47,-12],[75,-13],[147,24],[25,-3],[23,-6],[45,-19],[-27,-19],[-15,-27],[13,-27],[28,-20],[99,-22],[221,-39],[98,5],[71,-27],[35,-32],[-93,-17],[-13,-32],[5,-22]],[[5504,7981],[52,-9],[23,-2],[50,-1],[217,14],[87,1],[77,-22],[22,-4],[97,-8],[13,-3],[22,-5],[17,-7],[66,-30],[12,-3],[15,1],[15,4],[19,14],[8,10],[5,7],[16,49],[7,13],[5,4],[8,2],[9,-5],[18,-10],[15,-1],[19,4],[35,16],[15,9],[21,18],[41,13],[145,22]],[[6675,8072],[-80,-67],[1,-21],[135,-32],[13,-6],[14,-9],[22,-19],[8,-10],[3,-10],[-23,-62],[-28,-51],[-17,-21],[-4,-4],[-27,-19],[-115,-67],[-13,-10],[-4,-5],[1,-6],[6,-10],[278,-255],[24,-16],[19,-17],[30,-45]],[[7466,8369],[-25,-18],[-35,-52],[-34,-56],[-29,-57],[-6,-70],[-5,-72],[12,-46],[31,-46],[18,-25],[12,-9],[11,-13],[14,-30],[9,-13],[54,-37],[39,-20],[34,-9],[29,-13],[65,-90],[33,-18],[169,-73]],[[6675,8072],[65,12],[23,6],[22,8],[8,4],[11,8],[10,12],[12,22],[4,12],[0,9],[-12,17],[1,5],[6,6],[17,4],[13,3],[176,16],[9,5],[5,8],[-8,14],[-11,9],[-12,7],[-6,5],[1,5],[14,7],[34,12],[9,22],[-1,79]],[[9064,2780],[1,1],[159,140],[41,23],[42,18],[45,16],[48,13],[116,21],[38,9],[89,30],[21,11],[16,10],[6,6],[3,5],[-4,6],[-11,3],[-21,1],[-70,-6],[-18,0],[-17,4],[-23,6],[-644,251],[-8,6],[-4,5],[0,5],[2,6],[23,42],[3,17],[-10,118],[3,11],[4,10],[32,40],[13,22],[8,24],[0,9],[-1,5],[-6,9],[-5,5],[-7,5],[-13,5],[-16,5],[-30,8],[-31,10],[-104,45],[-19,10],[-15,11],[-7,7],[-32,64],[-3,3],[-5,4],[-7,4],[-11,5],[-13,5],[-678,149],[-53,6],[-113,6],[-43,7],[-23,6],[-24,9],[-10,5],[-7,4],[-5,5],[0,5],[3,5],[7,4],[49,8],[7,4],[3,5],[1,5],[3,5],[19,14],[5,5],[1,4],[-1,5],[-7,5],[-24,1],[-189,-11],[-86,6],[-22,-1],[-20,-3],[-429,-89],[-33,-10],[-167,-34],[-23,-7],[-16,-11],[-12,-4],[-16,-2],[-30,0],[-125,11],[-45,2],[-15,-2],[-18,-4],[-30,-10],[-17,-7],[-13,-6],[-50,-34],[-9,-5],[-10,-3],[-83,-17],[-61,-7],[-53,-4],[-212,-2]],[[5948,3891],[35,38],[6,9],[4,10],[-1,104],[-1,5],[-3,4],[-12,6],[-21,7],[-48,7],[-25,3],[-19,0],[-22,0],[-23,0],[-43,5],[-52,9],[-46,18],[-142,71]],[[7296,4681],[72,-8],[169,12],[185,24],[156,40],[46,6],[57,-48],[-7,-94],[-72,-29],[-40,-8],[-30,-18],[-12,-24],[14,-21],[58,-46],[33,-17],[52,-8],[104,11],[185,52],[88,12],[42,9],[39,21],[18,26],[-16,24],[-41,22],[-7,12],[73,27],[28,-9],[32,-2],[26,5],[16,16],[16,0],[40,-13],[103,-22],[43,-13],[32,-19],[85,-61],[8,-13],[12,-69],[-65,-49],[51,-83],[-21,-40],[71,10],[48,-24],[49,-30],[57,-15],[58,-6],[126,-30],[60,-10],[-16,-7],[-7,-2],[-10,-1],[-241,37],[-118,6],[-29,-43],[24,-16],[40,-15],[38,-20],[17,-29],[22,-14],[54,0],[108,9],[104,-10],[206,-36],[109,0],[40,13],[-13,18],[-42,17],[-44,7],[-27,9],[-35,20],[-38,18],[222,-74],[136,-30],[16,-2],[2,0],[2,-61],[-70,-130],[-6,-21],[-2,-77],[-43,-92],[17,-142],[-35,-144],[1,-76],[51,-52],[163,-77],[57,-49],[9,-57],[-44,-43],[-49,-22],[-30,-14],[-171,-54],[-641,-145]],[[8446,5014],[73,-41],[78,-34],[48,-14],[49,-10],[43,-13],[29,-19],[-163,-70],[-41,-9],[-15,-6],[-11,-11],[-24,-45],[-15,0],[7,36],[8,12],[-38,-6],[-31,-43],[-44,-31],[-41,-22],[-15,-24],[-41,19],[-1,24],[16,22],[-44,32],[-42,9],[-39,16],[-42,6],[-45,-21],[-21,-21],[-45,-4],[-57,21],[-16,20],[-14,38],[1,36],[47,39],[-30,56],[2,42],[90,16],[199,-20],[107,-3],[78,23]],[[5948,3891],[-12,-20],[0,-6],[3,-8],[9,-8],[10,-6],[13,-5],[100,-36],[59,-30],[10,-10],[-1,-7],[-10,-3],[-22,-4],[-30,-4],[-10,-3],[-10,-3],[-7,-4],[-6,-5],[-3,-5],[-2,-5],[2,-25],[11,-44],[6,-10],[18,-14],[13,-12],[4,-7],[-2,-7],[-4,-5],[-29,-22],[-60,-34],[-7,-5],[-6,-6],[-7,-28],[-4,-56],[2,-12],[4,-5],[98,-82],[6,-6],[7,-9],[-6,-6],[-10,-2],[-11,-1],[-11,0],[-13,2],[-27,5],[-61,16],[-264,101],[-78,46],[-10,4],[-21,8],[-21,6],[-44,8],[-38,4],[-30,1],[-79,-4],[-65,-6],[-59,-10],[-51,-15],[-100,-40],[-26,-7],[-61,-13],[-2872,-279],[-45,-7]],[[2088,3141],[-4,2],[-28,45],[-24,141],[-33,192],[-33,44],[-248,182],[-232,170],[-55,16],[-51,-5],[-51,1],[-50,8],[-48,11],[-271,92],[-89,14],[-118,6],[-90,12],[-63,33],[-37,64],[-4,181]],[[9064,2780],[-756,-172],[-67,-34],[-99,-108],[-80,-39],[-89,-26],[-82,-31],[-48,-7],[-45,8],[-44,12],[-46,3],[-55,-28],[-20,-110],[-32,-45],[-103,-46],[-42,-47],[-29,-21],[-36,-17],[-40,-16],[-134,-25],[-275,2],[-126,-48],[-165,-140],[-157,-83],[-38,-32],[-26,-89],[4,-24],[183,-234],[75,-144],[77,-78],[3,-25],[-13,-53],[11,-48],[61,-96],[-1,-46],[-39,-55],[-151,-151],[-223,-123],[-258,-201],[-304,-190],[-63,-18],[-178,-6],[-72,-9],[-548,-140],[-69,150],[-70,151],[-69,150],[-70,151],[-69,150],[-70,151],[-69,150],[-69,151],[-70,150],[-69,151],[-70,150],[-69,150],[-69,151],[-70,150],[-69,151],[-70,150],[-6,15],[-45,98],[-28,25],[-34,20],[-412,141],[-512,176],[-391,134],[-300,90],[-67,35]]],
transform:{scale:[.00040847069726973216,.0007314618185818553],translate:[7.47983239800007,30.228905335000093]}},m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();