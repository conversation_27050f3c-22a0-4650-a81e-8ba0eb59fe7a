!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo={type:"Topology",objects:{sdn:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Blue Nile"},id:"SD.BN",arcs:[[0,1]]},{type:"MultiPolygon",properties:{name:"Red Sea"},id:"SD.RS",arcs:[[[2]],[[3,4,5]]]},{type:"Polygon",properties:{name:"Southern Darfur"},id:"SD.SD",arcs:[[6,7,8,9]]},{type:"Polygon",properties:{name:"Eastern Darfur"},id:"SD.SD",arcs:[[10,11,12,-7,13]]},{type:"Polygon",properties:{name:"Western Darfur"},id:"SD.WD",arcs:[[14,15,16]]},{type:"Polygon",properties:{name:"Central Darfur"},id:"SD.WD",arcs:[[-9,17,-16,18]]},{type:"Polygon",properties:{name:"Khartoum"},id:"SD.KH",arcs:[[19,20,21,22,23,24,25]]},{type:"Polygon",properties:{name:"Gezira"},id:"SD.GZ",arcs:[[26,27,-22,28]]},{type:"Polygon",properties:{name:"Gedarif"},id:"SD.GD",arcs:[[29,30,31,-29,-21]]},{type:"Polygon",properties:{name:"River Nile"},id:"SD.RN",arcs:[[32,-26,33,34,-5]]},{type:"Polygon",properties:{name:"Northern"},id:"SD.NO",arcs:[[-34,-25,35,36,37]]},{type:"Polygon",properties:{name:"White Nile"},id:"SD.WN",arcs:[[-28,38,39,40,41,-23]]},{type:"Polygon",properties:{name:"Sennar"},id:"SD.SI",arcs:[[-32,42,-2,-39,-27]]},{type:"Polygon",properties:{name:"North Darfur"},id:"SD.ND",arcs:[[43,-14,-10,-19,-15,44,-37]]},{type:"Polygon",properties:{name:"South Kordufan"},id:"SD.KS",arcs:[[-41,45,-12,46]]},{type:"Polygon",properties:{name:"North Kordufan"},id:"SD.KN",arcs:[[-24,-42,-47,-11,-44,-36]]},{type:"Polygon",properties:{name:"Kassala"},id:"SD.KA",arcs:[[47,-30,-20,-33,-4]]}]}},arcs:[[[7942,2355],[-13,-7],[-23,-18],[-9,-14],[-8,-17],[-7,-18],[-4,-18],[0,-15],[4,-28],[-1,-30],[4,-15],[13,-30],[1,-14],[-2,-14],[-75,-203],[-2,-16],[6,-20],[10,-14],[8,-15],[-2,-23],[-40,-141],[0,-8],[6,-10],[17,-16],[4,-11],[-5,-25],[-10,-18],[-32,-30],[-11,-12],[-3,-9],[0,-9],[-3,-13],[-8,-13],[-6,-1],[-8,3],[-10,-1],[-7,-8],[-8,-23],[-5,-6],[-6,3],[-1,11],[3,22],[-4,11],[-8,9],[-17,14],[-72,77],[-8,4],[-10,-5],[-72,-60],[-10,-11],[-9,-16],[-23,-55],[-52,-88],[-4,-26],[2,-31],[29,-162],[1,-34],[-5,-31],[-2,-4],[-6,-8],[-1,-3],[0,-9],[2,-18],[-1,-8],[-14,-25],[-35,-40],[-74,-255],[-18,-110],[2,-56],[-101,0],[-2,1],[-2,2],[-1,4],[-2,5],[-5,21],[-2,32],[4,57],[12,67],[39,94],[2,8],[0,4],[0,5],[-4,17],[-4,14],[-2,22],[6,49],[1,16],[-1,19],[-3,12],[-26,82],[-9,13],[-129,130],[-129,130],[-47,70],[-5,5],[-7,4],[-131,59],[-6,6],[1,8],[4,12],[16,34],[2,9],[2,14],[-30,273],[-29,274],[0,11],[3,7],[2,4],[8,12],[7,12],[7,21],[2,8],[9,98],[-1,85],[34,143],[4,61]],[[6787,2605],[18,16],[10,7],[12,6],[13,4],[13,3],[17,-2],[67,-21],[18,-1],[10,5],[432,325],[77,42],[21,2],[20,-6],[23,-15],[21,-22],[29,-36],[131,-251],[73,-108],[67,-80],[56,-52],[5,-7],[12,-25],[10,-34]],[[9202,8909],[-6,0],[-7,16],[0,24],[4,22],[9,15],[6,-5],[2,-2],[3,-9],[-7,-49],[-4,-12]],[[9028,6186],[-544,-53],[-11,0],[-17,7],[-212,131]],[[8244,6271],[-294,235],[-21,26],[-11,15],[-58,108],[-77,180],[-11,19],[-22,30],[-71,79],[-138,97],[-110,56],[-88,30],[-8,6],[-6,10],[-281,799],[-2,10],[1,7],[1,4],[20,52],[7,33],[2,19],[0,34],[-10,121],[-28,112],[-5,40],[2,58],[10,76],[13,42],[9,20],[1,3],[0,3],[-5,11],[-1,3],[-1,8],[1,44],[3,31],[0,6],[0,7],[-4,17],[-7,24],[-11,27],[-7,12],[-11,27],[-3,6],[-4,6],[-4,5],[-2,3],[-12,25],[-12,40],[-1,3],[-4,6],[-3,2],[-2,2],[-4,2],[-27,6],[-6,2],[-7,3],[-5,4],[-10,9],[-5,6],[-36,58],[-7,7],[-32,23],[-5,5],[-2,3],[-8,15],[-1,3],[-1,5],[0,6],[1,15],[1,8],[10,37],[4,17],[0,24],[-4,53],[1,5],[2,6],[20,29],[3,6],[4,8],[2,7],[4,26],[1,10],[-1,9],[-2,12],[-3,16],[-1,4],[0,33],[-1,8],[-5,15],[-1,3],[-2,3],[-4,5],[-8,7],[-10,13],[-9,14],[168,158],[-235,210]],[[6758,9828],[12,0],[126,0],[139,0],[138,0],[135,0],[4,0],[138,0],[139,0],[138,0],[139,0],[138,0],[139,0],[139,0],[138,0],[139,0],[138,0],[139,0],[139,0],[-2,-11],[-2,-3],[-2,-2],[-10,-1],[-10,-5],[14,-12],[6,-10],[0,-89],[14,-101],[-6,-21],[9,-7],[2,-8],[0,-9],[4,-8],[6,-8],[5,-7],[32,-89],[77,-138],[51,-57],[63,-94],[4,-12],[-1,-15],[-4,-12],[-7,-6],[-12,5],[-12,-10],[-11,10],[-16,33],[-4,7],[-4,9],[-2,8],[4,4],[8,-3],[4,-6],[3,-7],[3,-7],[14,-12],[8,-4],[5,3],[-1,6],[-15,20],[-9,29],[-7,10],[-9,-4],[-17,15],[-4,-8],[-5,2],[-4,7],[-2,9],[1,9],[4,9],[1,9],[-6,9],[-17,4],[-9,-13],[-4,-19],[1,-18],[3,-4],[7,-1],[2,-5],[-2,-8],[-2,-7],[2,-18],[0,-9],[-2,-9],[-6,-6],[-6,-3],[-4,-4],[0,-12],[3,-6],[17,-24],[16,-43],[7,-47],[6,-97],[8,-46],[27,-88],[1,-48],[-1,-12],[-3,-11],[-6,-7],[-18,3],[-2,-10],[2,-22],[-5,-52],[5,-30],[0,-11],[14,-43],[5,-26],[3,-24],[3,-36],[-9,-28],[0,-5],[-2,-8],[-7,-10],[-3,-7],[0,-20],[4,-25],[38,-117],[3,-25],[0,-48],[-1,-14],[-3,-8],[-10,-16],[-2,-11],[4,-45],[-1,-14],[1,-6],[3,-7],[9,-19],[12,-42],[24,-157],[5,-76],[2,-9],[5,-5],[5,-4],[4,-5],[0,-5],[-3,-10],[0,-5],[14,-24],[7,-37],[13,-47],[8,-31],[5,-38],[8,-14],[12,-28],[14,-28],[20,-21],[3,-9],[5,-8],[7,-4],[7,3],[7,4],[4,-1],[36,6],[20,11],[3,-14],[13,-8],[11,-3],[12,1],[4,-8],[1,-12],[10,-11],[-5,-28],[10,6],[7,2],[6,-6],[10,-12],[67,-22],[25,-29],[16,-33],[26,-30],[17,-13],[26,-12],[7,-10],[-7,-13],[-16,-10],[-4,-25],[16,-42],[29,-31],[32,-9],[24,5],[3,27],[12,4],[21,15],[3,-8],[-5,-6],[-8,-5],[-6,-6],[-1,-6],[0,-8],[-1,-9],[-7,-8],[-9,-3],[-8,1],[-7,-1],[-4,-7],[8,1],[20,4],[8,-2],[6,-4],[5,-4],[6,-5],[8,-2],[16,-2],[8,-4],[9,-2],[7,4],[1,6],[-8,5],[10,10],[11,-14],[19,-36],[-5,-10],[10,-8],[15,-1],[9,8],[7,-11],[2,-30],[3,-9],[12,-2],[6,11],[-1,17],[-8,14],[10,-7],[24,-30],[4,-12],[0,-11],[2,-8],[6,-16],[-1,-7],[-85,-84],[-17,-29],[-35,-113],[-17,-31],[-12,-11],[-29,-17],[-11,-12],[-3,-14],[1,-16],[-2,-15],[-10,-11],[-14,-3],[-10,8],[-9,11],[-10,7],[-21,-8],[-3,-1],[-3,-6],[1,-5],[1,-5],[0,-8],[-6,-14],[-10,-10],[-9,0],[-3,16],[0,14],[0,13],[-6,7],[-13,0],[-14,-6],[-1,0],[-40,-33],[-19,-28],[-11,-11],[-15,-4],[-13,5],[-18,21],[-12,4],[-21,-14],[-26,-55],[-24,-15],[-24,-4],[-79,-29],[-7,-5],[-1,-8],[7,-34],[0,-14],[-3,-30],[-3,-16],[-13,-27],[-3,-17],[-5,-15],[-34,-47],[-55,18],[-18,-1],[-28,-21],[-18,-2],[-9,2],[-32,-6],[-6,1],[-23,17],[-6,2],[-16,4],[-10,5],[-9,7],[-10,5],[-12,-2],[-4,-5],[0,-1]],[[2045,3235],[34,-284],[-11,-203],[-60,-102],[5,-502],[148,-197],[99,-102],[87,-278],[55,-203],[-3,-80]],[[2399,1284],[-6,-1],[-27,-9],[-12,-7],[-6,-2],[-4,-1],[-24,1],[-4,0],[-9,-3],[-14,-3],[-19,0],[-3,-1],[-26,-8],[-44,-19],[-2,0],[-7,-3],[-7,-1],[-24,1],[-7,-1],[-7,-1],[-9,-4],[-5,-3],[-6,-3],[-8,-1],[-3,0],[-7,-1],[-3,-1],[-11,-6],[-4,-1],[-3,0],[-7,1],[-3,0],[-6,-3],[-3,-1],[-3,1],[-4,0],[-3,2],[-6,3],[-4,1],[-7,2],[-4,-1],[-4,-1],[-5,-3],[-4,0],[-7,1],[-4,0],[-14,-7],[-7,-2],[-15,2],[-7,-2],[-8,-4],[-10,-3],[-3,-2],[-5,-5],[-6,-9],[-29,-79],[-2,-8],[0,-6],[1,-8],[1,-4],[5,-14],[2,-7],[0,-4],[0,-5],[-1,-4],[-6,-22],[-16,-40],[-3,-12],[-6,-14],[-5,-8],[-8,-10],[-16,-28],[-8,-10],[-20,-16],[-21,-21],[-5,-3],[-5,-3],[-7,-2],[-5,0],[-2,-1],[-3,-3],[-2,-5],[-2,-15],[1,-196],[-1,-9],[-4,-17],[-37,-50],[-17,-20],[-5,-8],[-4,-13],[-11,-141],[-1,-4],[-2,-7],[-54,-113],[-4,-11],[0,-5],[0,-79],[-4,-5],[-6,-4],[-29,-12],[-54,-7],[-13,-3],[-66,-51],[-18,-10],[-34,-32],[-3,-6],[-2,-11],[-2,-4],[-34,-6],[-62,13],[-17,-4],[-11,2],[-24,12],[-33,-4],[-38,11],[-20,-1],[-19,-10],[-10,-4],[-19,6],[-19,0],[-8,4],[-9,7],[-9,5],[-10,2],[-11,-3],[-8,-6],[-14,-13],[-9,-2],[-12,6],[-9,17],[-5,19],[0,18],[8,19],[24,34],[10,21],[8,42],[1,25],[-5,16],[-10,1],[-22,-27],[-17,-1],[-15,11],[-8,15],[-2,18],[13,94],[3,5],[7,13],[9,4],[19,-1],[10,3],[7,5],[13,16],[23,36],[8,7],[5,9],[-1,11],[-6,22],[1,13],[15,58],[0,5],[-4,6],[-3,1],[-5,0],[-4,10],[-2,6],[1,23],[-1,10],[-6,18],[0,9],[7,21],[22,42],[8,21],[3,29],[-17,127],[-13,33],[-99,196],[-99,197],[-108,129],[-62,53]],[[712,1480],[174,311],[153,122],[126,102],[120,489],[26,272],[1,23],[11,29],[35,55],[313,378]],[[1671,3261],[182,-32],[99,-5],[93,11]],[[3204,2322],[20,-120],[0,-179]],[[3224,2023],[10,-84],[0,-31],[0,-3],[39,-194],[209,-633],[11,-60],[89,-234],[41,-109]],[[3623,675],[-6,-3],[-10,-3],[-58,-3],[-77,13],[-3,0],[-12,-4],[-4,-1],[-48,8],[-85,-1],[-28,6],[-154,-4],[-230,-96],[-6,-1],[-34,4],[-15,8],[-26,21],[-113,162],[-112,162],[-46,21],[-9,11],[-7,11],[-35,72],[-4,6],[-2,2],[-4,4],[-1,1],[-16,6],[-9,5],[-22,15],[-4,5],[-4,5],[-2,3],[-1,4],[-4,114],[0,5],[-2,3],[-3,6],[-25,40],[-3,2]],[[2045,3235],[27,-3],[26,-6],[20,-11],[16,-12],[112,-125],[80,-57],[30,-17],[129,-129],[11,-13],[6,-14],[1,-15],[-6,-12],[-12,-9],[-54,-18],[-12,-6],[-6,-6],[-3,-6],[-2,-14],[6,-14],[17,-26],[38,-37],[49,-63],[59,-112],[31,-34],[22,-13],[22,-2],[18,4],[20,2],[25,-4],[34,-14],[20,-15],[12,-11],[26,-35],[30,-28],[28,-14],[22,-8],[233,4],[84,-20]],[[1295,5197],[9,-17],[-37,-246],[4,-36],[-2,-111],[3,-44],[9,-41],[14,-23],[31,-40],[22,-35],[18,-40],[15,-47],[13,-66],[2,-32],[-2,-29],[-5,-25],[-9,-28],[-126,-255],[-191,-254],[-42,-92],[-19,-73],[-2,-35],[8,-23],[12,-6]],[[1020,3599],[-68,-91],[-72,-7],[-43,-27],[-186,-333],[-165,-224],[33,-264],[-41,-150]],[[478,2503],[-3,4],[-5,-11],[-8,6],[-23,-5],[-39,-15],[-6,-6],[-4,-2],[-4,-2],[16,81],[0,9],[0,9],[-2,9],[-44,165],[-19,37],[-2,9],[14,25],[30,85],[-8,9],[-61,28],[-75,60],[-17,-31],[-19,-22],[-23,-15],[-28,-10],[-23,-4],[-25,0],[-25,6],[-21,12],[-12,15],[-23,53],[-16,25],[-3,8],[1,5],[10,23],[15,55],[49,113],[17,29],[20,24],[11,7],[64,31],[10,9],[55,70],[21,34],[5,31],[-7,16],[-21,31],[-8,18],[-3,14],[0,43],[-9,28],[-38,43],[-10,29],[0,16],[1,12],[-2,11],[-10,14],[-14,16],[1,7],[15,29],[48,74],[6,9],[14,18],[17,14],[95,54],[11,10],[23,25],[13,9],[16,4],[14,6],[9,13],[1,22],[-5,23],[-5,15],[-10,8],[-34,7],[-10,6],[-7,11],[-6,16],[-1,9],[0,6],[6,12],[2,4],[8,4],[3,4],[1,5],[-2,5],[-3,3],[-2,5],[-9,79],[-4,10],[-19,17],[-13,27],[11,26],[24,23],[26,14],[119,39],[6,6],[3,10],[-1,6],[-9,24],[-3,13],[-5,40],[0,19],[5,12],[22,18],[11,13],[4,12],[0,14],[3,18],[7,15],[54,68],[12,12],[13,9],[21,9],[4,7],[27,59],[4,16],[8,86],[-1,25],[-6,23],[-13,21],[-13,16],[-10,20],[-4,21],[4,23],[12,16],[47,35],[38,56],[15,13],[15,2],[28,4],[92,-23],[44,5],[79,35],[39,10],[68,0],[72,-13],[70,-29],[16,-1],[7,22],[0,1]],[[712,1480],[-85,152],[-1,20],[23,104],[4,7],[10,13],[1,8],[1,15],[2,14],[3,13],[10,26],[3,14],[-1,21],[-1,10],[-15,54],[-8,53],[-8,9],[-14,0],[-57,-7],[-6,3],[0,22],[-2,7],[-15,17],[-48,20],[-21,13],[-21,25],[-18,31],[-12,35],[-3,35],[33,227],[11,38],[2,13],[-1,11]],[[1020,3599],[17,-3],[33,0],[17,-4],[17,-9],[17,-14],[18,-20],[18,-14],[17,-10],[64,-25],[44,-25],[18,-3],[26,3],[26,11],[94,66],[98,45],[24,7],[22,2],[22,-1],[19,-6],[13,-10],[10,-18],[4,-17],[-1,-13],[-6,-15],[-9,-19],[-23,-36],[-82,-96],[-13,-21],[-8,-25],[0,-21],[8,-17],[16,-13],[22,-7],[109,-10]],[[7375,5415],[1,-41],[17,-66],[81,-218]],[[7474,5090],[-205,40],[-133,56],[-17,0],[-24,-15],[-28,-30],[-51,-87],[-29,-71]],[[6987,4983],[-143,-23],[-23,0],[-26,-4],[-15,-8],[-10,-7],[-26,-25],[-7,-8],[-3,-4],[-7,-4],[-4,-2],[-4,1],[-28,7],[-4,1],[-106,-19],[-128,-45],[-8,-4],[-3,-3],[-2,-4],[-5,-18],[-2,-7]],[[6433,4807],[-67,4],[-22,-4],[-10,-5],[-17,-6],[-7,0],[-6,0],[-43,16],[-11,7]],[[6250,4819],[-34,126],[-17,45],[-129,264],[-91,146],[-148,400]],[[5831,5800],[125,92],[23,6]],[[5979,5898],[10,-35],[10,-19],[79,-103],[27,-26],[30,-16],[313,-61],[29,-12],[188,-124],[294,-94],[74,-48],[51,-18],[23,-4],[40,1],[40,11],[79,36],[21,13],[88,16]],[[7247,3944],[-25,5],[-12,5],[-29,6],[-43,-3],[-17,3],[-7,3],[-39,30],[-8,3],[-9,2],[-8,1],[-8,-1],[-16,-8],[-20,-15],[-38,-36],[-48,-58],[-18,-15],[-15,-2],[-14,10],[-50,73],[-23,16],[-25,4],[-25,-8],[-21,-15],[-19,-21],[-15,-29],[-12,-27],[-29,-108],[-13,-18],[-18,-1],[-29,37],[-13,3],[-14,-10],[-11,-23],[-5,-21],[-6,-65]],[[6545,3661],[-54,-73],[-16,-11],[-14,-1],[-9,12],[-3,23],[1,125],[-2,26],[-5,30],[-30,73],[-52,104],[-23,59],[-16,58],[-3,21],[-1,14],[2,14],[4,18],[29,80],[38,80],[34,47],[19,21],[13,23],[8,23],[3,27],[-3,113],[-3,27],[-5,16],[-4,6],[-7,5],[-41,19],[-11,10],[-7,14],[0,15],[5,12],[11,6],[14,1],[13,-1],[11,2],[7,6],[5,17],[-1,18],[-4,20],[-15,47]],[[6987,4983],[52,-71],[19,-13],[116,-48],[97,-61],[2,-2],[2,-3],[41,-106],[86,-148],[5,-15],[1,-28],[-191,-404],[-14,-38],[3,-13],[3,-8],[4,-5],[4,-3],[3,-4],[1,-2],[-1,-2],[-5,-1],[-1,-2],[-1,-3],[2,-4],[10,-12],[3,-5],[19,-38]],[[7474,5090],[85,-24],[71,-31],[96,-61],[32,-25],[26,-27],[23,-29],[32,-50],[11,-22],[81,-240],[136,-182],[29,-25],[27,-4],[34,8],[278,122],[48,11],[206,6],[35,-5]],[[8724,4512],[7,-72],[9,-92],[9,-92],[13,-136],[-53,-201],[-3,-15],[0,-19],[4,-18],[10,-37],[2,-37],[-11,-32],[-25,-74],[-2,-15],[-2,-42],[-6,-21],[-88,-154],[-58,-243],[1,-14],[5,-12],[2,-6],[0,-7],[-5,-11],[-9,-10],[-7,-13],[0,-16],[14,-30],[3,-11],[0,-18],[-12,-82],[-5,-14],[-9,-5],[-16,6],[-6,3],[-3,3],[-4,3],[-7,1],[-5,0],[-180,-32],[-15,-6],[-1,0]],[[8271,2941],[-1,0],[-198,54],[-57,28],[-23,18],[-18,19],[-16,22],[-9,10],[-126,88],[-7,7],[-18,26],[-31,35],[-21,35],[-10,12],[-25,23],[-14,10],[-10,5],[-7,2],[-56,29],[-8,6],[-4,5],[-4,5],[-16,25],[-8,8],[-6,5],[-7,4],[-22,9],[-26,13],[-13,10],[-45,72],[-34,80],[-16,31],[-3,7],[-2,7],[0,8],[2,7],[3,6],[9,10],[5,4],[4,5],[4,5],[3,6],[2,8],[0,7],[-1,5],[-2,5],[-4,1],[-6,0],[-7,-1],[-6,-1],[-6,3],[-4,8],[-5,2],[-3,0],[-10,-7],[-7,0],[-8,2],[-10,10],[-7,5],[-3,1],[-4,-7],[-3,0],[-5,7],[-21,40],[-4,4],[-5,2],[-4,5],[-2,6],[-3,10],[-6,12],[0,4],[2,3],[3,2],[2,1],[1,2],[0,4],[-2,5],[-5,8],[-26,26],[-6,8],[-4,7],[-1,5],[0,15],[-1,4],[-1,2],[-7,10],[-4,9]],[[8244,6271],[10,-240],[-8,-87],[-24,-75],[-37,-49],[-38,-32],[-51,-34],[-46,-19],[-47,-8],[-405,0],[-33,-5],[-17,-10],[-148,-244],[-25,-53]],[[5979,5898],[431,736],[65,145],[-11,59],[0,35],[-60,196],[-1,4],[14,305],[-1,4],[-2,3],[-243,160],[-3,4],[-1,4],[0,4],[213,2271]],[[6380,9828],[12,0],[61,0],[61,0],[61,0],[61,0],[61,0],[61,0]],[[5831,5800],[-85,34],[-31,6],[-206,-23],[-2123,86]],[[3386,5903],[-9,2452],[-1477,0],[-12,6]],[[1888,8361],[0,83],[0,70],[0,70],[0,70],[0,70],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[0,92],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[57,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[57,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,-1],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[57,0],[59,0],[57,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[58,0],[23,0],[8,6],[29,69],[2,5],[27,63],[15,20],[11,7],[12,2],[11,-2],[11,-6],[13,-15],[6,-17],[-1,-18],[-14,-47],[-19,-66],[35,0],[17,0],[60,0],[61,0],[61,0],[61,0],[61,0],[61,0],[61,0],[61,0],[61,0],[49,0]],[[6545,3661],[22,-23],[7,-12],[8,-17],[6,-19],[3,-28],[2,-116],[6,-67],[11,-41],[43,-103],[62,-105],[7,-24],[0,-23],[-13,-60],[-5,-39],[3,-45],[80,-334]],[[6787,2605],[-284,4],[-2,-3],[0,-4],[1,-4],[0,-5],[0,-9],[-2,-18],[-1,-9],[0,-10],[5,-34],[7,-26],[1,-8],[1,-10],[-1,-17],[-198,-1],[-198,-2]],[[6116,2449],[-92,126],[-35,27],[-53,5],[-9,2],[-8,4],[-3,10],[-3,12],[-31,252]],[[5882,2887],[-26,245],[-1,30],[2,6],[14,19],[58,64],[94,78],[52,24],[4,3],[4,6],[7,12],[3,8],[3,7],[0,4],[1,5],[-1,4],[-3,17],[-8,25],[-1,9],[0,8],[1,13],[16,61],[2,11],[-1,5],[-1,3],[-2,3],[-3,2],[-9,8],[-3,2],[-34,11],[-13,7],[-5,5],[-15,17],[-6,8],[-16,19],[-16,22],[-39,78],[-35,99],[-6,12],[-2,3],[-3,2],[-13,10],[-25,24],[-22,15],[-12,11],[-2,2],[-2,3],[-3,6],[-2,8],[-4,15],[-10,28],[-9,40],[-9,65],[-1,101],[11,97],[1,6],[13,45],[22,39],[9,10],[5,4],[22,14],[18,9],[14,8],[5,4],[5,5],[5,8],[64,168],[1,4],[4,4],[67,56],[3,3],[0,5],[-2,10],[-1,12],[-1,15],[7,9],[8,6],[190,98]],[[8271,2941],[-8,-5],[-2,-2],[0,-4],[-4,-19],[-1,-8],[-2,-7],[-6,-8],[-5,-3],[-12,-1],[-5,-2],[-5,-8],[-112,-227],[-7,-21],[-1,-21],[-2,-9],[-6,-10],[-6,-6],[-13,-8],[-6,-6],[-4,-9],[-11,-32],[-6,-40],[-5,-18],[-11,-16],[-23,-16],[-5,-6],[-2,-7],[0,-4],[0,-13],[-1,-7],[-18,-25],[-40,-18]],[[3386,5903],[-16,-2],[-317,-2],[-7,-7],[-5,-12],[127,-1307],[3,-10],[6,-7],[23,-19],[26,-60],[64,-246],[88,-304],[-24,-169],[0,-9],[1,-6],[2,-4],[38,-75],[7,-28],[-2,-28],[-8,-35],[-2,-7],[-8,-13],[-60,-87],[-8,-10],[-2,-7],[-2,-11],[-3,-57],[-2,-9],[-17,-32],[-8,-22],[-13,-26],[-91,-139],[-16,-44],[-3,-11],[-6,-101],[4,-89],[0,-11],[-2,-9],[-1,-9],[-17,-54],[-32,-65],[-2,-10],[-2,-8],[2,-18],[22,-101],[1,-9],[-1,-9],[-3,-28],[0,-37],[1,-7],[2,-7],[30,-57],[7,-17],[4,-24],[2,-18],[38,-88]],[[1295,5197],[0,43],[0,171],[0,172],[0,171],[-1,172],[0,171],[0,172],[0,171],[0,171],[0,172],[0,171],[0,172],[0,171],[-1,172],[0,171],[0,172],[0,171],[0,92],[0,92],[0,93],[0,92],[96,0],[51,0],[67,0],[80,0],[74,-1],[73,0],[87,0],[60,0],[1,1],[1,0],[1,0],[2,0],[0,1],[1,1],[0,1],[1,2],[0,4]],[[6116,2449],[157,-214],[2,-5],[2,-11],[1,-9],[3,-75],[-8,-120],[2,-77],[9,-49],[42,-99],[-2,-18],[-10,-23],[-141,-146],[-140,-146],[-8,-14],[-39,-121],[-37,-71],[-16,-20],[-65,-100],[-128,-155],[-128,-156],[-42,-21],[-128,-9],[-67,-2],[-8,-2],[-11,-6],[-7,-8],[-9,-6],[-8,4],[-9,8],[-158,175],[-140,110],[-141,110],[-27,-20],[-159,-95],[-32,-24],[-18,-18],[-1,-106],[-28,-54],[-50,-59],[-138,-37],[-71,-32],[-73,-48],[-12,-12],[-45,-42],[-39,-47],[-12,-28],[-1,-32],[3,-26],[7,-18],[-1,0],[-237,2],[-237,3],[-29,39],[-61,156]],[[3224,2023],[34,-33],[53,-27],[63,0],[38,12],[31,11],[31,23],[22,28],[25,35],[41,50],[35,35],[25,31],[18,51],[25,58],[19,54],[7,28],[12,31],[22,15],[25,0],[31,-12],[44,-3],[28,-12],[22,-16],[25,0],[35,4],[69,16],[37,15],[19,16],[3,23],[-6,24],[6,38],[22,74],[13,62],[19,35],[18,16],[26,19],[21,39],[29,39],[15,12],[22,3],[19,-7],[41,-39],[40,-35],[38,-35],[41,-31],[58,-15],[16,10],[19,5],[14,-2],[11,-4],[12,-5],[11,-3],[11,1],[160,298],[21,21],[23,11],[23,5],[23,1],[17,-3],[9,-3],[6,-3],[4,-3],[6,-8],[11,-17],[4,-8],[2,-9],[1,-12],[-3,-73],[2,-11],[5,-9],[14,-12],[18,-4],[155,8],[44,-8],[16,-8],[14,-14],[50,-90],[18,-22],[23,-19],[20,-10],[15,-4],[16,-1],[16,3],[15,9],[14,14],[24,35],[7,8],[7,6],[11,6],[34,15],[20,12],[19,17],[15,21],[33,70],[13,20],[17,16],[18,8],[16,0],[31,-8],[13,-1],[14,3],[18,9],[68,50],[18,6],[14,-1],[15,-4],[116,-77]],[[9028,6186],[-1,-6],[1,-6],[12,-20],[2,-14],[-2,-14],[-15,-25],[2,-8],[6,-7],[6,-9],[1,-10],[-1,-29],[1,-3],[3,-7],[1,-4],[-1,-3],[-4,-6],[-2,-3],[-2,-44],[-14,-45],[-7,-10],[-20,-17],[-9,-10],[-5,-14],[-1,-13],[1,-24],[-2,-14],[-7,-29],[0,-11],[4,-17],[7,-13],[10,-12],[8,-13],[1,-2],[4,-13],[-1,-13],[-2,-13],[-1,-16],[3,-16],[11,-30],[2,-15],[-6,-19],[-25,-31],[-10,-19],[-20,-68],[-27,-91],[-28,-98],[-16,-31],[-29,-37],[-8,-16],[-32,-141],[-2,-32],[9,-32],[-20,-16],[-12,-22],[-30,-108],[-11,-20],[-43,-51],[-5,-13],[-1,-17],[7,-70],[12,-123],[4,-41]]],
transform:{scale:[.0016796082677315277,.0013546677746774736],translate:[21.809448689952603,8.68164174400006]}},m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();