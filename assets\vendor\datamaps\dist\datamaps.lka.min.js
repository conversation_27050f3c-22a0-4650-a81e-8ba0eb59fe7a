!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo={type:"Topology",objects:{lka:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Mahanuvara"},id:"LK.KY",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Mātale"},id:"LK.MT",arcs:[[5,6,-5,7,8,9]]},{type:"Polygon",properties:{name:"Nuvara Ĕliya"},id:"LK.NW",arcs:[[10,11,12,-2]]},{type:"Polygon",properties:{name:"Ampāra"},id:"LK.AP",arcs:[[13,14,15,16,-6,17,18]]},{type:"MultiPolygon",properties:{name:"Maḍakalapuva"},id:"LK.BC",arcs:[[[19]],[[-19,20,21,22]]]},{type:"Polygon",properties:{name:"Pŏḷŏnnaruva"},id:"LK.PR",arcs:[[-21,-18,-10,23,24]]},{type:"Polygon",properties:{name:"Trikuṇāmalaya"},id:"LK.TC",arcs:[[-22,-25,25,26,27,28]]},{type:"Polygon",properties:{name:"Anurādhapura"},id:"LK.AD",arcs:[[-24,-9,29,30,31,32,-26]]},{type:"Polygon",properties:{name:"Vavuniyāva"},id:"LK.VA",arcs:[[-27,-33,33,34]]},{type:"MultiPolygon",properties:{name:"Mannārama"},id:"LK.MB",arcs:[[[35]],[[36]],[[37]],[[38]],[[39,-34,-32,40,41,42]]]},{type:"Polygon",properties:{name:"Mulativ"},id:"LK.MP",arcs:[[-28,-35,-40,43,44,45]]},{type:"MultiPolygon",properties:{name:"Yāpanaya"},id:"LK.JA",arcs:[[[46]],[[47]],[[48]],[[49,50]],[[-45,51,52,53]]]},{type:"MultiPolygon",properties:{name:"Kilinŏchchi"},id:"LK.KL",arcs:[[[54]],[[-44,-43,55]],[[-53,56,-50,57]]]},{type:"Polygon",properties:{name:"Kuruṇægala"},id:"LK.KG",arcs:[[-8,-4,58,59,60,-30]]},{type:"Polygon",properties:{name:"Puttalama"},id:"LK.PX",arcs:[[-31,-61,61,62,-41]]},{type:"Polygon",properties:{name:"Ratnapura"},id:"LK.RN",arcs:[[-12,63,64,65,66,67,68,69,70]]},{type:"Polygon",properties:{name:"Gālla"},id:"LK.GL",arcs:[[-68,71,72,73]]},{type:"Polygon",properties:{name:"Hambantŏṭa"},id:"LK.HB",arcs:[[74,75,-66,76,-15]]},{type:"Polygon",properties:{name:"Mātara"},id:"LK.MH",arcs:[[-76,77,-72,-67]]},{type:"Polygon",properties:{name:"Badulla"},id:"LK.BD",arcs:[[78,-64,-11,-1,-7,-17]]},{type:"Polygon",properties:{name:"Mŏṇarāgala"},id:"LK.MJ",arcs:[[-77,-65,-79,-16]]},{type:"Polygon",properties:{name:"Kægalla"},id:"LK.KE",arcs:[[-3,-13,-71,79,80,-59]]},{type:"Polygon",properties:{name:"Kŏḷamba"},id:"LK.CO",arcs:[[-70,81,82,83,-80]]},{type:"Polygon",properties:{name:"Gampaha"},id:"LK.GQ",arcs:[[-81,-84,84,-62,-60]]},{type:"Polygon",properties:{name:"Kaḷutara"},id:"LK.KT",arcs:[[-69,-74,85,-82]]}]}},arcs:[[[6121,4e3],[38,-387],[94,-185],[-8,-83],[-115,-64],[-92,-15],[-54,-20]],[[5984,3246],[-177,22],[-184,-16],[-156,7],[-98,92],[-38,3],[-44,9],[-61,49],[-74,40],[13,-74],[39,-96],[-86,-88],[-276,-194],[-73,12],[-84,-1],[-124,-51],[-87,-11],[-154,11],[-97,-46],[35,6],[34,-9],[75,-82],[63,-36],[51,-41],[-51,-86],[-92,-58],[-125,43],[-192,170],[-122,7]],[[3899,2828],[2,51],[31,39],[-31,32],[-65,92],[49,27],[148,14],[67,44],[47,1],[47,7],[33,31],[25,34],[-50,37],[-62,37],[-1,37],[10,46],[-52,38],[-108,35],[-37,15],[7,50],[-184,96],[-53,79]],[[3722,3670],[89,-6],[76,13],[-7,12],[-3,17],[14,15],[19,14],[13,51],[62,0],[66,-28],[73,-3],[6,26],[-6,29],[63,21],[61,-7],[46,14],[-26,34],[-34,33],[50,8],[51,14]],[[4335,3927],[88,-72],[272,-102],[157,-5],[91,60],[-14,94],[26,40],[60,21],[63,-14],[159,-79],[123,11],[135,67],[176,-1],[70,20],[52,-38],[32,10],[27,15],[47,47],[87,8],[135,-9]],[[6146,4600],[-3,-42],[31,-58],[9,-49],[-41,-121]],[[6142,4330],[-41,-121],[20,-209]],[[4335,3927],[3,26],[24,19],[8,31],[-12,32],[-21,27],[-26,26],[-43,51],[10,54],[61,33],[15,22],[0,23],[12,31],[2,30],[-78,45],[-24,79],[-13,86],[-21,18],[-16,7],[-1,44],[-29,44],[-43,42],[-30,40],[-32,5],[-38,13],[-12,45],[20,42]],[[4051,4842],[91,40],[144,31],[76,55],[68,-2],[52,-20],[63,26],[32,31],[30,12],[28,10],[-3,18],[-10,12],[55,34],[41,37],[-1,47],[24,41],[43,0],[32,-4],[25,10],[83,52],[56,29],[81,24],[89,22]],[[5150,5347],[38,-22],[59,-40],[-1,-45],[23,-15],[36,-8],[27,11],[2,16],[52,15],[49,-9],[68,-8],[63,-22],[-129,-80],[-42,-121],[-11,-59],[-42,-103],[-14,-19],[-8,-18],[-124,-17],[-29,-70],[58,-156],[-8,-49],[42,-36],[113,-15],[107,27],[86,46],[36,-31],[66,-24],[18,45],[10,46],[229,-10],[222,24]],[[5984,3246],[-4,-46],[33,-64],[-10,-23],[9,-22],[20,-13],[15,-13],[-29,-22],[-21,-24],[24,-45],[6,-18],[-14,-19],[-28,-21],[-35,-19],[-39,-45],[-19,-50],[-95,-84],[-162,-21],[-77,-22],[-68,-37],[-21,-50],[-57,-40],[-80,3],[-77,-2],[4,-41],[137,-41],[28,-36],[24,-39],[22,-16],[17,-17],[-63,-61],[40,-15],[39,-12],[-44,-36],[-81,-12]],[[5378,2223],[-242,-76],[-133,-11],[-142,3],[-106,-5],[-90,-9],[-235,19],[-222,30],[-104,5],[-88,40],[15,26],[33,18],[-29,19],[3,42]],[[4038,2324],[49,26],[-7,34],[-33,67],[-99,44],[-124,24],[-37,74],[30,95],[-27,17],[-24,20],[23,19],[33,6],[59,31],[38,36],[-14,3],[-6,8]],[[9485,3935],[4,-10],[34,-4],[35,26],[26,0],[1,0],[32,-103],[-1,-38],[1,0],[29,0],[0,1],[4,36],[10,35],[18,30],[27,22],[0,17],[-3,4],[-9,16],[7,8],[24,-6],[38,-22],[14,-20],[9,-54],[66,-63],[107,-217],[-20,-602],[10,-41],[44,-90],[7,-54],[-24,-102],[-197,-406],[-11,-42],[-5,-55],[-14,-46],[-62,-72],[-13,-32],[-25,-43],[-82,-78],[-39,-36],[29,-62],[-47,-74],[-272,-256]],[[9237,1502],[-38,7],[-104,19],[-71,49],[-55,4],[-49,2],[-128,94]],[[8792,1677],[112,939],[-21,51],[-52,44],[0,49],[-30,49],[20,51],[9,48],[-127,45],[-152,24],[-22,11],[-27,10],[-32,0],[-34,9],[-33,35],[-18,40],[-55,59],[50,80],[89,63],[70,32],[-93,77],[-12,55],[-60,62],[-95,49],[-17,49],[6,61],[-50,135],[8,34],[-28,3],[-27,10],[63,58],[-84,14],[-107,-34],[-52,-11],[-47,-12],[-16,-24],[-29,-28],[-59,-9],[-44,-2],[1,-33],[23,-31],[5,-30],[-19,-25],[-30,-21],[-20,-18],[-86,-29],[-106,3],[0,-98],[23,-96],[-80,-34],[-87,-18],[-37,41],[-34,50]],[[7349,3464],[-15,46],[-38,46],[-30,30],[-17,23],[-53,5],[-37,-5],[-64,38],[-47,60],[-28,112],[-18,32],[-12,32],[12,23],[23,26],[46,125],[-17,128],[-190,24],[-210,-70],[-91,-50],[-51,-12],[-43,-6],[-78,-16],[-105,105],[-34,61],[-6,42],[-28,36],[-64,24],[-12,7]],[[6146,4600],[80,1],[160,9],[78,8],[155,-36],[127,-75],[207,-38],[100,-35],[84,-6],[10,27],[20,30],[27,17],[24,27],[-14,61],[20,57]],[[7224,4647],[116,-13],[137,4],[114,-22],[146,-126],[95,-54],[130,-22],[136,-15],[-6,-77],[-42,-74],[8,-43],[39,-38],[135,-48],[73,-42],[280,-29],[285,18],[100,34],[2,-65],[33,-62],[13,-145],[32,-42],[149,88],[87,42],[117,11],[82,8]],[[9710,4013],[-64,-31],[-60,28],[-18,12],[-9,5],[-3,13],[-3,12],[0,1],[1,5],[17,161],[-12,56],[-47,23],[-18,23],[-143,125],[-7,27],[-17,21],[-20,25],[-14,26],[2,26],[13,24],[6,24],[0,1],[-21,29],[0,1],[55,-32],[52,-74],[39,-34],[9,-15],[40,-64],[104,-98],[48,-45],[72,-224],[-2,-51]],[[7224,4647],[-61,198],[38,197],[32,75],[96,38],[122,10],[155,-12],[40,2],[-36,95],[-71,93],[-31,94],[34,404],[-23,133]],[[7519,5974],[72,15],[383,-24]],[[7974,5965],[13,-27],[89,-178],[34,-117],[-77,-64],[-44,106],[-6,14],[-65,107],[-31,0],[-5,-81],[5,-25],[-3,0],[-3,-7],[0,-13],[6,-15],[14,-10],[36,-7],[1,0],[7,-8],[35,-54],[75,-32],[72,3],[25,55],[26,0],[27,-29],[7,-8],[36,-92],[35,-36],[48,-31],[31,-30],[53,-72],[20,12],[28,12],[12,12],[19,-24],[-19,-12],[10,-10],[8,-6],[2,-1],[5,-5],[2,-12],[1,0],[21,11],[44,12],[22,11],[0,-1],[10,-14],[12,-18],[-9,-20],[-15,-21],[1,-16],[1,-15],[30,-35],[53,-24],[34,-15],[32,-31],[-20,-1],[-10,-4],[-13,-5],[-8,-3],[-8,-3],[-13,-28],[-12,-28],[50,-87],[27,-19],[61,-42],[1,0],[90,10],[30,0],[29,-53],[34,-33],[10,-10],[185,-115],[59,-56],[11,-49],[-95,-17],[0,-1],[-44,18],[-105,80],[-55,28],[-71,15],[-1,0],[-7,0],[-51,-3],[-44,-27],[-30,-56],[51,-14],[49,-5],[43,10],[32,26],[50,-11],[46,-14],[16,-11],[11,-8],[-7,-28],[57,-31],[57,-32],[44,-30],[47,-46],[-22,-16],[-6,-14],[7,-18],[21,-24],[26,19],[-16,35],[51,-10],[99,-44],[24,-20],[78,-120],[-5,-5],[-24,-65],[4,-7],[0,-1],[20,-25],[5,-10],[-16,-17],[-31,-14],[-18,-16],[21,-23],[21,-16],[8,-13],[1,-1],[12,-15],[32,-16],[-46,-20],[4,-10]],[[5150,5347],[6,67],[83,70],[-85,40],[7,89],[29,100],[62,97],[35,107],[63,99],[169,18],[190,0],[55,20],[18,44],[31,45],[56,35]],[[5869,6178],[64,11],[50,-44],[69,-16],[65,-31],[18,-53],[54,-32],[180,11],[324,-47],[76,0],[71,-24],[69,-32],[76,-23],[54,3],[30,-26],[7,-33],[-2,-33],[15,-48],[-23,-32],[-49,-19],[16,-7],[21,-4],[108,15],[60,84],[22,24],[27,67],[29,15],[33,15],[28,30],[39,25],[53,0],[66,0]],[[5869,6178],[92,196],[10,32],[18,31],[52,31],[61,24],[36,37],[-29,40],[-49,22],[-39,29],[-60,85],[-113,100],[-18,61],[-45,27],[-36,24],[-4,52],[66,92],[-32,54],[-80,103],[-2,110],[72,63],[50,66],[-40,19],[-35,27],[-66,43],[-186,77],[-109,37],[-125,2],[-121,-30]],[[5137,7632],[19,45],[35,37]],[[5191,7714],[168,46],[198,15],[82,14],[74,26],[82,14],[68,18]],[[5863,7847],[1,-40],[51,-22],[-46,-25],[-40,-28],[67,-32],[73,4],[59,32],[44,46],[28,42],[1,0],[154,-111],[138,-139],[56,-35],[78,-14],[62,-30],[92,-132],[24,-14],[24,-16],[1,0],[105,-28],[52,-67],[60,-134],[11,25],[14,10],[23,-4],[1,0],[41,-13],[-12,-25],[119,-77],[39,-38],[0,-46],[-31,-19],[-45,-12],[-41,-28],[1,0],[31,5],[32,5],[8,3],[16,5],[0,-1],[93,-119],[14,-62],[-107,-11],[22,42],[-28,33],[-48,4],[-34,-44],[12,-34],[30,-24],[17,-26],[-32,-40],[-25,12],[-46,21],[-75,28],[-74,10],[-72,-18],[-55,-44],[20,-23],[66,6],[89,43],[61,-38],[55,-47],[67,-38],[94,-16],[197,2],[66,25],[-44,60],[113,30],[44,9],[47,-2],[1,-1],[52,-17],[47,-30],[35,-35],[31,-103],[66,-132],[3,-34],[2,-35],[-55,23],[-56,68],[-50,14],[-2,-18],[21,-83],[1,-7],[24,-32],[28,-8],[26,-7],[1,0],[58,0],[46,-12],[27,-98],[33,-68],[5,-11],[9,-42]],[[4051,4842],[-108,62],[16,89],[-21,60],[-40,59],[-11,43],[-36,36],[-69,3],[-59,14],[-8,45],[5,47],[-12,54],[8,32],[-2,32],[-64,32],[-81,-2],[-77,29],[-70,42],[-128,62],[-142,32],[-79,7],[-64,27],[-82,20],[-85,13],[-394,107],[-92,31]],[[2356,5818],[-48,36],[-63,30],[-87,-2],[-76,34],[-72,-19],[-89,0],[-26,45],[-107,258],[-63,72],[47,63],[72,20],[20,23],[6,18],[133,65],[78,98],[-25,106]],[[2056,6665],[36,-11],[41,-3],[9,238],[-34,150],[190,-46],[210,-1],[416,65]],[[2924,7057],[2,-83],[43,-72],[65,-14],[63,-17],[28,-31],[16,-35],[45,-54],[90,-29],[45,-7],[42,-2],[65,70],[76,20],[79,110],[169,130],[130,36],[79,-25],[73,-31],[136,1],[114,51],[108,58],[93,65],[52,47],[60,44],[102,47],[-36,67],[-79,60],[-111,23],[-8,56],[318,7],[124,51],[112,19],[118,13]],[[2924,7057],[-162,124],[-101,46],[1,33],[16,38],[110,7],[64,85],[119,40],[155,-11],[241,18],[230,51],[21,89],[-65,98],[-66,63],[-116,32]],[[3371,7770],[199,74],[119,58],[76,68],[-5,44],[-15,43],[0,23],[-4,21],[-63,6],[-33,27],[11,37],[77,5],[82,-3],[148,2],[106,-40],[66,-59],[105,28],[74,39],[42,12],[41,14],[8,13],[10,13],[139,2],[132,-30],[16,-82],[115,-8],[49,-51],[-41,-37],[-7,-34],[67,9],[62,26],[135,-13],[136,-49],[52,-41],[-79,-173]],[[1558,7739],[-37,-28],[16,44],[21,-16]],[[383,8049],[-42,-2],[53,20],[77,-2],[13,-10],[-57,0],[-30,0],[-14,-6]],[[12,8094],[-4,-3],[-8,23],[11,-6],[1,-14]],[[1415,8013],[146,-84],[-1,0],[-70,9],[-123,36],[-72,7],[111,-75],[87,-59],[39,-40],[-103,7],[-67,31],[-45,33],[-35,16],[-44,14],[-176,93],[-87,23],[-88,23],[-64,10],[-34,6],[-94,6],[-47,11],[-21,25],[13,24],[55,11],[191,0],[85,-12],[87,-14],[183,-41],[174,-60]],[[2737,8483],[5,-88],[26,-83],[79,-75],[9,-18],[5,-19],[-4,-39],[-16,-37],[10,-60],[-6,-52],[-94,-19],[-19,-26],[-15,-28],[-22,-29],[-15,-30],[21,-23],[19,-17],[-54,-92],[705,22]],[[2056,6665],[-114,38],[-109,27],[-127,7],[-112,26],[-49,17]],[[1545,6780],[16,10],[7,12],[42,79],[6,3],[3,2],[10,23],[39,51],[38,249],[-103,159],[9,28],[7,181],[0,2],[0,1],[-10,60],[-13,30],[-22,22],[-17,26],[39,13],[58,6],[38,7],[248,133],[129,38],[54,28],[28,62],[52,64],[29,89],[137,211],[5,12],[13,30]],[[2387,8411],[31,0],[147,8],[91,27],[81,37]],[[2737,8483],[113,-9],[-11,71],[111,86],[329,23],[117,1],[115,-12],[113,6],[73,111],[58,1],[195,-6],[108,4],[72,23],[59,116],[21,4],[32,-24],[84,-42],[98,-32],[-12,58],[13,58],[91,43],[9,42],[21,13]],[[4546,9018],[22,8],[26,13]],[[4594,9039],[506,-243],[5,-5],[29,-29],[49,-36],[91,-51],[59,-45],[-52,-16],[-30,8],[-3,1],[-1,0],[-45,34],[-24,8],[-1,0],[-77,7],[-34,-6],[17,-20],[6,-7],[222,-122],[86,-29],[1,0],[-35,32],[-21,29],[11,22],[59,8],[28,-11],[45,-80],[42,-48],[162,-193],[-33,7],[-90,20],[-57,1],[-24,-37],[34,-9],[69,-7],[62,-23],[1,-3],[9,-56],[1,0],[28,0],[14,15],[13,6],[15,5],[19,8],[22,-51],[3,-7],[60,-82],[81,-73],[83,-32],[6,-3],[10,-5],[-1,-18],[-19,-18],[-40,-8],[-12,7],[-47,45],[-51,16],[-117,18],[-68,18],[31,-25],[18,-15],[154,-86],[0,-8]],[[698,9117],[-108,-4],[-100,20],[-44,32],[0,105],[13,27],[28,-3],[29,-22],[18,-27],[73,-14],[88,-4],[22,-9],[50,-62],[-34,-20],[-35,-19]],[[1177,9409],[31,-15],[35,2],[18,8],[-25,26],[2,15],[36,-1],[43,-19],[17,-35],[-12,-53],[-13,-13],[-38,-3],[-141,17],[-27,22],[5,96],[16,14],[23,-14],[30,-47]],[[1502,9613],[18,-2],[83,2],[14,-7],[6,-39],[11,-16],[49,-18],[66,-17],[55,-18],[4,-6],[15,-18],[-30,-19],[-56,0],[-60,11],[-43,16],[-30,5],[-77,-27],[-54,-4],[-1,0],[-66,29],[-66,58],[-104,123],[48,44],[11,56],[26,45],[94,12],[49,-9],[1,0],[4,-21],[-17,-30],[-11,-35],[1,-35],[7,-28],[0,-2],[4,-6],[14,-20],[20,-13],[15,-11]],[[3160,9618],[-58,-73],[-90,-94]],[[3012,9451],[-220,57],[-79,27],[-53,1],[10,-62],[29,-19],[46,-20],[28,-26],[-28,-32],[-32,-6],[-31,10],[-29,14],[-82,25],[-160,83],[-91,23],[47,-31],[84,-43],[46,-31],[-103,12],[-103,25],[-205,68],[-164,77],[-98,20],[-9,4],[-37,14],[-35,8],[-5,-18],[-47,19],[-18,25],[-8,29],[-17,33],[-28,29],[-16,13],[-44,34],[-28,29],[92,28],[168,92],[76,20],[1,0],[334,-17],[174,11],[64,-4],[25,-34],[-5,-54],[13,-19],[37,-7],[35,-3],[102,-15],[22,-7],[19,-23],[46,1],[36,7],[14,2],[21,2],[15,1],[33,-11],[14,-5],[120,-78],[172,-111]],[[4546,9018],[-291,118],[-100,27],[-89,12],[-14,3],[-12,4],[-78,35],[-29,10],[-29,3],[-35,-1]],[[3869,9229],[-9,25],[-54,98]],[[3806,9352],[10,-2],[0,19],[-155,46],[-376,183],[-308,210],[-156,50],[-246,35],[-64,27],[-9,39],[90,23],[29,3],[89,9],[177,5],[86,-9],[59,-25],[39,-84],[39,-35],[560,-390],[83,-36],[167,-56],[674,-325]],[[1854,8595],[-22,-9],[-36,39],[26,9],[25,21],[28,-12],[11,-12],[-6,-18],[-26,-18]],[[2387,8411],[26,63],[0,149],[10,21],[-8,15],[-51,6],[-118,36],[-45,26],[-29,48],[-5,9],[-21,60],[-2,34],[51,32],[95,28],[105,20],[86,7],[39,12],[94,74],[11,5],[16,7],[31,11],[39,9],[49,4],[-41,45],[-24,20],[-25,19],[-63,37],[-83,37],[-316,110],[-64,47],[138,-3],[130,-31],[258,-88],[246,-37],[64,-25],[101,-52],[26,-28],[-53,-16],[12,-17],[15,-18],[-27,-17],[1,0],[89,-36],[211,31],[386,91],[104,-6],[228,-37],[269,-72],[203,-23],[1,0]],[[3869,9229],[-53,-2],[-33,-7],[-5,-5],[-10,-9],[-16,-5],[-57,25],[-23,2],[-139,-2],[-39,7],[-304,156],[-131,50],[-47,12]],[[3160,9618],[205,-132],[133,-49],[308,-85]],[[3722,3670],[-39,49],[-62,38],[-103,9],[-94,-23],[-15,-48],[5,-53],[-14,-30],[-31,-25],[-45,-1],[-209,-27],[-101,-28],[-93,-40],[-89,-47],[-87,-30]],[[2745,3414],[-74,32],[-107,72],[-25,21],[-12,22],[-38,26],[-119,-13],[-105,-44],[-124,-22],[-118,50],[-102,-3],[-163,-76],[-91,-21]],[[1667,3458],[-10,152],[-159,505],[-7,88],[8,88],[46,70],[64,65],[17,29],[30,26],[62,9],[50,17],[-23,23],[-53,8],[22,33],[29,30],[55,10],[62,3],[46,12],[30,23],[-16,27],[-23,28],[14,68],[40,62],[8,31],[16,27],[55,18],[52,21],[12,27],[21,26],[90,28],[18,24],[6,26],[26,35],[63,18],[-4,-5],[-4,-4],[27,5],[33,20],[1,31],[25,25],[89,-30],[53,25],[-51,57],[25,30],[32,29],[5,58],[-4,69],[17,33],[10,31],[-51,19],[-58,12],[-40,46],[-16,50],[-45,101],[2,52],[-8,49]],[[1667,3458],[-75,17],[-63,-23],[-50,3],[-33,22],[-83,-6],[-79,-12],[-102,3]],[[1182,3462],[-185,803],[-26,33],[18,12],[13,1],[31,-13],[7,45],[21,51],[5,24],[5,25],[-24,39],[-32,35],[-6,23],[-7,24],[1,94],[-99,287],[-11,33],[-51,63],[-43,133],[-70,104],[-108,289],[0,82],[18,-5],[1,-1],[1,0],[0,-3],[10,-9],[15,48],[-23,151],[-22,46],[71,28],[66,51],[48,58],[19,48],[20,32],[126,118],[-1,-2],[-64,-89],[-81,-85],[18,-21],[5,-5],[31,-19],[42,-9],[50,0],[-41,-70],[-44,-51],[-18,-9],[-20,-1],[-16,-7],[-7,-27],[4,-61],[-4,-19],[-72,-137],[3,-25],[5,-51],[75,-53],[21,-15],[-85,-106],[136,-59],[198,-9],[101,42],[-19,46],[-32,36],[-13,13],[-15,11],[-38,29],[-43,16],[-12,28],[144,163],[-6,29],[-18,22],[-21,19],[-14,19],[-3,31],[5,53],[-2,22],[-2,4],[-12,19],[-7,3],[-8,5],[-8,10],[8,29],[20,24],[56,44],[12,28],[9,54],[40,107],[40,250],[27,28],[28,15],[21,16],[9,38],[0,106],[25,53],[58,33],[69,28],[40,26]],[[5378,2223],[12,-95],[32,10],[31,7],[58,-38],[74,-14],[-10,-24],[-14,-19],[-16,-10],[-116,-32],[-20,-33],[45,-43],[65,-31],[98,-11],[103,25],[87,38],[78,2]],[[5885,1955],[-70,-63],[-44,-66],[-79,-56],[-40,-57],[-42,-111],[-81,-68],[-65,-73],[13,-42],[27,-40],[4,-159],[47,-54],[61,-48],[82,-27],[77,-32],[42,-47],[33,-60]],[[5850,952],[79,-52],[40,-65],[-22,-45],[-90,18],[-157,19],[-50,34],[-65,30],[-81,-4],[-82,-12],[-82,8],[-313,68],[-245,28]],[[4782,979],[0,24],[23,14],[-55,24],[3,24],[-47,35],[-103,-10],[-77,8],[29,46],[-62,67],[-188,-14],[-73,-1],[-30,-41],[-83,-12],[-84,-7]],[[4035,1136],[-37,32],[-69,-2],[-133,39],[-63,1],[-72,18],[-59,29],[-52,32]],[[3550,1285],[-154,160],[-181,152],[-23,43],[65,15],[-62,70],[-95,52],[-60,14],[-47,27],[-25,74],[-131,75],[-41,52],[-28,48],[6,41],[-74,117],[-14,53]],[[2686,2278],[94,50],[-105,67],[22,30],[18,36],[46,43],[55,40]],[[2816,2544],[66,18],[177,-107],[53,-40],[66,-31],[31,10],[43,6],[31,-22],[20,-25],[107,-14],[130,43],[54,4],[64,-2],[92,-31],[118,-23],[85,-7],[85,1]],[[4035,1136],[-88,-16],[-58,-34],[51,-46],[65,-40],[-25,-72],[-132,18],[-82,46],[-90,40],[-70,13],[-69,-11],[42,-48],[85,-32],[90,-77],[32,-73],[-74,-55],[-151,16],[45,-31],[20,-42],[-31,-53],[-38,-50],[27,-50],[66,-37],[77,-31],[67,-36],[-58,-42],[-68,-41],[-59,-14],[-26,-24],[14,-26],[33,-11],[75,-19],[-26,-31],[-77,-17],[-47,-50],[1,-28],[7,-27],[1,-2]],[[3564,103],[-28,2],[-20,5],[-15,13],[-18,11],[-33,6],[-20,-4],[-16,-8],[-16,-8],[-23,1],[-7,4],[-7,3],[-27,23],[-14,5],[-3,1],[0,1],[-30,6],[-183,39],[-75,9],[-53,7],[-93,44],[-21,6],[-41,12],[-32,-19],[-79,39],[-349,220],[-28,13],[-1,0],[-3,3],[-9,7],[-6,10],[2,22],[-11,11],[-15,12],[-38,30],[-198,248],[-8,24],[1,21],[1,22],[-7,13],[-36,44],[-36,119],[-78,84],[-50,120]],[[1841,1324],[30,-4],[23,-19],[25,-14],[27,12],[44,10],[31,-18],[22,-22],[157,-43],[78,-10],[85,-22],[69,-36],[71,-28],[185,-21],[116,39],[130,6],[127,-57],[66,-38],[66,-32],[9,51],[-33,56],[0,55],[80,21],[149,55],[152,20]],[[9237,1502],[-131,-124],[-134,-80],[-248,-105],[-81,-14],[-109,-35],[-793,-372],[-159,-51],[-515,-109],[-1,0],[-7,37],[0,2],[-30,14],[-1,0],[-7,-1],[-26,-4],[-16,-20],[-9,-44],[-23,-16],[-37,-6],[-50,-13],[-93,-33],[-93,-25],[-445,-57],[-216,-44],[-236,-26],[-108,-24],[-65,-55],[-116,14],[-71,-17],[-40,-9],[-165,-72],[-164,-73],[-11,-7]],[[5037,133],[-24,8],[-108,29],[-51,47],[31,10],[2,21],[-37,12],[-41,3],[-60,23],[-10,41],[139,35],[-47,98],[39,22],[15,22],[-47,6],[-50,-5],[-70,23],[-15,43],[25,23],[21,27],[-23,14],[-38,-4],[-81,18],[-74,31],[52,110],[164,74],[-29,41],[29,45],[-7,19],[40,10]],[[5850,952],[105,41],[108,35],[121,24],[93,41],[-42,58],[21,58],[54,0],[58,2],[35,30],[-1,34],[53,-23],[41,28],[107,1],[106,-9],[73,-15],[33,-47],[42,-3],[46,1],[39,16],[54,4],[80,-62],[65,-67],[107,-26],[117,38],[42,25],[66,17],[124,19],[254,59],[47,20],[11,-7],[37,-4],[114,25],[120,-16],[55,54],[0,23],[-2,21],[19,15],[19,18],[-23,35],[-1,43],[84,40],[102,28],[78,44],[90,41],[115,13],[76,53]],[[5037,133],[-65,-45],[-175,6],[-1,0],[-73,-6],[-57,-13],[-59,-20],[-104,-47],[-30,-4],[-29,-4],[-71,16],[-76,23],[-43,8],[-30,6],[-247,-18],[-71,8],[-35,21],[-23,28],[-19,17],[-13,12],[-72,-25],[-65,-3],[-115,10]],[[7349,3464],[-166,-24],[0,-53],[-33,-48],[-117,-88],[-39,-40],[-71,-17],[-60,38],[-70,5],[-50,-45],[-13,-52],[53,-41],[66,-38],[43,-53],[50,-31],[157,25],[46,-35],[24,-12],[31,-23],[23,-35],[11,-35],[0,-48],[-20,-46],[-16,-71],[-23,-28],[-28,-26],[-12,-40],[-22,-37],[-55,-7],[-43,11],[-26,-16],[-19,-20],[-43,-33],[-54,-26],[-109,-18],[-92,-30],[105,-87],[-124,-22],[-38,-43],[-145,-8],[-10,-53],[9,-55],[-37,-47],[17,-33],[75,-12],[5,-45],[-63,-59],[-9,-38],[1,-39],[40,-71],[64,-59],[-89,-19],[-14,-61],[-46,13],[-46,18],[-60,-56],[-17,-68],[-37,36],[-50,36],[-19,82],[-29,10],[-30,8],[-29,57],[-40,52],[-34,4],[-33,11],[-20,44],[-33,31],[-51,1]],[[2816,2544],[-2,25],[-6,25],[57,32],[-13,46],[-74,23],[-93,3]],[[2685,2698],[13,21],[-1,24],[-64,121],[23,75],[54,74],[62,26],[32,43],[-90,16],[-104,-15],[-53,64],[19,73],[67,21],[56,31],[11,88],[42,20],[-7,34]],[[2686,2278],[-125,17],[-139,-35],[27,-74],[-83,13],[-56,77],[-22,7],[-27,3],[-53,-36],[-66,-20],[-47,13],[-48,-4],[-164,-84],[-48,-12],[-49,-7],[-124,-28],[-198,73],[-5,-42],[27,-48],[13,-33],[1,-31],[-2,-2],[-6,-7]],[[1492,2018],[-160,244],[-58,193],[-6,19],[0,184],[5,8]],[[1273,2666],[92,-22],[71,-22],[76,-11],[33,11],[39,10],[213,-37],[73,8],[73,4],[58,-22],[51,-28],[48,-15],[50,-12],[75,-5],[92,2],[5,51],[17,48],[68,40],[85,23],[115,-7],[78,16]],[[1273,2666],[43,60],[11,29],[-1,30],[0,1],[-205,432],[29,63],[-2,-39],[16,-24],[23,-19],[20,-23],[41,-85],[20,-21],[27,0],[6,24],[12,11],[11,1],[1,0],[2,-1],[-1,78],[0,1],[-9,34],[-22,28],[-20,9],[-57,7],[-25,10],[-2,3],[-16,19],[7,20],[17,19],[8,18],[-25,111]],[[1841,1324],[-18,44],[-9,5],[-18,6],[-20,10],[-11,14],[4,15],[21,4],[22,-2],[1,0],[10,1],[-33,134],[-2,11],[-89,136],[-207,316]]],
transform:{scale:[.0002339101097109631,.0003906233722372289],translate:[79.55144290500013,5.923732815000093]}},m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();