!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo={type:"Topology",objects:{kos:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Peć"},id:"-99",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Istok"},id:"-99",arcs:[[5,6,-5,7,8]]},{type:"Polygon",properties:{name:"Klina"},id:"-99",arcs:[[9,10,-1,-7,11]]},{type:"Polygon",properties:{name:"Dečani"},id:"-99",arcs:[[12,13,-3]]},{type:"Polygon",properties:{name:"Đakovica"},id:"-99",arcs:[[-11,14,15,16,-13,-2]]},{type:"Polygon",properties:{name:"Orahovac"},id:"-99",arcs:[[17,18,-15,19]]},{type:"Polygon",properties:{name:"Zvečan"},id:"-99",arcs:[[20,21,22,23]]},{type:"Polygon",properties:{name:"Leposavić"},id:"-99",arcs:[[24,25,-24,26]]},{type:"Polygon",properties:{name:"Kosovska Mitrovica"},id:"-99",arcs:[[27,28,29,-21,-26,30]]},{type:"Polygon",properties:{name:"Vučitrn"},id:"-99",arcs:[[31,32,33,34,-28]]},{type:"Polygon",properties:{name:"Srbica"},id:"-99",arcs:[[-29,-35,35,-12,-6,36]]},{type:"Polygon",properties:{name:"Zubin Potok"},id:"-99",arcs:[[-30,-37,-9,37,-22]]},{type:"Polygon",properties:{name:"Kosovo Polje"},id:"-99",arcs:[[38,39,40,41]]},{type:"Polygon",properties:{name:"Glogovac"},id:"-99",arcs:[[42,-40,43,44,-36,-34]]},{type:"Polygon",properties:{name:"Obilić"},id:"-99",arcs:[[45,-41,-43,-33,46]]},{type:"Polygon",properties:{name:"Podujevo"},id:"-99",arcs:[[47,-47,-32,-31,-25,48]]},{type:"Polygon",properties:{name:"Priština"},id:"-99",arcs:[[49,50,51,52,-42,-46,-48]]},{type:"Polygon",properties:{name:"Lipljan"},id:"-99",arcs:[[53,54,55,56,57,58,-44,-39,-53]]},{type:"Polygon",properties:{name:"Gnjilane"},id:"-99",arcs:[[59,60,61,62,-55,63]]},{type:"Polygon",properties:{name:"Vitina"},id:"-99",arcs:[[64,65,66,-62]]},{type:"Polygon",properties:{name:"Novo Brdo"},id:"-99",arcs:[[-64,-54,-52,67]]},{type:"Polygon",properties:{name:"Kosovska Kamenica"},id:"-99",arcs:[[-60,-68,-51,68]]},{type:"Polygon",properties:{name:"Prizren"},id:"-99",arcs:[[69,70,71,72,-16,-19,73]]},{type:"Polygon",properties:{name:"Dragaš"},id:"-99",arcs:[[74,-72]]},{type:"Polygon",properties:{name:"Suva Reka"},id:"-99",arcs:[[75,76,77,-74,-18,78,-58]]},{type:"Polygon",properties:{name:"Mališevo"},id:"-99",arcs:[[-59,-79,-20,-10,-45]]},{type:"Polygon",properties:{name:"Štimlje"},id:"-99",arcs:[[79,-76,-57]]},{type:"Polygon",properties:{name:"Uroševac"},id:"-99",arcs:[[-63,-67,80,81,-77,-80,-56]]},{type:"Polygon",properties:{name:"Štrpce"},id:"-99",arcs:[[82,83,-70,-78,-82]]},{type:"Polygon",properties:{name:"Kačanik"},id:"-99",arcs:[[84,-83,-81,-66]]}]}},arcs:[[[2474,5809],[59,-199],[117,-72],[-44,-108],[-88,-109],[15,-126],[117,-109],[59,-126],[29,-163]],[[2738,4797],[-176,109],[-191,0]],[[2371,4906],[-117,54],[-118,-18],[-190,-73],[-103,91],[-73,162],[-59,109],[-132,36],[-132,-72],[-132,54],[-176,36],[-132,54],[-88,91],[44,126],[-308,18],[-202,127]],[[453,5701],[-11,25],[-45,73],[-327,284],[-5,5],[-65,108],[10,140],[47,58],[120,88],[54,39],[64,28],[205,-49],[214,-117],[194,-52],[143,146],[8,69],[-8,64],[4,68],[48,77],[49,29],[219,74],[313,48]],[[1684,6906],[71,-158],[0,-180],[0,-199],[0,-181],[73,-108],[147,-126],[191,-91],[190,0],[118,-54]],[[3466,6899],[83,-246],[-26,-177],[-40,-115],[-68,-175],[11,-174]],[[3426,6012],[-145,-4],[-162,-54],[-88,-109],[-190,-36],[-191,54],[-176,-54]],[[1684,6906],[150,23],[473,93],[276,105],[129,158],[-26,67]],[[2686,7352],[66,-6],[333,-29],[160,-91],[124,-142],[97,-185]],[[4201,5393],[-246,-343],[-190,0],[-377,-262],[-281,-89],[-154,-62]],[[2953,4637],[-215,160]],[[3426,6012],[134,-159],[112,-63],[99,-20],[71,-143],[36,-160],[191,-73],[132,-1]],[[2371,4906],[-103,-145],[-176,-72],[-44,-199],[-58,-126],[-147,-55],[-117,36],[-118,-162],[-264,72],[-308,127],[-88,216],[-216,-22]],[[732,4576],[-1,1],[0,1],[-1,0],[-96,112],[-286,144],[-118,118],[69,92],[7,92],[-16,99],[1,114],[21,57],[65,114],[76,181]],[[2953,4637],[-138,-82],[-61,-151],[24,-193],[-5,-227],[-1,-220],[134,-176],[179,-118],[95,-57],[99,-80],[-99,-141]],[[3180,3192],[-256,-112],[-122,-86],[-234,-224]],[[2568,2770],[-96,94],[-707,475],[-88,14],[-391,-9],[-68,9],[-46,48],[-50,116],[-8,50],[10,95],[-14,54],[-33,43],[-89,70],[-27,44],[7,45],[60,83],[1,58],[-28,55],[-77,67],[-31,40],[-161,355]],[[3955,3785],[-29,-144],[-29,-235]],[[3897,3406],[-74,-163],[-102,18],[-59,73],[-73,54],[-88,-127],[-88,-144],[-103,18],[-130,57]],[[2953,4637],[181,-165],[117,-36],[176,18],[132,0],[88,-127],[-29,-162],[88,-217],[132,-90],[117,-73]],[[4513,7941],[146,-145],[176,-90],[-14,-199],[-59,-144],[-147,36],[-234,18]],[[4381,7417],[-118,108],[-161,235],[-117,127],[44,108],[-118,109],[-234,144],[-288,53]],[[3389,8301],[152,213]],[[3541,8514],[224,-49],[117,-127],[235,0],[176,-90],[132,-90],[44,-127],[44,-90]],[[5541,8879],[-34,-129],[-1,-133],[48,-89]],[[5554,8528],[-161,-9],[-44,-108],[-132,-91],[-74,-90],[-132,-18],[-132,54],[-117,-217],[-249,-108]],[[3541,8514],[121,233],[24,172],[-42,43],[-58,3],[-61,-7],[-51,14],[-34,45],[-32,68],[-48,152],[-69,133],[-15,79],[38,91],[47,31],[186,7],[127,45],[448,304],[138,56],[143,16],[87,-24],[56,-16],[109,-81],[57,-55],[38,-47],[55,-99],[-18,1],[-58,16],[-66,-54],[4,-36],[-9,-103],[-17,-91],[-23,-6],[37,-58],[423,-223],[463,-244]],[[5897,7601],[-152,33],[-147,-36],[-176,72],[-88,-36],[59,-145],[-59,-162],[-132,18],[-103,-73],[-14,-216],[0,-109],[-132,0],[-147,-36],[-176,-126]],[[4630,6785],[-191,18],[-117,0],[-205,144]],[[4117,6947],[-118,163],[118,72],[117,18],[147,217]],[[5554,8528],[76,-65],[104,-144],[179,-338],[58,-268],[-74,-112]],[[5897,7601],[-30,-156],[27,-185],[48,-106],[120,-127],[69,-153],[5,-175]],[[6136,6699],[-137,-70],[-98,-201],[-63,-183],[-13,-104],[-148,-129],[-266,-91]],[[5411,5921],[-131,67],[-125,120]],[[5155,6108],[-144,171],[-102,36],[-132,-18],[-74,18],[15,162],[15,145],[-103,163]],[[5155,6108],[-89,-56],[-118,-23],[-75,-41],[-83,-97],[-229,121],[-70,-85],[-6,-67],[-42,-81],[-99,-82],[-119,-213],[-24,-91]],[[3466,6899],[108,139],[88,18],[132,-18],[117,-181],[103,-145],[88,-18],[15,253]],[[2686,7352],[-31,83],[-127,73],[-90,88],[49,197],[97,115],[97,27],[101,0],[111,35],[45,39],[32,46],[54,109],[51,48],[60,-1],[66,-17],[69,1],[119,106]],[[6053,5140],[-162,-54],[-190,36],[-132,-54],[-132,18]],[[5437,5086],[-59,91],[-117,90],[88,90],[58,145]],[[5407,5502],[74,36],[0,108],[88,36],[132,-108],[176,0],[117,18],[161,109]],[[6155,5701],[30,-199],[44,-91],[-74,-126],[-102,-145]],[[5411,5921],[-165,-76],[0,-108],[44,-145],[117,-90]],[[5437,5086],[-147,-126],[-29,-127],[-118,-72],[-132,0],[-102,-108],[-130,-51]],[[4779,4602],[-46,145],[-42,84],[-101,-148],[-179,70],[-59,29],[33,75],[6,223],[-30,134],[-8,99],[-60,80],[-92,0]],[[6214,6477],[-132,-144],[-15,-163],[74,-162],[44,-127],[-30,-180]],[[6136,6699],[34,-95],[44,-127]],[[7793,6419],[-171,4],[-102,109],[-118,90],[-88,145],[-220,-91],[-176,-54],[-59,-108],[-102,-18],[-74,-73],[-102,54],[-59,109],[-73,72],[-59,-108],[-73,-73],[-103,0]],[[5541,8879],[67,-35],[118,-41],[382,-19],[91,-64],[89,-164],[88,-370],[49,-93],[100,-53],[80,39],[78,52],[96,-15],[88,-157],[9,-219],[32,-222],[161,-172],[63,-16],[134,1],[65,-14],[61,-34],[111,-86],[59,-32],[183,-39],[116,-5],[54,-54],[0,-37],[-1,-148],[-24,-118],[-77,-236],[-20,-109]],[[7793,6419],[-18,-35],[-27,-21],[-3,-21],[57,-35],[96,-27],[71,2],[137,4],[216,-27]],[[8322,6259],[-139,-271],[-122,0],[-199,-8],[46,-130],[170,-113],[69,-117],[-62,-101],[-81,-71]],[[8004,5448],[-214,-10],[-118,0],[-110,-111],[-116,-131]],[[7446,5196],[-176,89],[-161,72],[-191,36],[-88,-36],[0,-162],[0,-145],[-147,-54],[-190,36],[-103,54],[-161,-72],[-88,90],[-88,36]],[[7446,5196],[-116,-70],[-73,-105]],[[7257,5021],[-115,-132],[-21,-110],[68,-129],[53,-129],[-37,-114],[-163,-16],[-36,-136]],[[7006,4255],[-264,0],[-264,-18],[-220,36],[-132,0]],[[6126,4273],[-147,91],[-88,-73],[-190,18],[-206,55],[-161,18],[-161,18],[-206,-109]],[[4967,4291],[-89,116],[-145,29]],[[4733,4436],[46,166]],[[8238,5032],[118,-199],[132,-126],[132,-163],[-44,-162],[243,-62],[172,-54],[128,-1]],[[9119,4265],[-12,-108],[29,-224],[-26,-110],[-118,-102],[-340,-95],[-122,-117],[-6,-170],[223,-309],[58,-195],[-13,6],[-240,-58],[-117,-2],[-102,64]],[[8333,2845],[37,218],[-73,180],[-73,109],[-59,126],[-59,145],[-190,36],[-120,171],[-100,100],[-103,54],[-59,127],[-206,-85],[-175,-96]],[[7153,3930],[-147,325]],[[7257,5021],[160,-61],[132,36],[117,-108],[103,-73],[132,73],[-29,162],[117,54],[103,-72],[146,0]],[[8333,2845],[-55,-61],[-25,-28],[-59,13],[-53,50],[-66,20],[-94,-48],[9,-62],[41,-62],[6,-47],[-61,-6],[-199,70],[-100,-8],[-36,-26]],[[7641,2650],[-1,3],[-157,129],[-152,13],[-200,64],[-147,181],[16,117],[100,155],[-21,64],[-102,-42]],[[6977,3334],[-77,88],[0,142],[321,32],[-68,334]],[[8004,5448],[161,-91],[88,-54],[73,-144],[-88,-127]],[[8322,6259],[360,-46],[130,-39],[87,-71],[184,-210],[96,-57],[84,0],[248,103],[123,2],[170,-35],[32,-19],[115,-70],[48,-155],[-33,-62],[-60,-36],[-68,-28],[-57,-38],[5,-21],[-31,-143],[-7,-19],[-11,-14],[-39,-17],[-11,-20],[9,-31],[40,-62],[5,-26],[-57,-162],[-285,-431],[-229,-209],[-51,-78]],[[4755,2737],[-38,-87],[-14,-116],[58,-108],[172,-57],[160,-15],[100,-26],[114,-62],[105,-117],[24,-98],[0,-3]],[[5436,2048],[-406,-127],[-535,-167],[-147,-77]],[[4348,1677],[-26,193],[-73,127],[-118,-18],[-161,18],[-176,54],[-220,-18],[-205,-54],[-176,-163],[-176,-197]],[[3017,1619],[-1,226],[-15,124],[-62,188],[-216,430],[-109,138],[-46,45]],[[3897,3406],[132,-18],[73,-145],[73,-90],[30,-108],[176,-109],[205,0],[147,-18],[22,-181]],[[4348,1677],[-111,-125],[-58,-151],[-70,-348],[-7,-160],[55,-214],[17,-67],[-19,-145],[-4,-25],[-60,-132],[-95,-151],[-51,-52],[-66,-68],[-122,-39],[-55,37],[-109,141],[-54,31],[-37,-23],[-62,-107],[-42,-33],[-94,-5],[-69,35],[-132,130],[3,51],[-29,87],[-1,51],[27,85],[35,39],[41,29],[47,55],[60,129],[0,89],[-28,93],[-26,140],[-180,433],[-35,132]],[[4967,4291],[79,-101],[168,-78],[88,-134],[-84,-130],[17,-98],[-46,-78],[86,-67]],[[5275,3605],[74,-129],[16,-114],[-8,-108],[25,-99]],[[5382,3155],[-8,-88],[-9,-96],[-194,-53],[-75,-76],[-62,-58],[-113,-41],[-100,-17],[-66,11]],[[3955,3785],[235,18],[0,145],[117,90],[118,18],[102,-36],[147,73],[161,-19],[-29,163],[-73,127],[0,72]],[[6126,4273],[-29,-144],[-103,-109],[-88,-72],[-15,-145],[-44,-108],[-161,-18],[-272,-32],[-139,-40]],[[6977,3334],[-162,-36],[-117,-73],[-44,-162],[-103,0],[-117,-55],[-73,37],[-147,0]],[[6214,3045],[-132,36],[-59,90],[-102,54],[-59,109],[-103,0],[0,-145],[15,-108],[-73,0],[-103,72],[-138,39],[-78,-37]],[[6214,3045],[73,-145],[0,-54],[-88,-72],[-88,-37],[0,-108],[31,-149]],[[6142,2480],[-138,-81],[-191,-173],[-67,-60],[-145,-67],[-165,-51]],[[7641,2650],[-39,-29],[-336,-472],[-7,-62],[12,-38],[17,-38],[9,-60],[5,-143],[-3,-15],[-8,-50],[-60,-12],[-247,46],[-45,8],[-49,46],[-22,21],[-53,101],[-93,141],[-203,182],[-217,154],[-77,39],[-39,10],[-44,1]]],transform:{scale:[.0001748181816181669,.00014192025882588116],translate:[20.024751424000016,41.844010316000094]}},m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return"";
}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();