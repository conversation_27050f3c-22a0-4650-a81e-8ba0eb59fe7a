!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo={type:"Topology",objects:{npl:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Gandaki"},id:"NP.PM",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Bheri"},id:"NP.MP",arcs:[[5,6,7,8]]},{type:"Polygon",properties:{name:"Dhawalagiri"},id:"NP.PM",arcs:[[-4,9,10,11,12]]},{type:"Polygon",properties:{name:"Karnali"},id:"NP.MP",arcs:[[-12,13,-9,14,15]]},{type:"Polygon",properties:{name:"Lumbini"},id:"NP.PM",arcs:[[-3,16,17,18,-10]]},{type:"Polygon",properties:{name:"Mahakali"},id:"NP.SP",arcs:[[19,20]]},{type:"Polygon",properties:{name:"Seti"},id:"NP.SP",arcs:[[-8,21,-20,22,-15]]},{type:"Polygon",properties:{name:"Bagmati"},id:"NP.MM",arcs:[[23,24,-1,25]]},{type:"Polygon",properties:{name:"Janakpur"},id:"NP.MM",arcs:[[26,27,28,-24,29]]},{type:"Polygon",properties:{name:"Narayani"},id:"NP.MM",arcs:[[-25,-29,30,-17,-2]]},{type:"Polygon",properties:{name:"Sagarmatha"},id:"NP.PW",arcs:[[31,32,-27,33]]},{type:"Polygon",properties:{name:"Bhojpur"},id:"NP.PW",arcs:[[34,35,-32,36]]},{type:"Polygon",properties:{name:"Mechi"},id:"NP.PW",arcs:[[-35,37]]},{type:"Polygon",properties:{name:"Rapti"},id:"NP.MP",arcs:[[-11,-19,38,-6,-14]]}]}},arcs:[[[6206,4880],[-23,-21],[-18,-34],[-24,-87],[-13,-23],[-49,-71],[-17,-36],[-12,-38],[-20,-139],[-15,-52],[-13,-25],[-12,-14],[-33,-11],[-25,-17],[-22,-25],[-12,-21],[-9,-21],[-64,-215],[-18,-38],[-37,-66],[-7,-23],[-2,-27],[3,-16],[5,-22],[23,-74],[6,-36],[2,-34],[-4,-51],[-7,-24],[-9,-16],[-9,-5],[-10,-3],[-12,0],[-12,3],[-10,6],[-10,10],[-7,15],[-6,14],[-6,14],[-4,5],[-13,15]],[[5681,3677],[-14,41],[-14,29],[-12,15],[-11,10],[-12,7],[-13,3],[-11,-4],[-8,-5],[-26,-38],[-66,-80],[-55,-42],[-10,-19],[-3,-39],[5,-24],[6,-16],[5,-9],[6,-10],[1,-15],[-5,-7],[-42,9],[-18,-11]],[[5384,3472],[-14,42],[-12,21],[-22,-3],[-17,-24],[-14,-28],[-14,-12],[-11,2],[-8,5],[-5,12],[-2,22],[-3,22],[-16,18],[-6,20],[-4,41],[-1,31],[-7,25],[-21,22],[-53,18],[-111,2],[-52,21],[-24,27],[-44,66],[-25,19],[-30,-6],[-55,-55],[-29,-13],[-17,3],[-33,14],[-17,4],[-14,-5],[-28,-19],[-12,-2],[-18,18],[-34,63],[-21,21],[-29,6],[-100,-2],[-21,-22],[-10,-4],[-43,29],[-14,0],[-17,15],[-20,5],[-35,-2],[-36,11],[-14,-3],[-13,-26],[-18,26],[-21,20],[-11,21],[8,33],[18,19],[21,-2],[37,-17],[45,13],[38,37],[18,54]],[[4373,4075],[70,38],[15,25],[15,40],[32,141],[9,57],[5,19],[25,56],[15,46],[16,32],[14,21],[19,77],[-1,60],[-5,22],[-21,37],[-8,22],[-11,45],[-13,32],[-9,26],[-12,82],[-28,84],[22,23],[9,15],[10,24],[19,103],[9,35],[24,58],[3,4],[18,38],[9,31],[4,35],[-5,60],[7,36],[6,23],[51,82],[-4,36],[-4,18],[-52,108],[-6,24],[3,12],[9,15],[17,19],[30,28],[79,119],[12,25],[15,47],[10,18],[17,12],[24,1],[24,-7],[20,4],[19,12],[33,38],[31,45],[9,17],[14,18],[18,14],[52,12],[16,9],[33,37],[38,16],[11,9]],[[5154,6310],[12,-35],[62,-64],[84,-31],[15,-11],[13,-19],[20,-61],[26,-39],[10,-22],[19,-113],[16,-48],[31,-16],[28,2],[21,-16],[20,-22],[24,-15],[68,-20],[11,-11],[12,-43],[8,-18],[24,-16],[24,-9],[14,-20],[-6,-113],[16,-21],[56,-5],[23,-21],[33,-65],[21,-30],[16,-12],[14,-3],[68,7],[15,-4],[20,-12],[14,-3],[12,7],[10,15],[15,58],[7,15],[37,35],[11,5],[11,9],[8,20],[12,42],[8,21],[8,14],[28,12],[25,-18],[45,-56],[60,-34],[9,-29],[-14,-122],[-4,-21],[-7,-22],[-9,-12],[-21,-15],[-7,-18],[-2,-15],[0,-46],[-8,-33],[-32,-33],[-7,-25],[10,-134],[0,-48],[-5,-73]],[[3118,6484],[-57,-90],[-15,-65],[-5,-34],[-11,-43],[-15,-34],[-134,-185],[-71,-78],[-14,-23],[-45,-117],[-25,-46],[-15,-23],[-23,-23],[-7,-15],[-7,-20],[-26,-88],[-31,-30],[-53,-3],[-19,3],[-16,8],[-55,39],[-44,-94],[-9,-36],[-5,-38],[2,-21],[19,-74],[5,-37],[2,-54],[-6,-32],[-7,-26],[-8,-15],[-6,-7],[-30,-21],[-11,-11],[-9,-20],[-2,-18],[5,-21],[7,-16],[7,-21],[5,-28],[0,-91],[7,-23],[75,-54],[25,-51],[13,-55],[28,-77],[-65,-61],[-18,-88],[-16,-46],[-2,-23],[5,-26],[10,-22],[38,-53],[11,-18],[20,-48],[8,-14],[11,-5],[10,-1],[11,-3],[10,-12],[56,-89],[10,-32],[6,-31],[0,-40],[-2,-53],[-1,-13],[-3,-12],[-4,-11],[-8,-13],[-7,-9],[-13,-9],[-26,-9],[-10,-1],[-23,5],[-6,0],[-7,-5],[-6,-7],[-38,-66],[-6,-26],[0,-1]],[[2482,3834],[-29,17],[-63,11],[-36,-29],[-49,-103],[-29,-35],[-33,5],[-35,36],[-34,45],[-61,63],[-49,93],[-26,39],[-29,18],[-62,26],[-24,33],[-1,0],[-15,45],[-25,32],[-109,99],[-19,27],[-5,22],[-1,26],[-6,35],[-16,45],[-22,43],[-26,33],[-28,18],[-19,-5],[-8,-22],[-5,-31],[-11,-28],[-18,-16],[-20,-6],[-14,11],[4,35],[-20,8],[-8,19],[5,20],[19,14],[-17,59],[-71,145],[-17,69],[-25,146],[-25,57],[-29,27],[-122,41]],[[1249,5021],[0,1],[1,39],[14,96],[14,32],[41,45],[15,20],[171,331],[9,20],[11,22],[8,56],[3,49],[-4,45],[-40,159],[-14,77],[-6,82],[-13,36],[-16,28],[-18,17],[5,-32],[16,-54],[4,-31],[-49,50],[-11,-6],[-17,65],[-15,25],[-16,-5],[-17,-17],[-17,-9],[-20,22],[-46,66],[-60,112],[6,29],[20,33],[26,27],[24,11],[25,-6],[26,-16],[49,-45],[63,-88],[26,-12],[79,19],[27,-3],[13,-10],[29,-33],[12,-7],[107,23],[21,-12],[20,-44],[18,-70],[53,-108],[-4,-77],[13,-14],[2,-14],[-15,-22],[0,-17],[12,-1],[10,4],[20,14],[30,32],[-3,28],[-17,29],[-10,38],[-6,60],[-18,49],[-43,84],[-64,169],[-17,18],[-15,44],[-9,55],[3,51],[58,115],[14,19],[18,11],[80,104]],[[1865,6829],[32,-66],[2,-62],[-2,-23],[0,-18],[8,-11],[22,-14],[44,-13],[100,-61],[67,-20],[21,-11],[18,-19],[28,-23],[24,-14],[99,-15],[110,89],[25,-15],[5,-7],[3,-4],[1,-1],[8,-27],[7,-16],[11,-10],[16,1],[15,10],[19,37],[7,26],[8,20],[9,15],[12,14],[9,17],[14,43],[9,16],[12,15],[121,96],[19,7],[94,-3],[56,-46],[5,-29],[1,-21],[-1,-24],[-2,-23],[-2,-18],[-4,-17],[-3,-26],[0,-14],[2,-15],[6,-13],[7,-10],[26,-19],[10,-4],[11,0],[14,8],[10,9],[1,2],[4,7],[13,14],[27,-4],[75,-55]],[[4373,4075],[-17,65],[-11,8],[-9,-3],[-8,4],[-7,26],[-1,28],[4,13],[-2,19],[-8,15],[-57,87],[-13,15],[-16,11],[-25,8],[-35,3],[-17,10],[-19,25],[-12,23],[-8,25],[-11,19],[-15,18],[-27,25],[-16,10],[-33,3],[-18,9],[-24,26],[-67,105],[-26,18],[-20,10],[-97,-31]],[[3758,4669],[-6,66],[-16,39],[-27,46],[-115,156],[-13,67],[-8,19],[-16,27],[-10,22],[-15,60],[-12,19],[-8,24],[30,80],[-15,22],[-1,19],[8,25],[21,37],[19,13],[15,3],[14,-1],[9,8],[7,25],[-1,21],[-4,16],[-13,31],[-4,17],[14,19],[29,25],[65,31],[65,52],[41,99],[13,50],[6,69]],[[3830,5875],[35,-9],[33,-20],[19,-4],[29,2],[40,-10],[22,0],[59,21],[26,18],[28,27],[110,73],[17,40],[41,203],[22,69],[68,154],[11,17],[7,23],[6,37],[10,190],[6,29],[12,28],[14,21],[11,26],[5,20],[-8,84]],[[4453,6914],[1,1],[10,35],[5,43],[9,40],[25,43],[30,18],[31,2],[31,-5],[28,6],[55,47],[29,15],[9,8],[10,33],[8,13],[38,18],[23,6],[15,0],[15,-7],[12,-10],[7,-19],[3,-33],[16,14],[15,8],[14,-6],[11,-26],[17,-29],[22,6],[24,17],[21,-1],[12,-24],[7,-66],[9,-27],[17,-7],[19,5],[17,-4],[8,-35],[-8,-40],[-17,-39],[-11,-40],[7,-47],[27,-47],[7,-18],[5,-23],[7,-77],[28,-30],[27,-16],[13,-32],[-30,-209],[23,-65]],[[3830,5875],[-48,20],[-183,163],[-25,16],[-18,6],[-75,-10],[-63,10],[-16,-3],[-12,-8],[-22,-7],[-17,11],[-58,71],[-8,20],[-2,25],[1,33],[0,38],[-4,31],[-6,20],[-48,87],[-21,31],[-16,17],[-71,38]],[[1865,6829],[-16,30],[-16,23],[-36,40],[-10,13],[-5,32],[-3,39],[10,151],[-10,39],[-5,24],[-1,25],[6,24],[11,26],[38,68],[19,25],[51,28],[17,5],[24,1],[9,-4],[9,-9],[31,-49],[13,-10],[15,-1],[22,5],[43,23],[7,44],[-13,39],[-12,47],[-6,57],[1,57],[4,49],[8,40],[13,32],[48,58],[20,33],[8,52],[1,38],[-1,52],[-5,22],[-7,16],[-24,12],[-6,3],[-20,16],[-16,19],[-14,23],[-12,30],[-7,42],[-2,30],[1,20],[34,190],[6,62],[3,79],[3,30],[4,19],[7,14],[16,22],[9,15],[8,16],[5,18],[2,20],[-1,28],[-5,23],[-12,18],[-17,10],[-47,17],[-21,13],[-16,22],[-15,11],[-22,6],[-92,-3],[-14,-4],[-13,-6],[-26,-27],[-31,-80],[-8,-9],[-6,0],[-10,4],[-11,11],[-26,34],[-5,20],[-1,21],[4,46],[0,28],[-2,23],[-5,22],[-5,16],[-6,16],[-12,44],[-8,39],[-4,6],[-7,4],[-27,0],[-41,-9],[-11,5],[-10,10],[-15,23],[-11,13],[-26,19],[-27,33],[-12,12]],[[1520,9152],[6,20],[4,38],[-1,16],[-10,39],[0,18],[14,16],[19,3],[20,-2],[15,4],[20,24],[14,34],[9,40],[6,76],[15,45],[5,24],[-2,21],[-7,39],[-2,22],[-1,86],[4,39],[13,35],[6,34],[-3,39],[3,31],[25,13],[23,-16],[45,-76],[25,-24],[32,-8],[14,8],[14,95],[6,20],[9,10],[23,7],[17,13],[6,21],[4,21],[8,16],[28,6],[26,-15],[150,-118],[26,-11],[28,8],[26,16],[27,8],[52,-40],[42,6],[25,-9],[11,-13],[14,-36],[9,-14],[12,-3],[12,7],[12,11],[11,6],[52,6],[29,-5],[19,-17],[5,-38],[-13,-141],[3,-65],[14,-42],[22,-28],[32,-20],[19,-31],[-6,-47],[-28,-93],[1,-34],[8,-51],[11,-50],[12,-28],[25,-6],[22,14],[23,8],[55,-61],[25,-7],[25,0],[28,-12],[10,-15],[15,-41],[10,-14],[20,-6],[19,6],[19,-1],[18,-21],[16,-36],[20,-34],[22,-29],[23,-21],[60,-32],[22,-23],[94,-205],[21,-26],[13,1],[25,14],[13,-2],[14,-16],[1,-20],[-6,-25],[-4,-29],[12,-42],[74,-101],[23,-62],[14,-22],[14,-14],[15,-9],[13,-14],[8,-30],[18,-45],[25,16],[28,37],[24,18],[12,-10],[18,-36],[10,-14],[12,-3],[28,7],[14,-4],[70,-107],[10,-25],[16,-60],[13,-17],[18,2],[8,26],[3,34],[6,27],[9,6],[32,14],[29,6],[15,-2],[14,-9],[17,-23],[4,-12],[4,-33],[4,-11],[9,-10],[21,-13],[9,-9],[29,-51],[10,-33],[3,-36],[-4,-21],[-7,-13],[-7,-17],[-2,-29],[21,-53],[78,29],[34,-59],[3,-44],[-3,-37],[2,-31],[37,-53],[3,-27],[-1,-30],[5,-35],[15,-24],[16,-13],[13,-19],[11,-69],[13,-21],[34,-24],[22,-26],[11,-38],[12,-92],[3,-53],[3,-25],[8,-20],[15,-10],[31,-8],[13,-19],[15,-15],[50,-17],[18,-1],[19,13]],[[5384,3472],[12,-44],[4,-28],[-5,-36],[-12,-27],[-34,-38],[-34,23],[-31,-28],[-31,-42],[-34,-21],[-20,3],[-15,7],[-13,1],[-15,-11],[-23,-60],[-16,-21],[-20,15],[-28,-58],[-50,-148],[-15,-30],[-42,52],[-25,-30],[-27,-4],[-83,20],[-15,0],[-10,-5],[-10,-16],[-7,-22],[1,-19],[12,-8],[25,-1],[4,-12],[-13,-30],[-9,-27],[-1,-28],[2,-28],[-4,-27],[-20,-28],[-1,-1]],[[4781,2715],[-27,-14],[-57,-8],[-24,-17],[11,-39],[35,-70],[3,-23],[5,-25],[-1,-20],[-12,-8],[-7,-10],[-9,-23],[-1,3],[-7,12],[-56,36],[-170,163],[-90,60],[-135,32],[-115,2],[-32,-20],[-6,-24],[-3,-30],[3,-29],[18,-45],[-1,-30],[-9,-30],[-26,-71],[-21,-37],[-24,-24],[-28,-3],[-28,18],[-12,25],[-8,34],[-14,47],[-15,31],[-61,91],[-45,32],[-151,-2],[-77,35],[-57,56],[-31,18],[-152,18],[-28,57],[-13,93],[-12,184],[-15,94],[-21,62],[-7,5]],[[3248,3321],[3,21],[9,56],[7,28],[5,15],[6,13],[9,12],[13,12],[23,13],[19,4],[61,-10],[54,31],[-29,52],[-56,47],[-15,20],[2,15],[3,9],[42,82],[55,85],[30,16],[29,-10],[15,-2],[16,5],[15,9],[36,39],[40,25],[7,11],[6,15],[-4,27],[-4,14],[-8,15],[-11,14],[-8,23],[-4,39],[12,78],[6,65],[16,85],[32,51],[10,18],[6,20],[-2,31],[-5,23],[-23,54],[-5,24],[1,24],[9,25],[16,25],[55,47],[16,33]],[[1295,9044],[-26,-34],[-20,-31],[-36,-91],[-16,-55],[-6,-32],[1,-16],[6,-9],[9,-11],[10,-15],[8,-20],[5,-23],[3,-24],[5,-60],[20,-138],[2,-82],[-8,-41],[-14,-26],[-16,-9],[-27,-4],[-8,-4],[-39,-38],[-11,-6],[-10,-2],[-11,1],[-13,-1],[-46,-19],[-36,-27],[-24,-34],[-17,-12],[-15,-4],[-21,6],[-16,-7],[-31,-43],[-4,-14],[-4,-22],[3,-16],[6,-22],[5,-28],[4,-38],[0,-57],[-5,-29],[-8,-24],[-13,-21],[-10,-37],[3,-24],[13,-16],[35,-16],[27,-6],[7,-4],[5,-7],[2,-4],[27,-65],[61,-105],[-39,-88],[-39,-40],[-53,-29],[-18,-32],[-18,-77],[-16,-51],[-8,-37],[-5,-34],[-3,-32],[-7,-47],[-15,-41],[-23,-56],[-16,-26],[-18,-20],[-59,-32],[-21,-21],[-34,-56],[-12,-31],[-7,-33],[-2,-18],[-9,-18],[-3,-16],[-5,-64],[-18,-36],[-7,-35],[-8,-50],[-20,-70],[12,-137],[-7,-182],[1,-32],[2,-26],[4,-15],[6,-21],[17,-51],[7,-27],[9,-49],[2,-29],[-2,-179],[0,-5],[0,-1]],[[619,5734],[-20,1],[-25,-24],[-12,-34],[-3,-42],[10,-156],[-6,-32],[-24,23],[-52,110],[-23,23],[-24,3],[-24,-12],[-24,-6],[-24,18],[-14,31],[-34,122],[-9,14],[-22,9],[-10,9],[-10,18],[-13,40],[-7,16],[-20,22],[-44,13],[-23,15],[-18,25],[-41,96],[-40,41],[-10,4],[-23,8],[-23,32],[-7,99],[1,50],[3,44],[9,42],[15,40],[19,26],[20,19],[18,24],[10,42],[0,1],[-4,80],[11,110],[24,93],[38,33],[21,-5],[25,-1],[24,12],[15,33],[-3,38],[-21,102],[7,36],[21,5],[15,-22],[12,-4],[25,161],[5,57],[-3,46],[-9,14],[-11,-1],[-11,3],[-7,25],[-9,100],[-26,80],[-9,41],[5,44],[13,17],[36,21],[7,22],[4,32],[8,16],[11,14],[11,25],[1,0],[25,33],[10,19],[9,22],[0,13],[-3,13],[-2,15],[5,17],[7,10],[15,14],[7,10],[27,54],[15,50],[2,54],[-11,65],[-17,65],[-12,62],[0,62],[18,68],[32,46],[74,34],[26,39],[63,138],[27,77],[16,88],[11,42],[18,18],[44,10],[24,13],[15,17],[32,53],[44,52],[12,23],[37,103],[17,31],[74,98],[25,65],[-17,65],[18,29],[21,45],[20,25],[12,-30],[12,-44],[21,-9],[83,49],[9,1],[13,-20],[16,-41],[14,-47],[16,-80],[28,-81],[10,-38],[12,-108]],[[1249,5021],[-57,19],[-9,27],[-2,31],[-5,26],[-14,14],[-19,-11],[-24,1],[-24,9],[-20,11],[-23,29],[-6,35],[-2,37],[-10,37],[-17,13],[-50,1],[-23,7],[-21,22],[-47,87],[-20,24],[-38,19],[-21,18],[-13,28],[-25,82],[-15,20],[-46,15],[-21,12],[-22,20],[-8,20],[1,23],[2,21],[-4,15],[-27,1]],[[1295,9044],[1,-4],[15,-23],[33,14],[29,2],[57,-46],[28,9],[13,35],[-2,68],[17,21],[21,12],[12,15],[1,5]],[[7401,3791],[-22,-110],[-5,-59],[3,-87],[-1,-53],[-4,-24],[-8,-14],[-28,-9],[-7,-5],[-12,-22],[-17,-43],[-19,-77],[-15,-44],[-15,-36],[-48,-93],[-59,-129],[-6,-35],[-13,-47],[-8,-23],[-14,-25],[-6,-16],[0,-1],[0,-1],[6,-34],[2,-15],[6,-100],[-29,-57],[-71,-22],[-15,-15],[-6,-17],[-1,-21],[-3,-25],[-11,-34],[-14,-11],[-17,-2],[-176,20],[-9,5],[-8,8],[-26,37]],[[6725,2555],[-30,52],[-31,29],[-37,19],[-39,13],[-54,16],[-77,77],[-17,27],[-12,25],[-5,24],[0,3],[-4,57],[-3,15],[-5,13],[-6,14],[-5,16],[-5,15],[-2,28],[-10,155],[-16,91],[-6,19],[-7,13],[-13,9],[-60,22],[-63,51],[-21,11],[-17,5],[-84,-7],[-13,-5],[-34,-20],[-26,-4],[-27,-12],[-27,-31],[-20,-10],[-14,-3],[-78,14],[-14,79],[-6,14],[-10,16],[-12,12],[-91,6],[-20,7],[-5,11],[-1,11],[6,18],[-1,15],[-4,19],[-16,38],[-8,26],[-5,20],[-2,16],[1,13],[2,14],[8,30],[2,16]],[[6206,4880],[-2,-32],[9,-36],[27,-29],[30,0],[26,21],[23,9],[39,-64],[23,-16],[24,-9],[20,-3],[16,7],[34,28],[15,4],[16,-13],[11,-22],[13,-18],[21,-4],[19,16],[29,56],[17,19],[20,1],[18,-12],[36,-32],[27,9],[28,0],[29,-6],[26,-12],[25,-30],[16,-30],[16,0],[34,91],[9,14],[26,17],[9,14],[1,21],[0,24],[4,20],[17,6],[6,-54],[2,-109],[20,-63],[23,-53],[27,-45],[32,-37],[54,-44],[27,-28],[18,-38],[5,-26],[6,-54],[21,-51],[2,-23],[-1,-23],[1,-30],[9,-57],[13,-33],[43,-68],[16,-46],[-1,-47],[-5,-49],[-1,-51],[14,-61],[22,-15],[57,12],[30,-6],[4,1]],[[7969,3996],[23,-40],[36,-102],[8,-63],[0,-67],[7,-124],[11,-69],[1,-52],[4,-40],[-5,-25],[-6,-19],[-9,-25],[-9,-36],[-13,-71],[-12,-51],[-14,-33],[-15,-18],[-29,-18],[-27,-9],[-11,-7],[-9,-8],[-40,-71],[-18,-24],[-61,-123],[-110,-182],[-19,-49],[-21,-83],[-10,-55],[-3,-22],[0,-20],[2,-18],[11,-53],[2,-22],[-3,-30],[-6,-40],[-32,-97],[0,-16],[20,-10],[68,3],[40,-16],[19,-11],[10,-12],[8,-22],[9,-18],[6,-9],[38,-42],[5,-9],[4,-8],[-8,-24],[-37,-81],[-33,-51],[-1,-14],[4,-12],[6,-9],[6,-11],[6,-14],[11,-61],[9,-71],[0,-72],[-4,-36],[-8,-22],[-51,-27],[-33,-40],[-35,-91],[-30,-21],[-12,3],[-16,7],[-32,26],[-11,0],[-11,-10],[-17,-35],[-5,-19],[-3,-15],[-1,-17],[2,-15],[10,-50],[3,-32],[0,-123],[2,-26],[3,-24],[5,-28],[8,-28],[22,-60],[7,-23],[4,-29],[-2,-23],[-4,-22],[-57,-221],[-2,-6],[0,-1]],[[7514,632],[-2,1],[-28,-3],[-7,5],[-7,11],[-85,95],[-37,22],[-44,-25],[-29,-6],[-21,-22],[-84,-130],[-27,-28],[-19,-6],[-9,14],[-2,19],[-1,21],[-2,21],[-10,15],[-11,-5],[-13,-9],[-11,5],[-66,94],[-18,39],[-13,86],[9,182],[-9,84],[-17,37],[-97,96],[-13,8],[-97,-68],[-54,-52],[-44,-43],[-23,-13],[-22,13],[-21,2],[-21,-33],[-17,-37]],[[6542,1022],[0,1],[-2,100],[3,50],[11,78],[19,89],[40,107],[7,27],[4,28],[2,43],[-2,31],[-2,23],[-3,14],[-2,22],[-1,19],[3,28],[5,29],[18,58],[78,194],[8,55],[3,62],[3,19],[2,13],[3,11],[2,20],[0,29],[-7,57],[-7,32],[-7,22],[-6,13],[-7,12],[-10,13],[-30,28],[-11,15],[-6,16],[-4,21],[0,22],[2,17],[20,42],[57,73]],[[7401,3791],[9,2],[17,15],[39,68],[7,10],[-1,26],[-6,17],[-8,15],[-6,18],[-1,27],[4,22],[0,20],[-11,22],[-13,16],[-1,14],[2,16],[0,20],[5,41],[1,14],[-4,19],[-14,37],[-1,24],[107,196],[24,-9],[13,-47],[1,-61],[-8,-49],[-10,-42],[3,-23],[9,-18],[9,-28],[8,-71],[9,-30],[18,-18],[0,-1],[25,-13],[25,-4],[24,-10],[20,-28],[29,-65],[12,-15],[12,-4],[10,7],[10,9],[11,4],[11,-11],[42,-61],[24,-17],[16,2],[45,41],[27,25],[16,36],[8,47]],[[6542,1022],[-2,-6],[-20,-27],[-43,-23],[-18,-1],[-114,54],[-36,67],[0,85],[-4,74],[-48,36],[-28,-5],[-69,-49],[-32,5],[-2,33],[2,38],[-21,19],[-16,4],[-15,7],[-28,27],[-11,44],[-7,51],[-18,47],[-27,28],[-62,37],[-28,31],[-1,1],[-13,38],[-19,8],[-20,-13],[-17,-23],[-13,0],[-148,72],[-16,20],[-7,52],[10,56],[18,57],[12,53],[7,97],[-3,94],[-12,92],[-20,89],[-31,82],[-36,46],[-353,115],[-27,31],[-23,58],[-12,46],[-17,23],[-37,-11],[-12,7],[-13,60],[-11,22],[-30,21],[-13,14],[-12,21],[-5,45],[-22,9],[-24,-18],[-62,-137],[-27,-32],[-39,-3],[-49,16],[-16,10],[-1,-1]],[[8541,3945],[0,-4],[-3,-154],[13,-110],[9,-50],[2,-24],[0,-13],[-7,-48],[-2,-57],[10,-101],[0,-34],[-2,-25],[-23,-144],[-23,-247],[-6,-33],[-12,-99],[-5,-26],[-14,-29],[-22,-45],[-3,-15],[-6,-35],[15,-90],[49,-149],[4,-41],[-1,-33],[-4,-16],[-6,-15],[-9,-9],[-11,-9],[-10,-10],[-6,-14],[-4,-38],[7,-109],[-1,-45],[-10,-149],[3,-36],[7,-31],[24,-45],[12,-30],[5,-29],[11,-373],[41,-12],[146,60],[44,2],[20,-14],[5,-44],[-6,-49],[-5,-18],[-6,-51],[-2,-16],[-7,-29],[-17,-25],[-34,-39],[-86,-162],[-28,-22],[-19,-31],[-28,-205],[-18,-54],[-6,-31],[3,-24],[4,-18],[2,-27],[1,-28],[-2,-21],[-10,-25],[-27,-45],[-4,-19],[0,-1]],[[8483,433],[-34,-21],[-39,-41],[-12,-55],[-24,-49],[-30,-35],[-31,-17],[-12,4],[-43,31],[-17,-5],[-9,-24],[-8,-29],[-13,-18],[-23,9],[-86,93],[-82,68],[-25,27],[-17,35],[-17,27],[-19,19],[-24,10],[-37,27],[-75,73],[-37,24],[-11,0],[-27,-6],[-9,2],[-8,18],[-1,34],[-9,17],[-20,8],[-27,-8],[-46,-28],[-28,-32],[-8,-5],[-13,5],[-8,10],[-5,12],[-9,8],[-18,10],[-8,1]],[[7969,3996],[0,57],[-1,43],[8,28],[10,25],[10,33],[3,36],[0,40],[4,36],[11,28],[20,6],[17,-22],[16,-28],[19,-15],[19,8],[27,43],[15,14],[23,-3],[24,-17],[22,-26],[17,-27],[5,-30],[-4,-73],[9,-33],[37,-23],[87,6],[34,-49],[10,-59],[20,-41],[27,-22],[31,-5],[37,11],[15,8]],[[9396,3608],[-29,-148],[-27,-99],[-19,-53],[-14,-27],[-12,-13],[-9,0],[-10,7],[-7,11],[-9,9],[-9,3],[-10,-6],[-5,-19],[-10,-88],[-14,-66],[-14,-44],[-24,-54],[-13,-42],[-9,-39],[-15,-100],[-12,-145],[-9,-62],[-4,-44],[-1,-37],[5,-29],[7,-23],[74,-142],[9,-13],[13,-11],[55,-19],[119,-87],[52,-36],[19,-24],[5,-17],[3,-15],[0,-16],[-3,-13],[-8,-16],[-18,-31],[-6,-19],[-2,-24],[-4,-21],[-6,-19],[-9,-13],[-11,-11],[-55,-35],[-15,-13],[-31,-43],[-69,-128],[-58,-128],[-24,-41],[27,-70],[28,-142],[10,-29],[10,-20],[21,-22],[49,-35],[2,-2],[61,-148],[18,-66],[2,-18],[1,-39],[-27,-206],[-3,-535],[2,-37],[6,-33]],[[9360,163],[-31,-42],[-45,-37],[-43,21],[-88,90],[-38,13],[-41,-4],[-39,-20],[-34,-37],[-14,-35],[-23,-89],[-15,-23],[-17,5],[-52,42],[-16,18],[-11,32],[-8,38],[-13,23],[-38,-21],[-65,-13],[-36,26],[-28,67],[-20,82],[-13,72],[-14,121],[-1,42],[-3,46],[-15,-1],[-17,-26],[-37,-70],[-16,-21],[-46,-29]],[[8541,3945],[8,4],[21,-2],[30,-33],[39,-73],[67,-157],[48,-46],[32,-3],[63,13],[71,-34],[28,31],[27,44],[35,21],[26,-14],[-6,-33],[-14,-41],[0,-37],[22,1],[109,54],[48,21],[21,4],[24,-12],[6,-12],[4,-19],[7,-20],[10,-15],[21,-1],[38,37],[18,10],[30,-9],[22,-16]],[[9396,3608],[27,-19],[33,5],[36,31],[29,47],[23,59],[19,67],[14,38],[14,5],[24,-24],[96,-8],[13,-8],[26,-23],[15,-5],[16,5],[33,23],[14,3],[28,-17],[53,-52],[28,-11],[30,-12],[16,-11],[11,-15],[2,-29],[-15,-45],[6,-101],[-12,-62],[-18,-63],[-29,-205],[-77,-231],[-31,-124],[-2,-26],[2,-24],[6,-48],[3,-11],[11,-20],[2,-11],[-2,-21],[-6,-14],[-6,-12],[-5,-16],[-11,-57],[-3,-27],[2,-34],[5,-27],[15,-47],[3,-28],[-7,-60],[-30,-120],[-19,-76],[-5,-172],[-18,-71],[-2,-21],[2,-20],[6,-18],[20,-34],[22,-88],[22,-25],[19,-16],[16,-27],[26,-64],[24,-80],[19,-86],[10,-38],[28,-75],[10,-38],[5,-42],[5,-106],[10,-97],[2,-46],[-2,-47],[-5,-49],[-21,-108],[-55,-194],[-18,-105],[-9,-53],[0,-25],[3,-31],[-10,-101],[-36,-118],[-47,-88],[-38,-8],[-17,30],[-40,43],[-17,29],[-8,24],[-8,43],[-6,20],[-3,-2],[-30,44],[-1,10],[-21,-10],[-38,-57],[-21,0],[-23,21],[-21,13],[-15,-11],[-9,-52],[-8,-37],[-18,-17],[-21,5],[-16,26],[-20,20],[-27,-17],[-13,-18]],[[3248,3321],[-27,19],[-155,-71],[-105,-22],[-47,26],[-67,120],[-95,85],[-147,215],[-53,38],[-21,21],[-23,44],[-25,37],[-1,1]]],
transform:{scale:[.0008139593663366394,.0004073543747374736],translate:[80.03028772000005,26.343767802000087]}},m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();