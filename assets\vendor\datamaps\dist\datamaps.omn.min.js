!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo={type:"Topology",objects:{omn:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Ad Dakhliyah"},id:"OM.DA",arcs:[[0,1,2,3,4,5,6]]},{type:"Polygon",properties:{name:"Al Wusta"},id:"OM.WU",arcs:[[7,8,9,10,11,-4]]},{type:"MultiPolygon",properties:{name:"Ash Sharqiyah South"},id:"OM.SS",arcs:[[[12]],[[-8,-3,13,14]]]},{type:"MultiPolygon",properties:{name:"Dhofar"},id:"OM.JA",arcs:[[[15]],[[16]],[[-10,17]]]},{type:"Polygon",properties:{name:"Al Dhahira"},id:"OM.ZA",arcs:[[-5,-12,18,19,20]]},{type:"Polygon",properties:{name:"Al Batnah North"},id:"OM.BS",arcs:[[21,-6,-21,22,23]]},{type:"MultiPolygon",properties:{name:"Musandam"},id:"OM.MU",arcs:[[[24],[25]],[[26]]]},{type:"Polygon",properties:{name:"Muscat"},id:"OM.MA",arcs:[[27,-1,28,29]]},{type:"Polygon",properties:{name:"Al Buraymi"},id:"OM.BU",arcs:[[-23,-20,30]]},{type:"Polygon",properties:{name:"Ash Sharqiyah North"},id:"OM.SN",arcs:[[31,-14,-2,-28]]},{type:"Polygon",properties:{name:"Al Batnah South"},id:"OM.BS",arcs:[[-29,-7,-22,32]]}]}},arcs:[[[7805,6999],[0,-1],[275,28],[321,-246],[-59,-233]],[[8342,6547],[-192,-36],[-212,-101],[-78,-7],[-291,-172],[-125,-196],[315,-834]],[[7759,5201],[121,-321],[143,-310]],[[8023,4570],[-404,156],[-2093,788]],[[5526,5514],[252,244],[202,456],[244,814]],[[6224,7028],[367,13]],[[6591,7041],[377,-286],[147,-12],[48,-18],[326,40],[39,63],[277,171]],[[8023,4570],[136,-53],[-180,-446]],[[7979,4071],[-26,6],[-37,-2],[-38,-9],[-40,-20],[-52,-10],[-16,-9],[-8,-9],[-22,-48],[-1,-11],[8,-11],[4,-10],[-11,-8],[-15,-5],[-32,-5],[-23,-9],[-23,-11],[-30,-22],[-11,-11],[-8,-11],[-3,-14],[4,-10],[18,-15],[4,-10],[-9,-22],[-23,-13],[-55,-18],[-22,-10],[-22,-14],[-19,-17],[-7,-19],[-7,-11],[-14,-13],[-13,-18],[-1,-25],[24,-42],[8,-25],[-21,-37],[-19,-56],[-1,-15],[-3,-30],[-29,-54],[-21,-58],[-15,-26],[-81,-90],[-12,-27],[5,-32],[9,-12],[12,-9],[9,-10],[4,-14],[1,-32],[-2,-15],[-6,-13],[-10,-27],[-2,-20],[9,-20],[61,-78],[10,-17],[8,-28],[0,-28],[-6,-26],[-22,-47],[-5,-21],[3,-24],[26,-52],[12,-16],[32,-29],[8,-17],[-11,-42],[-1,-21],[48,-67],[0,-15],[-31,-24],[-19,-11],[-5,-2],[-57,-6],[-6,-6],[-6,-6],[-14,-8],[-15,-7],[-13,-3],[-264,10],[-72,-9]],[[6949,2363],[-1103,226],[-1550,1963],[-17,19]],[[4279,4571],[16,40],[67,163],[63,152],[56,137],[48,116],[36,89],[24,57],[8,20],[7,16]],[[4604,5361],[30,4],[892,149]],[[8483,3621],[-6,9],[-6,8],[-6,32],[-2,32],[3,17],[0,1],[-3,10],[8,81],[7,13],[14,14],[7,6],[23,13],[9,2],[3,2],[2,2],[6,19],[3,6],[21,6],[22,1],[21,2],[23,12],[10,11],[5,5],[4,13],[1,2],[6,39],[8,19],[11,16],[61,61],[24,37],[14,34],[11,12],[19,4],[16,-17],[-2,-18],[-9,-19],[-5,-20],[3,-22],[7,-17],[40,-54],[11,-16],[-5,-11],[-11,-8],[-49,-28],[-52,-22],[-23,-16],[-30,-35],[-15,-24],[-15,-49],[-12,-18],[0,-11],[1,-1],[2,-9],[0,-7],[-6,-8],[-43,-32],[-22,-24],[-12,-10],[-11,-3],[-17,-1],[-14,-3],[-6,-7],[-4,-10],[-11,-12],[-14,-9],[-15,0]],[[7759,5201],[328,90],[322,88],[298,81],[284,78],[275,112],[157,63],[62,123],[12,138],[-24,54],[-2,50],[70,55]],[[9541,6133],[29,-19],[29,-13],[7,-5],[1,-10],[-7,-3],[-11,2],[-8,4],[0,-15],[9,-6],[16,-2],[18,2],[98,25],[34,-10],[51,-29],[8,-1],[20,2],[7,-1],[4,-5],[2,-11],[3,-5],[0,-3],[-1,-5],[1,-6],[8,-7],[7,-3],[8,-2],[8,-2],[8,0],[5,5],[-9,11],[-18,16],[5,13],[14,1],[25,3],[34,-7],[28,-27],[18,-34],[7,-31],[-25,-168],[-17,-45],[-25,-41],[-73,-77],[-38,-24],[-21,-17],[-9,-18],[-4,-24],[-23,-49],[-6,-50],[-12,-20],[-51,-16],[-15,-18],[-21,-37],[-101,-103],[-38,-92],[-24,-43],[-119,-143],[-10,-33],[-4,-9],[-10,-7],[-179,-60],[-37,-17],[-142,-88],[-172,-137],[-53,-61],[-12,-9],[-18,-10],[-20,-21],[-39,-56],[-4,-9],[-3,-36],[-4,-11],[-8,-9],[-74,-45],[6,-22],[-22,-14],[-53,-20],[2,-2],[-10,-19],[-2,0],[-8,-7],[-6,-5],[-3,-6],[3,-9],[8,-10],[11,-8],[13,-5],[19,7],[21,15],[14,6],[-1,-20],[-13,-14],[-23,-13],[-23,-5],[-12,11],[-8,0],[-7,-15],[-9,-28],[-10,-14],[-10,-7],[-28,-12],[-14,-10],[-16,-20],[-14,-26],[-10,-28],[-4,-27],[-32,-49],[-17,-36],[1,-17],[-7,-8],[-7,-34],[-4,-8],[-10,-1],[-17,-5],[-17,-7],[-12,-8],[-9,-27],[-27,-11],[-36,1],[-33,9],[-24,9],[-17,4],[-42,1],[-16,-2],[-30,-9],[-18,-3],[3,9],[8,5],[-6,3],[-45,9],[-26,2],[-27,9],[-3,20],[16,42],[0,-13],[5,-11],[10,-10],[12,-9],[5,21],[-13,46],[13,21],[17,21],[13,29],[3,31],[-11,22]],[[4973,885],[-19,-9],[-38,0],[-12,-5],[-5,9],[-1,2],[7,8],[15,6],[18,3],[25,-12],[10,-2]],[[5171,873],[-20,-2],[-56,2],[-18,5],[-11,23],[34,15],[49,13],[33,12],[4,-14],[0,-1],[12,-13],[17,-9],[18,-5],[-40,-19],[-22,-7]],[[6949,2363],[-302,-36],[-157,-34],[-215,-68],[-82,-46],[-54,-21],[-30,-17],[-12,-22],[-11,-11],[-136,-104],[-23,-37],[-6,-48],[9,-34],[1,-12],[-4,-13],[-19,-20],[-6,-16],[-12,-21],[-3,-11],[0,-42],[-12,-43],[-65,-119],[18,-20],[-3,-22],[-18,-20],[-24,-15],[-56,-19],[-27,-12],[-39,-54],[-8,-5],[-18,-4],[-9,-4],[-59,-51],[-4,-7],[2,-23],[-15,-3],[-199,20],[-25,-3],[-38,-14],[-28,-4],[-109,7],[-25,-3],[-83,-26],[-46,-9],[-326,-11],[-127,-26],[-29,-2],[-10,-2],[-7,-4],[-5,-5],[-8,-4],[-24,0],[-1,0],[-42,-8],[-11,-5],[-18,-17],[-18,-40],[-12,-17],[-19,-17],[-5,-8],[-20,-42],[-5,-7],[-17,-16],[-118,-75],[-36,-51],[-8,-20],[-4,-24],[5,-28],[14,-13],[43,-19],[24,-20],[10,-21],[0,-47],[-8,-20],[-52,-54],[-3,-9],[-2,-15],[1,-12],[8,-6],[10,-4],[1,-7],[-6,-14],[-7,-5],[-34,-13],[-12,-6],[-7,-9],[-5,-8],[-6,-18],[-12,-16],[4,-5],[-12,-11],[-38,-10],[-20,-8],[-6,-9],[-7,-14],[-11,-13],[-15,-6],[-52,-46],[-57,-32],[-8,0],[-12,16],[-15,-7],[-16,-15],[-15,-8],[-20,-2],[-18,-5],[-18,-3],[-18,3],[-20,-16],[-23,-6],[-52,-5],[-43,-13],[-21,0],[-14,13],[-15,-6],[-23,2],[-15,-4],[-43,22],[-9,-8],[-11,20],[-5,18],[-15,13],[-52,8],[-36,9],[-16,2],[-15,-1],[-26,-4],[-15,-2],[-45,7],[-17,0],[-13,-3],[-27,-9],[-8,-2],[-59,14],[-32,1],[-26,-15],[-12,5],[-12,2],[-237,-21],[-100,0],[-23,-4],[-56,-22],[-13,-8],[-5,-9],[0,-8],[3,-8],[2,-7],[-4,-9],[-18,-13],[-4,-6],[-14,-11],[-33,-5],[-58,-2],[-76,-13],[-25,-1],[-23,-6],[-26,-12],[-26,-8],[-23,5],[-24,-12],[-32,-20],[-28,-23],[-12,-19],[-11,-12],[-24,-11],[-44,-15],[-42,-9],[-203,-3],[-107,-12],[-208,-52],[-108,-43],[-79,139],[-80,139],[-79,139],[-80,138],[-49,86],[-13,15],[-22,6],[-48,2],[-19,9],[2,0],[7,1],[2,1],[-40,73],[-54,99],[-55,99],[-54,99],[-54,99],[-55,100],[-54,99],[-55,99],[-54,99],[-55,99],[-54,99],[-54,99],[-55,99],[-54,99],[-54,99],[-55,99],[-54,99],[-36,66],[-9,16],[142,38],[217,58],[217,59],[218,59],[218,58],[217,59],[218,58],[217,59],[218,58],[217,59],[218,58],[217,59],[218,58],[218,59],[217,58],[217,59],[218,58],[191,52],[41,99],[8,20],[24,58],[37,89],[47,115],[57,137],[62,152],[67,163],[70,169],[53,128]],[[4604,5361],[47,115],[0,24],[-14,22],[-67,85],[-103,128],[-103,128],[-102,128],[-103,128],[-81,101],[14,150],[-8,150],[4,30],[40,99],[121,158],[28,62],[27,103],[14,34],[24,26],[0,1],[47,38],[18,20]],[[4407,7091],[174,-13],[133,57],[168,36],[89,43],[-45,150],[53,50],[80,28],[0,86],[71,36],[186,28],[150,0],[43,102]],[[5509,7694],[198,-64],[377,-247],[117,-333],[23,-22]],[[7060,7376],[-147,-192],[-165,-105],[-157,-38]],[[5509,7694],[-8,89],[-125,314],[-168,161],[1,42]],[[5209,8300],[2,-1],[27,1],[24,5],[12,11],[7,17],[28,25],[20,38],[1,0],[19,6],[47,5],[23,8],[42,30],[7,5],[36,19],[7,10],[0,7],[3,19],[4,33],[6,6],[6,5],[69,5],[28,-83],[82,-125],[13,-10],[9,-24],[8,-43],[150,-209],[31,-26],[72,-47],[139,-126],[27,-32],[12,-19],[11,-30],[125,-111],[151,-95],[119,-71],[41,-20],[181,-52],[233,-46],[29,-9]],[[5556,8878],[-6,-28],[-36,-13],[-29,-8],[-15,-9],[-38,-20],[-20,-7],[-21,5],[-15,5],[-1,6],[-1,4],[3,26],[34,12],[12,11],[-7,8],[-5,11],[-2,16],[34,19],[38,-3],[24,-27],[51,-8]],[[5442,8860],[4,-28],[26,7],[6,17],[-18,9],[-11,0],[-7,-5]],[[5582,9999],[18,-28],[19,9],[7,5],[9,-8],[-6,-7],[0,-3],[2,-2],[4,-8],[12,7],[8,-2],[6,-7],[9,-6],[29,-6],[16,-1],[16,1],[-2,5],[-5,11],[-2,5],[27,7],[13,-26],[-3,-10],[-18,-1],[-32,1],[-22,-5],[-38,-25],[-21,-10],[2,0],[0,-6],[-11,-10],[28,-22],[14,-8],[18,-5],[11,5],[13,8],[14,2],[14,-15],[-23,-30],[-12,-4],[-8,20],[-13,-1],[-14,1],[5,-10],[1,-6],[-2,-6],[-4,-6],[16,-12],[19,-8],[17,-11],[9,-18],[-15,0],[-14,2],[-12,5],[-11,7],[-18,-6],[-16,5],[-9,12],[-1,17],[-8,-4],[-19,-6],[-7,-4],[-1,11],[3,8],[6,6],[9,3],[-29,4],[-31,-14],[-19,-19],[9,-13],[-6,-11],[-2,-12],[3,-14],[5,-13],[11,21],[11,11],[13,4],[8,-7],[2,-12],[-4,-13],[-6,-10],[25,-8],[114,1],[-11,-13],[-19,-13],[-39,-17],[-37,-10],[-12,-9],[5,-16],[32,12],[17,-11],[1,-19],[-29,-15],[6,-10],[17,-17],[4,-8],[12,-4],[28,-5],[-10,-5],[-3,-3],[-5,-14],[-19,7],[-21,5],[-18,-2],[-12,-10],[20,-24],[-9,-23],[-21,-21],[-16,-22],[-1,-8],[1,-24],[-25,-18],[-21,-28],[-14,-13],[-19,-8],[0,22],[-17,-22],[-18,-34],[-9,-37],[9,-34],[7,-12],[-53,-12],[-21,-2],[-20,4],[-8,7],[-13,21],[-11,7],[-30,4],[-5,2],[-2,1],[-8,12],[-6,46],[1,18],[14,10],[16,10],[10,13],[-6,13],[-30,29],[-6,17],[7,16],[24,25],[9,15],[5,92],[8,30],[3,19],[-6,21],[-20,39],[-15,2],[-64,-13],[-30,-1],[6,9],[12,37],[84,97],[31,46],[13,10],[21,8],[1,-6],[-1,-14],[14,-16],[-4,-6],[-5,-15],[9,0],[13,9],[33,2],[6,6],[8,3],[17,-4],[15,-12],[4,-18],[9,12],[8,-1],[11,-4],[15,0],[35,19],[13,2],[39,-8],[9,1],[3,9],[-6,9],[-10,7],[-9,3],[-25,2],[-15,0],[-11,-5],[-7,-7],[-13,-9],[-15,-5],[-14,3],[0,12],[9,43],[0,16],[38,-22],[16,-4],[7,15],[-6,8],[-14,10],[-18,9],[-14,4],[5,9],[7,2],[8,0],[7,3],[16,14],[7,13],[-2,22],[4,15],[8,0]],[[9255,6412],[-30,-30],[-84,-69],[-418,41],[-381,193]],[[7805,6999],[-33,101],[41,146],[-3,1],[1,2],[5,8]],[[7816,7257],[6,-1],[78,-24],[22,-4],[21,-7],[47,-35],[23,-14],[44,-11],[59,-5],[62,1],[52,8],[28,7],[11,7],[9,19],[11,0],[24,-5],[55,-1],[29,-4],[26,-9],[11,-13],[25,-41],[11,-9],[7,-2],[5,-4],[6,-5],[4,-4],[3,1],[7,1],[9,0],[8,-2],[18,-11],[10,-9],[6,-8],[9,0],[8,14],[3,-5],[3,-3],[2,-5],[2,-7],[8,0],[2,4],[6,10],[7,-5],[4,-1],[16,6],[31,-31],[8,-17],[-4,-16],[10,-14],[13,-47],[16,-19],[75,-52],[46,-16],[18,-18],[21,-44],[13,-17],[38,-27],[20,-21],[10,-60],[81,-62],[5,-8],[8,-31],[5,-7],[6,-5],[36,-21],[67,-30],[43,-24],[32,-28],[22,-32],[12,-22]],[[4407,7091],[42,127],[11,18],[60,65],[5,14],[-4,77],[-6,30],[-20,22],[-46,21],[-22,14],[-11,19],[5,15],[55,7],[54,16],[25,10],[37,15],[35,10],[36,5],[42,-1],[42,-5],[19,1],[21,5],[23,3],[33,-19],[24,-2],[148,45],[80,15],[14,16],[-47,63],[-15,43],[-20,37],[-38,12],[-71,-22],[-22,1],[-79,13],[-16,6],[-2,15],[10,18],[50,65],[8,15],[4,50],[-2,9],[-10,9],[-25,12],[-9,8],[-4,15],[3,56],[-16,43],[-1,22],[12,14],[18,14],[8,35],[36,24],[-4,20],[-19,31],[-7,11],[-3,19],[6,38],[0,19],[-11,79],[14,32],[38,36],[36,24],[6,4],[44,20],[49,10],[51,-6],[3,0],[35,-16],[14,-31],[11,-45],[5,-22],[-30,16],[-17,7],[-18,3],[-22,-5],[-4,-13],[9,-15],[48,-32],[37,-71],[39,-23],[18,-1]],[[9255,6412],[34,-66],[5,-15],[10,-19],[23,-14],[46,-19],[50,-60],[7,-17],[17,-16],[54,-37],[40,-16]],[[7060,7376],[69,-23],[74,-18],[53,-7],[29,-1],[86,4],[24,-4],[36,-36],[13,-7],[31,-6],[36,-14],[32,-3],[166,7],[56,-3],[51,-8]]],transform:{scale:[.0007866746139613907,.0009744540821801049],translate:[51.97861495000021,16.64240603028115]}},m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));
try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();