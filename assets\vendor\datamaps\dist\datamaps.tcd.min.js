!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo={type:"Topology",objects:{tcd:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Hadjer-Lamis"},id:"TD.HD",arcs:[[0,1,2,3,4,5,6]]},{type:"Polygon",properties:{name:"Kanem"},id:"TD.KM",arcs:[[7,-1,8,9,10]]},{type:"Polygon",properties:{name:"Lac"},id:"TD.LC",arcs:[[-7,11,-9]]},{type:"Polygon",properties:{name:"Batha"},id:"TD.BA",arcs:[[12,13,14,15,-3,16,17]]},{type:"Polygon",properties:{name:"Wadi Fira"},id:"TD.BI",arcs:[[18,19,-13,20,21]]},{type:"Polygon",properties:{name:"Guéra"},id:"TD.GR",arcs:[[22,23,24,25,-4,-16]]},{type:"Polygon",properties:{name:"Ouaddaï"},id:"TD.OA",arcs:[[26,27,-14,-20]]},{type:"Polygon",properties:{name:"Logone Occidental"},id:"TD.LO",arcs:[[28,29,30]]},{type:"Polygon",properties:{name:"Logone Oriental"},id:"TD.LR",arcs:[[31,32,33,-29,34]]},{type:"Polygon",properties:{name:"Mayo-Kebbi Est"},id:"TD.ME",arcs:[[35,36,37,38]]},{type:"Polygon",properties:{name:"Tandjilé"},id:"TD.TA",arcs:[[39,40,-35,-31,41,-36,42]]},{type:"Polygon",properties:{name:"Mandoul"},id:"TD.MA",arcs:[[43,44,-32,-41]]},{type:"Polygon",properties:{name:"Salamat"},id:"TD.SA",arcs:[[45,46,47,-24]]},{type:"Polygon",properties:{name:"Ville de N'Djamena"},id:"TD.NJ",arcs:[[48,49]]},{type:"Polygon",properties:{name:"Mayo-Kebbi Ouest"},id:"TD.MW",arcs:[[-37,-42,-30,-34,50]]},{type:"Polygon",properties:{name:"Borkou"},id:"TD.BR",arcs:[[51,-21,-18,52,-11,53,54,55]]},{type:"Polygon",properties:{name:"Tibesti"},id:"TD.TI",arcs:[[-55,56]]},{type:"Polygon",properties:{name:"Ennedi"},id:"TD.EN",arcs:[[-22,-52,57]]},{type:"Polygon",properties:{name:"Chari-Baguirmi"},id:"TD.CG",arcs:[[-26,58,-43,-39,59,-50,60,-5]]},{type:"Polygon",properties:{name:"Barh El Gazel"},id:"TD.BG",arcs:[[-17,-2,-8,-53]]},{type:"Polygon",properties:{name:"Sila"},id:"TD.SI",arcs:[[61,-46,-23,-15,-28]]},{type:"Polygon",properties:{name:"Moyen-Chari"},id:"TD.MO",arcs:[[62,-44,-40,-59,-25,-48]]}]}},arcs:[[[2004,3633],[173,-52],[32,-6],[67,-2],[23,2],[12,10]],[[2311,3585],[77,-41],[5,-6],[15,-39],[13,-2],[234,3],[10,1],[14,3],[18,6],[15,3],[8,1],[9,0],[35,-3],[11,-2],[22,-1],[13,-2],[14,-4],[10,-3],[6,0],[6,0],[8,3],[5,2],[11,7],[7,4],[8,3],[219,20],[11,2],[8,3],[8,3],[15,9],[6,2],[10,2],[19,4],[10,2],[7,2],[5,0],[14,-5],[8,-3],[24,-2],[7,0],[17,4],[11,2],[54,5],[7,1],[6,2],[13,9],[4,1],[8,0],[51,-7],[68,-17]],[[3495,3557],[-41,-39],[-15,-11],[-13,-6],[-85,-52],[-24,-23],[-3,-4],[-1,-3],[0,-5],[1,-4],[3,-3],[3,-2],[29,-16],[23,-8],[29,-15],[6,-5],[41,-51],[4,-6],[15,-40],[13,-22],[6,-8],[3,-2],[3,-3],[4,-2],[4,-1],[10,-3],[5,-1],[3,-2],[4,-4],[8,-15],[4,-5],[4,-4],[8,-4],[8,-7],[3,-2],[4,-2],[5,-2],[5,0],[6,-1],[12,1],[6,0],[7,-2],[6,-2],[7,-7],[1,-5],[1,-8],[24,-78],[4,-9],[17,-15],[4,-2],[4,-1],[23,-6],[4,-2],[3,-2],[3,-4],[6,-10],[3,-3],[10,-6],[6,-6],[3,-5],[1,-5],[-1,-17],[2,-4],[2,-3],[13,-9],[4,-2],[4,-2],[6,-1],[11,-1],[11,-1],[6,1],[6,0],[10,2],[5,1],[6,0],[5,-1],[4,-1],[8,-4],[4,-1],[5,-1],[20,-2],[6,0],[5,-2],[8,-3],[4,-2],[8,-3],[8,-2]],[[3902,2939],[7,-16],[-45,-105],[-3,-9],[0,-9],[6,-11],[97,-125],[-158,-30],[-184,-109]],[[3622,2525],[-136,45],[-58,38],[-68,38],[-68,7],[-77,0],[-49,25],[-9,90],[-49,38],[-48,38],[0,71],[-29,70],[-117,38],[-436,109],[-77,-58],[-97,-19],[-155,6],[-155,-25],[-49,-26],[-87,7],[-39,32],[-58,6],[-39,-26],[-97,-12],[-87,-13],[-68,0],[-88,-7]],[[1382,2997],[1,44],[2,5],[-16,48],[-13,15],[-1,3],[2,7],[-1,2],[-5,3],[-6,1],[-4,0],[2,-4],[-8,5],[-5,1],[-1,1],[1,7],[1,4],[8,9],[4,4],[-32,77],[-11,12],[-31,-4],[-17,1],[-7,10],[-2,6],[-6,6],[-9,5],[-8,2],[-5,-2],[-15,-15],[-11,10],[14,21],[-6,10],[-16,4],[-28,-5],[-15,5],[-6,4],[-17,7],[-3,4],[0,3],[-2,4],[-8,2],[-6,-1],[-18,-9],[-9,-1],[-5,3],[-1,6],[0,6],[-8,-2],[-6,1],[-3,2],[-2,6],[0,24],[-17,11],[-29,14],[-10,9],[0,8],[15,29],[2,13],[-3,10],[-8,9],[-14,10],[-1,1]],[[979,3468],[19,0],[472,-1],[9,1],[6,1],[3,4],[6,15],[14,22],[5,15],[0,4],[-1,3],[-17,36],[-2,3],[-3,2],[-4,2],[-7,4],[-3,3],[-2,3],[-1,12],[-3,11],[-4,5],[-4,6],[-1,3],[0,4],[3,2],[5,2],[7,0],[19,-4],[15,-5],[20,-4],[12,-2],[5,-1],[5,-1],[4,-2],[6,-2],[10,-2],[28,-3],[33,-8],[4,-1],[4,-2],[2,-2],[2,-3],[0,-7],[0,-3],[3,-2],[4,-2],[23,-7],[4,-2],[5,-5],[4,-2],[4,-2],[3,-2],[3,-2],[7,-13],[3,-2],[3,-3],[3,-2],[4,-1],[5,-1],[5,1],[6,4],[2,4],[2,4],[2,8],[1,4],[3,2],[3,2],[3,0],[8,2],[5,1],[5,-1],[7,-5],[3,0],[5,1],[49,26],[3,2],[1,3],[0,4],[-5,9],[-3,3],[-3,2],[-5,1],[-46,6],[-5,1],[-4,2],[-3,2],[-82,75],[-5,7],[5,4],[10,2],[121,15],[10,0],[6,-3],[4,-3],[23,-25],[11,-9],[17,-11],[8,-3],[114,-34]],[[3058,5557],[-173,-190],[-213,-486],[-126,-281],[-19,-146],[-59,-64],[-261,-205],[-175,-44],[10,-202],[110,0],[5,2],[4,2],[21,16],[4,2],[6,0],[5,0],[11,-2],[20,-5],[22,-3],[21,-5],[5,-2],[5,-3],[5,-8],[3,-9],[4,-14],[0,-4],[-5,-8],[-17,-20],[-2,-4],[0,-5],[10,-21],[3,-8],[4,-9],[3,-3],[8,-7],[20,-14],[6,-5],[2,-9],[1,-169],[-3,-15],[-12,-24]],[[2004,3633],[-175,305],[-155,-1],[-282,-45],[-8,-1],[-47,6],[-7,0],[-10,-1],[-23,-11],[-10,-3],[-9,5],[-144,149],[-27,20],[-37,21],[-11,8],[-83,73],[-7,4],[-6,3],[-10,2],[-11,2],[-62,4],[-8,1],[-11,3],[-9,7],[-27,25],[-10,8],[-44,9],[-13,7],[-55,39],[-32,10],[-6,4],[-4,3],[-4,9],[-7,27],[-2,4],[-5,5],[-5,3],[-6,0],[-6,0],[-3,2],[-17,14],[-7,3],[-6,2],[-12,1],[-33,0],[-9,1],[-5,2],[-4,2],[-11,12],[-43,8],[-157,7],[-7,2],[-7,5],[-9,9],[-13,6],[-11,2],[-13,1],[-101,0],[-7,1]],[[151,4417],[15,2],[32,17],[7,11],[1,12],[-6,24],[-3,3],[-10,6],[-1,4],[3,3],[19,8],[30,22],[10,5],[19,2],[18,1],[14,5],[9,15],[-2,13],[-13,26],[-4,13],[1,13],[6,12],[14,20],[31,42],[24,34],[28,25],[28,25],[28,26],[29,25],[28,26],[28,25],[29,26],[28,25],[28,26],[29,25],[28,26],[28,25],[29,26],[28,25],[28,26],[29,25],[28,25],[27,25],[52,35],[55,38],[37,26],[53,36],[54,37],[53,36],[53,36],[53,37],[54,36],[53,37],[53,36],[53,37],[54,36],[53,37],[53,36],[53,37],[54,36],[53,37],[53,36],[32,22],[13,12],[3,6]],[[1917,5909],[15,-4],[1126,-348]],[[979,3468],[-43,43],[-16,7],[-336,-2],[-217,196],[-217,196],[-7,20],[-7,19],[-6,20],[-7,20],[-7,20],[-7,20],[-6,20],[-7,20],[-7,20],[-7,20],[-7,20],[-7,20],[-6,20],[-7,20],[-7,19],[-7,20],[-10,29],[-13,32],[-18,43],[0,37],[31,28],[25,8],[34,9],[38,2],[23,3]],[[6217,5208],[0,-1018]],[[6217,4190],[0,-91],[214,-69],[9,-5],[4,-4],[-7,-135],[5,-14],[8,-13],[34,-34],[2,-6],[-1,-100],[22,-112],[-1,-66],[-3,-7],[-6,-9]],[[6497,3525],[-25,-26],[-7,-4],[-5,-2],[-10,-2],[-244,-32],[-7,-3],[2,-3],[7,-4]],[[6208,3449],[-270,53],[-223,-64],[-288,-111],[-52,-15],[-15,-2],[-14,0],[-578,14],[-17,-1],[-19,-3],[-341,-122],[-28,-12],[-44,-35],[-12,-7],[-13,-3],[-107,-6],[-16,-3],[-17,-4],[-248,-138],[-18,-17],[14,-34]],[[3495,3557],[61,57],[6,10],[2,13],[-7,143],[1,7],[30,116],[3,8],[5,7],[11,4],[286,58],[419,1123]],[[4312,5103],[88,-53],[118,-46],[58,-16],[199,-30],[78,0],[28,4],[189,38],[96,28],[41,16],[106,58],[51,38],[28,35],[73,131],[2,8],[9,75],[-4,63],[2,3],[4,2],[6,0],[10,-2],[15,-8],[6,-3],[10,0],[18,2],[6,0],[12,-1],[32,-6],[7,-2],[7,-3],[12,-7],[6,-4],[3,-4],[5,-14],[13,-58],[1,-4],[2,-3],[8,-8],[13,-9],[125,-51],[16,-4],[75,-8],[115,-23],[37,-11],[50,-9],[129,-9]],[[9178,5160],[-23,-2],[-25,-11],[-61,-47],[-75,-29],[-18,-14],[-7,-20],[7,-18],[16,-16],[20,-14],[21,-18],[9,-19],[2,-21],[-12,-73],[-7,-14],[-43,-50],[-7,-6],[-32,-8],[-22,-7],[-19,-10],[-85,-58],[-11,-13],[-5,-15],[-1,-12],[-5,-10],[-19,-11],[-35,-15],[-8,-10],[0,-16],[8,-34],[5,-11],[15,-20],[1,-6],[-4,-9],[-10,-4],[-191,-33],[-40,-12],[-39,-19],[-17,-22],[21,-24],[30,-14],[7,-8],[13,-67],[3,-4],[5,-3],[4,-4],[-1,-5],[-5,-3],[-13,-4],[-4,-2],[-9,-11],[-1,-5],[3,-7],[9,-14],[11,-9],[16,-6],[54,-6],[16,-6],[9,-13],[6,-20],[-1,-18],[-14,-11],[-22,-5],[-25,-3],[-22,-8],[-35,-21],[-19,-9],[-150,-46],[-28,-11],[-22,-15],[-10,-8]],[[8287,4043],[-26,-2],[-15,0],[-12,2],[-11,7],[-23,23],[-4,3],[-40,16],[-323,70],[-253,25],[-13,1],[-57,-3],[-27,-3],[-44,-15],[-8,-3],[-16,-7],[-9,-2],[-19,-4],[-10,-1],[-18,1],[-33,5],[-92,30],[-67,30],[-4,0],[-6,0],[-11,-1],[-9,2],[-6,1],[-4,2],[-56,38],[-23,20],[-20,30],[-3,2],[-3,3],[-3,2],[-28,6],[-7,2],[-8,4],[-14,9],[-4,1],[-5,2],[-21,4],[-5,2],[-7,4],[-4,1],[-168,36],[-7,1],[-9,0],[-43,-3],[-5,-1],[-4,-1],[-44,-15],[-6,-1],[-6,0],[-11,1],[-34,5],[-5,0],[-4,-1],[-9,-3],[-4,-1],[-5,0],[-4,1],[-5,2],[-5,1],[-11,1],[-6,1],[-17,-1],[-7,-1],[-6,-2],[-7,-6],[-6,-8],[-4,-3],[-4,-2],[-23,-4],[-4,-2],[-3,-4],[-5,-9],[-15,-6],[-53,-28],[-150,-107]],[[6217,5208],[77,-6],[31,-6],[19,-7],[47,-22],[90,-28],[13,-3],[6,0],[6,1],[46,8],[38,11],[86,7]],[[6676,5163],[8,0],[5,-2],[9,-3],[17,-6],[21,-4],[11,-1],[9,0],[19,4],[9,3],[27,13],[33,12],[164,39],[60,25],[13,9],[12,10],[17,20],[3,2],[7,5],[7,4],[18,6],[55,10],[17,6],[23,13],[13,5],[5,1],[6,0],[8,-3],[68,-28],[105,-28],[11,-2],[7,0],[7,1],[55,20],[11,2],[241,32],[49,10],[35,5],[10,1],[14,-1],[5,-1],[78,-17],[44,-13],[17,-3],[7,0],[6,0],[43,12],[22,9],[26,5],[40,3],[31,-1],[24,-2],[24,-5],[6,1],[4,1],[12,8],[6,3],[4,0],[2,0],[2,-1],[26,-16],[38,-20],[9,-3],[12,-3],[8,-1],[14,-1],[9,0],[8,0],[5,1],[14,3],[5,2],[10,4],[8,3],[6,0],[8,0],[5,-1],[4,-2],[7,-4],[4,-2],[4,-1],[21,-5],[12,-1],[37,-9],[4,-2],[4,-4],[5,-10],[3,-3],[3,-2],[3,-2],[4,-1],[7,-2],[5,-1],[3,-2],[2,-3],[3,-6],[3,-3],[4,-2],[4,-2],[4,-2],[2,-3],[2,-3],[2,-3],[3,-2],[6,-2],[10,-2],[6,1],[6,1],[13,4],[5,1],[10,1],[29,1],[34,4],[20,0],[27,-1],[8,0],[18,4],[12,2],[8,0],[6,-1],[5,-1],[19,-10],[4,-1],[6,-2],[24,-2],[4,-1],[6,-2],[7,-2],[13,-3],[9,0],[7,0],[11,1],[20,4],[19,6],[7,1],[13,2],[9,1],[7,0],[22,-3],[57,-14],[5,-2],[4,-2],[3,-2],[46,-47],[1,-2]],[[6208,3449],[139,-69],[5,-1],[4,0],[9,2],[4,0],[4,-1],[3,-2],[3,-2],[3,-3],[1,-5],[-1,-9],[1,-14],[-1,-5],[-4,-5],[-10,-8],[-7,-2],[-7,-1],[-5,1],[-5,0],[-5,0],[-5,-3],[-64,-55],[-6,-7],[-1,-5],[2,-4],[6,-5],[30,-19],[5,-5],[2,-3],[0,-2],[-1,-3],[-4,-4],[-15,-7],[-15,-4],[-4,-1],[-4,-2],[-6,-5],[-7,-4],[-23,-23],[-35,-22],[-22,-21],[-7,-5],[-6,-4],[-4,-1],[-4,-2],[-4,-2],[-5,-5],[-5,-5],[-28,-40],[-61,-113],[-2,-12],[1,-36]],[[6047,2896],[-169,7],[-196,34],[-44,4],[-162,-99],[66,-70],[5,-10],[-2,-8],[-5,-9],[-91,-107],[-14,-8],[-13,-6],[-84,-20],[-11,-2],[-9,0],[-6,6],[-26,42],[-6,6],[-9,3],[-6,0],[-104,-1],[-13,-2],[-5,-6],[-3,-8],[-18,-182],[1,-6],[2,-3],[5,-6],[2,-4],[1,-5],[3,-3],[4,-2],[19,-2],[5,-1],[3,-2],[3,-3],[2,-3],[3,-2],[4,-2],[5,-1],[11,0],[5,-1],[4,-2],[2,-3],[4,-6],[2,-3],[4,-2],[4,-1],[5,-2],[5,-1],[10,-2],[4,-2],[15,-8],[38,-15],[4,-1],[18,-2],[5,-2],[3,-2],[3,-2],[2,-3],[3,-2],[5,-2],[16,-2],[5,-1],[13,-5],[19,-10],[4,-1],[6,-1],[17,0],[6,-1],[4,-1],[4,-2],[3,-3],[4,-2],[9,-2],[16,-4],[12,-5],[5,-1],[5,-1],[18,-2],[5,-1],[4,-2],[4,-2],[9,-7],[4,-2],[36,-12],[5,-1],[5,0],[16,2],[5,0],[6,0],[5,-1],[4,-2],[3,-2],[3,-3],[10,-11],[3,-2],[4,-3],[4,-1],[5,-2],[11,-1],[12,-1],[5,0],[16,3],[5,1],[5,0],[6,-1],[4,-2],[4,-2],[9,-6],[4,-2],[5,-2],[4,-1],[5,-1],[3,1],[8,1],[6,0],[5,0],[5,-1],[9,-3],[5,-1],[8,-3],[4,-3],[4,-1],[4,-2],[6,-1],[11,-1],[5,-1],[5,-2],[4,-1],[6,-5],[1,0],[9,-3],[11,-1],[5,-2],[4,-1],[3,-3],[14,-12],[6,-8],[4,-5],[4,-3],[5,-2],[6,0],[5,-2],[6,-2],[4,-5],[0,-3],[-3,-2],[-15,-7],[-7,-4],[-1,-12],[11,-108],[-18,-54]],[[5893,1978],[-17,-9],[-25,-5],[-4,-3],[0,-3],[1,-3],[-3,-4],[-8,-3],[-4,-3],[-4,-3],[-6,-10],[-4,-2],[-4,-1],[-3,2],[-3,3],[-4,2],[-7,1],[-3,-2],[0,-3],[1,-3],[0,-4],[-21,-24],[-6,-1],[-6,-1],[-7,0],[-24,-4],[-9,0],[-14,-2],[-7,0],[-4,3],[0,3],[1,2],[-1,3],[-1,0],[-2,1],[-7,0],[-6,-1],[-8,-4],[-7,-2],[-9,-2],[-8,-2],[-8,-4],[-8,-2],[-6,-3],[-16,-10],[-10,-9],[-4,-5],[-10,-7],[-3,-5],[-1,-4],[3,-6],[0,-4],[-4,-2],[-6,0],[-4,-2],[-2,-3],[-1,-2],[1,-4],[0,-3],[-2,-4],[-5,-2],[-5,-1],[-12,1],[-8,-1],[-3,-2],[-1,-3],[3,-2],[4,-2],[4,-2],[2,-3],[0,-3],[-3,-4],[-1,-3],[1,-2],[8,-4],[3,-3],[-3,-2],[-5,-1],[-5,0],[-6,-1],[-2,-3],[-1,-4],[-4,-3],[-7,-3],[-12,-4],[-6,-4],[-4,-4],[-4,-4],[-5,-7],[-7,-5],[-3,-5],[-4,-3],[-6,-1],[-7,-1],[-8,-3],[-5,-3],[-3,-3],[-17,-20],[-4,-10],[-4,-8],[-3,-1],[-3,1],[-3,1],[-5,0],[-8,-2],[-9,-6],[-6,-2],[-5,0],[-4,0],[-3,-2],[-3,-2],[-4,-6],[-6,-7],[-6,-5],[-12,-12],[-15,-12],[-14,-8],[-11,-6],[-37,-12],[-52,-5],[-130,-1],[-11,-2],[-6,-7],[-21,-57],[-4,-5],[-5,-2],[-27,-3],[-19,-5],[-11,-1],[-7,0],[-11,6],[-19,13],[-5,2],[-5,1],[-7,0],[-11,0],[-7,0],[-6,1],[-9,3],[-3,2],[-2,1],[0,1],[9,54],[0,6],[-1,3],[-2,3],[-3,2],[-4,2],[-10,2],[-5,2],[-3,2],[-3,3],[-2,6],[-2,3],[-3,3],[-12,5],[-4,2],[-20,13],[-4,1],[-5,1],[-6,1],[-5,1],[-4,2],[-6,4],[-12,14],[-7,9],[-3,2],[-7,4],[-6,4],[-7,9],[-3,2],[-3,2],[-4,2],[-52,9],[-9,3],[-7,4],[-7,4],[-11,10],[-5,2],[-6,1],[-10,1],[-20,3],[-39,1],[-6,1],[-3,2],[-4,2],[-4,3],[-19,6],[-4,1],[-3,3],[-3,6],[-3,3],[-6,1],[-19,1],[-5,1],[-7,4],[-4,3],[-8,1],[-6,1],[-4,2],[-2,3],[-1,7],[-2,4],[-2,2],[-4,3],[-6,1],[-8,2],[-5,-1],[-4,-2],[-4,-3],[-5,-2],[-5,-1],[-4,2],[-6,5],[-6,2],[-19,2],[-7,1],[-5,2],[-5,-1],[-8,-5],[-4,0],[-4,1],[-4,2],[-5,2],[-8,0],[-22,5],[-15,2],[-5,1],[-13,5],[-9,2],[-11,2],[-6,1],[-6,3],[-80,49],[-6,3],[-7,3],[-278,-7]],[[3877,1881],[-226,76],[19,102],[-19,140],[-39,166],[10,160]],[[8287,4043],[-77,-63],[-23,-25],[-1,-5],[22,-14],[15,-12],[3,-9],[-2,-10],[0,-14],[17,-24],[61,-37],[13,-24],[1,-36],[4,-12],[13,-15],[33,-26],[12,-14],[-8,-26],[-34,-29],[-88,-60],[-15,-7],[-102,-26],[-17,-6],[-33,-20],[-27,-25],[-78,-96],[-24,-46],[-15,-20],[-2,-4]],[[7935,3338],[-107,24],[-116,31],[-87,-19],[-97,-38],[-88,-57],[-48,-13],[-39,6],[-97,7],[-106,-20],[-126,32],[0,58],[-10,44],[-78,7],[-67,13],[-10,83],[-146,0],[-106,0],[-110,29]],[[2834,1046],[5,-7],[10,-9],[3,-4],[6,-17],[3,-5],[4,-3],[3,-2],[18,-20],[21,-15],[3,-3],[3,-5],[2,-3],[5,0],[12,1],[4,-1],[3,-4],[1,-10],[2,-3],[5,-2],[6,-1],[5,0],[4,-1],[13,-10],[8,-8],[4,-9],[1,-14],[-80,-80],[-21,-12],[-35,-9],[-21,-3],[-21,-5],[-17,-8],[-12,-22],[-14,-6],[-73,-5],[-135,-27],[-44,-23],[-73,-27],[-10,-2],[-7,-2],[-23,-15],[-83,-30],[-23,-16],[-10,-23],[1,-3],[-1,-3],[-4,-4],[-33,-26],[-5,-2],[-7,-1],[-15,0],[-51,6],[-8,-1],[-9,-3],[-19,-4],[-4,-2],[-3,-2],[-11,-10],[-10,-6],[-4,-2],[-5,-1],[-18,0],[-5,-1],[-4,-1],[-8,-3],[-4,-2],[-4,-1],[-36,-2],[-5,0],[-5,1],[-3,4],[-1,6],[-3,2],[-8,0],[-13,-1],[-6,0],[-6,2],[-4,2],[-2,4],[-1,4],[1,4],[4,6],[-2,3],[-5,3],[-12,4],[-9,5],[-4,4],[-6,3],[-8,3],[-17,2],[-23,0],[-11,3],[-67,30],[-34,10],[-38,15]],[[1731,621],[-2,7],[-5,10],[-1,3],[-1,8],[1,5],[39,107],[4,4],[7,6],[53,33],[2,3],[2,4],[-1,11],[2,14],[0,4],[-1,3],[-5,10],[-1,7],[2,21],[4,12],[38,76],[10,5],[34,9]],[[1912,983],[16,-9],[8,-3],[19,-6],[9,-2],[11,-2],[13,-1],[25,0],[24,2],[152,24],[36,-4],[23,0],[11,2],[141,48],[22,5],[20,2],[11,1],[8,-1],[94,-18],[5,0],[7,2],[8,4],[12,9],[13,16],[8,7],[7,4],[7,3],[36,10],[21,5],[21,3],[10,2],[6,3],[8,4],[27,19],[9,3],[13,-1],[8,-7],[39,-28],[12,-18],[1,-10],[1,-5]],[[3470,1009],[18,-40],[2,-6],[-8,-132],[-7,-15],[-8,-11],[-51,-51],[-14,-18],[2,-8],[3,-5],[200,-201],[0,-4],[-3,-2],[-4,-1],[-4,-2],[-3,-2],[-2,-4],[-2,-11],[-2,-4],[-2,-2],[-3,-3],[-4,-2],[-6,-4],[-6,-5],[-8,-12],[-3,-2],[-11,-6],[-3,-3],[-7,-9],[-2,-2],[-4,-2],[-4,-2],[-5,-1],[-5,0],[-12,1],[-6,0],[-6,0],[-15,-3],[-17,-1],[-5,-1],[-4,-3],[-3,-4],[1,-3],[3,-2],[3,-3],[3,-2],[136,-194]],[[3592,222],[-12,-8],[-6,-7],[-4,-11],[-6,-3],[-8,1],[-7,-1],[-17,-10],[-33,-27],[-19,-11],[-13,-6],[-6,0],[-5,4],[-11,4],[-19,4],[-13,-8],[-4,-5],[1,-6],[-3,-3],[-29,4],[-10,-1],[-8,-4],[-7,-7],[-18,1],[-41,-4],[-37,-7],[-15,-5],[-10,-8],[-7,-13],[1,-4],[4,-3],[3,-3],[-2,-5],[-3,-1],[-10,1],[-3,0],[-20,-11],[-3,-1],[-6,-3],[-22,-1],[-14,5],[-20,23],[-35,26],[-22,11],[-23,7],[-36,7],[-10,7],[-4,14],[2,8],[4,6],[2,6],[-5,9],[-8,6],[-8,2],[-9,2],[-10,2],[-15,8],[-11,9],[-4,12],[5,19],[1,5],[-1,6],[-1,5],[-38,-7],[-16,-6],[-16,-8],[-5,-6],[-12,-17],[-6,-5],[-13,-2],[-10,2],[-8,3],[-10,0],[-15,-8],[-5,-13],[0,-42],[-3,-9],[-12,-5],[-84,-8],[-19,-5],[-33,-17],[-19,-7],[-21,-2],[-22,0],[-32,-7],[-60,-5],[-21,-5],[-16,-5],[-13,-9],[-21,-20],[-14,-9],[-31,-12],[-17,-5],[-125,-18],[-33,-2],[-37,8],[-49,30],[-41,2],[-69,-6],[-35,1],[-33,7],[39,33],[26,35],[12,37],[1,64],[-21,2],[-30,5],[-20,0],[-45,22],[-32,51],[-58,135],[-128,178]],[[1672,604],[16,5],[43,12]],[[2834,1046],[17,0],[60,8],[26,-1],[24,-2],[42,-8],[38,-10],[13,-4],[50,-8],[29,-7],[160,-26],[25,0],[51,1],[101,20]],[[2629,1714],[9,-6],[13,-14],[2,-3],[2,-6],[-5,-34],[2,-15],[-8,-30],[-22,-43],[-43,-55],[-8,-7],[-175,-108],[-5,0],[-6,3],[-47,27],[-23,6],[-32,1],[-10,2],[-13,-3],[-108,-85],[-8,-9],[-3,-8],[-27,-49],[-2,-2],[-4,-3],[-68,-45],[-21,-18],[-7,-11],[-9,-8],[-5,-4],[-10,-4],[-4,-2],[-7,-7],[-8,-4],[-8,-8],[-11,-15],[-2,-2],[-24,-29],[-7,-4],[-7,-6],[-15,-18],[-7,-5]],[[1888,1083],[-61,16],[-71,20],[-75,30],[-86,46],[-38,17],[-18,84],[-46,40],[4,31],[-30,19],[-51,15],[-48,-8],[-93,-13],[-52,2],[-14,76],[9,86]],[[1218,1544],[38,-2],[119,24],[44,-1],[55,-8],[29,-2],[27,4],[10,5],[17,10],[10,4],[9,2],[43,3],[57,-2],[159,-33],[54,0],[213,35],[16,3],[-41,24],[-33,7],[-63,25],[-44,24],[1,3],[-14,5],[-35,33],[-131,79],[-10,11],[-6,13],[-2,15],[-4,13],[-9,7],[-12,7],[-14,10],[-13,23],[-9,11],[-19,6],[-5,3],[-6,3],[-10,1],[-11,1],[-10,1],[-9,3],[-6,3],[-6,12],[0,16],[6,15],[10,11],[1,10],[-7,15],[-73,91],[-2,24],[12,26],[3,16],[-6,11],[-10,9],[-26,40],[-6,54]],[[1499,2267],[84,14],[19,5],[13,4],[31,15],[20,13],[8,4],[3,2],[6,4],[4,2],[4,2],[6,1],[6,0],[50,-3],[44,-7],[10,-3],[17,-6],[33,-16],[7,-2],[8,-2],[10,-5],[0,-31],[6,-10],[14,-9],[17,-8],[15,-5],[16,-2],[10,-1],[5,-4],[1,-12],[4,-8],[8,-12],[15,-17],[10,-5],[14,-6],[16,-4],[16,-2],[23,0],[16,-1],[15,-5],[20,-10],[57,-38],[18,-6],[36,-2],[76,-12],[37,-3],[22,2],[36,6],[17,1],[50,-22],[9,-2],[11,10],[25,-3],[31,-10],[4,-5],[1,-3],[-29,-180],[17,-82],[3,-8],[5,-5],[11,-6],[6,-3],[15,-5],[4,-2],[4,-3],[4,-5],[5,-9],[3,-11],[6,-7],[22,-10]],[[3683,1476],[10,0],[7,-1],[5,-2],[71,-23],[90,-43],[20,-13],[14,-13],[3,-6],[-2,-8],[-19,-31],[-3,-3]],[[3879,1333],[-2,1],[-9,6],[-4,2],[-4,2],[-10,2],[-7,1],[-9,2],[-9,3],[-4,2],[-3,2],[-5,5],[-3,2],[-4,3],[-4,1],[-5,1],[-6,0],[-5,0],[-10,-2],[-24,-9],[-2,-1],[-4,-1],[-11,0],[-18,0],[-5,0],[-6,-1],[-6,-2],[-13,-6],[-26,-5],[-5,0],[-44,1],[-9,-2],[-11,-4],[-19,-10],[-9,-5],[-5,-4],[-1,-4],[0,-3],[1,-3],[8,-4],[3,-2],[2,-3],[2,-8],[2,-2],[1,-5],[0,-7],[-8,-16],[-3,-12],[0,-10],[4,-17],[1,-8],[-2,-6],[-7,-5],[-124,-64],[-8,-7],[5,-7],[10,-7],[89,-43],[9,-6],[3,-4],[-5,-6],[-71,-49]],[[1912,983],[-11,14],[-2,11],[-1,33],[3,14],[-1,5],[-5,15],[-2,3],[-1,2],[-4,1],[-1,1],[1,1]],[[2629,1714],[210,-2],[57,7],[59,29],[5,1],[4,-2],[4,-2],[2,-2],[2,-3],[3,-3],[3,-2],[11,-6],[3,-2],[3,-3],[2,-3],[1,-3],[1,-16],[-2,-16],[1,-4],[2,-3],[10,-6],[2,-3],[5,-14],[2,-2],[3,-3],[3,-2],[7,-4],[64,-21],[6,-2],[4,-3],[3,-4],[5,-3],[9,-4],[7,-2],[6,0],[15,1],[25,-2],[8,-2],[17,-7],[8,-2],[19,-3],[28,0],[3,-1],[2,-2],[5,-5],[8,-4],[12,-5],[4,-2],[7,-12],[7,-5],[6,-2],[7,-1],[48,2],[5,1],[28,-2],[34,-5],[17,-3],[10,-3],[2,-3],[4,-2],[4,1],[11,2],[8,0],[6,-1],[4,-1],[3,-3],[12,-17],[3,-3],[2,-2],[8,-4],[33,-11],[18,-7],[9,-5],[7,-2],[6,0],[14,1],[13,-1],[14,-2],[13,-4],[9,-2],[7,-1],[14,1]],[[3879,1333],[41,-18],[26,-26],[17,-40],[84,2],[20,-16],[20,-24],[26,-40],[12,-23],[55,-11],[72,-25],[63,-21],[78,-2],[61,-6],[22,-17],[-11,-7],[-7,-2],[-4,-19],[-20,-24],[-28,-22],[-26,-13],[-16,-1],[-13,1],[-10,-3],[-18,-11],[-14,6],[-5,5],[-6,1],[-10,-7],[-12,-66],[5,-4],[2,-2],[-1,-4],[-7,-4],[-6,-1],[-4,-6],[-5,-5],[3,-6],[15,-5],[34,-11],[21,-13],[8,-21],[-8,-18],[-7,-36],[5,-39],[18,-28],[35,-15],[19,-20],[2,-33],[13,-27],[23,-29],[5,-25],[-3,-43],[-3,-35],[14,-39],[14,-44],[18,-37]],[[4486,354],[-100,-2],[-88,-13],[-76,-19],[-37,-4],[-39,1],[-131,14],[-17,1],[-21,-1],[-18,-4],[-19,-18],[-19,-6],[-20,-3],[-34,-3],[-19,-3],[-15,-7],[-9,-14],[-11,-5],[-45,9],[-13,-10],[-19,-8],[-128,-29],[-15,-7],[-1,-1]],[[6047,2896],[13,-15],[4,-9],[6,-34],[3,-6],[5,-5],[5,-1],[9,0],[25,1],[43,-1],[7,0],[5,1],[2,3],[0,4],[-9,35],[0,8],[1,4],[4,3],[10,2],[248,5],[27,-4],[21,-5],[236,-103],[482,-156],[221,-33],[531,63],[17,0],[14,-3],[37,-14],[145,-29],[21,-9],[14,-9],[267,-401],[1,0]],[[8462,2188],[-4,0],[-20,-4],[-20,-5],[-12,-5],[-18,-10],[-10,-5],[-8,1],[-14,4],[-13,-6],[-10,-9],[-27,-15],[-10,-19],[-13,-13],[-27,9],[-15,-2],[-79,6],[-18,-6],[-11,-11],[-7,-13],[-6,-9],[9,-13],[-10,-7],[-57,-9],[-17,-5],[-29,-14],[-22,-13],[-3,-1],[-5,-5],[-10,1],[-19,6],[-10,0],[-18,-4],[-8,0],[-10,-2],[-5,-5],[-5,-4],[-5,-2],[-14,-1],[-17,-3],[-13,-6],[-5,-9],[-2,-13],[-9,-18],[-2,-12],[2,-13],[9,-24],[2,-10],[3,-6],[17,-13],[6,-5],[0,-4],[-2,-5],[1,-4],[7,-4],[-29,-32],[-3,-13],[1,-7],[4,-9],[1,-5],[-4,-6],[-10,-2],[-11,-1],[-8,-2],[-11,-12],[-8,-13],[-12,-10],[-24,-4],[-5,-1],[-30,-7],[-4,2],[-10,-3],[-12,1],[-11,3],[-9,1],[-30,-12],[-27,-23],[-49,-74],[-25,-25],[-31,-21],[-33,-8],[-13,2],[-39,10],[-13,5],[-14,-7],[-48,-37],[-7,-8],[-3,-10],[-8,-2],[-52,-22],[-10,-11],[-14,-23],[-1,-2],[1,-9],[-2,-2],[-3,0],[-4,1],[-4,-1],[-12,-7],[-7,-3],[-7,-2],[-3,1],[-9,6],[-5,1],[-5,0],[-2,-2],[-1,-2],[-2,0],[-5,-2],[-6,-3],[-6,-3],[-2,-3],[-3,-12],[-13,-17],[-3,-10],[-1,-21],[-6,-8],[-13,-3],[0,-4],[4,-2],[1,-2],[1,-9],[-14,5],[-5,-3],[-2,-5],[-4,-6],[-21,-7],[-9,-5],[-3,-7],[-8,-9],[-37,-26],[-10,-5],[-21,-6],[-7,-14],[-1,-16],[-7,-12],[-5,-1],[-6,2],[-1,0],[-6,3],[-4,1],[-4,-1],[-6,-6],[-3,-2],[-12,-2],[-5,-6],[-4,-7],[-8,-6],[-7,4],[-9,-4],[-43,-9],[-39,-13],[12,-25],[-48,0],[-7,0],[-16,4],[0,-1],[-18,1],[-1,0],[-21,7],[-8,1],[-4,-6],[-1,-8],[-3,-6],[-7,-4],[-12,-1],[-17,-7],[6,-14],[10,-15],[-5,-7],[-24,-5],[-14,-13],[-11,-16],[-20,-13],[-11,3],[-10,0],[-9,-3],[-18,-9],[-6,-2],[-17,-2],[-79,2],[-19,-2],[-21,11],[-11,5],[-10,1],[-14,-2],[-27,-13],[-14,-2],[-16,3],[-11,7],[-8,8],[-8,3],[-8,-2],[-1,-4],[1,-4],[-1,-2],[-9,0],[-13,3],[-7,1],[-8,-2],[-28,-15],[-3,-4],[2,-3],[0,-3],[-6,-3],[-6,1],[-3,9],[-7,3],[-4,-1],[-3,-3],[-2,-3],[-1,-4],[-3,-5],[-7,-1],[-9,0],[-7,0],[-24,-11],[-11,-2]],[[6153,1005],[0,1],[-267,540],[-71,86],[-4,6],[2,5],[3,3],[3,2],[4,2],[16,7],[3,2],[1,4],[-2,6],[-1,4],[1,4],[6,5],[4,3],[2,4],[-2,5],[1,5],[4,9],[3,3],[2,2],[7,5],[3,3],[3,4],[3,7],[0,7],[-2,10],[-10,16],[-12,9],[-10,42],[50,162]],[[1510,2877],[1,7],[3,7],[5,4],[-18,10],[-18,5]],[[1483,2910],[2,16],[7,13],[14,12],[13,4],[23,-1],[14,-7],[18,-9],[11,-12],[6,-13],[-3,-14],[-17,-13],[-15,-6],[-19,-2],[-27,-1]],[[1672,604],[-26,36],[-14,12],[-55,36],[-40,43],[-16,12],[-19,9],[-25,5],[-35,4],[-13,2],[-3,4],[0,6],[-2,8],[-9,16],[-5,6],[-7,3],[-7,2],[-6,3],[-5,8],[-9,6],[-37,18],[-13,5],[-9,1],[-8,0],[-9,-1],[-9,-2],[-15,3],[-211,111],[-210,111],[-18,20],[-9,27],[-271,204],[-84,43],[-2,9],[58,54],[78,44],[29,27],[47,60],[2,6],[0,6],[2,5],[8,2],[14,0],[232,10],[277,-44]],[[7065,8483],[-844,-1089],[141,-371],[258,-448],[0,-216],[-23,-278],[-94,-263],[234,-433],[85,-139],[-146,-83]],[[4312,5103],[-79,58],[-43,43],[-8,5],[-12,5],[-1112,343]],[[1917,5909],[3,8],[2,15],[4,29],[4,29],[3,28],[4,29]],[[1937,6047],[742,860],[518,601],[515,597],[47,170],[281,0],[117,16],[118,77],[187,62],[352,77],[211,46],[24,140],[-24,154],[-23,155],[188,92]],[[5190,9094],[117,-39],[117,-38],[21,-6],[96,-32],[118,-38],[117,-38],[117,-38],[117,-38],[117,-39],[118,-38],[117,-38],[117,-38],[117,-38],[118,-38],[117,-38],[117,-39],[117,-38]],[[1937,6047],[4,28],[4,29],[4,28],[3,29],[4,28],[4,29],[4,28],[4,29],[4,28],[4,29],[3,28],[4,29],[4,29],[4,28],[4,29],[3,28],[4,29],[4,28],[4,29],[1,5],[3,23],[4,29],[3,28],[4,29],[4,28],[4,29],[4,28],[4,29],[3,28],[4,29],[4,29],[4,28],[4,29],[4,28],[3,29],[4,28],[4,29],[4,28],[4,29],[3,28],[4,29],[4,28],[4,29],[4,28],[4,29],[4,28],[3,29],[4,29],[4,28],[4,29],[4,28],[3,29],[4,28],[4,29],[4,28],[4,29],[4,28],[3,29],[4,28],[4,29],[4,28],[3,26],[30,50],[14,16],[38,43],[38,43],[38,43],[38,43],[20,22],[7,11],[-2,10],[-14,14],[-22,16],[-55,38],[-56,38],[-55,38],[-55,38],[-27,18],[-77,38],[-17,12],[-13,14],[-12,15],[-7,29],[7,29],[24,24],[38,13],[-16,15],[-15,16],[-16,15],[-16,15],[-16,16],[-16,15],[-16,15],[-16,16],[-16,15],[-16,15],[-15,16],[-16,15],[-16,15],[-16,16],[-16,15],[-16,15],[-16,15],[-22,22],[-33,25],[-18,8],[-45,14],[-15,9],[-4,10],[0,17],[0,12],[-1,33],[-2,46],[-1,53],[-1,52],[-2,46],[0,33],[-1,12],[-7,26],[-8,26],[-8,27],[-11,39],[-11,39],[-12,39],[-11,39],[-12,40],[-11,39],[-11,39],[-12,39],[-11,39],[-12,40],[-11,39],[-11,39],[-12,39],[-11,39],[16,5],[55,17],[55,16],[55,16],[54,17],[55,16],[55,16],[55,17],[54,16],[55,16],[55,17],[55,16],[54,16],[55,17],[55,16],[55,16],[54,17],[42,12],[20,2],[86,-28],[118,-38],[117,-38],[117,-39],[117,-38],[117,-38],[118,-38],[117,-38],[117,-38],[117,-38],[118,-39],[117,-38],[117,-38],[117,-38],[118,-38],[117,-38],[117,-38],[117,-39],[118,-38],[117,-38],[117,-38],[117,-38],[117,-38],[118,-38]],[[7065,8483],[118,-38],[117,-38],[117,-38],[117,-38],[117,-38],[118,-39],[117,-38],[117,-38],[117,-38],[118,-38],[117,-38],[117,-38],[117,-39],[118,-38],[117,-38],[117,-38],[117,-38],[118,-38],[117,-38],[117,-39],[117,-38],[117,-38],[118,-38],[117,-38],[117,-38],[0,-146],[0,-145],[1,-145],[0,-145],[0,-146],[0,-145],[0,-145],[1,-145],[0,-146],[0,-145],[0,-145],[0,-145],[0,-146],[1,-145],[0,-145],[0,-145],[0,-37],[-11,-19],[-26,1],[-110,24],[-116,11],[-109,0],[-62,-8],[-125,-30],[-71,-4],[-146,20],[-45,-4]],[[3877,1881],[-194,-405]],[[1499,2267],[-7,64],[7,38],[5,10],[21,21],[7,10],[-1,7],[-4,3],[-5,3],[-3,4],[0,8],[4,9],[6,33],[6,12],[9,11],[44,31],[13,18],[-12,20],[-26,22],[-10,11],[-15,28],[-3,12],[2,12],[7,14],[4,0],[6,0],[6,1],[4,3],[-1,4],[-10,6],[-2,2],[5,6],[16,10],[5,6],[-23,36],[-6,7],[-17,1],[-11,5],[-6,10],[-2,16],[4,5],[10,5],[6,5],[-4,6],[-3,4],[-5,6],[-2,7],[4,2],[27,3],[-4,7],[-16,9],[-10,9],[1,8],[3,5],[-1,4],[-7,5],[-5,6]],[[1483,2910],[-17,-1],[-16,-8],[-12,-1],[-13,7],[-21,15],[-24,11],[-5,4],[-3,10],[5,2],[7,1],[4,5],[-1,6],[-4,3],[-5,3],[-3,5],[0,7],[5,11],[2,7]],[[7935,3338],[4,-7],[26,-21],[37,-45],[19,-13],[34,-10],[39,-5],[40,0],[37,3],[45,9],[36,13],[31,19],[27,26],[119,-51],[97,-24],[13,-8],[-48,-72],[-22,-20],[2,-8],[32,-32],[70,-140],[2,-7],[1,-8],[-1,-7],[-25,-69],[6,2],[6,2],[11,5],[61,12],[37,5],[13,-6],[8,10],[5,-4],[1,-9],[-3,-11],[-17,-32],[-53,-193],[5,-30],[19,-29],[29,-26],[34,-22],[32,-10],[77,-17],[24,-15],[3,-5],[0,-19],[10,-3],[90,6],[22,0],[14,-8],[13,-44],[24,-47],[1,-8],[1,-18],[-4,-11],[-16,-22],[-6,-11],[-3,-13],[0,-12],[-3,-7],[-15,-11],[-6,-6],[-37,-88],[-52,3],[-58,12],[-24,13],[-35,6],[-9,1],[-34,-1],[-9,2],[-18,5],[-8,2],[-23,-2],[-20,-4],[-20,1],[-22,13],[-8,-5],[-12,0],[-13,3],[-15,2],[-13,-2],[-9,-4],[-18,-13],[-11,-5],[-15,-2],[-25,-3]],[[6153,1005],[-3,0],[-14,4],[-11,-11],[-13,-3],[-79,3],[-20,-2],[-25,-4],[-11,0],[-8,-2],[-23,-6],[-5,-3],[-8,-2],[-42,0],[-15,-4],[-25,11],[-29,-4],[-28,-8],[-22,1],[-9,-4],[-5,1],[-4,2],[-5,1],[-72,-1],[-9,3],[-37,-9],[-12,-2],[-16,1],[-18,3],[-17,4],[-10,5],[-9,-7],[-9,2],[-10,6],[-12,4],[-9,-1],[-20,-3],[-38,-2],[-12,-3],[-5,-7],[-6,-1],[-25,4],[-8,-1],[-6,0],[-25,5],[-38,-7],[-37,-11],[-21,-10],[-14,-8],[-6,-5],[-3,-4],[-3,-3],[-19,-4],[-15,-5],[-7,-1],[-6,-2],[-2,-7],[-3,-5],[-7,-3],[-8,0],[-9,2],[-21,-21],[0,-9],[16,-9],[15,6],[7,-6],[2,-11],[5,-8],[11,-5],[114,-32],[8,-10],[15,-6],[29,-14],[19,-14],[-31,-14],[-9,-10],[-9,-3],[-11,-4],[-38,-50],[-95,-71],[-10,-12],[-8,-15],[-14,-12],[-30,-19],[-40,-40],[-21,-12],[-16,-5],[-56,-13],[-40,-13],[-33,-18],[-19,-25],[0,-30],[-28,-27],[-76,-10],[-316,-6]]],
transform:{scale:[.001053627620262024,.001599075231523149],translate:[13.449183797000103,7.455566711000117]}},m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();