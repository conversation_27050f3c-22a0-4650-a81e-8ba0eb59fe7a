!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo={type:"Topology",objects:{sle:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:"Southern"},id:"SL.SO",arcs:[[[0]],[[1,2,3,4]]]},{type:"Polygon",properties:{name:"Western"},id:"SL.WE",arcs:[[5,-4,6]]},{type:"Polygon",properties:{name:"Northern"},id:"SL.NO",arcs:[[7,-5,-6,8]]},{type:"Polygon",properties:{name:"Eastern"},id:"SL.EA",arcs:[[9,-2,-8]]}]}},arcs:[[[2336,2253],[32,-16],[128,4],[59,-17],[47,-29],[40,-40],[35,-47],[-38,-23],[-3,-37],[20,-95],[-9,-58],[-22,-35],[-30,-32],[-29,-53],[-4,-52],[29,-107],[-3,-41],[-42,-38],[-57,-9],[-54,20],[-30,48],[68,0],[-45,81],[-58,80],[-66,61],[-68,24],[-48,4],[-302,78],[-108,54],[-281,41],[-340,89],[0,24],[124,29],[13,45],[41,41],[49,18],[127,20],[362,0],[55,8],[125,32],[198,16],[69,-5],[65,-26],[-49,-57]],[[5700,5163],[35,-136],[25,-45],[29,-24],[48,-55],[11,-25],[5,-22],[-5,-54],[3,-51],[8,-27],[11,-20],[70,-64],[15,-9],[57,-54],[141,-178],[226,-219],[22,-31],[10,-22],[-1,-16],[-2,-11],[0,-16],[3,-17],[5,-18],[81,-219],[12,-66],[3,-123],[12,-87],[1,-36],[-4,-36],[-18,-69],[-14,-31],[-9,-13],[-10,-12],[-12,-11],[-27,-17],[-40,-43],[-11,-11],[-214,-151],[-9,-13],[-7,-16],[-33,-149],[-2,-80],[3,-27],[6,-17],[9,2],[10,9],[49,63],[10,10],[10,5],[7,-7],[56,-139],[4,-23],[-3,-19],[-7,-15],[-6,-16],[-13,-76],[-12,-31],[-18,-27],[-12,-11],[-14,-9],[-17,-3],[-15,4],[-10,11],[-13,33],[-8,14],[-10,9],[-12,1],[-12,-6],[-107,-75],[-92,-52],[1,-7],[11,-7],[34,-13],[17,-42],[18,-75],[14,-176],[13,-71],[16,-40],[16,-5],[15,-8],[17,-4],[19,0],[16,5],[15,8],[13,10],[73,65],[105,121],[26,18],[31,15],[19,3],[19,-1],[18,-4],[15,-8],[14,-9],[11,-11],[50,-65],[-6,-10],[-76,-79],[-7,-25],[-4,-34],[12,-146],[-2,-20],[0,-69],[9,-76],[-4,-73],[6,-15],[13,-9],[37,-9],[40,-5],[43,-1],[40,-5],[87,-24],[112,-46],[87,-49],[241,-94],[2,0]],[[7144,1384],[-215,-270],[-29,-26],[-273,-121],[-56,-11],[-115,-177],[-60,-54],[-11,-12],[9,-34],[47,-75],[11,-41],[-7,-28],[-17,-18],[-25,-8],[-72,-8],[-11,-22],[12,-65],[-7,-49],[-21,-61],[-31,-54],[-91,-50],[-14,-56],[7,-82],[-26,-15],[-105,-47],[-55,43],[-136,31],[-66,34],[-63,43],[-50,42],[30,5],[15,7],[10,12],[13,21],[-93,2],[-40,-6],[-27,-18],[-21,0],[-30,73],[-46,47],[-176,144],[-468,266],[-776,275],[-798,310],[-635,198],[100,82],[121,4],[124,-38],[109,-48],[-6,28],[-16,21],[-24,13],[-33,5],[-26,7],[-27,39],[-26,20],[-62,24],[-35,3],[-59,-31],[-28,14],[-28,24],[-27,12],[-11,28],[30,61],[42,61],[62,53],[35,50],[44,35],[59,-19],[37,17],[38,5],[83,0],[17,3],[40,14],[23,3],[24,-7],[40,-29],[15,-6],[65,18],[56,33],[64,29],[88,4],[-7,12],[-7,24],[-6,11],[60,18],[61,8],[45,23],[15,64],[-25,0],[-21,-5],[-22,-1],[-23,-4],[-23,-12],[-19,29],[-5,13],[-33,-6],[-38,-1],[-31,-5],[-13,-19],[-9,-28],[-21,-14],[-59,-16],[-39,-18],[-63,-2],[-33,-7],[-68,-32],[-72,-13],[-71,-29],[-40,-10],[-22,2],[-66,15],[-36,3],[-76,27],[-24,63],[-17,68],[-55,46],[0,20],[16,15],[9,19],[3,25],[-4,32],[-25,-22],[-24,-15],[-22,-2],[-19,19],[-23,0],[0,-46],[-80,76],[41,109],[128,172],[54,-28],[18,-17],[20,22],[-19,43],[-3,45],[19,34],[46,12],[-66,44],[-60,-51],[-98,-149],[-165,-100],[-107,-38],[-48,37],[-26,-1],[-213,39],[-21,1],[-21,3],[-34,13],[-132,91],[-48,21],[-41,30],[-30,60],[-11,59],[10,40],[33,29],[59,27],[-60,-9],[-168,-68],[-157,99],[-44,85],[-31,35],[-50,15],[-36,15],[-44,38],[-70,80],[65,13],[112,62],[63,14],[52,-6],[54,-16],[110,-46],[-67,66],[-84,57],[-71,65],[-56,158],[-6,11],[-15,11],[5,24],[21,43],[8,46],[20,46],[30,41],[34,35],[-106,14],[-45,41],[-26,63],[-51,82],[-38,25],[-44,18],[-38,27],[-15,53],[2,46],[7,43],[14,39],[8,15]],[[1076,4313],[1,0],[5,5],[162,37],[38,15],[45,34]],[[1327,4404],[55,63],[38,31],[56,57],[36,53],[21,24],[17,28],[15,30],[9,14],[10,11],[22,9],[31,3],[62,-7],[31,-8],[22,-11],[11,-12],[9,-14],[7,-15],[9,-14],[11,-12],[18,-4],[24,3],[68,33],[30,6],[198,3],[19,-3],[16,-7],[14,-9],[9,-13],[8,-15],[18,-13],[29,-7],[61,1],[58,-7],[32,-13],[13,-10],[11,-12],[3,-18],[2,-38],[3,-18],[6,-17],[9,-14],[12,-11],[23,-6],[252,-14],[15,-7],[13,-10],[9,-14],[7,-15],[12,-10],[19,-3],[32,16],[16,16],[11,18],[21,46],[10,12],[12,11],[14,9],[101,42],[30,19],[20,15],[24,24],[23,8],[32,6],[94,2],[25,-4],[16,-7],[15,-8],[20,-3],[28,4],[95,40],[22,5],[29,4],[48,0],[67,-10],[23,-8],[202,-111],[164,-62],[13,-10],[9,-13],[20,-49],[8,-15],[19,-26],[13,-10],[13,-9],[17,-5],[19,-2],[33,-8],[13,1],[13,18],[6,35],[1,17],[-4,17],[-12,32],[-8,12],[-14,7],[-14,8],[-10,12],[-31,60],[-6,15],[-1,17],[4,17],[8,14],[59,77],[9,9],[17,-1],[22,1],[31,12],[53,2],[42,-10],[19,1],[24,6],[34,18],[24,6],[23,-7],[59,-35],[125,-32],[84,-6],[88,5],[60,17],[155,65],[22,15],[78,70],[22,28],[14,25],[16,73],[7,81],[3,19],[8,14],[10,12],[19,13],[72,32],[37,9],[40,6],[187,-10],[49,9],[75,48]],[[857,4732],[11,-6],[55,-5],[35,-23],[22,-19],[206,-39],[50,-15],[31,-16],[7,-15],[2,-19],[-4,-59],[11,-45],[44,-67]],[[1076,4313],[14,24],[-30,-6],[-19,-12],[-17,-15],[-25,-11],[-23,-5],[-14,0],[-16,3],[-27,2],[-146,-23],[-75,-28],[-91,-51],[-82,-62],[-50,-60],[-21,0],[8,51],[-8,138],[-14,69],[-2,40],[16,37],[-38,30],[-115,180],[-7,1],[-7,12],[-16,2],[-15,6],[-7,25],[-17,34],[-84,109],[-24,14],[-15,32],[-20,31],[-42,12],[46,56],[15,26],[5,42],[-9,30],[-43,62],[-14,30],[71,-11],[20,-12],[22,14],[8,-8],[15,-28],[56,24],[60,-2],[59,-18],[51,-28],[42,-46],[68,-110],[114,-58],[80,-74],[84,-58],[60,9]],[[8972,6897],[-1,0],[-127,4],[-58,-12],[-200,-126],[-1,-1],[-1,-4],[-2,-8],[4,-11],[9,-10],[14,-9],[61,-31],[9,-15],[1,-18],[-21,-34],[-20,-14],[-22,-9],[-43,-2],[-41,2],[-75,15],[-20,1],[-21,-3],[-98,-27],[-39,-4],[-25,2],[-21,6],[-44,24],[-217,149],[-2,1],[-34,26],[-24,22],[-61,75],[-47,44],[-21,10],[-30,7],[-339,43],[-24,-3],[-24,-7],[-32,-14],[-19,-11],[-15,-13],[-11,-12],[-35,-54],[-13,-32],[-9,-35],[-4,-65],[-3,-16],[-5,-16],[-8,-15],[-19,-27],[-23,-22],[-97,-63],[-31,-15],[-242,-83],[-30,-15],[-10,-12],[-9,-14],[-61,-183],[-35,-77],[-9,-13],[-20,-25],[-29,-23],[-76,-49],[-16,-13],[-9,-12],[-6,-14],[14,-138],[-2,-81],[3,-17],[6,-16],[18,-27],[7,-16],[5,-17],[0,-20],[-7,-36],[-34,-99],[-7,-35],[2,-17],[6,-15],[19,-25],[7,-15],[4,-14],[0,-16],[-2,-10],[-43,-128],[-19,-37],[-19,-25],[-17,-11],[-19,-4],[-14,6],[-13,10],[-11,11],[-13,8],[-15,6],[-17,2],[-17,4],[-15,7],[-24,21],[-14,8],[-16,5],[-19,-1],[-166,-41],[-20,-1],[-18,2],[-18,3],[-50,17],[-23,3],[-26,0],[-38,-6],[-22,-8],[-18,-9],[-54,-38],[-72,-64]],[[857,4732],[29,4],[0,22],[-40,9],[-35,16],[-25,26],[-13,38],[45,0],[0,22],[-97,22],[-91,31],[-49,49],[32,78],[39,28],[99,38],[43,23],[24,32],[21,35],[26,19],[44,-20],[-1,38],[9,35],[17,33],[20,28],[32,-9],[220,-15],[62,-17],[37,-4],[27,10],[66,45],[31,14],[-61,12],[-70,-8],[-63,5],[-7,37],[-3,-2],[64,78],[119,255],[-82,-99],[-53,-88],[-65,-67],[-118,-35],[-59,3],[-50,9],[-44,0],[-85,-50],[-60,-5],[-55,19],[-34,45],[75,13],[39,40],[33,46],[59,36],[0,22],[-30,5],[-18,5],[-12,13],[-10,21],[-21,0],[-31,-100],[-24,-39],[-37,-16],[-53,16],[-47,39],[-60,78],[-34,-18],[-26,3],[-19,22],[-12,36],[-16,-56],[27,-35],[50,-23],[54,-18],[-45,-22],[91,-66],[5,-73],[-48,-70],[-71,-57],[14,-28],[0,-28],[-16,-24],[-34,-9],[-27,10],[-20,25],[-88,143],[-24,51],[-21,96],[-27,59],[-34,55],[-31,38],[-16,36],[-2,53],[6,100],[16,2],[-16,344],[92,102],[38,8],[14,-11],[11,-21],[28,-21],[254,2],[53,8],[67,29],[96,10],[190,-4],[-137,40],[-22,-9],[-25,-18],[-57,0],[-61,11],[-40,18],[-36,-15],[-53,-10],[-48,5],[-21,32],[-11,25],[-23,6],[-24,0],[-12,3],[-2,26],[8,10],[11,6],[33,63],[66,30],[82,15],[76,4],[0,24],[-58,18],[-75,-13],[-141,-49],[-77,-8],[-83,17],[-65,46],[-24,78],[-13,-15],[-7,-2],[-4,-5],[0,-25],[-49,47],[-18,-4],[-7,-13],[-7,-11],[-8,-19],[-71,57],[11,154],[-53,60],[0,20],[79,55],[144,44],[87,15],[52,-12],[126,-77],[75,-25],[50,8],[101,71],[128,61],[60,37],[45,54],[44,102],[27,48],[32,44],[19,9],[52,5],[12,4],[-4,27],[-45,54],[-15,33],[15,93],[53,100],[69,48],[64,-65],[73,37],[65,4],[63,-4],[70,11],[55,45],[79,128],[58,28],[45,13],[2,25],[-13,29],[-1,26],[27,34],[19,16],[56,31],[14,-2],[17,-11],[16,-4],[13,21],[8,19],[11,12],[23,19],[16,21],[113,352],[0,17],[-6,14],[2,14],[25,12],[19,5],[11,6],[7,8],[9,15],[28,107],[16,27],[31,11],[32,-4],[27,2],[13,28],[4,138],[10,10],[54,93],[30,30],[103,61],[11,2],[4,12],[0,40],[6,16],[17,0],[17,-3],[10,8],[-1,16],[-13,36],[-2,10],[45,241],[0,35],[-4,22],[2,20],[20,29],[119,68],[150,53],[498,103],[77,-3],[59,-38],[60,-52],[79,-47],[62,-23],[55,-11],[56,-3],[105,22],[623,132],[22,30],[16,199],[1069,5],[340,2],[118,1],[585,3],[84,-8],[112,-33],[46,-4],[36,-31],[24,-72],[45,-215],[25,-38],[246,-151],[136,-127],[115,-144],[79,-146],[39,-111],[20,-31],[18,-11],[54,-19],[22,-17],[14,-25],[23,-86],[24,-31],[85,-58],[32,-32],[12,-26],[17,-90],[23,-30],[35,-16],[25,-21],[-4,-46],[-18,-81],[14,-57],[36,-49],[50,-53],[52,-33],[6,-18],[-35,-24],[-32,-29],[41,-13],[58,-2],[22,4],[32,-5],[36,1],[30,-7],[40,-57],[40,-35],[44,-28],[34,-16],[142,-120],[4,-41],[-13,-36],[-19,-36],[-15,-40],[0,-28],[14,-55],[-5,-30],[-26,-38],[-35,-22],[-37,-16],[-33,-22],[-59,-101],[-36,-141],[24,-118],[119,-35],[137,5],[131,-13],[78,-22],[39,-31],[3,-21],[3,-20]],[[8972,6897],[2,-8],[-51,-193],[-16,-112],[6,-237],[5,-33],[31,-91],[0,-15],[-3,-34],[2,-12],[11,-14],[24,-15],[9,-8],[136,-175],[50,-36],[18,-31],[142,-186],[-26,-69],[-45,-63],[-63,-43],[-76,-6],[-67,-13],[-64,-51],[-52,-69],[-53,-95],[-51,-47],[-23,-39],[-8,-43],[0,-39],[-9,-26],[-36,-4],[-47,-281],[-49,-79],[-60,-69],[-77,-110],[-34,-105],[67,-58],[24,109],[66,68],[93,30],[110,-6],[49,-20],[95,-60],[35,-7],[61,23],[116,75],[196,186],[40,51],[80,159],[32,44],[39,35],[42,26],[50,19],[100,14],[23,-2],[90,-26],[27,-15],[28,-9],[38,5],[-2,-168],[-102,-398],[-58,-124],[-7,-73],[24,-92],[26,-65],[-9,-60],[-82,-77],[-64,-36],[-65,-20],[-68,-9],[-267,-2],[-66,-10],[-78,-37],[-49,-59],[-77,-140],[-35,-32],[-84,-46],[-30,-24],[-13,-30],[-5,-38],[8,-786],[-76,-11],[-24,-8],[-156,-83],[-126,-104],[-442,-526],[-35,-42],[-37,-28],[-44,-18],[-126,-29],[-135,-74],[-492,-380],[-59,-74]]],transform:{scale:[.00030191621942194275,.00030768939863986026],translate:[-13.301096157999893,6.919419664000117]}},m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){
a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();