!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo={type:"Topology",objects:{ken:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:"Coast"},id:"KE.CO",arcs:[[[0]],[[1,2,3,4]]]},{type:"Polygon",properties:{name:"Central"},id:"KE.CE",arcs:[[5,6,7]]},{type:"Polygon",properties:{name:"Nairobi"},id:"KE.NA",arcs:[[8,9,-7]]},{type:"Polygon",properties:{name:"Nyanza"},id:"KE.NY",arcs:[[10,11,12]]},{type:"Polygon",properties:{name:"Eastern"},id:"KE.NA",arcs:[[13,-5,14,-9,-6,15,16]]},{type:"Polygon",properties:{name:"Rift Valley"},id:"KE.",arcs:[[-16,-8,-10,-15,-4,17,-11,18,19]]},{type:"Polygon",properties:{name:"Western"},id:"KE.WE",arcs:[[-13,20,-19]]},{type:"Polygon",properties:{name:"North-Eastern"},id:"KE.NE",arcs:[[-2,-14,21]]}]}},arcs:[[[9096,2664],[-10,-22],[-23,-3],[-17,22],[2,-11],[-2,-12],[-5,-8],[-8,-4],[-4,-1],[-2,-3],[-4,-2],[-7,-2],[-9,3],[-7,5],[-4,4],[-2,3],[-16,-2],[-22,-7],[-47,-20],[-11,7],[-11,-1],[-9,-9],[-3,-14],[7,-8],[13,-7],[10,-7],[-5,-10],[-16,0],[-10,6],[-5,10],[-3,12],[-27,-4],[0,23],[15,32],[17,24],[23,10],[29,7],[26,11],[11,21],[14,13],[34,-1],[38,-10],[25,-12],[25,-33]],[[6073,4751],[235,-29],[15,4],[10,11],[8,12],[10,12],[13,6],[20,6],[40,6],[38,0],[10,-3],[6,-5],[4,-5],[6,-2],[15,-10],[67,-67],[107,-4],[23,-9],[23,2],[25,5],[26,-1],[20,-9],[11,-9],[14,-8],[38,-5],[9,-4],[8,-6],[3,-5],[7,-2],[45,-9],[16,-6],[20,-10],[17,-12],[11,-16],[17,-10],[7,-9],[14,-29],[14,-5],[18,2],[12,-4],[5,-5],[12,-19],[4,-4],[22,-10],[16,-25],[49,-147],[8,-16],[15,-3],[9,-8],[5,-12],[1,-15],[7,-1],[32,-31],[4,-7],[15,-5],[33,-2],[20,-7],[15,-9],[12,-11],[6,-15],[3,-18],[38,-47],[1,-6],[-2,-16],[2,-16],[11,-10],[-10,-15],[3,-15],[16,-34],[3,-33],[4,-10],[10,1],[8,0],[-2,-15],[-2,-6],[-4,-7],[6,-10],[19,-47],[1,-7],[-1,-21],[17,-21],[7,-17],[8,-30],[44,-73],[18,-15],[-9,-8],[-7,-8],[-9,-19],[27,-2],[22,-15],[14,-21],[5,-22],[28,-27],[15,-39],[5,-88],[9,-39],[12,-37],[8,-66],[4,-11],[8,-9],[51,-89],[11,-12],[17,-14],[9,-15],[3,-66],[7,-27],[2,-1],[-3,-8],[-6,-6],[-5,-4],[-3,-3],[-1,-18],[9,-46],[-2,-8],[-7,-11],[1,-8],[6,-10],[13,-11],[6,-8],[10,-49],[8,-14],[-5,-3],[-8,-8],[-5,-3],[10,-15],[16,-56],[17,-31],[6,-16],[-5,-10],[0,-6],[5,-20],[68,22],[968,356],[8,2],[666,34]],[[9562,3091],[-1,-20],[0,-1],[-96,-80],[-45,-57],[-6,-18],[-27,-14],[-71,-53],[-44,-45],[-26,-13],[-20,20],[-23,-8],[-21,2],[-15,11],[-1,23],[-25,-22],[-5,-24],[-12,-24],[-36,3],[-49,9],[-10,1],[-16,-8],[-25,-23],[-33,-11],[-13,-14],[-14,-12],[-20,1],[-16,7],[-12,7],[-8,10],[-6,15],[1,15],[13,28],[-1,13],[-3,14],[5,13],[19,23],[-45,-28],[-5,-121],[-26,-28],[-24,15],[-23,63],[-23,14],[-13,7],[-11,11],[-12,3],[-15,-14],[9,-16],[3,-13],[-3,-13],[20,7],[16,-3],[11,-12],[4,-17],[-3,-7],[-11,-5],[-3,-6],[4,-10],[9,-2],[9,-2],[4,-7],[3,-75],[7,-40],[16,-26],[11,-2],[14,1],[15,-1],[16,-9],[12,-15],[2,-11],[-14,-27],[-31,-31],[-28,-4],[-4,11],[41,17],[-21,7],[-18,9],[-30,26],[-5,9],[2,5],[-1,4],[-13,4],[2,-2],[-9,-3],[-18,-3],[-31,-14],[-28,-27],[-27,-19],[-26,11],[1,-9],[3,-9],[7,-8],[10,-3],[6,-2],[-2,-7],[-8,-11],[8,-15],[35,-20],[9,-14],[-15,-24],[-34,-28],[-39,-23],[-58,-20],[-77,-75],[-43,-13],[-39,7],[-37,13],[-40,8],[-41,-4],[-126,-32],[-52,-31],[-32,-8],[-36,-19],[-63,-42],[-14,-12],[-11,-15],[-23,-43],[-38,-39],[-5,-17],[2,-8],[19,-20],[1,-7],[-4,-16],[-2,-9],[-23,-66],[-3,-33],[26,-17],[-6,-27],[15,-12],[26,2],[26,16],[1,-12],[-10,-12],[-44,-34],[-26,-29],[-7,-12],[-5,-52],[-8,-18],[-43,-50],[-1,-14],[14,-38],[0,-19],[-8,-16],[-11,-14],[-15,-13],[-17,-10],[-61,-23],[-16,-9],[-30,-29],[-17,-11],[-22,-3],[9,18],[14,39],[2,15],[-22,-5],[-10,-10],[-2,-13],[0,-15],[-1,-10],[-7,-25],[2,-6],[5,-5],[2,-6],[-22,-30],[-26,-55],[-33,-44],[-13,-59],[-14,-24],[-16,-12],[-11,-2],[-27,10],[-6,3],[-17,15],[-6,1],[-33,3],[5,9],[1,4],[-13,8],[-9,-19],[3,-15],[15,-10],[25,1],[-15,-15],[5,-12],[18,-5],[43,15],[14,-8],[8,-16],[4,-15],[-3,-33],[-101,-229],[-32,-25],[-36,-5],[-25,16],[0,35],[-27,-13],[-8,-8],[4,-10],[9,-11],[4,-9],[6,-10],[12,-9],[28,2],[14,-6],[-4,-18],[-26,-24],[-4,-7],[-3,-9],[-9,-13],[-12,-11],[-10,-5],[-12,5],[-9,24],[-10,6],[-8,12],[3,24],[-2,14],[-22,-15],[-32,14],[-15,2],[-14,-9],[13,-10],[15,-5],[33,-6],[-9,-19],[-12,-8],[-40,-8],[-12,0],[-14,1],[-12,-1],[-4,-7],[1,-22],[2,-10],[5,-11],[18,20],[42,3],[44,-11],[24,-19],[-5,-25],[-62,-88],[-40,-84],[-41,-126],[-14,-30],[-11,14],[-11,-1],[-10,-9],[-10,-11],[-2,-6],[1,-13],[-4,-6],[-18,-13],[-4,-4],[-43,-107],[-8,20],[4,30],[-4,14],[-16,-10],[-9,-14],[-8,-16],[-10,-16],[-6,1],[-9,4],[-8,-1],[-3,-15],[2,-8],[20,-23],[-13,-14],[-42,-5],[-48,9],[-30,31],[-1,-6],[-7,-15],[-37,9],[-27,-21],[-23,-30],[-24,-14],[-5,-2],[-5,0],[-4,-2],[-4,-4],[-106,63],[-107,63],[-107,64],[-106,63],[-107,63],[-107,64],[-107,63],[-106,63],[-107,63],[-9,5],[-98,59],[-107,63],[-106,63],[-107,63],[-107,64],[-107,63],[-106,63],[-69,41],[-17,18],[-12,21],[-22,64],[-9,16],[-18,13],[-24,9],[-26,6],[-27,2],[-60,-3],[-3,14],[10,20],[5,17],[-6,8],[-21,3],[-9,7],[-2,11],[3,12],[6,11],[6,10],[19,16],[28,18],[29,14],[24,1],[-2,18],[5,15],[12,9],[23,1],[-25,126]],[[4740,1530],[22,6],[116,13],[10,-1],[12,-1],[20,2],[8,9],[5,12],[20,147],[84,237]],[[5037,1954],[33,-7],[29,7],[152,60],[31,16],[11,11],[8,5],[8,1],[6,-2],[5,-2],[5,-3],[4,-3],[23,-25],[4,-3],[38,-24],[42,-15],[7,-1],[17,-7],[6,-2],[22,-17],[54,-68],[26,-42],[6,-16],[2,-5],[4,-4],[9,-6],[6,-3],[54,-18],[5,-3],[18,-12],[15,-16],[8,-7],[4,-1],[2,0],[8,8],[3,2],[7,3],[13,3],[16,0],[18,-1],[4,1],[5,2],[5,4],[7,4],[7,0],[7,-1],[13,-2],[7,-2],[11,-4],[21,-11],[18,-6],[5,-2],[10,-6],[4,-3],[6,-5],[3,-3],[4,-3],[7,-2],[13,-2],[12,-4],[21,-12],[5,-2],[7,-1],[7,0],[36,5],[8,0],[7,-1],[7,-1],[37,-12],[60,-5],[5,-2],[2,-5],[-3,-8],[-1,-5],[0,-3],[3,-2],[4,0],[5,0],[19,5],[6,3],[5,2],[5,3],[7,1],[21,2],[6,2],[6,2],[19,13],[5,2],[6,2],[7,1],[8,0],[22,-2],[8,0],[7,1],[6,2],[13,2],[7,0],[7,-2],[12,-4],[6,-1],[7,1],[5,2],[12,11],[9,7],[4,2],[4,1],[6,0],[5,-2],[10,-5],[6,-2],[7,-1],[13,-1],[2,2],[0,2],[-515,625],[-2,4],[6,2],[19,-4],[20,-3],[15,0],[16,1],[7,2],[6,2],[22,11],[4,3],[25,29],[4,4],[10,5],[6,2],[21,5],[6,2],[5,2],[14,9],[5,4],[228,376],[6,22],[2,29],[-32,160],[-1,13],[3,14],[13,14],[28,24],[5,9],[2,5],[-5,557],[-9,23],[-148,267],[-569,808],[-1,13],[13,-2],[11,0],[12,5],[28,-11],[37,15],[63,38],[34,0],[74,-20],[30,6],[17,-8],[20,0],[16,2],[7,2],[6,-8],[15,-7],[31,-9]],[[3997,4838],[29,-24],[259,-160],[9,-11],[132,-265],[4,-17],[0,-79],[1,-9],[4,-9],[40,-39],[11,-15],[0,-27],[-1,-8],[2,-74],[3,-12],[6,-17],[3,-4],[4,-4],[2,-4],[-2,-5],[-13,-6],[-166,-51],[-9,-1],[-8,1],[-6,2],[-24,15],[-17,14],[-10,6],[-6,2],[-5,1],[-5,0],[-5,-1],[-6,-2],[-11,-4],[-3,-2],[-3,-5],[-4,-9],[-12,-75],[0,-11],[3,-7],[46,-32],[4,-3],[4,-4],[3,-5],[13,-39],[12,-19],[3,-4],[4,-4],[6,-2],[7,0],[7,1],[7,1],[6,3],[21,10],[5,2],[5,0],[5,-2],[3,-4],[2,-5],[0,-7],[-2,-8],[-4,-10],[-6,-23],[1,-20],[-35,-40],[-14,-11],[-2,-2],[-3,1],[-40,22],[-6,2],[-6,0],[-14,-2],[-6,-2],[-11,-5],[-8,-3],[-12,-2],[-15,1],[-7,1],[-7,0],[-7,0],[-6,-2],[-23,-13],[-9,-7],[-5,-6],[-3,-8],[1,-14],[4,-15],[0,-7],[-15,-34],[-25,-44],[-17,-14]],[[4054,3544],[-19,18],[-12,8],[-17,8],[-36,-6],[-16,-5],[-9,-1],[-10,-1],[-18,0],[-19,2],[-9,3],[-21,8],[-22,10],[-4,4],[-2,5],[0,5],[2,6],[12,17],[0,4],[-2,4],[-4,2],[-12,3],[-3,1],[-9,4],[-7,1],[-6,-1],[-34,-30],[-6,-3],[-7,-3],[-21,-2],[-10,-2],[-11,-4],[-8,-2],[-7,1],[-50,14],[-7,0],[-10,-4],[-13,-8],[-40,-41],[-7,-5],[-16,-6],[-9,-2],[-9,-4],[-7,-6],[-21,-26],[-34,-29]],[[3484,3481],[-10,3],[-21,11],[-19,11],[-6,2],[-8,2],[-146,18],[-7,8],[-2,14],[48,115],[4,4],[6,1],[23,0],[7,1],[6,2],[5,4],[4,6],[1,11],[0,8],[-8,31],[-1,6],[2,18],[9,26],[0,4],[0,1],[-1,2],[-17,19],[-3,5],[-9,22],[-2,5],[0,7],[3,18],[-1,6],[-1,5],[-43,98],[-10,42],[2,9],[10,27],[2,10],[0,8],[-16,58],[-2,19],[-9,31],[-2,5],[-5,2],[-4,1],[-5,1],[-12,3],[-13,4],[-5,2],[-4,4],[-6,9],[-4,5],[-4,3],[-6,1],[-16,0],[-15,-1],[-26,-4],[-4,0],[-2,1],[-2,4],[-5,17],[-5,11],[-3,11],[-1,27],[-10,50],[2,15],[3,9],[2,14],[0,7],[-6,16],[-2,5],[-4,3],[-4,2],[-5,0],[-37,-6],[-6,0],[-4,3],[-3,4],[-3,5],[-3,4],[-5,2],[-7,0],[-7,-1],[-27,-5],[-15,-2],[-7,1],[-6,1],[-6,2],[-3,2],[-3,2],[-62,112],[-5,11],[-2,12],[-1,13],[0,7],[2,6],[14,30],[2,11],[-3,52],[4,25],[4,13],[4,7],[6,20],[18,25],[3,5],[16,41],[7,10],[16,13],[12,6],[51,17],[4,3],[4,4],[3,10],[3,5],[8,3],[14,1],[28,-2],[20,3],[12,3],[66,33],[4,3],[3,5],[4,11],[4,3],[6,1],[9,-4],[6,-4],[30,-25],[4,-4],[3,-5],[5,-17],[6,-6],[11,-6],[24,-5],[12,-5],[8,-4],[29,-26],[6,-2],[8,-1],[13,4],[8,3],[18,9],[6,1],[7,-1],[6,-4],[3,-5],[0,-4],[-2,-3],[-4,-3],[-110,-55],[-10,-6],[-6,-6],[-4,-5],[-2,-5],[-1,-6],[0,-6],[3,-11],[2,-5],[3,-5],[39,-28],[16,-10],[21,-3],[57,-3],[22,-5],[59,7],[90,18],[15,1],[11,-1],[59,-17],[5,-2],[1,-2],[-3,-6],[0,-13],[-4,-20],[-3,-8],[-2,-4],[-4,-4],[-4,-3],[-10,-4],[-3,-3],[0,-4],[7,-16],[3,-5],[50,-50],[13,-10],[15,-8],[5,-2],[3,-1],[6,0],[7,3],[6,4],[7,5],[5,3],[5,0],[16,-7],[7,-2],[7,0],[8,2],[10,8],[15,16],[4,6],[10,11],[21,15],[4,4],[3,4],[4,10],[3,14],[5,41],[-2,9],[-29,39],[-3,5],[-1,5],[-1,13],[4,8],[7,9],[33,25],[15,18],[44,63]],[[4054,3544],[-32,-26],[-7,-7],[-14,-10],[-24,-21],[-5,-3],[-5,-1],[-11,0],[-64,10],[-15,0],[-13,3],[-7,0],[-6,-2],[-5,-4],[-2,-16],[-3,-8],[-6,-7],[-58,-32],[-2,-2],[-1,-4],[3,-5],[18,-20],[-1,-4],[-2,-4],[-10,-8],[-10,-10],[-11,-5],[-35,4]],[[3726,3362],[-20,16],[-14,7],[-7,3],[-14,2],[-34,-2],[-7,1],[-6,1],[-40,13],[-12,7],[-12,9],[-11,13],[-10,15],[-13,11],[-42,23]],[[1086,4810],[10,1],[6,0],[6,-2],[6,-2],[5,-3],[18,-14],[10,-6],[5,-2],[6,-2],[14,-2],[16,0],[24,2],[33,0],[59,-6],[8,1],[14,2],[6,3],[16,8],[6,2],[8,1],[7,1],[8,-1],[5,-2],[5,-3],[13,-10],[4,-3],[6,0],[6,-1],[5,-1],[14,-9],[6,-3],[6,-2],[6,-1],[7,1],[13,4],[5,2],[6,3],[4,4],[8,14],[3,5],[4,4],[5,3],[6,1],[8,1],[7,0],[14,-3],[18,-6],[21,-10],[6,-2],[6,-2],[7,0],[7,1],[6,2],[10,5],[5,2],[4,1],[5,-1],[6,-1],[6,-2],[5,-3],[7,-7],[4,-5],[4,-11],[6,-30],[4,-4],[5,-3],[5,-3],[24,-8],[6,-4],[3,-6],[0,-13],[2,-6],[3,-5],[57,-38],[4,-3],[4,-5],[2,-4],[1,-10],[0,-6],[-6,-34],[-3,-6],[-5,-4],[-10,-3],[-9,0],[-8,0],[-7,1],[-6,2],[-6,2],[-5,3],[-9,7],[-3,4],[-3,5],[-4,11],[-3,4],[-5,3],[-6,2],[-15,1],[-7,2],[-5,2],[-5,4],[-3,4],[-7,15],[-4,5],[-5,3],[-6,2],[-6,0],[-6,-1],[-11,-4],[-6,-2],[-7,0],[-6,2],[-6,2],[-13,10],[-15,9],[-4,3],[-4,5],[-10,13],[-4,4],[-4,2],[-8,-4],[-8,-9],[-30,-46],[-11,-11],[-6,-2],[-12,-4],[-6,-3],[-5,-3],[-4,-4],[-3,-6],[-2,-8],[-2,-47],[-3,-17],[0,-6],[-1,-6],[-3,-7],[-11,-14],[-5,-4],[-14,-10],[-5,-3],[-10,-12],[-2,-3],[-2,-11],[1,-10],[1,-6],[2,-5],[8,-15],[4,-11],[2,-4],[20,-25],[2,-6],[2,-6],[-5,-34],[0,-6],[3,-15],[1,-2],[1,-3],[10,-12],[2,-4],[1,-4],[15,-111],[-3,-29],[-8,-17],[1,-12],[3,-12],[25,-57],[4,-14],[0,-12],[-9,-10],[-15,-9],[-16,-8],[-16,-11],[-14,-11],[-16,-31],[-6,-20],[-5,-8],[-9,-5],[-15,-5],[-334,-79],[-10,0],[-46,3],[-7,1],[-5,2],[-6,2],[-6,1],[-7,-1],[-6,-2],[-26,-15],[-22,-9],[-20,-13],[-4,-4],[-4,-4],[-1,-10],[3,-10],[22,-31],[3,-6],[1,-12],[4,-10],[7,-12],[18,-20],[14,-49],[6,-7],[30,-18],[18,-27],[9,-18],[46,-171],[2,-14]],[[1036,3363],[-202,93],[-269,124],[-269,123],[-93,43],[-47,3],[-17,9],[-2,27],[-19,1],[-6,-1],[-95,0],[-7,210],[-5,140],[21,148],[30,212],[23,164],[-1,40]],[[78,4699],[58,121],[5,7],[18,26],[3,5],[1,6],[1,5],[-4,18],[0,7],[1,5],[3,5],[3,4],[2,3],[13,9],[15,9],[11,4],[24,7],[3,2],[2,4],[3,8],[1,4],[2,34],[4,22],[5,17],[2,4],[3,3],[22,19],[37,19],[8,2],[12,1],[22,-2],[11,2],[8,2],[5,3],[4,4],[3,5],[10,20],[3,4],[3,3],[2,2],[9,5],[6,2],[6,2],[8,1],[9,0],[10,0],[67,-11],[15,-1],[7,0],[7,2],[10,6],[12,4],[7,2],[9,-2],[11,-4],[23,-18],[4,-6],[4,-10],[4,-19],[1,-20],[1,-6],[2,-5],[3,-4],[69,-57],[2,-5],[0,-5],[-4,-16],[4,-7],[10,-7],[23,-7],[33,-17],[6,-5],[3,-4],[1,-6],[0,-5],[-3,-12],[-1,-31],[-4,-11],[-6,-9],[-2,-5],[-3,-9],[-1,-4],[1,-4],[3,-4],[3,-5],[6,-4],[7,-4],[9,-1],[11,1],[16,4],[33,12],[21,4],[8,0],[28,-7],[30,-12],[8,0],[11,1],[28,8],[10,2],[10,0],[26,-4],[18,-1],[30,6]],[[6761,8414],[0,-16],[18,-191],[12,-35],[9,-58],[1,-137],[-2,-16],[-4,-10],[-4,-3],[-28,-16],[-6,-6],[-14,-18],[-8,-6],[-10,-5],[-64,-17],[-12,-4],[-9,-6],[-19,-17],[-5,-3],[-5,-3],[-12,-4],[-33,-6],[-11,-5],[-7,-3],[-7,-5],[-9,-10],[-4,-7],[-1,-8],[0,-14],[-3,-21],[-6,-5],[-8,-4],[-156,-286],[-26,-32],[-66,-17],[-15,-7],[-4,-5],[0,-7],[-3,-4],[0,-5],[4,-6],[6,-16],[18,-29],[18,-64],[4,-57],[15,-62],[4,-11],[2,-6],[7,-37],[3,-5],[1,-25],[-4,-34],[0,-15],[2,-11],[8,-7],[3,-4],[4,-5],[6,-16],[4,-4],[4,-3],[5,-3],[8,-7],[6,-10],[18,-43],[38,-61],[52,-65],[9,-7],[40,-23],[18,-14],[4,-3],[14,-18],[10,-19],[3,-5],[4,-4],[13,-11],[7,-8],[12,-19],[13,-47],[2,-4],[3,-5],[4,-4],[18,-14],[5,-3],[5,-2],[31,-9],[18,-8],[5,-2],[17,-14],[10,-6],[76,-36],[9,-7],[8,-7],[7,-9],[4,-4],[2,-5],[6,-10],[28,-26],[5,-3],[6,-2],[13,-4],[5,-3],[8,-6],[5,-4],[4,-3],[23,-9],[4,-3],[1,-2],[-1,-2],[-195,-65],[-14,-6],[-7,-7],[2,-11],[207,-456],[2,-7],[0,-9],[-11,-6],[-14,-6],[-149,-47],[-3,-4],[-4,-3],[-41,-58],[-15,-16],[-14,-10],[-28,-18],[-17,-16],[-35,-42],[-14,-22],[-3,-9],[-4,-15],[-3,-4],[-5,-5],[-416,-129],[-9,-3],[-5,-3],[-4,-4],[-3,-3],[-16,-29],[-17,-21],[-4,-4],[-5,-3],[-6,-3],[-6,-1],[-77,-8],[-7,-2],[-5,-2],[-22,-10],[-4,-2],[-5,-3],[-6,-7],[-1,-6],[3,-5],[4,-4],[24,-14],[3,-2],[1,-3],[68,-239],[4,-40],[-2,-7],[-3,-7],[-9,-15],[-2,-7],[11,-213]],[[5037,1954],[-14,54],[-13,25],[-370,352],[-5,7],[-4,11],[1,7],[2,6],[8,7],[13,10],[5,3],[6,3],[6,2],[21,4],[7,2],[5,2],[4,4],[7,9],[2,5],[11,54],[2,5],[3,5],[4,3],[4,4],[5,3],[6,3],[11,3],[22,5],[4,1],[-2,5],[-8,8],[-28,15],[-19,6],[-17,3],[-9,0],[-15,-2],[-16,-1],[-7,0],[-38,11],[-78,51],[-8,4],[-25,5],[-6,3],[-29,15],[-23,16],[-34,18],[-70,29],[-35,10],[-61,8],[-54,45],[-24,15],[-20,9],[-27,7],[-12,8],[-12,12],[-13,23],[-7,17],[-1,5],[-15,27],[-2,12],[0,12],[12,36],[-18,22],[-184,181],[-30,43],[-8,17],[-4,12],[1,5],[1,2],[11,23],[8,23],[-4,7],[-12,3],[-33,6],[-16,0],[-11,-1],[-41,-15],[-7,-2],[-7,0],[-7,2],[-6,7],[-3,6],[9,36]],[[3997,4838],[37,15],[120,35],[44,24],[10,6],[129,111],[3,6],[2,6],[4,45],[2,5],[3,7],[6,7],[17,18],[4,6],[4,11],[2,25],[0,7],[-2,6],[-15,30],[-3,5],[-14,16],[-4,4],[-7,9],[-9,21],[-3,11],[1,52],[-1,14],[-2,6],[-3,6],[-6,7],[-13,3],[-565,60],[-15,3],[-5,3],[-2,5],[0,6],[2,6],[3,4],[19,12],[8,8],[3,4],[12,12],[3,4],[3,12],[2,5],[4,4],[9,7],[3,4],[2,7],[1,8],[-3,22],[0,15],[5,19],[5,5],[7,3],[7,0],[14,-2],[8,0],[7,0],[94,16],[28,7],[8,3],[12,6],[6,3],[7,1],[14,1],[11,-1],[6,-1],[6,-2],[16,-8],[5,-2],[13,-3],[22,-3],[4,-3],[6,-6],[8,-5],[4,-3],[5,-2],[6,-2],[15,-1],[15,-1],[6,2],[6,3],[13,10],[6,3],[6,1],[6,0],[6,-1],[6,-2],[5,-3],[17,-14],[21,-11],[4,-3],[4,-4],[39,-46],[11,-20],[11,-12],[6,-9],[3,-5],[3,-18],[3,-6],[4,-3],[8,-7],[8,-8],[8,-8],[5,-3],[12,-4],[13,-3],[7,0],[50,8],[19,6],[7,2],[7,0],[6,0],[6,-2],[10,-5],[12,-8],[4,-2],[11,-3],[8,-1],[9,0],[23,6],[8,0],[6,-2],[14,-8],[5,1],[5,4],[6,8],[6,5],[7,3],[24,8],[18,11],[26,20],[7,4],[17,7],[13,7],[8,7],[8,9],[7,4],[7,1],[6,-2],[6,-3],[8,0],[10,2],[30,14],[10,2],[7,1],[16,-1],[14,-2],[8,0],[8,1],[29,8],[12,4],[6,4],[5,6],[10,15],[6,5],[7,4],[24,12],[9,1],[8,0],[11,-5],[5,-3],[6,-1],[9,1],[49,23],[20,13],[21,18],[9,9],[5,4],[5,2],[11,2],[7,0],[6,-1],[18,-6],[21,-5],[5,0],[2,3],[-27,353],[-6,16],[-13,16],[-76,73],[-15,18],[-6,17],[-13,139],[-6,16],[-15,15],[-68,52],[-10,10],[-3,1],[-2,1],[-4,1],[-4,0],[-5,-1],[-3,0],[-6,-2],[-10,-5],[-17,-14],[-5,-3],[-6,-2],[-6,-1],[-8,-1],[-21,3],[-7,0],[-8,-1],[-6,-1],[-6,-2],[-19,-11],[-22,-9],[-7,-2],[-15,0],[-21,2],[-7,0],[-6,-2],[-11,-5],[-4,-3],[-11,-5],[-6,-2],[-13,-3],[-37,-4],[-13,-3],[-17,-7],[-6,-1],[-7,-1],[-8,0],[-7,1],[-6,2],[-6,3],[-4,3],[-3,4],[-85,165],[-14,17],[-8,7],[-53,34],[-32,29],[-6,9],[-4,10],[-12,71],[-3,6],[-3,4],[-4,4],[-4,3],[-51,21],[-15,9],[-8,7],[-3,5],[-21,45],[-30,45],[-14,16],[-9,7],[-19,12],[-16,9],[-19,12],[-4,4],[-3,4],[-3,4],[-4,10],[-3,5],[-14,13],[-7,4],[-23,9],[-14,3],[-123,9],[-59,-1],[-36,-3],[-6,0],[-5,1],[-2,5],[4,28],[-10,33],[-55,119],[-7,12],[-10,13],[-73,53],[-10,13],[-102,237],[-7,12],[-9,8],[-15,3],[-16,0],[-14,-4],[15,-31],[-8,-22],[-14,-20],[-69,-60],[-10,-5],[-7,-1],[-31,4],[-5,1],[-15,-1],[-12,-2],[-9,-4],[-8,-7],[-33,29],[-8,43],[1,48],[-11,42],[-29,41],[-22,21],[-42,19],[-8,25],[0,71],[-1,7],[-6,11],[-17,24],[-9,28],[-13,23],[-24,34],[-29,20],[-69,37],[-51,37],[-5,5],[-8,31],[-5,8],[-9,1],[-21,-15],[-13,-4],[-13,3],[-16,14],[-10,4],[-50,-1],[-19,5],[-7,21],[34,112],[0,12],[-4,12],[-11,21],[-2,12],[2,10],[12,21],[3,11],[-7,7],[-15,0],[-30,-3],[-43,14],[-9,5],[-7,4],[-8,4],[-10,26],[0,1211]],[[2695,9395],[183,1],[43,5],[13,0],[12,-3],[23,-9],[16,-2],[233,4],[195,3],[11,-2],[19,-9],[7,-1],[3,-1],[241,2],[75,-17],[89,-37],[51,-10],[12,-7],[20,-27],[9,-5],[25,1],[9,-2],[8,-9],[5,-10],[7,-23],[4,-6],[6,-1],[8,0],[7,-2],[68,-35],[122,-65],[122,-64],[142,-76],[102,-53],[114,-61],[129,-69],[120,-63],[124,-66],[39,-20],[23,-19],[32,-42],[19,-19],[16,-7],[38,-10],[28,-20],[16,-2],[39,8],[39,2],[133,-12],[134,-12],[70,4],[63,23],[2,6],[-1,9],[3,9],[12,4],[6,-2],[24,-16],[10,-10],[2,-8],[5,-6],[18,-4],[16,-2],[7,1],[18,6],[2,-4],[1,-5],[7,-4],[73,-4],[0,7],[1,15],[0,6],[34,-36],[19,-17],[23,-10],[123,-28],[94,-20],[32,0],[53,8],[21,-2],[18,-6],[20,1],[72,12],[12,-2],[16,-6],[112,-43],[49,-8],[48,1],[48,22]],[[4740,1530],[-3,13],[-22,112],[-19,25],[-104,48],[-172,79],[-96,44],[-269,124],[-268,123],[-269,124],[-268,123],[-269,124],[-268,123],[-269,124],[-268,123],[-268,123],[-212,98],[-57,26],[-269,123],[-268,124],[-66,30]],[[1086,4810],[21,26],[52,44],[24,23],[9,23],[9,16],[6,8],[6,6],[13,10],[37,23],[12,13],[5,10],[11,36],[10,20],[4,10],[0,8],[-2,4],[-4,5],[-5,10],[-6,16],[-1,13],[11,67],[0,6],[-1,6],[-13,32],[-4,18],[-17,30],[-3,4],[-5,3],[-4,3],[-52,21],[-14,7],[-2,8],[2,44],[4,14],[5,9],[16,7],[7,2],[7,1],[7,1],[7,0],[7,-2],[17,-7],[7,-1],[7,-1],[7,0],[9,4],[9,7],[31,40],[6,4],[5,3],[4,1],[3,1],[4,-1],[16,-3],[8,0],[6,0],[4,0],[5,2],[22,9],[14,3],[7,0],[7,-1],[7,-1],[11,-5],[6,-2],[15,-1],[6,1],[5,1],[4,2],[23,12],[8,7],[5,7],[7,18],[4,6],[5,4],[29,12],[5,4],[10,7],[5,6],[3,7],[0,7],[0,10],[-3,6],[-4,5],[-7,7],[-2,3],[-1,4],[0,26],[-2,16],[-3,5],[-22,25],[-7,15],[-2,3],[-2,3],[-3,2],[-4,2],[-9,4],[-24,7],[-13,3],[-6,2],[-12,5],[-10,5],[-7,2],[-6,1],[-72,2],[-39,-4],[-7,-2],[-6,-2],[-6,-2],[-4,-4],[-4,-3],[-22,-24],[-5,-4],[-6,-2],[-6,0],[-7,1],[-6,3],[-7,1],[-7,1],[-7,-1],[-35,-7],[-26,-7],[-46,-5],[-7,1],[-7,2],[-6,2],[-11,5],[-5,5],[-5,6],[-5,13],[-1,10],[1,10],[2,12],[-2,7],[-3,5],[-6,3],[-6,1],[-8,1],[-7,1],[-6,2],[-6,3],[-128,111],[-23,29],[-2,19],[-36,26],[-11,11]],[[846,5954],[6,9],[2,23],[9,19],[61,11],[42,34],[26,13],[103,9],[40,15],[16,41],[-13,41],[-22,42],[-2,20],[-3,17],[75,50],[26,83],[29,36],[12,5],[27,4],[12,5],[13,10],[9,11],[40,70],[7,22],[1,200],[6,13],[23,24],[5,11],[-7,12],[-29,22],[-11,11],[-9,18],[-5,21],[-1,21],[1,20],[12,46],[0,20],[-56,112],[-23,45],[-48,96],[-9,40],[11,26],[23,14],[25,12],[19,18],[3,23],[-11,18],[-34,29],[-8,19],[-7,52],[-12,14],[-12,-2],[-8,-8],[-10,-8],[-17,1],[-12,10],[-53,90],[-19,89],[-25,65],[-5,9],[-9,9],[-6,2],[-36,14],[-2,2],[-3,2],[-2,4],[-2,3],[-5,2],[-3,-2],[-6,-8],[-3,-1],[-5,-2],[-14,-10],[-8,-2],[-17,4],[-11,9],[-19,25],[-40,36],[-12,18],[-37,156],[-14,22],[-27,14],[-58,14],[-27,14],[-13,23],[-13,127],[-16,41],[-9,11],[-12,10],[-10,11],[-3,13],[4,11],[13,26],[1,9],[-5,12],[-15,22],[-1,11],[7,9],[11,4],[13,3],[12,5],[23,30],[12,41],[3,83],[-8,22],[-18,9],[-24,6],[-23,10],[-41,36],[-22,7],[-34,-8],[-13,-10],[-6,-10],[-6,-4],[-14,7],[-19,41],[-28,30],[-1,5],[-13,-1],[-25,-6],[-24,-1],[-22,-5],[-10,0],[-16,5],[8,8],[25,13],[-16,15],[-19,6],[-3,6],[60,26],[14,13],[-4,15],[-27,11],[-25,1],[-49,-15],[-20,-3],[-29,8],[2,18],[16,24],[13,26],[-3,17],[-15,12],[-18,10],[-15,11],[-10,17],[1,10],[5,10],[5,14],[4,24],[0,12],[-4,12],[-11,12],[-14,10],[-11,12],[-1,14],[1,31],[-15,24],[-28,19],[-37,14],[127,103],[126,103],[126,103],[127,103],[98,42],[87,37],[86,36],[85,35],[122,36],[102,32],[95,29],[102,32],[115,35],[102,32],[87,27],[22,-34],[88,40],[98,44],[27,-27],[-47,-80],[125,0],[94,-23],[-61,-127],[50,-76],[60,-90],[2,0],[111,-31],[7,31],[7,43],[35,20],[48,-3],[4,-60],[94,0],[80,0],[3,-18],[16,-24],[2,-20],[-4,-21],[0,-17],[8,-15],[22,-12],[49,-34],[26,-15],[29,-5],[5,0]],[[78,4699],[-39,105],[-39,107],[4,20],[73,80],[10,11],[101,111],[45,29],[11,13],[3,21],[-14,38],[-1,19],[5,9],[22,21],[9,11],[3,9],[31,101],[1,4],[21,32],[37,20],[51,15],[40,17],[30,27],[21,44],[5,24],[2,13],[11,9],[80,33],[21,16],[18,42],[36,38],[33,66],[7,21],[1,23],[0,23],[6,22],[11,21],[12,19],[26,20],[20,-4],[21,-9],[24,4],[2,0],[7,10]],[[6761,8414],[8,4],[17,-1],[6,-10],[-12,-19],[156,-4],[39,-11],[11,-11],[6,-14],[10,-14],[19,-11],[40,2],[22,27],[27,69],[32,32],[82,61],[99,74],[23,26],[21,32],[35,76],[25,54],[25,25],[107,43],[108,42],[121,49],[57,23],[5,6],[-1,6],[1,4],[12,-1],[4,-1],[4,-1],[3,0],[127,36],[99,28],[7,6],[7,16],[7,6],[162,51],[132,42],[104,33],[79,42],[27,-30],[75,-42],[31,-29],[21,-32],[13,-10],[23,-10],[60,-16],[20,-10],[32,-25],[81,-92],[55,-36],[61,-20],[65,-7],[128,6],[140,7],[63,15],[33,1],[100,20],[20,-1],[33,-11],[18,-3],[18,4],[53,25],[60,-16],[55,-24],[56,-9],[61,28],[-105,-123],[-105,-124],[-104,-124],[-105,-123],[-65,-76],[-65,-77],[-64,-76],[-65,-76],[-103,-84],[-103,-84],[-103,-83],[-102,-84],[-43,-35],[-18,-29],[0,-55],[2,-133],[2,-138],[0,-77],[0,-109],[0,-110],[0,-110],[0,-109],[0,-129],[0,-129],[0,-128],[0,-129],[0,-129],[0,-129],[0,-17],[0,-111],[0,-129],[0,-129],[0,-129],[0,-128],[0,-129],[0,-129],[0,-129],[0,-128],[0,-129],[1,-84],[1,-70],[1,-69],[2,-112],[2,-112],[1,-112],[2,-112],[1,-72],[2,-73],[1,-78],[32,-34],[31,-34],[32,-34],[32,-34],[62,-66],[64,-68],[69,-73],[57,-61],[62,-65],[64,-69],[63,-67],[63,-67],[48,-51],[15,-23],[5,-19],[-4,-65]]],
transform:{scale:[.0007995350316031589,.0009708850872694938],translate:[33.890468384000144,-4.677504164999959]}},m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();