!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo={type:"Topology",objects:{zmb:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Luapula"},id:"ZM.LP",arcs:[[0,1,2]]},{type:"Polygon",properties:{name:"Northern"},id:"ZM.NO",arcs:[[3,-3,4]]},{type:"Polygon",properties:{name:"Central"},id:"ZM.CE",arcs:[[5,6,7,8,9,10,11,12]]},{type:"Polygon",properties:{name:"Copperbelt"},id:"ZM.CO",arcs:[[-11,13,14]]},{type:"Polygon",properties:{name:"Eastern"},id:"ZM.EA",arcs:[[15,-6,16,17]]},{type:"Polygon",properties:{name:"Lusaka"},id:"ZM.LS",arcs:[[-16,18,19,-7]]},{type:"Polygon",properties:{name:"North-Western"},id:"ZM.NW",arcs:[[-14,-10,20,21]]},{type:"Polygon",properties:{name:"Southern"},id:"ZM.SO",arcs:[[-20,22,23,-8]]},{type:"Polygon",properties:{name:"Western"},id:"ZM.WE",arcs:[[-9,-24,24,-21]]},{type:"Polygon",properties:{name:"Muchinga"},id:"ZM.MU",arcs:[[-17,-13,25,-1,-4,26]]}]}},arcs:[[[7247,6650],[-3,-6],[-1,-3],[0,-4],[0,-11],[-1,-6],[-7,-22],[-1,-12],[0,-6],[2,-5],[3,-4],[1,-4],[0,-6],[-147,-219],[-12,-13],[-14,-7],[-362,-69],[-5,-2],[-23,-3],[-9,-12],[-4,-14],[1,-19],[6,-33],[-3,-45],[3,-18],[7,-14],[17,-21],[6,-13],[-1,-12],[-2,-14],[-2,-13],[5,-10],[-3,-6],[-3,-6],[-3,-5],[-3,-3],[-3,-2]],[[6686,5988],[0,1],[-37,-3],[-18,-8],[-29,-24],[-45,-24],[-14,-3],[-10,6],[-15,17],[-11,5],[-11,0],[-8,-4],[-13,-13],[-16,-12],[-38,-15],[-13,-11],[-5,-13],[-11,-40],[-3,-23],[-3,-10],[-2,-8],[4,-8],[25,-38],[14,-6],[13,-1],[10,-5],[4,-18],[-3,-19],[-9,-15],[-12,-9],[-17,-2],[-6,2],[-4,4],[-3,5],[-4,3],[-6,0],[-12,-1],[-6,1],[-22,9],[-36,23],[-7,2],[-34,1],[-10,1],[-8,4],[-11,11],[-15,20],[-9,5],[-69,-2],[-21,-6],[-18,-9],[-15,-11],[-9,20],[-13,-3],[-15,-8],[-12,1],[-9,5],[-7,0],[-4,2],[-1,14],[-2,10],[-70,149],[-22,32],[-51,33],[-7,12],[-4,19],[-10,19],[-11,17],[-11,11],[-30,19],[-12,11],[-5,15],[-2,28],[-8,8],[-30,-5],[-18,6],[-42,26],[-11,10],[-15,38],[-12,10],[-20,-13],[-3,9],[-10,20],[-4,5],[-7,8],[-8,6],[-9,0],[-11,-7],[-10,16],[-7,49],[-10,11],[-14,5],[-7,12],[-8,70],[-3,11],[-12,13],[-6,10],[3,4],[1,10],[-15,60],[-5,9],[-12,15],[-7,11],[-10,30],[-5,8],[-4,15],[-1,54],[3,18],[44,76],[3,4],[19,17],[4,3],[-6,38],[3,22],[8,15],[9,11],[15,38],[-1,35],[1,10],[3,6],[7,13],[2,10],[0,12],[-9,54],[0,10],[12,20],[11,24],[4,11],[0,10],[-1,22],[1,10],[5,9],[15,18],[3,11],[2,11],[8,20],[2,11],[0,45],[4,24],[8,23],[11,22],[15,18],[6,10],[6,22],[13,25],[4,5],[5,3],[11,-1],[5,1],[12,11],[13,16],[11,19],[5,20],[-5,7],[-20,18],[-5,10],[2,12],[5,21],[-1,12],[-21,27],[-11,18],[0,14],[4,10],[-8,15],[4,10],[5,9],[3,12],[2,13],[-1,12],[-6,19],[-8,16],[-7,18],[-2,26],[2,21],[5,16],[2,16],[-4,20],[-2,0],[-7,5],[-2,2],[-3,6],[-1,10],[-2,5],[-29,29],[8,19],[21,29],[13,31],[8,39],[2,81],[-6,73],[3,22],[27,65],[8,44],[8,18],[16,8],[2,4],[-23,27],[-2,15],[-5,6],[-8,3],[-8,7],[-12,19],[-5,21],[-1,47],[-2,11],[-8,21],[-1,10],[-4,13],[-16,22],[-4,9],[5,6],[19,7],[6,8],[-27,-1],[-11,5],[-4,13],[0,18],[-2,13],[-3,11],[-7,11],[-20,17],[-10,12],[-5,16],[2,14],[1,19],[-1,19],[-18,25],[-30,19],[-42,10],[-37,17],[-13,37],[17,37],[36,28],[78,44],[80,68],[142,165],[109,170],[32,87],[4,90],[-36,90],[-3,19],[22,11],[102,18],[148,27],[135,24],[180,32]],[[6495,9818],[-1,-1],[-6,-21],[-25,-58],[-10,-17],[-3,-4],[-12,-13],[-8,-8],[-3,-1],[-26,-8],[-33,-14],[-7,-5],[-4,-3],[-8,-11],[-10,-16],[-25,-59],[-9,-17],[-3,-4],[-6,-6],[-3,-4],[-2,-5],[-27,-78],[-19,-97],[-28,-85],[-5,-9],[-11,-15],[-3,-3],[-8,-5],[-26,-9],[-8,-4],[-3,-3],[-5,-8],[-20,-81],[-2,-5],[-4,-1],[-11,0],[-4,-1],[-3,-4],[-2,-5],[-2,-12],[-2,-6],[-2,-21],[5,-11],[4,-5],[12,-9],[15,-9],[9,-4],[8,-5],[6,-6],[6,-7],[7,-6],[7,-4],[9,-1],[7,2],[5,3],[4,0],[6,-3],[5,-3],[3,-3],[11,-15],[2,-5],[4,-9],[3,-4],[3,-4],[3,-2],[10,-2],[5,-2],[3,-3],[5,-8],[3,-3],[4,-1],[4,1],[7,4],[4,0],[3,-3],[2,-7],[1,-11],[3,-8],[2,-7],[2,-7],[-4,-25],[-1,-6],[1,-10],[15,-61],[-2,-9],[-2,-7],[-2,-6],[-2,-4],[-4,-3],[-4,-2],[-5,-1],[-3,-3],[-2,-5],[-1,-6],[-3,-24],[1,-8],[1,-10],[3,-8],[7,-16],[18,-57],[4,-8],[5,-6],[3,-3],[7,-6],[3,-2],[5,0],[4,1],[8,5],[5,1],[5,0],[11,-1],[4,-2],[4,-2],[13,-13],[3,-4],[13,-32],[6,-7],[5,-4],[4,-2],[3,-3],[3,-4],[2,-3],[2,-2],[3,-1],[10,-2],[5,-3],[3,-7],[5,-11],[5,-5],[5,-3],[10,-2],[4,-2],[8,-5],[4,-2],[4,0],[14,3],[5,-1],[18,-7],[15,-3],[4,-2],[4,-2],[15,-10],[27,-10],[16,-2],[5,-2],[3,-2],[6,-7],[4,-2],[5,0],[10,1],[5,0],[10,-1],[5,0],[10,1],[4,0],[4,-2],[4,-2],[3,-4],[2,-4],[9,-18],[9,-17],[12,-14],[10,-9],[3,-6],[3,-8],[4,-17],[3,-8],[4,-5],[8,-4],[10,-2],[8,-4],[4,-2],[4,-4],[3,-3],[2,-4],[2,-5],[10,-35],[1,-9],[-4,-23],[-22,-66],[-1,-6],[-1,-7],[2,-11],[2,-6],[14,-39],[8,-17],[2,-4],[0,-5],[-3,-5],[-14,-13],[-6,-7],[-5,-8],[-5,-10],[-3,-10],[-14,-76],[-3,-10],[-3,-4],[-2,-3],[-2,-2],[-12,-11],[-8,-5],[-4,-1],[-10,-2],[-5,1],[-11,2],[-5,2],[-13,7],[-9,4],[-5,1],[-39,2],[-20,-3],[-4,-1],[-17,-9],[-7,-5],[-13,-13],[-26,-31],[-7,-6],[-4,-3],[-20,-9],[-31,-18],[-27,-8],[-8,-4],[-3,-2],[-5,-4],[-3,-3],[-4,-7],[-11,-14],[-5,-4],[-25,-17],[-3,-3],[-3,-4],[-2,-8],[-2,-32],[-3,-10],[-3,-6],[-4,-2],[-3,-4],[-5,-8],[-14,-27],[-1,-7],[1,-7],[5,-13],[3,-6],[7,-9],[1,-5],[-2,-7],[-3,-6],[-34,-53],[-4,-10],[-2,-5],[-4,-24],[-4,-6],[-5,-2],[-5,-1],[-18,2],[-5,0],[-6,-3],[-5,-11],[-9,-9],[-104,-77],[-4,-6],[-2,-7],[1,-118],[-2,-7],[-2,-5],[-38,-41],[-17,-22],[-7,-14],[-1,-4],[1,-5],[7,-4],[65,-27],[8,-6],[77,-90],[3,-2],[5,-2],[5,-2],[10,1],[9,2],[5,0],[5,0],[4,-2],[40,-25],[5,-2],[5,0],[4,3],[2,5],[1,6],[2,26],[4,25],[1,6],[-1,7],[-8,13],[-6,8],[-10,16],[-3,7],[-3,11],[-1,10],[1,8],[1,6],[1,6],[-2,7],[-16,28],[-5,16],[10,13],[14,12],[236,146],[6,2],[5,-1],[4,-2],[14,-13],[28,-18],[33,-13],[9,-5],[16,-11],[4,-4],[4,-8],[23,-52],[6,-8],[3,-3],[19,-15],[5,-4],[5,-7],[23,-46],[2,-5],[1,-5],[1,-11],[4,-55],[-50,-380],[6,6],[8,17],[25,11],[4,7],[1,10],[3,11],[5,12],[6,8],[29,24],[10,14],[4,22],[6,8],[6,4],[12,3],[106,-3],[15,-3],[13,-9],[267,-224],[3,-4],[2,-5],[1,-5],[0,-7],[0,-3],[-1,-2]],[[8765,9048],[34,-121],[43,-110],[7,-102],[-93,9],[-86,25],[-86,9],[-100,-17],[-72,-43],[-57,-42],[-65,-9],[-64,26],[-22,-68],[-57,31],[-65,-6],[-57,43],[-43,-34],[-50,-51],[-43,-26],[-22,-59],[-57,-85],[64,-76],[65,-128],[36,-110],[28,-119],[65,-51],[86,-42],[29,-34],[2,-253],[-55,-13],[-4,4],[-4,8],[-6,7],[-9,2],[-8,-5],[-7,-19],[-6,-4],[-11,-3],[-29,-18],[-16,-7],[-22,-3],[-9,-4],[-4,-10],[6,-8],[1,-7],[-4,-3],[-13,0],[-6,-3],[-2,-7],[3,-40],[-3,-12],[-6,-7],[-17,-12],[-6,-8],[-9,9],[-17,7],[-18,4],[-14,0],[-16,-3],[-12,-6],[-38,-33],[2,-7],[11,-13],[5,-8],[2,-7],[3,-4],[10,-1],[7,2],[6,4],[7,1],[6,-7],[4,-21],[-14,-7],[-31,0],[-7,-10],[-1,-9],[4,-10],[17,-22],[4,-6],[-2,-4],[-3,-8],[-4,-7],[-6,-5],[-5,-7],[-1,-7],[-1,-6],[-38,-39],[-14,-6],[-18,0],[8,10],[3,10],[-1,11],[-5,11],[-9,9],[-4,-10],[-1,-24],[-13,-4],[-16,5],[-7,11],[9,13],[-18,0],[-11,-25],[-7,-33],[-8,-22],[-7,-6],[-24,-10],[-9,-1],[-6,-3],[-9,-5],[-11,-5],[-12,-1],[17,-21],[-4,-9],[-12,2],[-11,14],[-8,-4],[-8,-9],[-4,-11],[-2,-11],[-15,-35],[-70,-102],[-18,-44],[-7,-25],[-6,-9],[-19,-21],[-5,-3],[-9,-14],[-4,-3],[-20,4],[-10,1],[-8,-5],[-7,-9],[-5,-10],[-5,-20],[-4,-9],[-5,-8],[-7,-4],[-11,-2],[-15,-8],[-8,-18],[-6,-23],[-9,-21],[-20,-18],[-12,-12],[-25,-11]],[[6495,9818],[14,3],[227,41],[227,40],[172,31],[220,40],[146,26],[22,-96],[43,-100],[54,-93],[58,-72],[28,-26],[35,-24],[38,-16],[36,-1],[18,11],[17,14],[19,12],[20,0],[10,-9],[16,-25],[10,-8],[11,-2],[7,3],[6,5],[45,18],[10,3],[6,3],[2,4],[3,1],[7,-5],[3,-7],[3,-18],[3,-7],[11,-9],[11,-1],[12,1],[13,-1],[13,-8],[19,-25],[13,-10],[33,-11],[17,-17],[6,-26],[0,-37],[7,-44],[19,-31],[83,-74],[14,-7],[17,0],[18,8],[29,18],[20,2],[127,-41],[-16,-41],[0,-50],[18,-40],[36,-10],[18,9],[12,11],[14,6],[21,-3],[25,-17],[9,-3],[8,1],[16,4],[8,0],[10,-6],[8,-5],[31,-38],[17,-14],[17,-7]],[[8079,4692],[-19,-16],[-40,-10],[-19,-13],[-35,-31],[-8,-10],[-3,-3],[-6,5],[-8,1],[-7,-2],[-3,-8],[-4,-14],[-16,-22],[-8,-26],[-11,-2],[-23,6],[-8,-3],[-10,-15],[-23,-11],[-11,-20],[-15,-74],[-5,-14],[-21,-25],[-2,-2],[-14,-17],[5,-16],[-9,-8],[-25,-3],[-10,-6],[-22,-19],[-12,-4],[-6,-7],[0,-17],[-3,-16],[-19,-8],[-1,-4],[-14,-24],[-13,-14],[-6,-8],[-5,-13],[-8,-5],[-8,-3],[-663,-2],[0,-3],[3,-3],[4,-2],[10,-3],[17,-3],[5,-1],[5,-4],[4,-10],[23,-81],[3,-7],[5,-3],[22,-11],[40,-13],[9,-5],[4,-3],[10,-10],[4,-3],[4,-2],[4,-3],[2,-4],[4,-9],[5,-9],[1,-4],[1,-6],[1,-11],[0,-8],[-1,-8],[-4,-13],[-5,-10],[-8,-17],[-6,-9],[-10,-9],[-22,-13],[-7,-7],[-12,-14],[-8,-5],[-4,-4],[-3,-6],[-4,-11],[-4,-8],[-6,-7],[-25,-25],[-33,-51],[-6,-8],[-5,-3],[-5,-1],[-5,0],[-10,2],[-5,-1],[-4,-2],[-3,-6],[-4,-10],[-2,-13],[-3,-9],[-5,-6],[-4,-4],[-5,-2],[-7,-5],[-9,-11],[-3,-7],[-3,-11],[-2,-21],[-5,-19],[-3,-2],[-3,-7],[-4,-11],[-6,-37],[-5,-51],[5,-11],[18,-18],[21,-29],[4,-7]],[[6901,3441],[-43,3],[-8,5],[-9,10],[-3,3],[-5,1],[-6,0],[-5,-1],[-6,-2],[-8,-6],[-9,-1],[-7,-4],[-6,3],[-3,4],[-3,3],[-4,-1],[-5,-8],[-6,-19],[-2,-5],[-3,-5],[-2,-3],[-6,-4],[-7,-2],[-13,-2],[-8,0],[-7,1],[-14,4],[-6,0],[-6,-4],[-17,-16],[-4,-3],[-5,-2],[-18,2],[-5,0],[-68,-31],[-4,-4],[-6,-6],[-7,-7],[-50,-33],[-7,-7],[-5,-3],[-28,-1],[-5,2],[-6,-3],[-7,-5],[-12,-20],[-5,-13],[-3,-23],[-10,-43],[-23,-50],[-57,-12],[-70,-2],[-72,21],[-150,-53],[-44,-10],[-46,-23],[-106,-109],[-68,-27],[-75,-11],[-74,-2],[-141,43],[-24,-17],[-28,-63],[-22,-71],[-23,-47],[-26,-11],[-50,12],[-144,54],[-146,27],[-11,0],[-11,-2],[-8,-4],[-43,-33],[-7,-12],[-2,-10],[6,-14],[11,-9],[20,-6],[46,-8],[13,-5],[11,-12],[22,-50],[12,-46],[2,-17],[1,-10],[-1,-5],[-1,-6],[-8,-12],[-15,-14],[-91,-29],[-37,-89]],[[4939,2451],[-3,-3],[-5,-9],[-3,-3],[-6,2],[-9,5],[-6,1],[-9,-1],[-6,-3],[-8,-10],[-6,8],[-3,9],[-5,7],[-4,5],[-5,4],[-10,11],[-2,4],[-1,5],[2,4],[3,3],[3,3],[3,3],[2,4],[0,4],[-3,3],[-1,5],[-1,6],[1,19],[0,6],[-3,4],[-14,6],[-3,4],[-1,5],[-1,6],[-1,6],[-1,4],[-5,4],[-44,17],[-4,2],[-1,1],[-1,4],[2,9],[-2,5],[-5,3],[-13,-1],[-7,-3],[-4,-5],[-1,-5],[-1,-8],[-2,-4],[-11,-7],[-2,-4],[-2,-6],[0,-6],[-2,-5],[-2,-5],[-2,-4],[-3,-2],[-5,-4],[-6,-6],[-3,-4],[-3,-3],[-5,-2],[-4,-1],[-4,-3],[-2,-4],[-2,-5],[-2,-5],[-4,-2],[-4,0],[-14,8],[-16,6],[-4,0],[-3,-6],[-3,-7],[-71,-148],[-2,-8],[-14,-10],[-9,15],[-10,3],[0,12],[-44,220],[-22,15],[-200,12],[-8,3],[4,7],[20,20],[0,6],[-9,9],[-113,76],[-34,8],[-50,7],[-1215,38]],[[2860,2805],[1,70],[-4,37],[1,9],[1,6],[3,4],[26,26],[6,8],[3,3],[4,3],[5,0],[5,0],[5,-1],[5,-1],[6,1],[6,5],[3,6],[1,6],[-2,29],[1,10],[3,5],[13,15],[12,21],[5,11],[3,10],[1,44],[0,6],[-2,5],[-25,52],[-2,8],[0,10],[0,20],[2,10],[2,7],[7,10],[5,10],[2,9],[1,12],[2,7],[2,6],[3,4],[3,3],[4,3],[9,3],[5,4],[2,5],[0,6],[0,12],[2,32],[-5,20],[-1,16],[1,9],[2,7],[4,9],[3,5],[2,4],[8,24],[6,13],[15,27],[5,8],[3,3],[4,3],[3,2],[6,2]],[[3051,3528],[21,15],[11,11],[15,3],[15,1],[473,-67],[5,-6],[3,-4],[6,-14],[7,-9],[9,-6],[13,-4],[22,-4],[38,-21],[6,-8],[5,-9],[6,-8],[9,-3],[9,2],[30,15],[5,52],[10,21],[35,48],[54,49],[36,62],[7,10],[11,10],[14,6],[14,2],[9,-3],[16,-9],[10,-2],[5,2],[3,5],[4,5],[9,2],[71,-6],[16,5],[30,24],[12,5],[106,5],[14,15],[4,5],[6,17],[-2,112],[-1,5],[0,5],[0,5],[5,9],[15,25],[2,5],[2,6],[1,6],[0,6],[-1,12],[-2,11],[-7,19],[-7,13],[-42,62],[-10,10],[-32,21],[-4,4],[-1,4],[4,8],[9,4],[17,7],[8,4],[4,6],[1,4],[1,5],[-2,39],[1,25],[3,27],[7,22],[18,30],[9,20],[1,6],[1,6],[1,6],[-2,5],[-3,3],[-26,15],[-7,5],[-4,4],[-5,7],[-3,4],[-2,5],[-3,15],[-3,10],[-5,8],[-5,8],[-6,8],[-7,5],[-7,4]],[[4166,4407],[349,-13],[74,-11],[1,-5],[-1,-6],[-1,-6],[-1,-6],[0,-5],[7,-25],[2,-5],[2,-4],[12,-13],[3,-4],[2,-5],[1,-5],[2,-17],[3,-9],[1,-6],[2,-17],[5,-21],[3,-6],[5,-6],[3,-2],[9,-8],[15,7],[23,20],[19,25],[8,25],[-6,22],[-1,15],[4,12],[8,6],[11,3],[10,-3],[6,-17],[4,-6],[5,-6],[6,-1],[5,5],[1,8],[-1,7],[1,7],[27,41],[8,15],[0,7],[-2,6],[-1,6],[3,9],[5,4],[8,1],[7,0],[4,2],[2,13],[-4,13],[0,13],[13,16],[14,-2],[6,-1],[4,-3],[15,-11],[9,-5],[10,-4],[72,-6],[16,-6],[13,-14],[57,-164],[9,-19],[11,-18],[17,-17],[9,-5],[8,-2],[4,2],[4,2],[11,8],[6,3],[7,1],[23,1],[7,2],[4,2],[7,2],[10,2],[48,1],[29,4],[5,2],[4,2],[7,5],[3,4],[3,3],[4,4],[6,4],[11,2],[7,0],[7,-1],[53,-17],[11,-1],[7,0],[17,6],[16,3],[27,1],[9,-1],[40,-13],[15,-2],[56,5],[6,1],[4,2],[8,4],[11,8],[4,2],[34,6],[69,3],[9,4],[4,6],[-6,111],[1,5],[2,4],[3,4],[123,109],[6,7],[15,26],[18,21],[7,7],[29,20],[34,15],[4,2],[3,3],[55,80],[33,39],[3,4],[2,3],[2,7]],[[6058,4739],[7,0],[21,4],[21,1],[19,-11],[8,-12],[5,-16],[8,-11],[13,-3],[9,5],[16,23],[12,8],[19,9],[50,39],[121,54],[17,12],[50,55],[20,10],[20,-1],[15,-11],[15,-14],[16,-12],[11,-1],[7,3],[7,-1],[7,-11],[3,-10],[-2,-11],[-3,-10],[-4,-8],[-7,-5],[-15,-4],[-6,-8],[-1,-8],[2,-21],[-2,-9],[-5,-3],[-8,-2],[-7,-5],[0,-14],[3,-12],[5,-11],[5,-11],[8,-8],[13,-9],[18,-9],[34,-11],[55,0],[13,2],[13,32],[1,112],[0,151],[0,144],[0,151],[0,177],[1,152],[0,168],[0,118],[0,108]],[[6686,5984],[16,4],[4,2],[3,3],[3,4],[8,13],[4,9],[2,6],[1,6],[4,51],[1,6],[2,5],[3,4],[3,3],[3,3],[70,34],[18,5],[20,3],[4,0],[6,-4],[7,-9],[15,-21],[16,-34],[1,-5],[1,-5],[2,-18],[1,-5],[1,-5],[3,-7],[13,-20],[4,-8],[5,-8],[6,-6],[13,-9],[7,-4],[7,-3],[11,-3],[5,-2],[5,-6],[8,-17],[4,-5],[19,-9],[11,-2],[4,-2],[4,-2],[4,-3],[4,-3],[14,-5],[12,-8],[9,-5],[21,-4],[5,-2],[12,-8],[4,-2],[5,-1],[15,0],[7,-3],[8,-4],[12,-8],[8,-3],[6,0],[15,3],[10,0],[5,-1],[5,-4],[4,-5],[5,-11],[4,-6],[5,-6],[8,-4],[6,-1],[5,1],[9,3],[9,1],[10,-2],[5,-3],[4,-5],[5,-11],[2,-8],[0,-8],[-1,-45],[-3,-12],[-8,-20],[-1,-5],[-1,-6],[0,-12],[1,-11],[1,-5],[1,-5],[4,-5],[6,-6],[10,-8],[22,-12],[9,-5],[4,-5],[5,-8],[3,-13],[0,-8],[-1,-7],[-7,-21],[-1,-6],[0,-6],[1,-5],[6,-9],[10,-11],[24,-22],[12,-7],[8,-3],[12,7],[7,6],[7,6],[10,16],[4,2],[4,0],[5,-2],[4,-5],[11,-31],[6,-10],[6,-7],[10,-7],[17,-9],[4,-4],[3,-5],[2,-10],[1,-7],[-1,-7],[-2,-5],[-2,-5],[-5,-8],[-12,-16],[-2,-4],[-9,-26],[-1,-5],[1,-5],[13,-52],[1,-11],[0,-6],[-4,-25],[3,-7],[6,-7],[52,-42],[17,-10],[8,-6],[8,-5],[5,-1],[10,0],[28,7],[6,0],[5,-1],[30,-10],[11,-1],[6,-1],[23,-10],[7,-1],[5,1],[10,0],[11,-10],[64,-72],[11,-9],[5,-1],[11,0],[5,1],[5,1],[4,2],[3,3],[3,4],[3,4],[2,4],[8,22],[1,2],[1,9],[1,19],[2,5],[2,4],[3,3],[8,4],[17,7],[5,1],[38,0],[11,-2],[5,-2],[30,-16],[7,-6],[4,-4],[3,-4],[5,-14],[3,-16],[14,-221],[5,-19],[16,-37],[20,-25],[11,-16],[1,-10],[2,-18],[-1,-34],[0,-50]],[[4166,4407],[-29,22],[-13,13],[-3,6],[-4,9],[-4,13],[-1,11],[4,66],[3,16],[6,18],[4,9],[3,4],[17,15],[3,4],[2,5],[3,7],[9,49],[3,8],[3,6],[3,3],[8,4],[17,7],[8,5],[4,4],[4,6],[10,27],[2,3],[4,4],[19,11],[5,5],[5,9],[9,18],[3,10],[3,9],[0,6],[-2,18],[-2,10],[-10,23],[-1,7],[-2,8],[0,17],[2,10],[2,7],[2,5],[2,4],[3,4],[7,7],[47,34],[7,6],[8,11],[6,11],[3,8],[2,7],[23,116],[0,1],[-4,2],[-4,2],[-5,1],[-6,0],[-19,-4],[-5,2],[-5,4],[-3,8],[-3,5],[-3,3],[-4,1],[-3,4],[-3,9],[-2,4],[-3,4],[-4,2],[-4,2],[-2,0],[-15,1],[-5,2],[-4,3],[-7,6],[-4,2],[-23,11],[-4,3],[-3,3],[-6,8],[-3,3],[-8,6],[-9,5],[-4,3],[-4,4],[-2,6],[-1,5],[1,6],[8,13],[8,22],[3,6],[3,9],[1,6],[-2,6],[-2,4],[-16,20],[-4,2],[-4,4],[-4,5],[-2,9],[-3,6],[-3,5],[-4,3],[-3,4],[-4,5],[-2,9],[0,7],[6,11],[3,8],[8,44],[2,7],[3,6],[7,13],[6,7],[8,5],[4,1],[5,1],[15,2],[4,1],[9,4],[7,6],[6,6],[8,12],[9,19],[30,44],[7,5],[8,4],[34,7],[5,2],[4,4],[0,4],[-2,3],[-6,8],[0,1],[-8,23],[-2,7],[-1,8],[0,14],[1,8],[3,7],[2,6],[4,10],[0,31],[-1,6],[-10,35],[-1,6],[-1,7],[4,2],[4,-1],[19,-5],[24,-1],[8,1],[16,3],[10,2],[108,-13],[5,3],[6,4],[10,15],[5,5],[11,7],[4,3],[13,7],[11,17],[5,4],[4,3],[9,3],[4,2],[6,6],[13,13],[7,6],[4,1],[12,4],[8,-7],[13,-9],[12,-10],[0,-6],[-2,-7],[3,-6],[24,-5],[10,-5],[3,-2],[12,19],[13,5],[3,2],[37,29],[3,3]],[[4797,5899],[4,-4],[37,-47],[24,-9],[38,-4],[39,2],[27,8],[17,15],[9,14],[11,8],[22,-2],[17,-8],[49,-34],[12,-18],[4,-25],[5,-22],[19,-7],[16,3],[12,1],[63,-7],[8,-5],[3,-9],[2,-10],[4,-9],[21,-19],[12,6],[13,15],[21,6],[17,-9],[13,-14],[14,-12],[23,0],[17,3],[17,-1],[16,-6],[15,-10],[71,-71],[12,-22],[16,-50],[11,-23],[29,-35],[8,-19],[0,-27],[-10,-16],[-14,-12],[-8,-14],[6,-26],[17,-27],[19,-22],[14,-24],[3,-52],[9,-10],[12,0],[14,7],[8,12],[12,29],[12,8],[39,-10],[35,-41],[58,-87],[15,-16],[6,-11],[7,-36],[4,-5],[12,-4],[3,-5],[-1,-5],[-4,-11],[0,-4],[1,-5],[-2,-11],[1,-5],[4,-5],[5,-1],[4,-1],[3,-3],[8,-35],[8,-6],[17,-1],[8,-3],[10,-15],[26,-150],[14,-42],[26,-29],[11,-16],[14,-4],[16,4],[15,7],[16,0]],[[7041,3126],[-2,2],[0,121],[-5,30],[-4,3],[-9,11],[-7,12],[-8,12],[-7,6],[-13,7],[-5,4],[-4,5],[-34,52],[-4,4],[-4,3],[-19,9],[-6,6],[-3,8],[-3,13],[-3,7]],[[8079,4692],[9,7],[19,7],[14,-3],[17,-5],[18,-3],[14,7],[67,60],[21,31],[10,38],[7,40],[15,59],[4,9],[18,5],[7,5],[4,7],[2,10],[6,20],[13,14],[12,15],[3,24],[5,0],[2,-4],[2,-5],[2,-5],[14,10],[19,12],[11,10],[9,13],[5,3],[18,2],[6,5],[4,6],[2,9],[11,-5],[7,2],[7,5],[10,5],[-5,19],[-1,21],[2,20],[4,16],[1,5],[-1,11],[0,4],[5,5],[5,1],[5,-1],[3,2],[11,13],[4,6],[3,9],[9,1],[18,6],[18,8],[7,10],[3,18],[13,46],[8,16],[8,7],[7,2],[5,4],[4,21],[8,20],[2,11],[1,13],[5,33],[0,34],[3,10],[4,7],[3,8],[4,25],[9,12],[18,99],[0,44],[2,8],[8,14],[2,6],[1,15],[4,6],[5,6],[8,10],[6,12],[11,30],[11,13],[42,39],[3,10],[15,21],[7,21],[4,22],[2,21],[70,-18],[51,0],[43,51],[14,59],[36,43],[50,0],[108,8],[194,68],[115,43],[36,93],[57,111],[0,48],[17,28],[34,17],[41,13]],[[9683,6506],[8,-130],[-3,-37],[-11,-38],[0,-16],[10,-23],[3,-14],[-3,-16],[-5,-16],[-8,-52],[-34,-92],[-3,-34],[2,-34],[6,-16],[24,-27],[10,-14],[11,-33],[15,-71],[16,-30],[30,-12],[63,22],[34,-13],[14,0],[10,-4],[1,-21],[-7,-16],[-4,-3],[-22,-15],[-7,-17],[-13,-3],[-3,-7],[-1,-25],[-8,-12],[-26,-12],[-9,-11],[-14,-23],[-17,-21],[-20,-14],[-19,-5],[-7,3],[-7,5],[-8,5],[-11,1],[-22,-5],[-7,-3],[-10,-13],[-14,-35],[-12,-10],[-9,-3],[-5,-3],[-4,-4],[-4,-4],[-5,-5],[-3,-7],[-4,-5],[-9,-1],[-4,5],[-10,23],[-6,7],[-8,2],[-6,-1],[-14,-4],[-7,-4],[-8,-8],[-7,-4],[-6,7],[-4,8],[-4,5],[-6,2],[-7,-4],[-12,-12],[-9,-14],[-26,-61],[-31,-53],[-12,-27],[-3,-18],[5,-74],[6,-11],[23,-17],[21,-23],[7,-24],[-4,-29],[-23,-61],[-14,-112],[1,-18],[4,-17],[7,-17],[13,-23],[3,-12],[-5,-14],[-25,-9],[-22,-31],[-15,-40],[-12,-57],[-12,-54],[-15,-39],[-24,-15],[-16,-3],[-7,-11],[-2,-16],[1,-18],[-4,-23],[-11,-9],[-32,-5],[-11,-9],[-23,-23],[-11,-2],[-16,7],[-12,1],[-9,-8],[-3,-21],[10,-32],[27,-7],[31,-1],[23,-11],[32,-53],[5,-12],[-7,-11],[-32,-17],[-9,-12],[5,-27],[25,-13],[31,-7],[26,-11],[13,-13],[42,-61],[4,-15],[2,-15],[5,-16],[7,-7],[15,1],[7,-4],[5,-9],[1,-5],[-2,-6],[-3,-51],[4,-15],[20,-30],[16,4],[27,51],[11,15],[12,7],[30,10],[8,9],[8,26],[8,5],[13,-11],[26,-58],[15,-21],[-62,-24],[-75,-30],[-107,-42],[-148,-58],[-143,-57],[-130,-51],[-90,-36],[-79,-31],[-69,-15],[-32,-11],[-65,-34],[-86,-33],[-112,-43],[-123,-47],[-58,-35],[-80,-49],[-73,-31],[-89,-25],[-110,-31],[-89,-25],[-15,-1],[-121,-32],[-85,-23],[-119,-50],[-73,-30],[-90,-39],[-80,-45],[-63,-37],[-75,-10],[-14,-4],[0,-1]],[[7041,3126],[-1,-7],[6,-30],[0,-45],[5,-44],[31,-126],[22,-49],[27,-20],[13,-6],[11,-13],[8,-18],[6,-19],[4,-21],[2,-66],[3,-11],[13,-20],[2,-10],[-2,-10],[-16,-35],[-5,-23],[2,-14],[24,-29],[5,-8],[9,-30],[-2,-3],[-12,-5],[-34,-16],[-24,-1],[-27,14],[-14,7],[-22,3],[-7,-3],[-13,-13],[-7,-5],[-13,-4],[-10,4],[-22,18],[-34,8],[-35,-6],[-34,-10],[-33,-7],[-37,5],[-74,23],[-38,4],[-19,-5],[-35,-18],[-37,-7],[-49,-19],[-21,-3],[-18,3],[-16,5],[-18,3],[-21,-7],[-32,-31],[-15,-11],[-74,-7],[-13,-3],[-188,-100],[-30,-37],[-8,-5],[-18,-5],[-16,-12],[-14,-14],[-8,-11],[-19,-39],[-10,-12],[-21,-5],[-39,-1],[-18,-3],[-4,-3]],[[5957,2138],[0,1],[-13,12],[-18,14],[-20,5],[-14,-17],[-10,6],[-9,0],[-7,-4],[-3,-10],[-10,7],[-6,-3],[-4,-6],[-4,-4],[-8,1],[-13,4],[-6,1],[-15,-2],[-5,1],[-8,4],[-58,53],[-11,6],[-17,1],[-34,-6],[-18,4],[-14,11],[-37,44],[-14,13],[-14,5],[-32,5],[-10,5],[-17,14],[-9,6],[-17,5],[-85,-7],[-14,-6],[-25,-20],[-9,-2],[-8,6],[-27,26],[-14,9],[-84,38],[-18,3],[-16,6],[-9,14],[-8,18],[-11,17],[-32,17],[-35,2],[-34,-9],[-28,-17],[-8,5],[-5,6],[-2,8],[2,8],[-16,12],[-20,4],[-37,-1]],[[3051,3528],[-22,1],[-10,-4],[-4,-3],[-20,-11],[-15,-2],[-12,0],[-8,1],[-5,2],[-4,3],[-4,3],[-3,3],[-9,5],[-16,4],[-56,6],[-11,0],[-5,-2],[-15,-9],[-9,-4],[-37,-9],[-13,-5],[-8,-4],[-3,-3],[-17,-7],[-33,-9],[-8,-1],[-41,3],[-8,0],[-8,-2],[-16,-6],[-8,-1],[-6,-1],[-6,2],[-7,2],[-7,4],[-6,5],[-7,6],[-18,24],[-10,16],[-2,4],[-3,10],[-1,5],[-1,6],[4,33],[0,5],[-2,23],[0,10],[1,3],[2,5],[1,2],[1,2],[3,2],[5,3],[19,8],[22,12],[3,3],[7,6],[9,11],[2,5],[1,6],[2,44],[4,23],[1,2],[1,4],[1,5],[3,4],[5,8],[7,14],[3,11],[3,12],[1,7],[0,36],[-2,8],[-4,8],[-9,13],[-6,5],[-6,2],[-33,-7],[-9,2],[-15,6],[-96,56],[-6,2],[-20,4],[-19,6],[-17,3],[-44,17],[-7,4],[-5,5],[-4,9],[-2,5],[-4,9],[-1,5],[-1,5],[-1,6],[1,26],[0,5],[-7,19],[-2,11],[-2,4],[-2,5],[-5,6],[-10,11],[-20,27],[-4,5],[-3,1],[-3,0],[-7,-5],[-4,-3],[-4,-1],[-4,-2],[-3,-4],[-2,-5],[-1,-6],[0,-11],[-2,-3],[-1,-1],[-5,-1],[-2,1],[-1,1],[-1,3],[0,5],[2,19],[-1,6],[-3,1],[-4,0],[-8,3],[-4,1],[-2,-3],[-1,-4],[2,-10],[0,-5],[-2,-4],[-3,-2],[-4,1],[-9,4],[-5,1],[-10,0],[-3,4],[-2,5],[-7,10],[-10,7],[-7,5],[-4,5],[-4,6],[-19,23],[-3,-1],[-3,-3],[-2,-5],[-4,-3],[-8,-2],[-1,-3],[2,-4],[2,-3],[3,-4],[1,-4],[-2,-3],[-4,-1],[-5,-1],[-12,1],[-6,2],[-5,-1],[-3,-3],[-3,-4],[-3,-3],[-6,-1],[-3,3],[-3,5],[-2,5],[-2,5],[-3,-1],[-2,-3],[-3,-4],[-3,-3],[-4,-1],[-5,0],[-32,11],[-7,5],[-5,5],[-1,12],[0,6],[-2,7],[-3,7],[-9,10],[-6,3],[-3,0],[-1,-4],[-1,-5],[-2,-4],[-3,-4],[-2,-3],[0,-5],[2,-4],[5,-5],[2,-4],[1,-5],[-2,-3],[-5,-2],[-7,1],[-10,2],[-6,4],[-17,13],[-13,14],[-4,1],[-5,0],[-11,-8],[-9,-3],[-4,-2],[-5,-8],[-4,-2],[-5,-1],[-8,1],[-5,0],[-5,-2],[-3,-3],[-4,-10],[-3,-4],[-3,-3],[-8,-4],[-4,-2],[-6,-1],[-7,1],[-6,3],[-6,6],[-5,2],[-4,-1],[-3,-4],[-2,-4],[-2,-6],[-2,-6],[-2,-6],[-3,-5],[-5,-2],[-4,2],[-7,9],[-4,1],[-3,-1],[-8,-12],[-9,-10],[-7,-6],[-4,-2],[-8,-3],[-8,-1],[-11,1],[-8,4],[-12,7],[-7,2],[-6,0],[-7,-6],[-4,-2],[-5,-1],[-3,-1],[-4,-3],[-3,0],[-6,1],[-7,1],[-18,8],[-16,0],[-13,4],[-6,11],[0,20],[-9,-6],[-20,-22],[-10,-4],[-9,-2],[-10,-3],[-28,-20],[-15,5],[-14,11],[-13,6],[-14,-4],[-30,-19],[-34,-10],[-31,-27],[-14,-10],[-88,-26],[-24,-15],[-27,-27],[-8,-7],[-11,-4],[-9,0],[-8,3],[-10,1],[-15,-5],[-22,-22],[-14,-8],[-35,0],[4,9],[0,7],[-1,5],[-3,7],[-35,-14],[4,20],[19,34],[11,15],[3,5],[1,5],[-3,3],[-5,1],[-66,8],[-8,3],[-5,3],[-7,6],[-5,8],[-2,4],[-2,5],[-3,11],[0,12],[0,12],[0,3],[-2,6],[-5,5],[-10,7],[-8,3],[-7,0],[-5,0],[-4,-2],[-22,-11],[-15,-4],[-5,0],[-4,2],[-2,1],[-2,4],[-2,4],[-3,11],[-5,15],[-5,8],[-12,15],[-75,56],[-21,10],[-7,1],[-5,-1],[-3,-3],[-22,-24],[-3,-3],[-5,-2],[-40,-2],[-4,-2],[-3,-3],[-2,-5],[-10,-59],[-2,-6],[-6,-11],[-9,-2],[-11,3],[-8,5],[-8,6],[-9,7],[-21,4],[-10,5],[-5,8],[-1,8],[-4,8],[-5,7],[-4,3],[-15,4],[-7,8],[-16,2],[-9,0],[-7,2],[-5,2],[-21,16],[-3,4],[-8,19],[-2,3],[-2,2],[-58,41],[-125,121],[-9,7],[-20,13],[-9,3],[-7,2],[-5,-1],[-4,-1],[-4,-3],[-3,-3],[-9,-18],[-2,-4],[-4,-3],[-11,-8],[-4,-3],[-2,-5],[-4,-9],[-3,-4],[-3,-3],[-25,-13],[-6,-2],[-8,-1],[-14,-1],[-8,2],[-7,2],[-3,2],[-14,12],[-21,25],[-10,8],[-26,14],[-16,3]],[[0,4429],[0,92],[0,153],[0,152],[0,153],[0,152],[106,0],[107,0],[106,0],[106,0],[106,0],[107,0],[106,0],[106,0],[106,0],[107,0],[106,0],[106,0],[106,0],[107,0],[106,0],[106,0],[28,0],[-10,37],[-15,32],[-19,29],[-46,56],[-18,28],[-8,33],[6,40],[17,45],[16,75],[15,71],[11,29],[38,66],[29,49],[7,17],[3,17],[-9,46],[-3,62],[-9,25],[-22,27],[-18,31],[-5,46],[6,142],[5,130],[18,49],[2,11],[-4,24],[-4,76],[-7,25],[-9,19],[-7,19],[0,26],[5,20],[15,40],[24,43],[4,12],[0,16],[-3,25],[1,13],[2,11],[9,15],[27,24],[8,14],[0,12],[-38,98],[-5,26],[3,144],[-16,3],[-5,14],[4,39],[-1,56],[3,18],[5,20],[-3,15],[-16,29],[-6,18],[-6,49],[30,-1],[40,-9],[36,-16],[20,-21],[0,-9],[-5,-18],[0,-9],[2,-12],[6,-20],[1,-12],[-1,-33],[6,-13],[17,3],[17,7],[14,-1],[32,-11],[17,-3],[32,0],[16,-2],[19,-10],[25,-18],[21,-23],[10,-20],[-3,-12],[-7,-9],[-7,-9],[0,-15],[4,-12],[15,-15],[6,-8],[4,-18],[1,-21],[-1,-22],[-3,-20],[-7,-16],[-27,-50],[-11,-14],[-36,-12],[-11,-11],[8,-18],[13,-8],[36,0],[16,-3],[19,-14],[29,-31],[17,-9],[20,-2],[18,4],[36,15],[8,1],[10,-1],[9,2],[6,8],[2,11],[4,10],[5,8],[8,8],[7,4],[19,5],[9,5],[9,10],[15,25],[10,10],[33,16],[64,0],[33,8],[64,37],[35,13],[33,-3],[123,13],[51,12],[81,47],[27,6],[10,-11],[2,-23],[-1,-26],[2,-20],[-1,-10],[-7,-7],[-17,-8],[-10,-9],[4,-9],[10,-9],[4,-8],[-7,-19],[-10,-9],[-7,-12],[1,-24],[6,-21],[20,-39],[6,-18],[-1,-10],[-12,-16],[-3,-9],[0,-10],[2,-18],[23,-82],[8,-18],[7,-8],[6,-4],[7,-1],[11,0],[12,-3],[5,-7],[4,-8],[8,-9],[49,-27],[15,-15],[1,-6],[1,-5],[-1,-5],[-7,-26],[5,-14],[13,-6],[17,0],[8,5],[5,7],[6,6],[11,2],[9,3],[3,10],[3,10],[7,6],[39,-1],[29,-22],[27,-29],[33,-22],[15,-3],[50,0],[9,1],[17,9],[6,2],[10,-2],[4,-3],[3,-5],[47,-44],[18,-8],[12,-11],[12,-31],[15,-8],[11,-2],[148,2],[19,-8],[17,-13],[18,-10],[30,2],[18,-9],[10,-1],[10,5],[16,13],[9,4],[32,7],[33,1],[20,-8],[15,-13],[13,-15],[16,-14],[30,-12],[69,-5],[33,-8],[16,-11],[15,-13],[14,-8],[18,4],[13,15],[10,17],[13,13],[22,0],[39,-5],[37,9],[33,23],[30,33],[16,40],[4,83],[8,39],[13,23],[13,14],[9,15],[1,27],[-10,43],[-1,19],[12,15],[7,1],[19,-3],[8,0],[33,13],[36,3],[25,11],[5,1],[15,-24],[9,-46],[4,-50],[-2,-34],[21,-88],[159,-115],[40,-80],[4,-41],[41,-139],[14,-21],[46,-44]],[[5957,2138],[-12,-6],[-29,-32],[-19,-27],[-2,-7],[-12,-21],[-3,-11],[2,-9],[4,-7],[4,-6],[2,-5],[-5,-35],[-11,-42],[-4,-40],[14,-29],[-20,-54],[-3,-22],[3,-17],[14,-43],[0,-23],[-20,-39],[-4,-8],[-5,-36],[-12,-16],[-34,-29],[-7,-18],[-17,-18],[-7,-8],[-12,-2],[-24,0],[-41,-9],[-310,-139],[-58,-43],[-84,-80],[-78,-38],[-132,-65],[-44,-30],[-34,-43],[-116,-199],[-14,-36],[-18,-80],[-23,-51],[-45,-53],[-88,-91],[-226,-267],[-9,-15],[-1,-10],[1,-26],[2,-23],[-29,-41],[-32,-35],[-25,-27],[-23,-15],[-13,-4],[-41,-2],[-9,-4],[-31,-24],[-20,8],[-81,-42],[-21,-3],[-13,-4],[-11,-8],[-25,-26],[-10,-3],[-12,3],[-49,17],[-14,8],[-12,12],[-13,17],[-11,10],[-14,7],[-23,4],[-36,13],[-65,41],[-40,8],[-31,-5],[-7,2],[-5,6],[-7,5],[-8,5],[-39,5],[-8,3],[-5,6],[-5,10],[-6,8],[-7,4],[-8,-5],[-30,-26],[-8,-5],[-20,-4],[-14,-10],[-15,-4],[-5,-2],[-1,-4],[2,-13],[-1,-4],[-12,-4],[-16,0],[-14,-4],[-5,-12],[-6,7],[-47,-28],[-10,-1],[-36,1],[-52,28],[-9,12],[-6,16],[1,15],[14,5],[-12,18],[-39,18],[-8,16],[-8,11],[-17,12],[-19,11],[-32,9],[-10,11],[-11,8],[-21,-3],[-45,-22],[-58,-13],[-5,-2],[-7,-9],[-5,-2],[-6,1],[-8,5],[-4,1],[-64,0],[-10,2],[-28,12],[-26,-1],[-9,1],[-17,9],[-26,23],[-16,9],[-6,7],[-5,12],[-9,12],[-12,8],[-12,3],[-14,1],[-7,3],[-11,17],[-4,3],[-10,6],[-4,4],[-1,6],[1,13],[-3,6],[-12,15],[-6,0],[-8,-12],[-2,7],[-4,7],[-6,5],[-8,2],[-2,5],[-7,30],[-3,2],[-15,15],[-3,4],[-10,0],[-6,2],[-4,4],[-6,8],[0,7],[6,17],[-3,3],[-9,-1],[-16,-6],[-8,0],[-14,12],[-9,16],[-1,1]],[[2557,516],[8,69],[5,22],[3,4],[5,6],[3,5],[2,2],[9,9],[4,6],[6,11],[2,7],[1,7],[-1,6],[-3,11],[-1,8],[0,55],[2,11],[2,7],[6,8],[14,12],[3,3],[14,28],[9,12],[16,33],[1,7],[0,6],[-4,8],[-2,4],[-3,3],[-3,8],[-1,11],[4,26],[2,12],[4,8],[7,6],[23,14],[5,5],[6,8],[10,18],[5,13],[2,9],[0,6],[-1,5],[-2,5],[-2,6],[-1,8],[2,15],[7,6],[8,5],[9,2],[44,25],[6,7],[8,12],[14,28],[5,14],[2,10],[-25,97],[-4,9],[-2,5],[-1,6],[1,16],[7,49],[1,12],[-1,9],[-3,3],[-7,6],[-8,6],[-5,2],[-10,3],[-90,13],[-5,2],[-5,5],[-11,26],[-2,11],[-3,16],[-1,4],[-3,4],[-4,4],[-30,23],[-23,24],[-3,3],[-1,1],[-1,2],[-2,12],[0,5],[1,3],[1,1],[3,1],[14,3],[4,1],[4,2],[7,6],[4,4],[86,133],[6,7],[16,9],[3,3],[14,13],[3,3],[5,1],[5,1],[4,2],[5,2],[11,8],[12,15],[2,4],[3,3],[4,3],[4,3],[4,2],[13,4],[12,6],[5,7],[6,13],[21,56],[3,4],[8,16],[10,27],[4,7],[6,7],[5,8],[5,10],[2,4],[3,4],[4,3],[4,2],[4,2],[3,3],[4,2],[3,3],[3,3],[3,5],[7,9],[5,7],[0,5],[-1,4],[-4,3],[-5,2],[-5,2],[-11,1],[-6,1],[-4,2],[-4,3],[-3,3],[-6,8],[-18,16],[-3,3],[-2,4],[-5,9],[-1,5],[-2,14],[0,11],[2,7],[3,5],[8,6],[4,4],[4,7],[-1,5],[-2,3],[-3,6],[-2,7],[-1,14],[2,8],[3,5],[4,3],[5,6],[6,11],[9,27],[13,108],[0,13],[-1,5],[-2,5],[-2,4],[-6,8],[-7,6],[-4,3],[-5,2],[-4,0],[-4,-1],[-4,-3],[-5,-8],[-8,-13],[-6,-7],[-4,-3],[-4,-2],[-5,-1],[-5,0],[-5,0],[-5,1],[-5,2],[-4,3],[-7,7],[-6,7],[-11,21],[-2,5],[-2,7],[-1,11],[1,6],[2,5],[12,9],[3,4],[4,6],[1,7],[1,6],[-1,6],[-1,5],[-2,4],[-3,4],[-3,3],[-4,3],[-8,4],[-19,14],[-3,5],[-3,5],[-2,9],[1,6],[3,5],[24,22],[4,5],[3,6],[4,13],[1,7],[-1,6],[-5,8],[-13,16],[-1,2],[-1,3],[-1,6],[1,6],[2,4],[3,3],[17,8],[4,1],[5,-2],[3,-2],[4,0],[3,1],[3,4],[12,26],[3,8],[3,14],[0,11],[-1,14],[0,8],[2,6],[2,5],[7,6],[7,10],[5,9],[2,7],[0,6],[-2,5],[-2,4],[-9,18],[-5,14]],[[2557,516],[-11,8],[-17,-9],[-11,18],[-23,12],[-58,13],[-28,-1],[-9,2],[-6,5],[-4,5],[-5,2],[-73,13],[-38,0],[-8,-3],[-6,-7],[-5,-7],[-9,-6],[-13,-13],[-9,-5],[-8,0],[-8,1],[-13,6],[-8,6],[-5,7],[-7,5],[-22,5],[-16,9],[-26,5],[-36,15],[-15,3],[-15,-2],[-36,-12],[-7,-3],[-9,6],[-46,2],[-16,2],[-15,-1],[-25,-6],[-37,-8],[-37,-9],[-36,-8],[-37,-9],[-36,-9],[-37,-8],[-37,-9],[-36,-8],[-37,-9],[-37,-8],[-36,-9],[-37,-8],[-36,-9],[-37,-8],[-37,-9],[-36,-9],[-26,-6],[-16,0],[-30,-7],[-34,-8],[-5,14],[-1,13],[6,14],[-19,18],[-17,23],[-17,14],[-13,7],[-12,5],[-28,2],[-14,-2],[-15,-4],[-15,-2],[-15,4],[-10,13],[0,14],[1,16],[-2,16],[-9,11],[-38,17],[-20,19],[-21,27],[-16,31],[-7,27],[-5,11],[-36,44],[-12,8],[-41,13],[-52,25],[-23,18],[-34,35],[-26,16],[-12,11],[-8,15],[-9,47],[-12,27],[-17,27],[-39,47],[-12,10],[-51,24],[-10,0],[-5,4],[-4,9],[0,17],[-2,9],[-11,12],[-27,10],[-12,9],[-7,12],[-10,29],[-62,112],[-6,9],[-8,5],[-14,3],[-5,3],[-4,6],[-6,12],[-13,21],[-6,13],[-9,10],[-22,5],[-4,2],[-6,8],[-4,3],[-3,-2],[-3,-3],[-8,-3],[-13,-5],[-7,-1],[-12,4],[-73,69],[-6,14],[-2,17],[-4,15],[-9,6],[-16,2],[-4,8],[2,11],[1,13],[-5,15],[-14,28],[-4,13],[0,17],[5,19],[7,17],[8,13],[1,13],[-14,8],[-28,7],[-1,6],[-1,22],[-1,10],[-3,5],[-10,10],[-4,6],[-4,15],[-2,13],[-3,13],[-9,14],[7,-1],[22,1],[-30,55],[-23,32],[-2,22],[0,16],[0,62],[0,64],[0,3],[0,46],[0,103],[0,103],[0,104],[0,103],[0,45],[0,30],[0,22],[0,40],[0,153],[0,152],[0,153],[0,152],[0,153],[0,152],[0,153],[0,153],[0,152],[0,153],[-1,152],[0,61]],[[6686,5984],[0,4]],[[8765,9048],[19,-3],[111,2],[35,-10],[31,-24],[9,-14],[17,-46],[12,-22],[11,-9],[33,-4],[73,-18],[61,-6],[11,-7],[12,-15],[3,-8],[3,-15],[5,-8],[6,-2],[15,2],[6,-2],[40,-31],[64,-28],[13,-10],[2,-59],[4,-14],[8,-8],[5,2],[6,5],[10,1],[6,-4],[12,-14],[8,-4],[0,-28],[-15,-73],[10,-27],[12,-5],[9,5],[10,8],[11,3],[10,-5],[3,-11],[2,-12],[4,-9],[19,-9],[8,19],[3,30],[5,26],[19,-11],[47,-3],[17,-14],[12,-25],[4,-14],[1,-15],[-2,-11],[-5,-8],[-3,-10],[3,-15],[8,-14],[21,-16],[10,-11],[12,-28],[7,-12],[11,-11],[10,-6],[32,-14],[5,-8],[14,-40],[5,-28],[-6,-21],[-25,-47],[-16,-47],[-5,-26],[0,-26],[11,-19],[70,-36],[18,-18],[14,-18],[15,-15],[22,-9],[16,-24],[17,-14],[13,-17],[2,-32],[-6,-56],[3,-24],[23,-63],[7,-12],[24,-25],[6,-8],[17,-46],[13,-18],[23,-3],[1,-27],[6,-18],[0,-10],[1,-4],[0,-4],[-14,-24],[-13,-15],[-34,-28],[-11,-6],[-11,-8],[-42,-55],[-14,-41],[-9,-17],[-18,-13],[-13,-3],[-15,-18],[-11,-6],[-13,1],[-29,7],[-12,-3],[-11,-5],[-22,-5],[-12,-5],[-10,-11],[-16,-28],[-13,-9],[-10,0],[-9,3],[-8,-3],[-8,-18],[0,-14],[6,-6],[11,0],[12,2],[12,-12],[7,-32],[2,-31],[3,-30],[8,-30],[13,-28],[15,-26],[19,-22],[28,-56],[-8,-30],[-11,-29],[-60,-107],[-2,-7],[3,-11],[-1,-7],[-5,-4],[-13,-7],[-4,-5],[-1,-15],[2,-13],[-1,-13],[-7,-13],[-22,20],[-8,-14],[4,-86],[-1,-13],[-5,-18],[-13,-32],[3,-8],[14,-6],[15,0],[15,2],[14,-3],[11,-15],[5,-16],[2,-35]]],
transform:{scale:[.0011695494501450135,.0009876095438543884],translate:[21.97987756300006,-18.069231871999932]}},m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();