!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo={type:"Topology",objects:{kgz:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Bishkek"},id:"KG.GB",arcs:[[0]]},{type:"Polygon",properties:{name:"Chuy"},id:"KG.GB",arcs:[[1,2,3,4,5],[-1]]},{type:"Polygon",properties:{name:"Ysyk-Köl"},id:"KG.YK",arcs:[[6,-2,7]]},{type:"Polygon",properties:{name:"Naryn"},id:"KG.NA",arcs:[[8,9,10,-3,-7]]},{type:"Polygon",properties:{name:"Batken"},id:"KG.BA",arcs:[[11,12],[13],[14],[15]]},{type:"Polygon",properties:{name:"Jalal-Abad"},id:"KG.DA",arcs:[[-4,-11,16,17,18]]},{type:"Polygon",properties:{name:"Talas"},id:"KG.TL",arcs:[[-5,-19,19]]},{type:"Polygon",properties:{name:"Osh"},id:"KG.OS",arcs:[[-10,20,-12,21,-17]]}]}},arcs:[[[4898,8879],[-14,5],[-12,-26],[-7,-31],[-5,-14],[-6,0],[-6,13],[-5,21],[-1,32],[3,22],[-1,31],[-14,16],[-37,61],[-9,40],[13,31],[49,24],[17,27],[11,29],[9,41],[4,3],[3,-5],[7,-45],[7,-34],[15,-31],[12,-30],[3,-11],[2,-13],[-2,-13],[-10,-11],[-6,-22],[-10,-16],[-2,-30],[0,-28],[3,-30],[-2,-6],[-9,0]],[[7238,9135],[10,-61],[-2,-21],[-3,-17],[-5,-5],[-7,1],[-12,7],[-6,0],[-5,-5],[-4,-8],[-23,-50],[-4,-20],[-5,-45],[-5,-14],[-7,-12],[-25,-16],[-18,-8],[-214,9],[-11,-4],[-5,-11],[-4,-13],[-2,-15],[-3,-16],[-4,-15],[-6,-16],[-6,-6],[-7,-2],[-29,9],[-42,35],[-10,4],[-6,-3],[-48,-52],[-13,-6],[-10,0],[-29,21],[-23,-3],[-58,26],[-9,2],[-6,-6],[-4,-11],[-17,-76],[-6,-8],[-6,-5],[-67,-31],[-63,-58],[-20,-25],[-19,-30],[-50,-119],[-6,-12],[-22,-31],[-9,-9],[-8,-3],[-7,0],[-6,2],[-11,6],[-15,17],[-21,0],[-126,-61],[-16,-3],[-69,12],[-12,-4],[-8,-10],[-9,-41],[-5,-10],[-9,-12],[-23,-21],[-12,-15],[-21,-37],[-12,-10],[-11,-5],[-12,-2],[-6,-8],[-2,-12],[0,-38],[-1,-7],[-4,-24],[-3,-11],[-7,-12],[-36,-45]],[[5811,8030],[-17,-16],[-3,-11],[-12,-28],[-47,-63],[-13,-8],[-6,4],[0,13],[3,44],[-1,13],[-2,12],[-2,12],[-3,9],[-6,8],[-5,-1],[-14,-14],[-66,-33],[-13,-1],[-7,4],[-38,35],[-6,-1],[-6,-5],[-5,-8],[-7,-8],[-26,-19],[-10,-2],[-138,10],[-36,26],[-8,-2],[-5,-6],[-5,-10],[-12,-19],[-29,-20],[-24,60],[-9,8],[-8,2],[-5,-9],[-7,-12],[-12,-12],[-131,-41],[-11,-12],[18,-87],[42,-136],[6,-33],[2,-26],[-5,-8],[-6,-1],[-8,3],[-20,15],[-6,1],[-71,-14],[-17,-19],[-36,-96],[-21,-62],[-6,-1],[-5,0],[-3,11],[-1,12],[-3,12],[-5,10],[-22,2],[-160,-50],[-10,6],[-6,7],[-9,7],[-36,12],[-22,0],[-6,-1],[-5,-5],[-2,-3],[-3,-25],[-17,-204],[1,-27],[3,-28],[23,-11],[7,-9],[7,-17],[2,-9],[-2,-8],[-11,-16],[-9,-16],[-11,-31],[-4,-14],[-4,-16],[-4,-24],[-1,-14],[1,-15],[4,-41],[-1,-20],[-4,-20],[-23,-50],[-7,-23],[-3,-17],[-1,-18],[2,-40],[-3,-18],[-6,-17],[-10,-17],[-10,-9],[-8,-1],[-6,4],[-4,6],[-8,15],[-4,6],[-4,3],[-5,0],[-6,-4],[-26,-36],[-8,-16],[-4,-15],[-2,-20],[-4,-61],[-5,-15],[-5,-9],[-5,2],[-4,4],[-3,9],[-2,10],[-1,15],[0,16],[4,54],[1,17],[-1,14],[-3,9],[-4,5],[-5,0],[-21,-15],[-7,-3],[-8,1],[-7,4],[-8,8],[-5,10],[-5,12],[-11,39],[-4,11],[-9,15],[-4,12],[-3,14],[-5,30],[-2,10],[-6,21],[-38,-61],[-17,-13],[-15,2],[-108,-23]],[[4147,6797],[-15,103],[-9,34],[-13,13],[-8,6],[-19,8],[-8,0],[-6,-2],[-6,-3],[-20,-22],[-5,-8],[-7,-16],[-6,-16],[-5,-8],[-7,-7],[-15,-5],[-9,2],[-84,66],[-12,5],[-12,-2],[-27,-13],[-14,-1],[-29,24],[-10,6],[-9,2],[-25,-15],[-4,4],[-2,9],[-7,47],[-4,22],[-3,11],[-5,12],[-9,14],[-16,20],[-11,9],[-10,4],[-15,0],[-6,8],[-13,27],[-11,8],[-40,15],[-47,35],[-13,6],[-9,1],[-6,-5],[-4,6],[-4,10],[-6,32]],[[3522,7243],[10,38],[2,13],[0,9],[-1,7],[-3,6],[-4,4],[-34,21],[-5,9],[-3,13],[-1,17],[1,13],[7,73],[3,19],[7,13],[10,8],[29,-1],[12,5],[11,16],[10,27],[3,8],[4,4],[25,25],[22,13],[17,4],[17,11],[13,2],[17,-4],[11,-10],[13,-5],[14,3],[35,31],[16,7],[21,2],[35,-10],[6,-5],[25,-28],[15,0],[20,10],[55,47],[13,16],[26,69],[2,39],[5,22],[15,42],[24,47],[4,14],[3,20],[-1,28],[-3,18],[-11,33],[-10,7],[-4,0],[-26,-6],[-9,0],[-8,4],[-7,6],[-6,8],[-3,8],[-3,10],[-3,11],[-5,43],[-3,9],[-5,0],[-7,-19],[-4,-22],[-2,-30],[2,-15],[1,-11],[1,-10],[-6,-28],[-23,-41],[-25,6],[-1,0]],[[3878,7936],[-65,254],[-14,77],[-7,82],[3,92],[10,87],[48,217],[24,106],[3,37],[-2,45],[-11,82],[-3,41],[3,43],[8,35],[19,66],[34,199],[10,36],[14,25],[43,50],[156,128],[15,7],[38,-3],[34,36],[30,169],[28,42],[18,-13],[32,-62],[17,-11],[143,34],[15,17],[2,35],[-9,42],[-25,68],[26,-29],[46,-84],[26,-21],[30,-13],[26,-27],[47,-77],[18,-19],[38,-8],[33,-43],[37,-20],[17,-19],[12,-32],[23,-78],[13,-30],[93,-143],[32,-24],[103,-35],[78,-76],[25,-45],[26,-25],[55,-34],[26,-30],[22,-34],[54,-66],[24,-11],[60,2],[205,-54],[35,8],[18,-5],[59,-49],[23,2],[27,23],[25,37],[22,45],[10,32],[29,157],[10,19],[14,7],[9,1],[47,8],[35,-10],[72,-42],[20,-3],[59,40],[24,1],[66,-32],[83,0],[77,-48],[10,3],[18,17],[11,1],[9,-12],[10,-39],[9,-10],[16,10],[28,53],[16,15],[24,-6],[47,-40],[24,-9],[19,9],[35,31],[40,-2],[18,8],[34,38],[4,9],[1,11],[2,9],[7,3],[16,-7],[6,1],[7,9],[19,45],[17,25],[18,6],[41,1],[35,28],[18,6],[18,-15],[103,-35],[31,-22],[10,-18],[26,-73],[13,-22],[15,-7],[15,-1]],[[8162,4870],[0,1],[-18,25],[-11,26],[-5,22],[-6,37],[-8,37],[-6,18],[-7,14],[-7,7],[-10,6],[-8,2],[-6,-1],[-11,-10],[-19,-26],[-14,-23],[-40,-46],[-23,-11],[-43,-11],[-67,-1],[-116,64],[-85,-3],[-12,5],[-9,8],[-3,10],[-2,24],[0,27],[1,15],[2,14],[12,55],[2,13],[0,15],[-3,14],[-8,15],[-14,5],[-30,3],[-11,-4],[-31,-30],[-149,-86],[-29,-8],[-4,8],[0,11],[3,12],[4,9],[9,15],[4,10],[4,11],[3,14],[10,78],[2,28],[1,14],[3,13],[14,45],[3,11],[0,10],[-1,10],[-6,13],[-6,9],[-30,38],[-46,43],[-13,27],[-1,12],[0,13],[-4,50],[-5,29],[-5,22],[-4,26],[0,8],[3,1],[8,-7],[1,-1],[1,-1],[2,-1],[6,-1],[6,6],[7,12],[16,86],[7,61],[5,102],[-7,47],[-4,1],[-5,0],[-14,-22],[-18,-14],[-30,-16],[-8,-7],[-10,-17],[-8,-10],[-12,-12],[-9,-5],[-163,-7],[-51,25],[-65,13],[-10,0],[-12,-7],[-28,-26],[-19,-11],[-21,-4],[-45,12],[-7,5],[-6,8],[-3,10],[-2,11],[-4,11],[-6,10],[-11,9],[-7,0],[-17,-14],[-15,-8],[-7,5],[-2,10],[2,13],[3,12],[15,44],[10,37],[5,30],[1,15],[2,43],[2,16],[3,14],[3,14],[14,46],[2,14],[5,44],[1,14],[-1,13],[-3,11],[-3,9],[-20,26],[-24,25],[-14,10],[-10,4],[-6,-3],[-15,-17],[-18,-7],[-121,11],[-4,9],[-4,14],[-1,12],[-15,36],[14,53],[10,15],[10,12],[7,4],[21,4],[57,-7],[28,5],[7,11],[5,13],[1,41],[1,13],[9,60],[18,81],[-3,8],[-5,3],[-5,-3],[-21,-22],[-6,-2],[-7,0],[-15,7],[-28,23],[-74,80],[-14,6],[-67,4],[-7,3],[-27,22],[-21,27],[-35,19],[-7,2],[-7,0],[-22,-15],[-6,-2],[-7,0],[-10,7],[-13,15],[-22,33],[-12,14],[-9,7],[-5,-3],[-22,-20],[-69,-40],[-8,-2],[-8,0],[-14,9],[-5,10],[-3,14],[1,13],[3,13],[3,11],[5,8],[19,26],[19,33],[8,18],[4,12],[3,12],[4,30],[1,15],[0,18],[-3,20],[-8,30],[-7,15],[-7,11],[-34,43],[-6,16],[-3,15],[2,33],[-1,18],[-4,10],[-6,5],[-10,3],[-7,7],[-3,10],[-2,13],[0,13],[6,60],[2,16],[15,85],[1,13],[-1,16],[-3,26],[-3,12],[-3,10],[-10,25],[-16,22],[-3,11],[-2,14],[-1,13],[0,13],[-2,35],[-5,28],[-6,14],[-7,9],[-23,12],[-8,1],[-6,-1],[-4,-10],[-3,-12],[-2,-13],[-4,-12],[-3,-11],[-9,-14],[-5,-5],[-11,-9],[-65,-9],[-8,2],[-5,8],[-3,9],[-14,59],[-6,12],[-6,9],[-26,25],[-6,7],[-6,6],[-7,5],[-23,4],[-4,8],[-3,11],[-12,80],[-2,22]],[[7238,9135],[16,0],[90,-30],[29,17],[39,37],[14,7],[14,-2],[25,-18],[36,1],[18,-19],[15,-26],[18,-20],[15,-1],[48,46],[18,7],[60,-4],[61,-30],[6,-14],[4,-17],[6,-13],[10,-16],[24,21],[15,20],[8,7],[20,6],[21,-4],[21,-16],[51,-61],[40,-13],[98,18],[41,-5],[42,12],[17,-6],[38,-27],[19,-3],[15,12],[33,42],[19,14],[40,6],[60,-12],[89,-62],[38,-44],[31,-52],[16,-16],[109,-22],[73,-60],[60,-7],[35,-28],[34,-2],[71,71],[36,14],[22,-13],[7,-25],[-5,-94],[1,-57],[5,-50],[9,-46],[13,-45],[33,-66],[71,-67],[29,-61],[41,-198],[25,-67],[46,-38],[87,-11],[73,28],[39,-3],[201,-87],[38,-51],[13,-30],[35,-103],[58,-107],[30,-80],[24,-84],[28,-74],[39,-47],[6,-38],[3,-79],[4,-41],[10,-35],[11,-31],[8,-35],[1,-47],[-24,-77],[-45,-31],[-227,5],[-47,-24],[-33,-28],[-14,-24],[-13,-36],[-8,-43],[-10,-91],[-9,-44],[-32,-62],[-41,-12],[-43,2],[-40,-20],[-51,-74],[-59,-45],[-71,-99],[-19,-15],[-21,-1],[-57,38],[-20,-10],[-16,-22],[-43,-82],[-19,-29],[-20,-17],[-42,-20],[-35,-29],[-102,-149],[-55,-21],[-17,-17],[-82,-118],[-122,-98],[-13,-15],[-12,-21],[-7,-27],[-7,-61],[-9,-24],[-32,-30],[-67,-28],[-84,-133],[-36,-34],[-16,-22],[-15,-38],[-4,-44],[16,-95],[3,-44],[-10,-42],[-16,-29],[-36,-44],[-15,-29],[-22,-70],[-18,-68]],[[8162,4870],[-24,-96],[-13,-39],[-13,-30],[-91,-162],[-16,-13],[-19,5],[-35,33],[-119,35],[-32,-2],[-31,-21],[-119,-131],[-14,-10],[-17,-3],[-46,4],[-70,-41],[-26,3],[-27,28],[-51,45],[-51,22],[-29,-4],[-52,-33],[-113,15],[-27,19],[-49,51],[-25,9],[-97,-45],[-36,-31],[-23,-48],[-13,-39],[-34,-50],[-15,-31],[-9,-46],[4,-47],[6,-48],[3,-49],[-8,-50],[-14,-31],[-36,-41],[-30,-54],[-24,-75],[-15,-89],[-6,-249],[-3,-39],[-11,-35],[-48,-77],[-23,-76],[-29,-173],[-20,-70],[-25,-50],[-79,-107],[-29,-59],[-15,-11],[-13,30],[-14,151],[-9,42],[-27,17],[-26,-61],[-27,-79],[-31,-39],[-17,8],[-34,39],[-17,11],[-22,-4],[-81,-81],[-12,-35],[-10,-41],[-15,-42],[-18,-26],[-19,-8],[-20,2],[-59,31],[-20,3],[-19,-4],[-42,-38],[-21,-3],[-15,34],[-8,43],[-14,108],[-2,47],[7,47],[12,34],[9,32],[-2,44],[-9,27],[-25,49],[-10,31],[-7,47],[-5,139],[-16,104],[-25,52],[-33,0],[-38,-47],[-198,-328],[-9,-11],[-11,-4],[-11,-10],[-5,-19],[-3,-23],[-7,-20],[-17,-9],[-17,17],[-17,25],[-16,14],[-23,-8],[-44,-42],[-23,-6],[-33,15],[-33,29],[-79,113],[-21,20],[-19,-4],[-3,-9]],[[5081,3238],[-24,21],[-70,-3],[-16,4],[-11,6],[-4,8],[-8,24],[-7,16],[-67,24],[-13,13],[-7,14],[4,29],[3,14],[2,14],[2,14],[0,14],[-1,13],[-3,15],[-5,17],[-12,26],[-10,12],[-125,95],[-19,23],[-37,65],[-15,37],[-7,19],[-8,32],[-7,19],[-29,60],[-34,68],[-15,19],[-10,18],[-4,12],[-2,12],[0,31],[-2,19],[-6,27],[-4,15],[-7,13],[-23,31],[-99,181],[-4,10],[-12,38]],[[4365,4377],[80,199],[12,38],[2,21],[-70,117],[-2,18],[2,16],[7,36],[6,14],[7,8],[7,-1],[8,-2],[8,1],[9,7],[10,25],[6,11],[6,6],[15,8],[8,7],[24,33],[3,3],[46,2],[73,33],[6,-1],[7,-3],[16,-15],[10,-4],[13,6],[4,6],[1,6],[-2,6],[-3,9],[-5,11],[-25,71],[-4,19],[-12,61],[0,19],[4,12],[9,10],[73,52],[23,29],[15,13],[13,8],[57,17],[15,10],[32,33],[27,19],[8,11],[7,15],[31,90],[5,22],[12,69],[11,92],[8,52],[2,26],[2,80],[2,26],[12,54],[3,17],[1,15],[-1,8],[-9,16],[-25,35],[-7,2],[-12,-1],[-7,-5],[-5,-7],[-3,-8],[-2,-16],[-1,-5],[-1,-5],[-2,-4],[-8,-10],[-113,-98],[-15,-4],[-6,6],[-9,5],[-11,3],[-18,-2],[-9,-8],[-19,-33],[-20,-20],[-11,-2],[-9,6],[-6,10],[-6,6],[-49,13],[-47,-4],[-20,-8],[-14,-10],[-9,-11],[-6,-3],[-6,2],[-6,7],[-14,27],[-9,13],[-13,11],[-21,6],[-15,-2],[-29,-18],[-10,-2],[-11,3],[-9,9],[-10,11],[-19,38],[-5,7],[-5,4],[-7,2],[-6,4],[-5,9],[-9,24],[-7,13],[-9,10],[-11,8],[-16,-7],[-5,19],[-13,67],[-9,30],[-4,2],[-1,14],[4,8],[23,43],[5,6],[6,4],[6,2],[37,-11],[17,-9],[8,-8],[9,-12],[7,-15],[5,-19],[4,-21],[2,-19],[0,-19],[0,-14],[-2,-37],[5,-19],[12,-17],[29,-20],[12,-1],[7,8],[0,17],[-12,196],[-5,51],[-3,21],[-4,11],[-6,7],[-29,14],[-40,4],[-32,14],[-3,4],[-1,7],[-1,11],[5,135],[0,38],[-3,35],[-3,19],[-4,14],[-3,8],[-4,6],[-22,14],[-10,11],[-4,3],[-3,-1],[-24,-27],[-6,-2],[-5,5],[-11,19],[-6,9],[-30,28],[-3,1],[0,-6],[1,-24],[-1,-16],[-3,-14],[-6,-15],[-7,-10],[-7,-6],[-8,2],[-10,11],[-11,19],[-21,51],[-6,13],[-6,9],[-7,12],[-3,11],[1,9],[2,5],[4,2],[13,0],[6,3],[5,5],[5,10],[3,11],[6,25],[21,61],[6,30]],[[2526,2634],[1,0],[32,3],[44,36],[80,46],[14,-6],[9,-16],[7,-39],[-1,-24],[-6,-45],[1,-26],[2,-20],[5,-27],[-2,-24],[-6,-15],[-9,-10],[-4,-12],[2,-16],[8,-16],[38,-34],[13,-21],[6,-25],[2,-38],[-6,-27],[-20,-37],[-3,-10],[1,-12],[29,-29],[9,-17],[5,-17],[1,-26],[-2,-16],[-5,-16],[0,-20],[2,-24],[9,-38],[1,-26],[-3,-23],[-5,-20],[-4,-25],[-1,-19],[0,-18],[4,-15],[9,-9],[24,-11],[12,-10],[15,-21],[10,-17],[9,-29],[14,-59],[20,-63],[14,-16],[9,2],[17,24],[8,8],[9,-5],[6,-14],[6,-52],[5,-23],[12,-25],[19,-50],[21,-78],[-12,-32],[-5,-27],[-3,-12],[-6,-11],[-8,-11],[-32,-35],[-45,-30],[-43,-41],[-24,-45],[-10,-14],[-14,-12],[-29,-18],[-20,-19],[-32,-12],[-57,21],[-28,-7],[-15,-7],[-20,-17],[-9,-4],[-9,1],[-18,25],[-7,6],[-13,-4],[-14,2],[-19,9],[-12,11],[-20,31],[-7,9],[-6,10],[-14,13],[-133,57],[-14,11],[-13,15],[-6,15],[-6,32],[-7,12],[-12,5],[-17,-1],[-11,-8],[-7,-12],[-10,-30],[-10,-22],[-17,-21],[-70,-36],[-22,-41],[-19,-80],[-3,-16],[-7,-69],[-8,-41]],[[2070,972],[-15,27],[-31,39],[-16,-3],[-32,-31],[-25,-10],[-9,-6],[-6,-10],[-13,-28],[-7,-11],[-31,-36],[-13,-20],[-29,-69],[-9,-16],[-12,1],[-51,21],[-39,-5],[-37,-22],[-30,-41],[-19,-68],[-13,-80],[-17,-68],[-29,-31],[-25,14],[-22,30],[-20,20],[-17,-17],[-11,-27],[-7,-6],[-8,3],[-14,-1],[-11,-7],[-28,-32],[-25,-8],[-27,10],[-24,30],[-14,50],[1,31],[4,32],[2,34],[-5,34],[-9,17],[-21,19],[-10,16],[0,1],[0,2],[-18,117],[-13,52],[-22,35],[-16,4],[-33,-13],[-16,4],[-11,14],[-21,38],[-12,10],[-16,-4],[-29,-23],[-15,-6],[-9,-7],[-5,-13],[-5,-13],[-5,-8],[-9,0],[-19,12],[-10,3],[-11,-10],[-81,-124],[-16,-8],[-16,12],[-1,35],[9,88],[0,42],[-4,45],[-9,24],[-14,-22],[-25,-114],[-8,-24],[-15,-10],[-14,11],[-31,37],[-18,9],[-20,1],[-19,-13],[-15,-28],[-14,-43],[-8,-12],[-27,26],[-37,8],[-70,-30],[-36,22],[-37,46],[-36,25],[-37,9],[-79,-10],[-16,-14],[-30,-55],[-16,-20],[-16,-12],[-17,-5],[-20,0],[-40,-14],[-18,14],[-22,48],[-5,7],[-9,-8],[-3,-16],[-3,-19],[-5,-18],[-36,-31],[-13,60],[5,292],[-3,47],[-7,40],[-13,33],[-16,29],[-14,32],[-6,45],[3,97],[10,95],[42,262],[17,81],[4,24],[0,15],[3,6],[13,-6],[27,-62],[43,-155],[88,64],[-2,33],[-20,79],[0,17],[0,16],[-2,16],[-4,14],[-7,78],[6,63],[14,56],[19,57],[2,16],[4,42],[4,11],[8,-5],[9,-37],[6,-12],[14,-4],[16,5],[357,265],[31,-7],[130,-176],[19,-13],[45,6],[21,-3],[20,-21],[13,-30],[11,-34],[15,-33],[16,-18],[32,-9],[35,-31],[72,-23],[22,-14],[22,-31],[9,-43],[-13,-55],[-15,-21],[-15,-16],[-14,-21],[-12,-34],[-8,-46],[-3,-43],[7,-32],[20,-15],[8,22],[7,54],[6,22],[11,11],[38,3],[20,14],[18,24],[16,34],[12,45],[8,85],[-1,75],[4,63],[24,47],[58,41],[12,16],[36,62],[5,16],[3,6],[5,1],[5,-4],[3,-6],[1,-4],[16,18],[6,12],[12,8],[49,-22],[28,20],[30,46],[15,60],[-18,60],[32,69],[53,19],[105,-31],[29,5],[13,42],[8,54],[14,45],[13,8],[9,-15],[8,-26],[9,-23],[13,-11],[15,-3],[28,6],[19,-2],[28,-56],[41,-26],[8,-29],[8,-36],[16,-33],[19,-25],[21,-17],[43,-18],[22,5],[8,28],[2,43],[6,47],[16,30],[16,-29],[13,-55],[6,-50],[0,-101],[6,-37],[18,-17],[13,8],[47,58],[15,29],[10,34],[17,78],[28,58],[33,4],[36,-18],[35,-6],[11,7],[5,2],[7,-4],[11,-9],[15,-5],[12,7],[6,27],[-5,19],[-6,8]],[[1628,1698],[12,17],[13,25],[10,15],[17,-2],[5,-21],[2,-27],[10,-20],[14,-3],[43,24],[30,2],[13,10],[9,29],[2,76],[-40,89],[-2,64],[15,30],[42,61],[6,38],[-7,23],[-11,5],[-24,-13],[-4,-6],[-10,-17],[-5,-3],[-7,2],[-3,4],[-3,6],[-51,44],[-25,34],[-15,40],[-13,67],[-15,60],[-21,27],[-28,-32],[-16,-77],[-5,-43],[-1,-41],[5,-39],[22,-103],[11,-31],[12,-13],[24,-11],[10,-17],[4,-71],[-38,-127],[1,-63],[12,-12]],[[1153,1701],[-13,0],[-1,-37],[7,-41],[12,-36],[30,-45],[9,-24],[26,-92],[5,-8],[47,78],[26,28],[30,12],[9,18],[2,44],[-7,45],[-11,6],[-29,-27],[-32,2],[-110,77]],[[2280,1747],[14,6],[9,30],[21,157],[-4,25],[-23,-14],[-33,-52],[-16,-16],[-22,-3],[-15,-36],[9,-36],[19,-29],[18,-17],[23,-15]],[[4365,4377],[-104,112],[-4,11],[-4,19],[-2,36],[1,32],[-1,24],[-3,17],[-19,30],[-6,14],[-2,23],[3,33],[-1,13],[-4,8],[-10,8],[-14,-4],[-15,-36],[-48,-164],[-6,-11],[-35,-49],[-10,-21],[-8,-21],[-3,-20],[-2,-19],[0,-18],[2,-33],[0,-13],[-1,-10],[-3,-7],[-6,-1],[-12,7],[-7,10],[-11,20],[-5,6],[-6,3],[-16,5],[-6,7],[-4,9],[-1,15],[-1,15],[-1,13],[-2,9],[-4,5],[-23,4],[-5,5],[-1,9],[2,11],[4,13],[11,25],[77,150],[4,17],[2,13],[-1,12],[-2,9],[-2,8],[-12,30],[-7,22],[-3,6],[-5,1],[-27,-11],[-6,-5],[-4,-8],[-3,-21],[1,-14],[2,-12],[2,-10],[1,-11],[0,-9],[-3,-10],[-5,-6],[-13,-8],[-10,-2],[-77,13],[-31,-2],[-4,2],[-6,4],[-3,-6],[-6,-13],[-17,-58],[-6,-16],[-11,-15],[-7,-13],[-6,-23],[-8,-35],[-8,-7],[-6,-3],[-6,1],[-4,3],[-2,6],[-6,22],[-4,13],[-4,10],[-5,6],[-5,-1],[-6,-5],[-47,-72],[-8,-17],[-7,-19],[-4,-28],[1,-21],[2,-35],[-1,-15],[-3,-29],[0,-12],[2,-13],[3,-13],[2,-16],[0,-13],[-1,-16],[-2,-15],[-8,-19],[-12,-20],[-53,-49],[-25,-52],[-16,-29],[0,-1]],[[3543,4041],[-20,10],[-54,-7],[-18,27],[-13,36],[-14,20],[-67,-64],[-42,-60],[-12,-3],[2,40],[-3,72],[-35,20],[-117,-22],[-39,10],[-35,32],[-28,63],[-39,124],[-18,14],[-22,3],[-17,18],[0,56],[2,15],[0,12],[-3,11],[-5,7],[-48,10],[-26,15],[-19,24],[-26,84],[-12,17],[-16,-28],[-5,-17],[-10,-65],[-7,-11],[-34,-10],[-52,-32],[-27,-18],[12,59],[6,44],[0,48],[-8,128],[-2,70],[-4,67],[-10,47],[-23,28],[-22,-7],[-22,-28],[-19,-36],[-27,-34],[-16,17],[-14,41],[-22,37],[-72,-25],[-23,23],[-5,104],[2,105],[-5,80],[-15,73],[-85,259],[-7,14],[-7,6],[-6,-10],[-1,-52],[-7,-13],[-9,8],[-5,39],[-9,9],[-5,-14],[-3,-27],[-6,-20],[-8,7],[-1,29],[17,138],[0,51],[-5,48],[-12,33],[-20,6],[-20,-16],[-11,-28],[-18,-94],[1,0],[9,-41],[25,-67],[4,-45],[-4,-49],[-13,-83],[-31,-133],[-25,-53],[-30,-13],[-40,35],[-43,84],[-13,6],[-5,-31],[8,-424],[-5,-85],[-20,-36],[-62,109],[-23,-59],[-10,-47],[-12,-2],[-12,25],[-8,34],[-11,93],[-11,30],[-21,3],[-19,-21],[-2,-34],[4,-44],[2,-51],[-9,-48],[-14,20],[-23,79],[-15,24],[-34,10],[-16,19],[-19,30],[-20,19],[-43,16],[-39,-12],[-18,4],[-17,32],[-12,35],[-15,31],[-16,25],[-18,15],[-20,-5],[-18,-17],[-14,-4],[-12,33],[-1,43],[13,151],[-1,51],[-9,51],[-51,179],[-15,41],[-17,22],[-31,-9],[-111,-130],[-31,-25],[-6,1],[-16,1],[-14,27],[-22,85],[-13,35],[-15,28],[-34,42],[-1,0],[-127,29],[-34,36],[-16,79],[19,64],[147,175],[54,87],[29,29],[28,37],[21,65],[26,116],[40,94],[90,156],[27,33],[91,21],[31,25],[10,41],[3,141],[16,90],[37,20],[45,-4],[37,18],[128,193],[75,44],[34,48],[13,91],[-3,2]],[[1834,7388],[1,1],[22,14],[121,-56],[11,-2],[13,2],[33,20],[19,19],[9,6],[113,7],[25,-14],[151,-138],[14,-7],[11,4],[6,5],[30,-13],[101,-86],[17,-12],[17,-21],[22,-40],[59,-4],[17,8],[17,20],[20,49],[10,45],[8,77],[6,32],[8,30],[12,23],[21,20],[17,10],[68,20],[29,1],[11,-8],[14,-26],[7,-27],[4,-24],[12,-15],[16,-8],[28,4],[32,-4],[31,-41],[15,-10],[37,24],[14,2],[30,-36],[50,-37],[19,-21],[18,-27],[42,-91],[13,-12],[16,-3],[28,9],[18,13],[11,24],[3,21],[0,27],[2,27],[15,26],[24,21],[88,44],[6,-3],[8,-16],[11,-11],[9,-6],[28,19]],[[1834,7388],[-29,20],[-127,184],[-29,24],[-29,-8],[-40,-60],[-20,-38],[-26,13],[-19,21],[-36,72],[-5,37],[11,38],[22,41],[10,21],[30,72],[6,28],[-2,17],[-5,17],[-2,26],[6,48],[10,48],[13,42],[14,26],[43,-56],[15,-3],[12,14],[9,24],[2,29],[-8,28],[-32,56],[-9,24],[-8,92],[34,42],[78,24],[14,30],[5,36],[-2,97],[3,52],[8,24],[34,23],[14,20],[12,31],[19,72],[16,15],[20,-12],[21,-21],[20,-7],[16,14],[14,28],[12,35],[9,36],[16,29],[22,2],[44,-30],[15,13],[10,2],[9,-10],[36,-61],[11,-9],[16,6],[99,128],[30,20],[64,6],[31,23],[15,6],[14,-12],[14,-19],[15,-13],[56,-28],[105,-117],[29,-17],[14,4],[26,25],[14,8],[116,-10],[60,-48],[107,-145],[32,-12],[65,2],[129,-63],[28,-31],[22,-48],[31,-117],[23,-42],[31,-29],[31,-17],[30,-3],[117,39],[40,-2],[34,-29],[18,-28],[15,-17],[16,-9],[65,-9],[21,-16],[14,-33],[-3,-75],[1,-53],[10,-31],[83,-29],[54,-44],[25,10],[1,44],[-1,1]],[[5081,3238],[-11,-35],[-3,-25],[-1,-9],[-7,-45],[-12,-51],[-6,-49],[6,-38],[43,-82],[11,-32],[7,-81],[-28,-15],[-76,41],[-20,2],[-17,-15],[-47,-98],[-15,-23],[-35,-39],[-31,-47],[-80,-183],[-97,-164],[-32,-29],[-28,-9],[-28,9],[-31,25],[-32,14],[-32,-8],[-32,-27],[-28,-41],[-29,-27],[-59,-17],[-25,-42],[-9,-26],[-8,-29],[-5,-32],[-3,-36],[-5,-58],[-15,-41],[-18,-38],[-13,-48],[-7,-102],[-4,-29],[-7,-20],[-26,-46],[-11,-35],[-8,-44],[-4,-49],[1,-47],[8,-49],[13,-16],[15,-8],[18,-22],[12,-43],[6,-52],[4,-105],[21,-132],[-5,-26],[-20,-26],[-15,-26],[-11,-35],[-11,-55],[-10,-85],[-9,-35],[-15,-18],[-171,-49],[-26,28],[-83,19],[-32,-6],[-99,-52],[-22,-32],[-7,-38],[-2,-35],[-7,-25],[-51,-20],[-92,-67],[-29,-5],[-32,19],[-16,17],[-11,7],[-11,-5],[-45,-50],[-14,-2],[-15,11],[-57,26],[-19,3],[-39,-18],[-14,-1],[-167,92],[-15,2],[-16,-10],[-9,-17],[-18,-46],[-10,-13],[-15,0],[-23,32],[-13,8],[-10,-9],[-29,-64],[-15,-5],[-15,2],[-30,16],[-15,-1],[-34,-36],[-20,-6],[-16,-12],[-11,-38],[-21,-132],[-37,-171],[-10,-2],[-1,1],[-9,28],[-8,40],[-3,23],[0,19],[-3,16],[-19,21],[-13,23],[-8,9],[-39,14],[-19,17],[-8,37],[-2,52],[-5,62],[-10,54],[-16,25],[-16,-5],[-32,-37],[-34,-14],[-15,-17],[-90,-133],[-33,-30],[-33,-8],[-17,12],[-17,26],[-13,40],[-7,48],[2,51],[9,30],[12,27],[11,36],[3,50],[-2,63],[-8,56],[-15,27],[-18,-1],[-92,-41],[-44,5],[-37,36],[-19,85],[7,39],[24,71],[4,36],[-5,40],[-13,37],[-1,2]],[[2526,2634],[-6,9],[-27,26],[-17,25],[-6,37],[5,35],[78,82],[24,50],[13,68],[6,46],[8,26],[60,68],[16,9],[41,4],[7,1],[17,-4],[8,-14],[-4,-25],[-13,-9],[-15,-3],[-11,-6],[-4,-23],[12,-8],[41,4],[13,-10],[53,-65],[25,-19],[21,9],[19,52],[10,62],[0,57],[0,1],[-5,28],[-4,20],[0,1],[-39,58],[-8,23],[-1,27],[7,40],[0,44],[-1,1],[-19,67],[3,41],[27,25],[30,-55],[30,-72],[26,-27],[35,-10],[64,-90],[36,4],[14,23],[9,30],[4,35],[1,36],[7,41],[17,0],[33,-31],[26,25],[11,163],[22,69],[31,28],[66,34],[78,101],[85,65],[43,50],[27,75],[-4,50],[-1,0],[-7,3]]],
transform:{scale:[.0011032367885788557,.00040728718861886184],translate:[69.22629602000012,39.189236959000084]}},m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();