!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo={type:"Topology",objects:{ner:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Tillabéri"},id:"NE.TL",arcs:[[0,1,2],[3]]},{type:"Polygon",properties:{name:"Diffa"},id:"NE.DF",arcs:[[4,5,6]]},{type:"Polygon",properties:{name:"Agadez"},id:"NE.AG",arcs:[[-7,7,8,9,10]]},{type:"Polygon",properties:{name:"Maradi"},id:"NE.MA",arcs:[[11,12,13,-9]]},{type:"Polygon",properties:{name:"Zinder"},id:"NE.ZI",arcs:[[14,-12,-8,-6]]},{type:"Polygon",properties:{name:"Dosso"},id:"NE.DS",arcs:[[15,-1,16]]},{type:"Polygon",properties:{name:"Niamey"},id:"NE.NI	",arcs:[[-4]]},{type:"Polygon",properties:{name:"Tahoua"},id:"NE.TH",arcs:[[-14,17,-17,-3,18,-10]]}]}},arcs:[[[2592,2505],[-11,-100],[-19,-27],[-17,-7],[-6,-4],[-5,-7],[-7,-26],[-9,-11],[-10,-8],[-49,-31],[-5,0],[-17,0],[-8,0],[-7,-3],[-9,-5],[-9,-6],[-87,-97],[-18,-34],[-3,-8],[-4,-13],[-22,-128],[-5,-18],[-12,-20],[-117,-142],[-20,-16],[-106,-59],[-7,-1],[-9,3],[-66,37],[-10,4],[-8,0],[-7,-9],[-11,-26],[-8,-23],[-11,-71],[-3,-13],[-4,-12],[-9,-4],[-11,-1],[-43,0],[-5,-2],[-6,-4],[-4,-10],[-13,-40],[-6,-9],[-9,-5],[-19,-4],[-13,-5],[-2,-5],[-2,-4],[-1,-17],[-4,-20],[-4,-18],[-1,-10],[1,-4],[1,-5],[5,-15],[1,-5],[0,-5],[-6,-39],[-10,-34],[-12,-24],[-4,-6],[-4,-3],[-4,0],[-4,1],[-7,3],[-8,7],[-2,1],[-2,1],[-13,2],[-8,0],[-22,-7],[-55,-56],[-3,-2],[-6,-4],[-7,-2],[-4,-1],[-8,-1],[-7,2],[-15,5],[-4,0],[-4,-1],[-5,-2],[-9,-5],[-6,-7],[-8,-12],[-4,-8],[-2,-6],[-1,-6],[0,-4],[1,-5],[38,-115],[2,-1],[3,-1],[96,-13],[10,-4],[7,-6],[4,-14],[-1,-7],[-2,-4],[-11,-9],[-22,-24],[-3,-3],[-15,-9],[-9,-9],[-9,-10],[-4,-7],[-2,-6],[-1,-4],[-2,-11],[1,-10],[1,-11],[11,-54],[1,-10],[0,-11],[-1,-16],[-4,-19],[0,-22],[3,-58],[2,-16],[3,-10],[0,-23],[2,-9],[25,0],[1,-2],[3,-2],[4,1],[2,7],[1,4],[2,7],[2,5],[3,4],[6,2],[6,-2],[4,-8],[2,-12],[0,-22],[2,-24],[7,-19],[14,-8],[14,-4],[22,-18],[14,-7]],[[1701,595],[-7,-13],[-15,-1],[-7,-1],[-12,0],[-7,-1],[-7,-6],[-10,-16],[-5,-6],[-7,-2],[-7,5],[-7,-3],[-3,-5],[-2,-6],[-1,-7],[-2,-6],[-9,-19],[-6,-4],[-22,8],[-4,-1],[-8,-8],[-4,-2],[-3,0],[-5,4],[-3,1],[-22,-8],[-3,-2],[-6,-8],[-4,-3],[-4,0],[-2,4],[-1,4],[-2,2],[-7,1],[-6,1],[-5,-3],[-3,-10],[-3,-9],[-4,2],[-3,5],[-5,1],[-3,-3],[-5,-10],[-2,-2],[-8,-1],[-14,1],[-9,-2],[-15,-9],[-6,-15],[1,-19],[6,-47],[21,-79],[4,-9],[4,-6],[7,-7],[6,-4],[6,-2],[4,-4],[2,-11],[-1,-14],[-4,-2],[-7,3],[-6,0],[-5,-6],[-5,-13],[-4,-6],[-5,-2],[-6,1],[-6,-2],[-3,-11],[1,-9],[7,-15],[1,-8],[-33,37],[-50,92],[-44,81],[-47,87],[-28,50],[-12,30],[2,24],[9,11],[37,10],[3,2],[4,9],[4,3],[2,-1],[7,-5],[3,0],[38,4],[9,3],[4,9],[-1,19],[0,4],[2,9],[0,3],[-2,7],[-8,11],[-4,7],[-5,13],[-3,11],[-5,51],[-2,10],[-4,12],[-6,9],[-11,10],[-7,7],[-7,9],[-2,4],[-4,17],[-16,25],[-26,9],[-61,7],[-6,-4],[-11,-16],[-7,-6],[-5,1],[-4,3],[-5,-1],[-3,-4],[-4,-12],[-11,-21],[-7,-16],[-7,-15],[-11,-9],[-11,-2],[-80,9],[-65,8],[-21,7],[-18,13],[-43,48],[-35,39],[-52,57],[-16,18],[-47,51],[-37,42],[-36,40],[-20,12],[-45,5],[-18,14],[-6,13],[-1,16],[0,16],[-1,54],[0,79],[0,72],[8,34],[13,-3],[51,-27],[34,-11],[14,-8],[4,2],[7,29],[3,10],[3,4],[12,-4],[10,-12],[12,-8],[17,9],[-12,18],[-9,9],[-11,3],[-27,4],[-11,5],[-69,42],[-21,20],[-12,26],[-3,38],[-26,35],[-34,27],[-29,12],[-24,-3],[-11,1],[-12,8],[-8,9],[0,6],[2,3],[0,3],[0,5],[0,7],[-2,6],[-6,2],[-84,-4],[-17,8],[-9,21],[3,13],[6,17],[4,16],[-4,8],[-13,6],[-7,10],[-6,11],[-21,19],[-10,11],[-3,5],[-5,9],[-5,16],[-5,7],[-13,11],[-4,7],[0,8],[5,16],[0,7],[-7,10],[-11,7],[-10,10],[-5,15],[-2,15],[-6,7],[-8,5],[-6,9],[0,7],[5,14],[1,6],[-3,7],[-4,4],[-4,4],[-3,6],[-8,16],[-3,9],[2,10],[16,31],[3,10],[3,26],[2,8],[6,10],[1,7],[-2,5],[-27,47],[-91,108],[-8,11],[-19,41],[-4,42],[42,156],[-4,26],[-18,49],[1,24],[7,14],[9,9],[6,11],[-2,20],[-3,42],[0,20],[5,9],[84,-27],[21,0],[20,5],[41,19],[20,1],[98,-45],[9,1],[17,5],[18,10],[19,9],[96,4],[17,5],[16,9],[53,65],[41,50],[51,61],[43,52],[17,13],[21,7],[47,2],[61,3],[61,2],[61,3],[61,3],[61,2],[61,3],[61,3],[61,3],[61,2],[61,3],[61,3],[61,2],[61,3],[61,3],[62,2],[61,3],[31,2],[3,11],[0,31],[2,16],[1,8],[5,4],[10,4],[25,0],[75,-17],[119,-26],[65,-14],[4,-2],[11,-3],[6,98],[7,22],[55,44],[49,71],[24,16],[50,13],[24,16],[16,25],[1,6]],[[2352,3405],[5,-12],[141,-522],[-2,-118],[2,-37],[5,-17],[89,-194]],[[1261,1450],[20,3],[4,-12],[3,-15],[5,-13],[13,13],[11,10],[12,33],[8,37],[5,34],[2,26],[-2,22],[-10,8],[-15,-5],[-6,9],[1,26],[-6,3],[-29,-3],[-34,-2],[-30,9],[-25,0],[-22,-12],[-13,-21],[-3,-19],[1,-30],[12,-32],[14,-27],[19,-24],[22,-11],[23,-4],[20,-3]],[[9744,5332],[0,-7],[-3,-39],[-2,-38],[-3,-39],[-2,-38],[-3,-39],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-3,-39],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-39],[-2,-38],[-3,-39],[-3,-38],[-2,-39],[-3,-39],[-1,-21],[-2,-10],[-2,-9],[-8,-15],[-22,-30],[-35,-49],[-36,-50],[-35,-49],[-36,-49],[-35,-50],[-36,-49],[-35,-50],[-36,-49],[-35,-49],[-36,-49],[-35,-50],[-35,-49],[-36,-50],[-35,-49],[-36,-49],[-35,-50],[-25,-34],[-37,-51],[-34,-48],[-18,-33],[-19,-35],[-19,-34],[-19,-34],[-19,-35],[-19,-34],[-18,-35],[-19,-34],[-19,-35],[-19,-34],[-19,-35],[-19,-34],[-19,-34],[-19,-35],[-18,-34],[-19,-35],[-19,-34],[-19,-34],[-16,-45],[-21,-58],[-9,-26],[-3,-16],[-1,-18],[2,-18],[9,-36],[1,-17],[-6,-20],[-9,-6],[-12,-2],[-13,-4],[-6,-6],[-20,-29],[-13,-12],[-2,-4],[1,-4],[7,-8],[2,-5],[4,-33],[-1,-16],[-5,-15],[-21,-23],[-10,-3],[-15,-3],[-26,-3],[-23,-13],[-16,-10],[-21,-38],[0,-49],[12,-59],[9,-43],[7,-39],[4,-27],[5,-27],[4,-27],[5,-27],[4,-27],[5,-27],[4,-27],[5,-27],[5,-27],[4,-27],[5,-27],[4,-27],[5,-26],[4,-27],[5,-27],[4,-27],[-156,8],[-1,0],[-18,-4],[-6,-6],[0,-14],[-9,-9],[-25,-31],[-5,-8],[-3,-8],[0,-4],[3,-6],[1,-5],[-2,-6],[-4,-1],[-5,0],[-2,-1],[-6,-12],[1,-4],[9,-2],[-11,-19],[-16,-8],[-17,-4],[-17,-10],[2,7],[2,9],[-2,6],[-6,2],[-3,-4],[-5,-14],[-5,-6],[1,9],[-1,3],[-10,-4],[-12,-1],[-7,3],[3,9],[0,5],[-17,-2],[-16,-8],[-57,-37],[-17,-4],[-18,5],[-3,-14],[-5,-1],[-9,5],[-9,4],[0,-6],[5,-2],[4,-4],[4,-5],[4,-7],[-8,-9],[-18,-25],[0,-3],[1,-7],[-1,-2],[-2,0],[-5,1],[-2,-1],[-2,-2],[-9,-4],[-2,-3],[3,-10],[4,-6],[0,-3],[-9,-1],[-1,3],[-4,6],[-3,3],[-2,-3],[-1,-1],[-4,-14],[-17,-26],[-10,-10],[-14,-5],[-8,-4],[-4,-19],[-6,-5],[-9,3],[-6,5],[-6,8],[-7,7],[0,-14],[-2,-5],[-4,-6],[-4,-4],[-5,-2],[-2,5],[-3,-3],[-23,-29],[-2,-13],[-8,-16],[-5,-12],[11,-5],[-4,-19],[-4,-9],[-5,-7],[-5,-2],[-14,-4],[-3,-3],[-5,-45],[-3,-10],[-9,-6],[-10,3],[-12,6],[-15,3],[-17,1],[-23,1],[-10,4],[-6,-5],[-5,4],[-4,8],[-7,4],[-6,-1],[-3,-4],[-3,-4],[-3,-2],[-5,1],[-1,3],[0,5],[-2,5],[-8,6],[-32,14],[-10,-3],[-13,-12],[-9,-2],[-21,-2],[-37,12],[-13,8],[-29,30],[-88,71],[-70,37],[-191,63],[-308,-2],[-115,18],[-60,-7],[-18,-4]],[[6634,1416],[-2,4],[-20,97],[2,14],[5,20],[109,149],[5,9],[2,6],[2,7],[1,10],[0,23],[-1,17],[0,7],[3,7],[257,309],[13,11],[8,1],[9,-3],[25,-19],[8,-5],[5,0],[8,2],[115,47],[159,134],[8,6],[7,1],[52,-3],[9,1],[8,6],[42,39],[5,8],[-4,6],[-25,12],[-8,5],[-4,8],[-3,9],[-4,13],[-2,11],[2,20],[2,11],[4,10],[6,8],[17,14],[18,12],[11,1],[1,3],[-1,2446]],[[7488,4910],[13,8],[10,13],[128,112],[482,289],[1618,0],[5,0]],[[7488,4910],[-215,-100],[-258,-229],[-186,-72],[-1034,-868],[-222,-110],[-259,-186],[-116,-53],[-38,-5],[-115,3],[-12,-1],[-12,-3],[-50,-34],[-108,-2],[-253,50],[-25,-35],[-51,-187]],[[4534,3078],[-127,88],[-34,4],[-251,-13]],[[4122,3157],[-37,484],[13,137],[0,13],[-3,12],[-10,8],[-98,33],[-4,2],[-9,7],[-43,43],[-4,5],[-4,6],[-11,24],[-1,9],[-139,429],[-3,6],[-55,81],[-142,139],[-338,272],[-13,13],[-14,17],[-84,147],[-110,118],[-8,11],[-5,12],[-14,86],[-4,11],[-8,4],[-67,7],[-11,3],[-10,6],[-7,15],[-3,10],[-2,8],[0,5],[0,10],[3,10],[19,39],[2,10],[-7,101],[1,5],[5,17],[0,5],[1,5],[-1,5],[-1,9],[-4,13],[-3,19],[0,11],[0,5],[1,5],[5,21],[83,220],[-60,23],[-331,5],[-9,0]],[[2578,5878],[-1,2],[0,105],[0,104],[0,105],[-1,104],[120,31],[119,31],[119,30],[119,31],[63,16],[139,35],[138,36],[63,16],[82,21],[28,14],[28,24],[49,57],[40,46],[40,46],[40,46],[40,46],[40,46],[40,45],[39,46],[40,46],[40,46],[40,46],[40,46],[40,46],[40,46],[39,45],[40,46],[40,46],[37,42],[23,27],[49,54],[46,49],[45,49],[45,49],[45,50],[63,68],[82,65],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[44,34],[42,34],[44,34],[43,34],[43,34],[43,33],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,33],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,33],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,33],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[43,34],[56,-17],[56,-16],[56,-17],[56,-17],[55,-16],[56,-17],[56,-17],[56,-16],[56,-17],[55,-17],[56,-16],[56,-17],[56,-16],[55,-17],[56,-17],[56,-16],[64,-20],[74,-51],[36,-39],[79,-87],[79,-87],[79,-87],[79,-88],[29,-31],[9,-6],[10,1],[40,27],[108,73],[108,73],[108,73],[108,73],[1,0],[0,1],[8,-53],[7,-53],[8,-53],[7,-53],[8,-53],[7,-53],[8,-53],[8,-53],[7,-53],[8,-53],[7,-53],[8,-53],[8,-53],[7,-53],[5,-36],[6,-36],[4,-34],[1,-17],[0,-44],[1,-62],[1,-72],[1,-71],[1,-62],[0,-44],[0,-17],[1,-22],[2,-14],[10,-12],[30,-20],[12,-11],[22,-33],[15,-29],[10,-21],[11,-21],[11,-20],[10,-21],[11,-21],[10,-20],[11,-21],[11,-21],[10,-21],[11,-20],[10,-21],[11,-21],[11,-20],[10,-21],[11,-21],[10,-21],[11,-20],[-26,-18],[-15,-33],[-6,-39],[6,-38],[7,-22],[9,-18],[11,-16],[52,-52],[17,-24],[37,-52],[37,-51],[37,-52],[37,-51],[15,-21],[9,-19],[1,-14],[-5,-14],[-13,-30],[-25,-58],[-25,-59],[-26,-58],[-25,-58],[-9,-22],[-20,-67],[-2,-35],[-3,-38],[-2,-39],[-3,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-39],[-2,-38],[-3,-39],[-2,-38],[-3,-39],[-3,-39],[-2,-38],[-3,-39],[-2,-38],[-3,-39],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-3,-39],[-2,-38],[-3,-39],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-38],[-2,-39],[-3,-39],[-2,-38],[-3,-39],[-2,-38],[-3,-32]],[[4534,3078],[40,-31],[9,-29],[3,-118],[3,-16],[25,-57],[7,-27],[21,-139],[3,-11],[6,-11],[11,-17],[7,-11],[13,-54],[9,-13],[11,-10],[54,-39],[9,-8],[5,-8],[3,-12],[3,-105],[1,-11],[4,-12],[8,-8],[31,-20],[10,-3],[8,0],[11,9],[58,67],[9,8],[11,3],[35,-2],[6,-1],[2,-3],[21,-43],[10,-15],[6,-5],[25,-15],[7,-3],[12,-7],[21,-22],[3,-2],[4,-1],[13,-1],[7,-4],[9,-8],[33,-40],[3,-2],[25,-16],[8,-6],[7,-8],[2,-5],[3,-7],[2,-9],[4,-29],[2,-6],[4,-9],[78,-71],[10,-13],[3,-13],[1,-15],[0,-26],[1,-11],[2,-10],[4,-17],[3,-30],[0,-29],[-1,-14],[-3,-12],[-8,-8],[-180,-119],[-4,-4],[-2,-3],[-2,-4],[-2,-5],[-3,-11],[-1,-5],[-1,-10],[1,-11],[2,-8],[5,-14],[9,-9],[11,-8],[21,-12],[10,-7],[6,-9],[0,-12],[-14,-65],[0,-13],[15,-119],[0,-15],[-4,-14],[-38,-93],[-3,-5]],[[5117,1282],[-1,0],[-19,7],[-9,6],[-21,22],[-18,14],[-15,18],[-7,8],[-17,8],[-161,30],[-21,-3],[-22,-12],[-230,-182],[-20,-8],[-22,0],[-66,16],[-14,-4],[-39,-59],[-30,-34],[-13,-9],[-4,-1],[-11,-2],[-47,-1],[-21,3],[-20,10],[-20,20],[-23,28],[-38,56],[-15,38],[-52,124],[-103,148],[-89,110],[-42,32],[-11,3]],[[3876,1668],[-1,14],[0,48],[1,14],[3,14],[8,13],[21,27],[7,14],[0,15],[-8,44],[0,13],[6,10],[9,10],[40,38],[7,7],[7,10],[3,3],[3,1],[3,2],[21,6],[6,3],[9,7],[2,3],[3,15],[6,90],[-1,42],[-3,8],[-4,23],[-3,19],[-2,9],[-19,62],[-4,22],[-1,26],[37,398],[-1,16],[2,16],[89,427]],[[6634,1416],[-307,-87],[-26,-14],[-120,-88],[-11,-15],[-3,-8],[-3,-21],[-3,-9],[-5,-6],[-2,-1],[-3,1],[-7,-3],[-12,-11],[-21,-26],[-109,-168],[-4,-7],[-8,-8],[-6,-6],[-5,-3],[-13,-1],[-134,21],[-12,-1],[-34,-13],[-23,0],[-184,29],[-22,3],[-167,65],[-23,17],[-11,17],[-18,40],[-13,18],[-27,27],[-15,9],[-17,5],[-8,0],[-16,-2],[-7,-3],[-7,-5],[-3,-2],[-2,0],[-6,3],[-5,4],[-63,75],[-16,25],[-8,10],[-8,5]],[[2828,1715],[-76,-24],[-34,-18],[-30,-27],[-99,-125],[-18,-11],[-19,-5],[-41,-2],[-1,-202],[-2,-20],[-20,-181],[-101,-208],[-41,-51],[-138,-137],[-3,-9],[7,-210],[-17,-111],[-1,-37],[16,-62],[3,-29],[-11,-28],[-11,-15],[-7,-12],[-2,-14],[3,-48],[2,-8],[4,-9],[2,-3],[3,-2],[15,-21],[7,-15],[3,-17],[-4,-18],[-8,-10],[-32,-26],[-5,5],[-6,13],[-5,4],[-2,7],[-1,7],[-3,6],[-3,14],[-2,7],[-3,3],[-4,3],[-4,5],[-21,40],[-5,6],[-7,6],[-23,11],[-29,23],[-7,3],[-6,6],[-8,1],[-9,-7],[-8,-5],[-9,3],[-8,7],[-5,7],[-9,14],[-15,32],[-4,15],[-3,19],[-2,18],[-2,5],[-8,8],[-2,5],[-109,130],[-26,50],[-6,16],[-2,4],[-6,2],[-21,3],[-7,4],[-7,5],[-5,7],[-4,19],[-5,10],[-6,9],[-13,9],[-31,36]],[[2592,2505],[43,-35],[14,-21],[48,-128],[6,-34],[26,-286],[2,-10],[60,-128],[36,-148],[1,0]],[[3876,1668],[-34,10],[-13,-4],[-36,-27],[-7,-2],[-4,3],[-4,4],[-6,4],[-179,93],[-179,93],[-20,6],[-93,-21],[-18,-12],[-7,-13],[-11,-31],[-9,-13],[-19,-18],[-7,-4],[-22,-6],[-91,5],[-64,-15],[-36,3],[-15,9],[-14,17],[-14,9],[-21,-3],[-125,-40]],[[2352,3405],[8,24],[5,33],[6,82],[4,16],[10,19],[36,48],[1,3],[0,2],[0,5],[0,1],[0,2],[0,1],[-1,1],[-7,25],[-3,23],[3,23],[56,180],[10,19],[12,17],[15,15],[27,18],[9,11],[5,20],[0,93],[0,73],[-1,113],[0,53],[2,8],[8,17],[3,9],[-4,83],[1,15],[4,15],[5,3],[6,0],[9,8],[0,89],[0,53],[-1,105],[0,104],[0,105],[-1,104],[0,105],[0,105],[0,58],[0,46],[0,105],[-1,104],[0,105],[0,105],[0,102]]],transform:{scale:[.001581896267426743,.0011822760442044297],translate:[.152941121000111,11.695773010000025]}},m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",
m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();