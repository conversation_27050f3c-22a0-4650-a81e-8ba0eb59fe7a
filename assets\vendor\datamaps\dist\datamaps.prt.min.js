!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo={type:"Topology",objects:{prt:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:"Setúbal"},id:"PT.SE",arcs:[[[0]],[[1,2,3,4,5]]]},{type:"MultiPolygon",properties:{name:"Azores"},id:"PT.AC",arcs:[[[6]],[[7]],[[8]],[[9]],[[10]],[[11]],[[12]],[[13]],[[14]]]},{type:"MultiPolygon",properties:{name:"Madeira"},id:"PT.MA",arcs:[[[15]],[[16]],[[17]],[[18]],[[19]]]},{type:"Polygon",properties:{name:"Aveiro"},id:"PT.AV",arcs:[[20,21,22,23]]},{type:"MultiPolygon",properties:{name:"Leiria"},id:"PT.LE",arcs:[[[24]],[[25,26,27,28,29]]]},{type:"Polygon",properties:{name:"Viana do Castelo"},id:"PT.VC",arcs:[[30,31]]},{type:"Polygon",properties:{name:"Beja"},id:"PT.BE",arcs:[[32,33,34,-3,35]]},{type:"Polygon",properties:{name:"Évora"},id:"PT.EV",arcs:[[36,-36,-2,37,38]]},{type:"MultiPolygon",properties:{name:"Faro"},id:"PT.FA",arcs:[[[39]],[[40]],[[41]],[[-34,42]]]},{type:"MultiPolygon",properties:{name:"Lisboa"},id:"PT.LI",arcs:[[[43]],[[44]],[[45]],[[-5,46,47]],[[48,49,-28]]]},{type:"Polygon",properties:{name:"Portalegre"},id:"PT.PA",arcs:[[50,-39,51,52]]},{type:"Polygon",properties:{name:"Santarém"},id:"PT.SA",arcs:[[53,-52,-38,-6,-48,54,-49,-27]]},{type:"Polygon",properties:{name:"Braga"},id:"PT.BR",arcs:[[55,56,-31,57,58]]},{type:"Polygon",properties:{name:"Bragança"},id:"PT.BA",arcs:[[59,60,61,62]]},{type:"Polygon",properties:{name:"Castelo Branco"},id:"PT.CB",arcs:[[63,-53,-54,-26,64,65]]},{type:"MultiPolygon",properties:{name:"Coimbra"},id:"PT.CO",arcs:[[[66]],[[67,68,-65,-30,69,-21]]]},{type:"Polygon",properties:{name:"Guarda"},id:"PT.GU",arcs:[[70,-66,-69,71,-60]]},{type:"Polygon",properties:{name:"Porto"},id:"PT.PO",arcs:[[72,73,-23,74,-56]]},{type:"Polygon",properties:{name:"Viseu"},id:"PT.VI",arcs:[[-72,-68,-24,-74,75,-61]]},{type:"Polygon",properties:{name:"Vila Real"},id:"PT.VR",arcs:[[-62,-76,-73,-59,76]]}]}},arcs:[[[8951,6971],[-2,-7],[-4,5],[0,3],[1,3],[3,1],[2,-5]],[[9066,7206],[16,-5],[3,-2],[1,-9],[-4,-13],[0,-5],[-1,-11],[-3,-7],[-6,-13],[-34,-38],[-14,-30],[0,-6],[0,-8],[2,-5],[1,-7],[2,-20],[12,-2],[15,-15],[12,-3],[10,-5],[2,2],[1,4],[-1,6],[-1,4],[2,3],[4,-1],[2,-3],[2,-4],[2,-4],[4,-4],[5,-3],[11,0],[6,4],[9,9],[7,3],[4,-3],[7,-15],[6,-9],[2,-4],[0,-1],[0,-1],[0,-2],[1,-5],[5,-23],[0,-5],[-2,-3],[0,-4],[2,-5],[6,-4],[3,-4],[2,-6],[-2,-15],[-6,-18],[0,-7],[2,-2],[9,-5],[5,-7],[9,-8],[3,-2],[4,-1],[4,0],[7,-5],[15,-19]],[[9219,6835],[10,-34],[3,-21],[0,-7],[0,-6],[-2,-9],[-14,-7],[-7,-5],[-14,-6],[-3,-3],[-2,-9],[-3,-7],[-14,1],[-3,-3],[-1,-5],[-1,-7],[-2,-14],[-3,-3],[-3,2],[-3,4],[-4,2],[-3,-1],[-2,-4],[-3,-1],[-1,-4],[0,-4],[0,-4],[-1,-4],[-2,-8],[-1,-2],[-1,-4],[-2,0],[-2,1],[-2,2],[-3,0],[-2,-8],[-1,-6],[-1,-5],[-1,-4],[-1,-6],[0,-4],[1,-5],[2,-8],[7,-10],[6,-12],[5,-4],[5,-1],[5,-1],[5,-7],[3,-8],[4,-18],[0,-23],[-3,-37],[-4,-22],[-2,-6],[-5,-23],[-12,11],[-2,0],[-10,0],[-18,5],[-35,-11],[-7,-5],[-4,-6],[-1,-6],[-2,-6],[-7,-11],[-2,-6],[-3,-19],[-1,-6],[-1,-7],[-2,-5],[-7,-4],[-4,1],[-7,4],[-3,4],[-3,3],[-3,1],[-3,-2],[-1,-4],[-2,-4],[-2,0],[-2,4],[-4,11],[-10,20],[-1,5],[-3,6],[-7,4],[-3,-2],[-3,-3],[-3,-2],[-3,0],[-2,2],[-2,5],[-4,7]],[[8964,6431],[2,15],[-1,27],[-2,20],[-6,21],[-14,4],[-16,16],[2,7],[3,3],[3,3],[3,5],[21,73],[7,58],[9,45],[-1,44],[-3,52],[-4,46],[-11,32],[-10,18],[-12,24],[-15,19],[-11,8],[6,10],[9,-6],[15,-21],[18,-11],[9,-7],[0,-11],[17,1],[40,-18],[20,11],[-12,1],[-11,4],[-29,22],[-3,7],[-3,11],[0,12],[0,10],[2,8],[4,4],[0,6],[-6,4],[-4,9],[-1,12],[3,9],[-7,2],[-7,-26],[-8,-5],[0,-5],[8,-2],[5,-12],[0,-13],[-4,-6],[-9,1],[-8,3],[-14,12],[-15,9],[-20,-6],[-18,-34],[3,-4],[-22,-15],[-39,-8],[-8,-5],[-7,-8],[-7,-2],[-10,5],[-2,0],[-1,4],[0,6],[1,5],[1,3],[3,7],[12,19],[3,26],[0,39],[-10,38],[-12,32],[-6,24],[8,7],[18,4],[17,2],[9,-8],[4,-10],[2,0],[-2,17],[6,5],[12,5],[6,2],[0,5],[6,11],[7,11],[11,14],[14,20],[2,2],[2,3],[2,1]],[[8908,7213],[4,2],[4,0]],[[8916,7215],[-1,-10],[2,-13],[3,-6],[4,-2],[5,1],[8,5],[4,4],[7,3],[4,-1],[4,-2],[3,-8],[12,-7],[2,0],[7,3],[6,13],[1,7],[5,22],[11,38],[3,1],[3,-2],[2,-4],[5,-5],[11,-6],[8,7],[6,5],[2,-2],[1,-4],[-1,-6],[0,-6],[-1,-6],[0,-7],[1,-7],[4,-9],[5,-2],[3,0],[11,-3]],[[2499,5724],[2,-17],[-6,-12],[-13,2],[-24,13],[-13,-6],[-7,0],[-3,9],[-2,10],[-5,9],[-3,9],[5,8],[8,13],[15,6],[15,1],[11,-2],[8,-9],[7,-15],[5,-19]],[[2234,6444],[51,-11],[6,3],[10,12],[6,2],[3,-2],[9,-11],[4,-4],[7,6],[40,21],[57,2],[15,-11],[10,-24],[1,-30],[-10,-26],[-15,-11],[-53,-11],[-18,-9],[-19,-3],[-18,2],[-9,-2],[-7,-5],[-11,11],[-10,7],[-23,10],[-35,2],[-48,59],[-14,32],[7,43],[31,5],[10,-5],[14,-19],[2,-7],[1,-10],[1,-8],[2,-6],[3,-2]],[[1291,6930],[3,-19],[-6,-9],[-12,0],[-31,7],[-9,-1],[-8,-4],[-15,-19],[-4,4],[-4,10],[-5,9],[-9,5],[-57,2],[-8,-7],[-26,40],[-9,25],[0,36],[12,18],[13,7],[28,-2],[16,-5],[40,-40],[14,-10],[1,-1],[5,-3],[7,-11],[5,-3],[8,-2],[26,-15],[18,-3],[7,-9]],[[1061,7004],[-5,-5],[-5,0],[-9,5],[-5,0],[-12,-6],[-5,0],[-9,7],[-10,26],[-8,12],[-19,18],[0,7],[13,4],[12,0],[5,2],[5,4],[4,6],[6,16],[4,6],[34,-26],[10,-14],[5,-37],[-3,-1],[-12,-9],[-4,-4],[2,-5],[1,-3],[2,-2],[3,-1]],[[1217,7172],[79,-49],[57,-51],[30,-18],[15,-13],[7,-15],[-7,-4],[-7,-2],[-16,1],[-6,4],[-13,11],[-12,4],[-7,4],[-7,6],[-5,7],[-6,6],[-11,-3],[-7,6],[-5,-6],[-4,3],[-7,15],[-5,5],[-41,23],[-7,8],[-8,22],[-3,3],[-3,-1],[-2,-1],[-1,0],[-4,2],[-1,3],[1,4],[-1,3],[-3,2],[-3,2],[-19,27],[-1,6],[0,10],[7,-5],[13,-14],[6,-3],[7,-2]],[[1688,7113],[-1,-7],[-3,-4],[-11,-8],[-10,6],[-27,1],[-9,11],[-5,-10],[-5,0],[-9,10],[-11,4],[-10,1],[-9,6],[-10,15],[-8,19],[-4,17],[-4,25],[9,20],[15,13],[14,4],[61,-6],[11,-8],[16,-16],[13,-19],[2,-16],[-1,-8],[3,-5],[4,-5],[1,-7],[-2,-3],[-9,-6],[-2,-3],[-1,-7],[2,-14]],[[1300,7483],[4,-4],[3,5],[6,-13],[13,-18],[7,-11],[2,-14],[-4,-10],[-8,-6],[-7,-1],[-8,0],[-8,3],[-8,5],[-7,9],[-4,14],[0,12],[7,25],[4,4],[4,2],[4,-2]],[[39,7808],[6,-1],[5,5],[7,-16],[4,-14],[0,-35],[-3,-19],[-7,-22],[-8,-15],[-7,8],[-7,-6],[-9,-1],[-8,2],[-6,5],[-3,11],[-3,16],[0,15],[4,9],[-2,14],[2,11],[3,13],[2,16],[3,13],[6,8],[8,4],[8,0],[-3,-11],[3,-6],[5,-4]],[[74,7946],[-13,-4],[0,5],[-7,25],[-1,4],[1,9],[3,8],[5,5],[5,1],[14,-5],[1,-24],[-8,-24]],[[6085,3],[0,-3],[-2,0],[0,4],[2,-1]],[[6152,91],[-2,-5],[-1,1],[-3,1],[-2,2],[0,1],[0,2],[1,1],[3,8],[4,-5],[0,-6]],[[5898,2052],[2,-21],[-14,39],[-3,8],[-3,7],[-1,9],[-1,10],[7,-11],[7,-19],[6,-22]],[[5668,2297],[11,-2],[11,4],[7,9],[6,4],[9,4],[15,3],[7,-4],[12,-16],[5,-3],[21,-27],[4,-5],[5,-2],[15,-1],[7,-3],[7,-7],[4,5],[4,2],[4,-2],[4,-5],[-7,-3],[-14,-11],[-5,-3],[-5,-6],[-13,-29],[-2,-7],[-7,-25],[-17,-11],[-19,-2],[-32,5],[-67,48],[-15,17],[-13,20],[-11,24],[-3,14],[-1,15],[2,12],[6,4],[3,4],[10,25],[3,3],[3,1],[3,-1],[4,-3],[4,-6],[10,-19],[4,-4],[2,0],[9,-5],[10,-11]],[[5968,2541],[8,-7],[3,6],[0,-25],[1,-9],[4,-6],[-14,4],[-6,0],[-5,-7],[-6,-11],[-4,-8],[-6,-2],[-8,7],[0,5],[4,1],[4,3],[2,6],[5,26],[8,13],[10,4]],[[9144,8530],[-3,-6],[0,-7],[-5,-10],[-9,1],[-2,-2],[-4,-6],[-2,-6],[-9,-19],[-2,-7],[-4,-6],[-3,-4],[-23,4],[-3,6],[0,5],[0,7],[1,9],[3,12],[2,6],[3,3],[1,1],[2,3],[0,2],[2,12],[1,5],[-1,6],[-4,14],[-3,16],[-4,0],[-6,-5],[-7,-7],[-3,-2],[-4,1],[-3,6],[-1,15],[0,8],[2,20],[-15,17],[-9,-21],[-1,-13],[-3,-9],[-14,2],[-44,71],[0,2]],[[8970,8654],[1,12],[7,37],[5,48],[2,3],[2,2],[0,-17],[0,-6],[8,21],[6,8],[5,-6],[3,0],[0,6],[-1,2],[-2,4],[-1,6],[1,4],[4,4],[2,-2],[2,-5],[2,-2],[4,2],[3,6],[5,14],[-6,6],[-4,10],[1,9],[8,4],[9,1],[7,5],[6,7],[6,9],[-6,-1],[-10,-8],[-9,-2],[-1,-2],[-3,0],[-9,6],[-3,7],[-3,23],[15,33],[-4,18],[0,-14],[-3,-11],[-3,-3],[-2,11],[-5,-17],[-5,-21],[-2,-23],[-1,-21],[-2,-13],[-4,-19],[-6,-18],[-5,-9],[39,280],[-1,19]],[[9022,9061],[1,0],[3,2],[18,4],[6,-7],[13,7],[6,7],[5,0],[4,-3],[3,-5],[6,-5],[4,1],[3,3],[2,11],[2,5],[7,12],[8,-13],[2,-5],[3,-9],[7,-9],[3,0],[2,4],[-1,5],[-2,5],[0,5],[0,4],[11,8],[8,-4],[19,21],[1,2],[2,9]],[[9168,9116],[24,-24],[2,-5],[1,-8],[-1,-6],[0,-18],[30,-22],[5,0],[5,1],[3,10],[1,3],[1,2],[6,7],[2,0],[2,-3],[0,-3],[0,-2],[0,-2],[-1,-4],[-12,-29],[-2,-9],[-2,-9],[1,-6],[2,-11],[2,-21],[3,-15],[0,-10],[-2,-5],[-4,-2],[-3,1],[-4,2],[-3,4],[-3,2],[-5,0],[-7,-6],[-5,-1],[-4,1],[-4,2],[-12,-2],[5,-25],[-1,-5],[-3,-9],[-9,-28],[-9,-16],[-9,-12],[-4,-10],[0,-4],[0,-4],[1,-5],[1,-5],[6,-9],[2,-7],[2,-6],[0,-23],[3,-11],[2,-2],[7,-5],[7,-9],[-17,-10],[-2,-4],[-3,-6],[1,-6],[4,-18],[3,-15],[2,-5],[2,-4],[3,-5],[5,-13],[-22,-14],[-9,-11],[-1,-4],[1,-6],[4,-11],[-1,-11],[-1,-7],[-12,-28],[2,-25]],[[8685,7745],[-4,-8],[1,9],[3,-1]],[[9233,8158],[-7,-25],[-4,-5],[-4,-4],[-3,-2],[-4,-2],[-4,-7],[-3,-7],[-2,-3],[-4,1],[-18,-9],[-2,-4],[-2,-4],[0,-5],[0,-5],[-3,-3],[-3,-7],[0,-4],[2,-9]],[[9172,8054],[-16,2],[-3,2],[-1,-4],[1,-6],[1,-16],[-1,-2],[-8,2],[-9,-6],[-5,-2],[-17,-10],[-7,-4],[0,4],[-1,5],[-1,7],[-3,19],[0,9],[-6,35],[-6,-3],[-5,-7],[-4,0],[-6,-3],[-19,-23],[-7,-6],[-5,-1],[-7,3],[-19,-13],[-2,-5],[-2,-8],[0,-6],[1,-15],[0,-9],[1,-7],[3,-4],[3,-4],[3,-7],[2,-13],[-2,-7],[-3,-5],[-3,-4],[-4,-4],[-8,-11],[3,-25],[1,-16],[5,-22],[-3,-6],[-10,-21],[-3,-2],[-4,0],[-3,2],[-3,1],[-2,-4],[-1,-6],[1,-6],[3,-9],[0,-2],[0,-2],[0,-3],[-1,-6],[-6,-6],[-19,3],[-26,-3],[-12,-8],[-5,-9],[-13,-42],[-4,-8],[-3,-4],[-8,-3],[-8,-24],[-2,-10],[2,-14],[1,-27]],[[8887,7645],[-11,4],[-7,6],[-6,1],[-5,-1],[-2,-2],[-4,-4],[-9,-16],[-9,-26],[-8,-15],[-9,-11],[-6,9],[-9,50],[-3,10],[-3,6],[-3,3],[-2,-1],[-5,2],[-4,-4],[-16,-16],[-16,0],[-4,-1]],[[8746,7639],[-2,20],[-5,16],[-14,24],[-3,9],[4,2],[18,-8],[30,31],[7,10],[8,2],[5,-10],[4,-2],[5,8],[-3,11],[-7,10],[7,16],[9,17],[3,9],[7,8],[12,12],[6,16],[4,13],[8,23],[1,14],[-1,16],[5,22],[2,31],[32,135],[36,168]],[[8924,8262],[2,-2],[11,-7],[3,-4],[6,-4],[13,-5],[4,1],[4,6],[10,7],[21,-4],[7,0],[9,-4],[5,-5],[6,-9],[4,-9],[1,-2],[3,-1],[4,0],[2,2],[1,2],[7,16],[2,4],[3,2],[3,0],[3,0],[2,-2],[3,-5],[3,-18],[0,-8],[0,-13],[1,-6],[1,-4],[2,-3],[3,3],[7,15],[11,10],[13,18],[8,-1],[1,4],[3,-1],[3,-4],[6,-17],[3,-16],[2,-11],[-1,-7],[-2,-5],[-3,-4],[-2,-2],[3,-2],[3,0],[17,10],[14,25],[4,1],[6,-4],[3,0],[3,4],[1,7],[-2,5],[-5,7],[-1,3],[-1,6],[0,5],[6,15],[17,25],[12,-8],[12,26],[5,-5],[3,-3],[2,-6],[2,-6],[-3,-23],[1,-14],[17,-43],[-5,-5],[-2,-4],[-1,-8],[1,-4],[0,-4],[1,-1],[-2,-10]],[[9212,9718],[-1,-1],[-5,0],[-38,-32],[-10,-6],[-35,-2],[-6,-2],[-5,-4],[-3,-3],[-7,-9],[-7,-4],[-3,0],[-4,1],[-3,-1],[-2,-2],[-3,-6],[-1,-6],[-1,-6],[-1,-13],[-1,-6],[-1,-6],[-3,-5],[-2,-6],[-1,-4],[2,-9],[-7,-20],[-3,1],[-4,1],[-4,4],[-8,2],[-12,7],[-3,3],[-2,-2],[-1,-3],[-4,-4],[-3,-2],[-20,-1],[-30,-16],[-3,-1],[-8,2],[-2,0]],[[8957,9557],[0,10],[-4,15],[0,19],[1,10],[9,5],[24,6],[7,5],[7,8],[5,8],[-44,-21],[-9,-2],[-5,1],[-8,7],[-7,27],[0,24],[3,37],[-3,24],[2,8],[3,8],[28,37],[14,23],[4,31],[5,5],[9,7],[10,5],[8,8],[3,8],[5,18],[3,9],[7,7],[7,3],[14,0],[8,5],[13,14],[8,5],[61,15],[7,6],[10,14],[4,4],[3,1],[7,0],[7,5],[12,13],[4,-8],[3,-12],[-1,-36],[1,-13],[5,-6],[6,2],[8,5],[8,1],[7,-5],[7,-9],[3,-12],[-1,-14],[-4,-11],[-7,-8],[-11,-12],[-12,-17],[-15,-35],[-4,-15],[-1,-16],[5,-14],[14,-9],[3,-7],[1,-10],[-2,-20]],[[9636,6725],[5,-8],[6,-1],[10,4],[10,7],[5,11],[2,-1],[1,0],[11,5],[11,2],[5,-7],[1,-1],[-4,-24],[-3,-12],[-5,-28],[-8,-25],[-3,-25],[-4,-10],[-3,-20],[-10,-2],[-23,15],[-7,1],[-3,-6],[-4,-17],[-4,-9],[-3,-3],[-6,-3],[-4,-4],[-5,1],[-5,3],[-6,0],[-4,-4],[-13,-5],[-2,-4],[0,-7],[1,-8],[0,-8],[-2,-15],[-9,-38],[-4,-29],[-11,-32],[-37,-50],[-7,-17],[-5,-25],[-4,-40],[-2,-12],[-2,-7],[-11,-18],[-4,-4],[1,-5],[0,-4],[-1,-4],[0,-4],[-4,-8],[-2,-3],[0,-9],[1,-1],[2,0],[2,-11],[0,-6],[1,-5]],[[9477,6185],[0,-1],[-10,-3],[-11,0],[-8,-7],[-8,-12],[-5,-5],[-3,-2],[-3,2],[-5,2],[-3,2],[-21,0],[-31,-21],[-7,-8],[-3,-3],[-2,-3],[-2,0],[-8,-6],[-3,-2],[-2,-2],[-1,-6],[-6,-1],[-7,-7],[-1,-5],[-2,-5],[-5,-6],[-11,-3],[-5,0],[-13,-3],[-6,-6],[-1,-6],[-1,-5],[1,-4],[3,-6],[0,-4],[-2,-2],[-14,-12],[-9,-12],[-6,-5],[-5,0],[-15,3],[-8,0],[-12,6],[-28,34],[-6,5],[-5,10],[-1,5],[-1,11],[-3,10],[-10,5],[-7,-2],[-4,-4],[-4,2],[-4,-1],[-9,-3],[-15,-9],[-2,-2],[-2,-6],[-1,-6],[-2,-8],[-7,-5],[-7,-5],[-17,5],[-6,4],[-15,15],[-5,1],[-16,-4],[-3,-2],[-2,-5],[-2,-4],[-2,-3],[-4,1],[-2,4],[-11,8],[-16,1],[-3,3],[-4,6],[-3,6],[-7,9],[-6,3],[-8,8],[-4,5]],[[8962,6119],[1,11],[3,23],[1,37],[-3,20],[-4,13],[-5,10],[-1,11],[6,36],[1,23],[4,21],[5,4],[12,4],[2,19],[0,4],[-4,-6],[-9,-4],[-10,0],[-3,14],[3,42],[3,30]],[[9219,6835],[2,8],[29,-1],[9,2],[6,-2],[2,-1],[5,-9],[4,-5],[3,-2],[4,0],[9,8],[4,-1],[3,-1],[12,-8],[9,-18],[34,-24],[20,-4],[10,-6],[26,-24],[6,-2],[26,-1],[7,-3],[9,-5],[18,-1],[5,2],[0,6],[6,14],[6,11],[12,9],[0,3],[7,6],[1,3],[4,5],[3,8],[2,8],[0,5],[1,6],[12,6],[13,11],[4,2],[2,0],[10,7],[4,0],[2,-1],[1,-3],[1,-2],[0,-4],[1,-3],[2,-4],[2,-4],[1,-6],[1,-6],[1,-7],[2,-5],[8,-13],[10,-8],[3,-5],[1,-5],[0,-6],[5,-17],[1,-6],[3,-12],[4,-5],[5,-6],[6,-1],[4,2],[2,4],[2,1]],[[9592,7205],[-17,-23],[-1,-3],[-4,-11],[-1,-6],[-3,-28],[0,-19],[0,-9],[0,-6],[2,-5],[3,-3],[2,-3],[2,-6],[-3,-12],[-16,-39],[-7,-41],[-4,-10],[4,-11],[0,-9],[-4,-10],[-6,-9],[6,-5],[11,-13],[42,-89],[2,-4],[2,-11],[2,-6],[3,-4],[7,-8],[2,-4],[16,-67],[4,-6]],[[9066,7206],[19,26],[18,6],[4,5],[8,13],[4,2],[5,-1],[3,2],[2,2],[2,3],[1,4],[1,5],[1,4],[3,0],[7,-4],[5,-1],[5,3],[3,0],[3,-1],[2,2],[1,4],[1,4],[3,0],[3,-3],[11,-19],[3,-4],[13,-4],[4,-3],[2,-4],[2,-5],[0,-5],[2,-3],[2,-1],[2,1],[2,-2],[2,-10],[2,-4],[6,-6],[7,11],[2,11],[0,8],[-3,4],[-3,2],[-3,1],[-7,11],[-2,8],[0,6],[2,14],[-2,7],[-2,2],[-5,1],[-20,15],[-3,3],[-3,5],[-1,6],[1,6],[3,4],[2,5],[2,7],[1,9],[0,14],[1,7],[2,5],[2,1],[5,0],[3,4],[3,10]],[[9205,7399],[8,11],[7,5],[3,3],[3,1],[11,1],[6,-4],[0,-4],[-1,-5],[-2,-11],[2,-11],[3,-5],[2,1],[1,5],[0,5],[1,5],[0,2],[1,1],[4,3],[6,1],[4,-2],[2,-1],[0,-1],[9,-16],[1,-7],[1,-12],[-1,-9],[2,-2],[9,-2],[4,1],[3,3],[3,4],[2,5],[7,7],[8,3],[5,-6],[18,-40],[11,-12],[4,-1],[5,0],[3,0],[9,0],[12,8],[4,6],[7,13],[3,-1],[6,-6],[3,-1],[4,3],[4,0],[4,-1],[9,-6],[3,2],[2,1],[2,3],[2,6],[2,6],[6,8],[6,3],[8,3],[6,4],[4,4],[2,6],[0,5],[-2,5],[-3,6],[6,6],[2,0],[1,1],[0,-1],[2,-7],[16,-11],[7,-10],[2,-8],[1,-15],[1,-5],[0,-8],[9,-13],[3,-16],[1,-11],[6,-14],[-2,-12],[3,-7],[5,-15],[3,-6],[6,-6],[7,1],[2,-2],[6,3],[8,14],[9,12],[11,6],[8,1],[5,-4],[4,-5],[2,-5],[0,-12],[-1,-5],[-6,-15],[0,-4],[-1,-8],[3,-5],[0,-1]],[[9322,5734],[0,-4],[-2,1],[0,9],[2,-3],[0,-3]],[[9339,5733],[-7,-8],[-10,-4],[-11,0],[-10,6],[0,6],[21,-5],[8,5],[9,16],[1,-2],[1,-3],[1,-6],[-2,-3],[-1,-2]],[[9332,5743],[-6,-3],[-3,5],[-2,1],[1,2],[2,0],[1,0],[4,2],[2,-1],[0,-2],[-2,0],[-1,-2],[2,-1],[2,-1]],[[9477,6185],[4,-9],[3,-5],[6,-10],[3,-5],[2,-15],[3,-20],[1,-18],[-1,-10],[7,-25],[3,-66],[4,-27],[-3,-12],[2,-19],[6,-36],[1,-4],[1,-4],[1,-2],[3,-2],[0,-5],[-3,-6],[-7,8],[-8,4],[-9,1],[-9,-2],[-9,-6],[-16,-18],[-6,-4],[-24,-25],[-3,-7],[-60,-68],[-13,-20],[-6,0],[-6,4],[-9,3],[-24,1],[-7,4],[-4,-9],[-4,1],[-5,5],[-4,3],[4,-10],[1,-5],[1,-7],[-9,22],[-14,20],[-29,26],[-16,6],[-13,9],[-13,-1],[-32,-7],[-14,8],[-15,9],[-12,-9],[-17,-4],[-18,11],[0,-2],[-22,20],[-17,5],[-18,-2],[-6,-3],[-5,-6],[-13,-23],[-24,0],[-5,-2],[-10,-11],[-6,-4],[-6,0],[-9,-5],[-9,-13],[-8,6],[-7,-12],[-3,-4],[-4,-3],[-8,-11],[-4,-3],[-7,4],[-4,7],[-4,5],[-6,-4],[3,23],[2,11],[2,5],[15,41],[6,22],[5,20],[-3,24],[7,12],[9,16],[6,36],[-3,14],[-3,13],[6,21],[11,21],[2,20],[7,27],[4,26]],[[8863,7260],[-7,-12],[-3,2],[4,14],[4,12],[4,8],[2,4],[2,1],[2,2],[0,-5],[-2,-12],[-6,-14]],[[8884,7284],[-6,-4],[-3,7],[2,11],[3,3],[2,-3],[3,-7],[-1,-7]],[[8877,7311],[-3,-4],[1,13],[3,12],[2,-5],[-3,-16]],[[8908,7213],[0,5],[-4,11],[-5,17],[-2,16],[3,13],[-10,-1],[-4,13],[-2,20],[-4,20],[17,58],[11,26],[14,17],[8,3],[17,3],[7,5],[15,17]],[[8969,7456],[0,-4],[-1,-3],[-2,-2],[-8,-16],[-15,-7],[1,-7],[5,-6],[-8,-18],[-2,-9],[-4,-8],[-4,-14],[-14,-60],[-6,-19],[-3,-6],[-5,-10],[0,-10],[7,-14],[1,-4],[2,-5],[3,-19]],[[8887,7645],[5,-20],[-1,-12],[28,-13],[16,8],[3,-12],[20,-32],[-5,-8],[-7,-3],[-12,1],[-4,-3],[-6,-6],[0,-5],[2,-5],[3,-2],[3,-4],[3,-10],[2,-5],[11,-17],[3,-4],[8,-5],[2,-3],[2,-3],[2,-5],[3,-11]],[[8968,7466],[-18,-21],[-34,-6],[-5,-7],[-8,-24],[-19,-45],[-22,-71],[-13,-23],[-4,-11],[-2,-13],[0,-16],[1,-27],[2,-20],[-6,-20],[-20,-10],[-23,-6],[-26,4],[-7,-9],[-7,-15],[-16,16],[-17,12],[-10,3],[-17,-4],[-8,4],[0,17],[3,14],[-5,36],[0,17],[2,8],[5,10],[9,16],[13,67],[-1,49],[-4,32],[4,18],[2,22],[15,46],[5,22],[8,30],[2,31],[2,26],[-3,21]],[[9461,7957],[3,-12],[13,-57],[7,-11],[12,-25],[6,-10],[14,-11],[3,-8],[4,-18],[6,-8],[9,-6],[16,-7],[3,-9],[0,-17],[-2,-19],[-3,-12],[-2,-12],[0,-18],[2,-17],[3,-10],[8,-10],[19,-39],[-5,-15],[-1,-17],[4,-17],[5,-12],[10,-9],[11,-5],[9,-7],[5,-16],[-1,-8],[-4,-16],[1,-7],[4,-7],[6,-5],[6,-1],[4,3],[10,9],[11,2],[10,-5],[9,-10],[7,-10],[5,-15],[3,-16],[-3,-14],[4,-6],[1,-7],[-2,-9],[-4,-8],[-13,-42],[-9,-20],[-9,-11],[6,-17],[-2,-12],[0,-12],[-3,-4],[-5,-6],[-5,-8],[-6,-5],[-19,-7],[-4,-5],[-2,-8],[-5,-10],[-7,-9],[-6,-4],[-6,-8]],[[9205,7399],[-18,9],[-5,8],[-1,6],[-1,6],[-3,9],[-4,6],[-3,6],[-1,7],[-1,8],[-4,6],[-4,4],[-9,7],[0,14],[11,29],[6,9],[22,26],[23,35],[10,12],[4,18],[5,8],[4,2],[8,-3],[6,2],[3,4],[1,4],[2,4],[9,19],[6,14],[2,6],[1,11],[3,4],[19,21],[8,20],[-1,5],[-1,6],[-7,8],[-15,26],[8,36],[8,20],[8,10],[5,4],[8,-4],[16,-14],[5,-8],[1,-2],[2,2],[2,2],[7,15],[3,4],[9,8]],[[9362,7858],[5,10],[6,13],[7,11],[7,5],[3,5],[4,13],[5,14],[6,7],[9,-1],[7,-5],[7,-2],[9,11],[5,8],[7,8],[8,4],[4,-2]],[[9172,8054],[3,-13],[4,-4],[5,-2],[5,-4],[2,-4],[2,-5],[0,-4],[0,-4],[0,-4],[0,-15],[-2,-8],[0,-16],[1,-29],[4,-11],[22,-14],[10,-11],[9,0],[14,2],[8,5],[0,4],[0,6],[-3,13],[-1,8],[2,11],[3,8],[5,43],[2,6],[8,2],[13,-7],[10,0],[2,-4],[1,-5],[1,-6],[0,-6],[1,-6],[0,-13],[0,-13],[1,-6],[2,-3],[5,-2],[4,-1],[9,-4],[5,-8],[8,-7],[8,-16],[8,-21],[2,-6],[3,-14],[4,-8]],[[8969,7456],[6,11],[4,12],[-11,-13]],[[9293,9353],[-1,-9],[-1,-3],[-1,-4],[-2,-3],[-2,-2],[-3,-4],[-1,-3],[-3,-3],[-3,-4],[-3,-2],[-4,0],[-4,5],[-9,8],[-4,-2],[-13,9],[-14,17],[1,7],[-2,5],[-8,13],[-20,12],[-6,-15],[-8,-9],[-12,-9],[-3,-1],[-9,-6],[-3,0],[-5,6],[-3,2],[-17,-4],[-12,-5],[-10,3],[-3,3],[-3,3],[-5,0],[-7,-3],[-7,-7],[-3,-4],[-3,-4],[-3,-1],[-3,-2],[-23,0],[-4,-1],[-1,0],[-3,2],[-2,6],[-2,11],[0,7],[2,6],[4,2],[3,2],[1,4],[-2,5],[-3,6],[-5,1],[-6,5],[-6,12],[-3,4],[-3,5],[-4,2],[-10,1],[-10,7],[-10,9],[-9,0]],[[8975,9433],[0,1],[-5,19],[-1,17],[-1,11],[-1,14],[-6,25],[-1,19],[-3,13],[0,5]],[[9212,9718],[0,-2],[34,-3],[18,8]],[[9264,9721],[1,-2],[0,-4],[-1,-5],[-2,-5],[-2,-6],[-3,-24],[-4,-10],[-3,-17],[-2,-8],[-2,-5],[-9,-16],[5,-1],[5,2],[3,1],[3,1],[3,0],[3,-1],[3,0],[8,6],[2,0],[3,-2],[10,-11],[2,-2],[1,-5],[-1,-11],[-1,-7],[-2,-37],[8,-6],[5,-1],[4,-3],[4,-3],[15,-5],[5,0],[12,5],[8,15],[4,-4],[2,-3],[7,-19],[-1,-10],[-16,-37],[-5,-8],[-7,-6],[-1,-2],[-3,-10],[-4,-6],[-3,1],[-2,0],[-2,-2],[-3,-8],[-4,-20],[2,-6],[-1,-4],[-1,-6],[-6,-15],[-1,-6],[-2,-2],[-1,-1],[-1,-3],[1,-5],[0,-5],[-4,-14]],[[9711,9087],[-6,3],[-17,1],[-36,28],[-4,9],[-3,11],[-2,11],[0,7],[2,8],[0,6],[-3,2],[-3,1],[-3,2],[-2,4],[-2,3],[2,6],[4,17],[-5,-3],[-15,-17],[-3,-8],[-8,-1],[-13,6],[-31,-4]],[[9563,9179],[-11,-1],[-13,7],[-5,7],[-12,27],[-6,7],[-6,0]],[[9510,9226],[2,11],[1,4],[2,4],[4,3],[5,1],[2,2],[-1,4],[0,6],[0,6],[2,11],[3,8],[4,7],[1,3],[-1,5],[1,9],[7,11],[14,7],[-10,9],[-3,6],[-3,12],[-2,16],[0,41],[13,15],[4,3],[3,3],[4,7],[3,13],[4,14],[4,7],[4,2],[3,1],[4,1],[3,3],[1,4],[0,5],[-2,6],[-2,5],[-2,6],[-1,6],[-1,11],[2,22],[13,69],[10,32],[3,6],[2,2],[3,4],[1,5],[-1,11],[0,7],[-7,16],[-4,15],[-1,8],[-1,14],[1,17],[1,9],[-2,16],[-1,4]],[[9599,9781],[5,10],[0,13],[-2,27],[1,11],[3,5],[6,7],[7,7],[5,1],[13,-12],[12,-17],[13,-9],[14,10],[9,11],[4,-3],[6,-5],[9,-11],[11,-4],[25,3],[10,9],[2,25],[12,-3],[15,-35],[12,-6],[28,2],[14,7],[5,17],[10,-6],[5,-10],[2,-14],[-3,-35],[3,-6],[7,-2],[10,-5],[-4,-16],[-10,-67],[-2,-8],[-3,-7],[-2,-7],[1,-8],[3,-7],[2,-7],[1,-16],[2,-16],[5,-13],[11,-7],[5,2],[6,5],[5,6],[3,5],[2,4],[6,-1],[32,-10],[20,-11],[18,-18],[16,-28],[6,-12],[4,-8],[-19,-44],[-10,-17],[-3,-8],[0,-6],[1,-6],[0,-5],[-15,-38],[0,-7],[-12,-2],[-6,-3],[-5,-7],[1,0],[0,-5],[0,-7],[-1,-4],[-2,-1],[-5,2],[-1,-1],[-13,-32],[-13,-20],[-7,-10],[-6,-6],[-23,-16],[-4,-4],[-26,-1],[-7,-2],[-4,-8],[-14,-33],[-3,-12],[-3,-7],[-14,-19],[-5,-10],[-1,-6],[0,-21],[-3,-15],[-16,-36],[-5,-9],[-11,-2],[-24,4],[-9,-7]],[[9705,8433],[-5,-1],[-17,-18],[-7,-5],[-3,-8],[-4,-19],[-4,-10],[4,-12],[2,-28],[5,-11],[5,-2],[11,-3],[4,-2],[4,-6],[5,-14],[9,-18],[16,-46],[-6,-18],[-2,-8],[0,-9],[-1,-8],[-3,-20],[-1,-3],[-4,-5],[-1,-4],[0,-4],[2,-9],[0,-4],[-1,-7],[0,-14],[-2,-8],[-10,-24],[-4,-4],[-9,-6],[-3,-4],[-3,-11],[1,-9],[2,-7],[0,-7],[-1,-8],[-5,-14],[-2,-7],[0,-8],[1,-6],[1,-6],[-5,-17],[-6,-4],[-59,-12],[-12,1],[-9,8],[-6,-3],[-16,1],[-8,-3],[-6,-4],[-6,-3],[-86,7],[1,-2]],[[9233,8158],[7,10],[1,5],[3,3],[1,2],[6,1],[4,8],[9,4],[7,0],[1,3],[1,4],[0,5],[3,1],[5,1],[2,3],[2,2],[4,1],[3,0],[2,-2],[2,-1],[2,2],[5,12],[3,3],[3,1],[8,1],[4,4],[0,4],[-2,11],[2,7],[5,9],[9,7],[6,2],[5,0],[3,-2],[4,-2],[8,-11],[1,17],[-1,14],[2,5],[2,2],[2,-1],[1,-3],[1,-3],[3,-1],[4,1],[8,8],[3,6],[2,4],[0,5],[0,5],[0,5],[-2,9],[-11,12],[-7,13],[-4,6],[-2,5],[-2,11],[1,6],[9,32]],[[9371,8414],[7,6],[7,1],[5,3],[15,18],[11,7],[10,10],[9,15],[7,18],[10,-6],[14,5],[4,5],[2,5],[3,12],[1,5],[6,15],[20,31],[15,-21],[10,-4],[4,0],[13,6],[8,16],[4,1],[4,1],[7,2],[3,-1],[4,-3],[-3,-49],[-5,-22],[-3,-6],[-4,-6],[-4,-9],[-1,-6],[1,-5],[1,-2],[3,-3],[2,-1],[5,-2],[8,-3],[10,-4],[18,11],[19,0],[5,3],[3,4],[1,6],[2,7],[2,6],[4,6],[3,-1],[2,-4],[2,-6],[4,-11],[3,-5],[9,-3],[10,1],[6,3],[4,-1],[2,-1],[10,-8],[7,-6],[4,-8],[1,-3]],[[8949,8344],[5,-3],[12,-10],[-8,-7],[-4,1],[-6,9],[-1,4],[-1,6],[1,-2],[2,2]],[[9144,8530],[5,-3],[4,-5],[12,-6],[9,-10],[6,-9],[7,-5],[3,-5],[2,0],[1,3],[1,3],[1,4],[1,4],[1,2],[17,5],[23,-6],[31,36],[21,14],[18,20],[7,6],[9,23],[14,16],[4,9],[4,4],[9,3]],[[9354,8633],[4,-18],[2,-3],[3,-6],[0,-6],[-2,-8],[-2,-5],[-3,-3],[-4,-2],[-2,-4],[-2,-6],[3,-8],[4,-3],[7,-3],[2,-3],[-1,-8],[4,-30],[2,-6],[6,-8],[1,-5],[-1,-5],[-9,-10],[-3,-7],[-3,-8],[-2,-13],[-4,-16],[10,-10],[7,-15]],[[8924,8262],[14,64],[6,12],[4,-11],[7,-5],[16,-1],[0,6],[-7,10],[-6,7],[-7,4],[-18,4],[-7,7],[-4,12],[0,17],[5,24],[12,38],[26,164],[5,40]],[[9711,9087],[-1,-15],[-5,-11],[1,-11],[4,-9],[13,-21],[5,-15],[9,-33],[3,-17],[3,-7],[6,-3],[5,-5],[2,-10],[-2,-11],[-6,-3],[-1,-21],[0,-5],[2,-9],[-1,-5],[-1,-4],[0,-8],[0,-16],[0,-12],[5,-27],[4,-28],[0,-14],[0,-10],[-2,-14],[-10,-27],[-2,-13],[1,-14],[3,-9],[5,-7],[4,-11],[2,-15],[-1,-10],[-9,-21],[-8,-28],[3,-27],[9,-24],[13,-20],[-7,-20],[-3,-6],[-17,-20],[-3,-4],[-1,-7],[-1,-7],[0,-6],[-1,-5],[-7,-8],[-8,-1],[-8,1],[-3,-1]],[[9354,8633],[4,7],[12,3],[4,2],[10,12],[12,6],[13,15],[16,10],[27,7],[-2,17],[10,45],[5,11],[2,20],[-7,15],[-9,9],[-4,12],[5,9],[1,4],[0,6],[-1,6],[-2,5],[-10,12],[-4,7],[0,6],[1,5],[5,10],[4,13],[5,23],[1,3],[3,5],[5,4],[10,-2],[4,-2],[6,-7],[13,-23],[2,-3],[3,-6],[7,0],[4,9],[9,33],[0,9],[-1,8],[-2,5],[-1,16],[5,5],[15,-6],[12,18],[4,15],[0,8],[-1,7],[-2,5],[-3,2],[-4,4],[-3,4],[-3,10],[-1,4],[2,4],[9,-1],[5,6],[-2,22],[2,15],[3,25],[11,53]],[[9293,9353],[7,-13],[0,-8],[0,-7],[3,-7],[6,-5],[11,-12],[-2,-11],[2,-12],[6,-20],[6,-6],[1,-1],[-5,-33],[-1,-20],[-2,-19]],[[9325,9179],[-17,-18],[-49,-26],[-11,-2],[-40,5],[-13,-5],[-27,-17]],[[9022,9061],[0,12],[-4,55],[0,29],[7,12],[0,5],[-15,5],[-7,23],[-6,28],[-7,24],[2,10],[-1,10],[-2,9],[-2,10],[0,25],[-2,8],[-3,2],[-1,9],[-2,9],[-6,14],[-2,12],[0,11],[0,23],[4,27]],[[9325,9179],[21,21],[8,1],[6,-3],[9,-15],[5,-6],[7,-3],[7,-1],[8,2],[7,4],[11,12],[6,5],[7,1],[1,0],[16,-3],[19,4],[47,28]],[[9264,9721],[13,21],[4,10],[5,8],[6,4],[6,-2],[8,1],[3,10],[2,14],[2,10],[8,4],[1,-22],[3,-24],[13,0],[2,5],[2,5],[2,5],[4,2],[11,-1],[24,9],[12,9],[6,-2],[11,-13],[7,-5],[21,-1],[-6,-26],[0,-8],[7,-6],[7,-1],[8,2],[8,5],[6,6],[3,6],[0,6],[1,5],[3,4],[3,0],[6,-7],[4,0],[5,-3],[5,-11],[4,-15],[2,-13],[5,7],[10,17],[5,6],[6,2],[15,-1],[7,2],[28,15],[13,13],[4,8]]],
transform:{scale:[.0025081462417241712,.0012125599964890141],translate:[-31.284901495999918,30.029242255000057]}},m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();