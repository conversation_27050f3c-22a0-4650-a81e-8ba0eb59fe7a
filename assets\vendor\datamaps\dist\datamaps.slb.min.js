!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo={type:"Topology",objects:{slb:{type:"GeometryCollection",geometries:[{type:"MultiPolygon",properties:{name:null},id:"-99",arcs:[[[0]],[[1]],[[2]],[[3]],[[4]],[[5]],[[6]],[[7]],[[8]],[[9]],[[10]],[[11]]]},{type:"MultiPolygon",properties:{name:"Malaita"},id:"SB.ML",arcs:[[[12]],[[13]],[[14]],[[15]],[[16]]]},{type:"MultiPolygon",properties:{name:"Rennell and Bellona"},id:"SB.RB",arcs:[[[17]],[[18]]]},{type:"MultiPolygon",properties:{name:"Central"},id:"SB.GC",arcs:[[[19]],[[20]],[[21]],[[22]],[[23]],[[24]]]},{type:"Polygon",properties:{name:"Guadalcanal"},id:"SB.GC",arcs:[[25,26]]},{type:"MultiPolygon",properties:{name:"Isabel"},id:"SB.IS",arcs:[[[27]],[[28]],[[29]],[[30]],[[31]]]},{type:"MultiPolygon",properties:{name:"Temotu"},id:"SB.TE",arcs:[[[32]],[[33]],[[34]],[[35]],[[36]],[[37]],[[38]],[[39]],[[40]]]},{type:"MultiPolygon",properties:{name:"Western"},id:"SB.WE",arcs:[[[41]],[[42]],[[43]],[[44]],[[45]],[[46]],[[47]],[[48]],[[49]],[[50]],[[51]],[[52]],[[53]],[[54]],[[55]]]},{type:"MultiPolygon",properties:{name:"Choiseul"},id:"SB.CH",arcs:[[[56]],[[57]],[[58]]]},{type:"MultiPolygon",properties:{name:"Makira"},id:"SB.MK",arcs:[[[59]],[[60]]]},{type:"Polygon",properties:{name:"Capital Territory (Honiara)"},id:"SB.CT",arcs:[[61,-26]]}]}},arcs:[[[5208,1931],[5,-24],[-14,2],[-6,8],[2,7],[13,7]],[[5231,1974],[-9,-4],[-9,5],[-2,17],[2,3],[3,8],[-1,22],[11,10],[10,-15],[6,-24],[-11,-22]],[[7723,2184],[-11,-5],[2,8],[7,14],[3,14],[0,11],[3,19],[5,4],[12,-16],[4,-17],[-8,-25],[-6,-6],[-11,-1]],[[4851,2827],[11,-26],[-5,-30],[2,21],[-5,9],[-1,-5],[-2,-2],[-2,4],[1,5],[2,1],[0,5],[0,1],[-7,7],[-4,16],[4,10],[6,-16]],[[3394,4688],[6,-13],[4,-31],[-5,-5],[-4,-1],[-3,8],[-8,17],[-5,1],[-2,3],[-1,4],[0,1],[-1,0],[-2,-1],[-2,-3],[1,0],[4,-2],[1,-14],[-3,-2],[-1,7],[-2,5],[-3,-18],[-3,-14],[0,4],[-6,11],[4,21],[4,6],[2,7],[2,1],[5,6],[5,-5],[4,3],[9,4]],[[386,7566],[4,-3],[4,0],[5,-2],[2,-12],[-14,-29],[-12,-8],[-4,-6],[-5,1],[-2,12],[-1,11],[0,15],[4,9],[19,12]],[[3033,9307],[3,-11],[-6,4],[3,7]],[[2984,9352],[10,-12],[-8,4],[-2,8]],[[3156,9322],[-3,-5],[0,32],[1,6],[3,-12],[-1,-21]],[[2903,9395],[-5,-4],[-7,7],[-5,15],[3,4],[10,-11],[4,-11]],[[2935,9853],[-4,-10],[-1,24],[5,-14]],[[2944,9996],[-2,-8],[-5,11],[7,-3]],[[4858,3572],[0,-11],[-1,-7],[-5,-10],[-1,-10],[-1,-5],[-3,-4],[4,-19],[10,-114],[0,-23],[-3,-23],[-11,16],[-10,31],[-8,37],[-9,96],[2,18],[7,20],[9,3],[10,-2],[10,7]],[[4443,3949],[23,-58],[19,-70],[9,-19],[4,-12],[6,-30],[6,-15],[7,-14],[6,-15],[10,-32],[5,-25],[1,-24],[-1,-49],[3,-23],[11,-46],[2,-30],[-4,-31],[-7,-25],[-7,-4],[-3,27],[-31,62],[-7,33],[-5,15],[-8,8],[-8,-5],[-6,-38],[-8,-23],[-12,23],[-13,32],[-11,34],[-4,28],[0,84],[0,80],[-2,30],[0,18],[13,39],[-3,22],[-6,23],[-2,28],[-4,-2],[-8,-4],[-3,-3],[0,29],[-5,26],[-16,49],[5,7],[4,-1],[6,-16],[10,-19],[26,-29],[5,-13],[3,-22]],[[5433,5355],[-4,-2],[-6,14],[3,6],[8,-1],[9,-6],[-3,-5],[-3,-4],[-4,-2]],[[3974,5393],[7,-11],[7,-25],[3,-16],[2,-12],[0,-14],[2,-7],[10,-12],[4,-4],[25,-71],[39,-75],[30,-47],[18,-42],[7,-26],[3,-26],[-6,-28],[-14,-16],[-17,-11],[-14,-14],[5,-18],[3,-18],[0,-18],[-12,-38],[0,-17],[2,-18],[2,-20],[3,-20],[14,-24],[4,-13],[4,-13],[11,3],[18,15],[7,-5],[20,-28],[7,-13],[0,-10],[-3,-8],[-2,-8],[5,-13],[6,-6],[5,1],[3,3],[1,2],[18,-15],[19,-24],[17,-30],[13,-34],[-10,7],[-7,-11],[-2,-16],[6,-8],[9,-7],[9,-16],[4,-22],[-4,-21],[7,-7],[3,-9],[1,-11],[0,-14],[1,-15],[4,-3],[5,-1],[5,-5],[4,-6],[6,-4],[4,-9],[2,-14],[-3,-7],[-19,-26],[14,-12],[5,-7],[3,-9],[10,7],[8,-5],[5,-12],[-3,-18],[6,-2],[14,-8],[0,-9],[-20,-9],[-8,-1],[-2,-7],[0,-16],[4,-16],[4,-7],[4,-11],[5,-50],[9,-15],[-10,-10],[-5,8],[-4,14],[-7,7],[-7,-8],[2,-15],[6,-16],[30,-56],[8,-22],[24,-107],[7,-12],[7,-12],[11,-26],[15,-47],[9,-107],[-1,-30],[-5,-17],[-8,-5],[-11,11],[-39,49],[-7,1],[-2,23],[-5,21],[-30,71],[-4,12],[-2,14],[-1,27],[-3,15],[-8,23],[-35,67],[-52,47],[-17,38],[-36,9],[4,24],[-21,43],[-10,13],[-5,2],[-13,-2],[-5,5],[-2,10],[1,22],[-1,10],[-18,28],[-42,35],[-17,31],[-54,182],[-14,67],[-10,33],[4,10],[2,8],[-2,8],[-4,10],[0,20],[-12,69],[-10,30],[-31,113],[-3,19],[0,74],[-2,12],[-24,97],[13,16],[22,34],[14,7],[0,12],[-18,72],[-30,71],[-3,13],[-10,9],[-26,46],[-8,20],[-9,61],[15,21],[28,-6],[28,-19],[15,-25],[5,-4],[8,3],[8,8],[5,11],[-3,12],[-23,27],[2,10],[5,9],[7,6],[7,-1],[0,-9],[17,0],[9,-10],[15,-37],[9,-13],[8,-8]],[[3859,5986],[-5,-2],[-7,1],[-14,36],[-4,17],[3,16],[9,4],[5,-8],[4,-15],[9,-49]],[[3412,1091],[8,-1],[18,2],[7,-6],[15,-24],[16,-19],[42,-36],[22,-30],[27,-23],[21,-31],[16,-12],[33,-18],[31,-25],[83,-107],[45,-51],[14,-30],[-18,-22],[-9,-1],[-6,5],[-7,2],[-9,-6],[-4,-10],[-6,-28],[-5,-10],[-11,1],[-9,18],[-8,23],[-8,15],[-11,-9],[-5,0],[-4,3],[-28,34],[-4,9],[-8,59],[-7,24],[-15,21],[-8,4],[-16,2],[-8,4],[-9,10],[-2,8],[2,9],[0,10],[-1,14],[0,10],[-2,11],[-8,12],[-6,5],[-8,4],[-9,2],[-7,-2],[-4,-6],[-7,-24],[-5,-7],[-6,-2],[-9,-1],[-16,3],[-13,9],[-29,29],[-15,9],[4,14],[2,5],[-13,18],[-13,-2],[-10,-10],[-8,-6],[-14,6],[-9,16],[-22,48],[-3,19],[-4,10],[-5,10],[-2,7],[5,24],[5,16],[15,25],[5,17],[3,-6],[2,-2],[3,-1],[3,-1],[11,-21],[7,-9],[8,-7]],[[3261,1348],[1,-23],[-17,13],[-15,6],[-13,11],[-12,26],[6,1],[5,-1],[5,-4],[5,-5],[13,-3],[13,-7],[9,-14]],[[3247,4371],[6,-26],[3,-31],[-2,-26],[-10,-11],[-11,9],[-6,21],[-1,28],[5,26],[5,2],[3,0],[4,2],[4,6]],[[3674,4316],[-11,-37],[-10,-11],[-5,-10],[-16,10],[-16,16],[-14,10],[-15,-8],[3,-16],[-11,-12],[-18,-7],[-15,-2],[3,11],[0,8],[-5,6],[-9,3],[4,13],[7,6],[17,0],[9,4],[8,11],[7,12],[5,10],[9,67],[8,29],[13,-2],[20,-65],[8,-10],[12,-1],[9,-2],[7,-6],[6,-10],[-10,-17]],[[2785,4411],[4,-7],[10,5],[-2,-34],[-11,-22],[-34,-29],[1,37],[3,26],[12,49],[8,2],[15,33],[8,11],[0,-6],[-1,-4],[-2,-3],[-2,-5],[0,-14],[-1,-3],[1,-11],[-7,-13],[-2,-12]],[[3489,4521],[13,-10],[14,2],[13,-3],[11,-28],[6,0],[7,23],[13,-4],[15,-19],[13,-22],[5,-31],[-5,-39],[-11,-35],[-12,-22],[-2,11],[-3,4],[-4,-1],[-6,-4],[-1,16],[-4,13],[-7,7],[-9,1],[2,-8],[3,-20],[-5,0],[-12,36],[-10,13],[-11,-7],[-12,-9],[-14,-1],[-13,5],[-11,10],[-1,8],[0,12],[-2,12],[-4,5],[-5,2],[-5,4],[-2,8],[-1,10],[6,3],[14,3],[14,7],[7,19],[6,-10],[3,11],[1,9],[-1,8],[-3,11],[2,6],[2,1],[6,-7]],[[2722,4530],[5,-19],[1,-14],[2,-12],[8,-11],[3,0],[6,15],[6,2],[15,-9],[-25,-92],[-11,-19],[-35,-19],[-7,8],[-5,14],[-6,8],[-9,-11],[-19,49],[2,15],[23,1],[-5,18],[3,13],[7,5],[10,-8],[0,18],[4,-2],[11,-6],[-1,14],[-3,13],[-4,11],[-7,9],[11,0],[7,-6],[6,-1],[4,5],[3,11]],[[3458,4549],[0,-28],[-11,8],[-6,-10],[-9,-37],[-9,13],[1,14],[5,16],[3,19],[-4,36],[3,12],[11,3],[12,-28],[4,-18]],[[3327,3920],[11,-24],[21,-15],[23,5],[7,-14],[11,9],[0,15],[4,26],[-10,13]],[[3394,3935],[5,8],[6,3],[10,-5],[17,-12],[15,-8],[8,-2],[8,0],[7,6],[14,18],[7,4],[16,-3],[24,-12],[15,-3],[5,2],[6,12],[4,4],[4,0],[7,-8],[4,-1],[56,0],[3,2],[8,6],[5,1],[1,-3],[3,-6],[5,-6],[15,-12],[15,-39],[30,-19],[13,-25],[21,-59],[28,7],[34,-28],[31,-42],[20,-39],[5,-23],[3,-28],[2,-58],[4,-11],[8,0],[12,6],[10,-2],[7,-7],[16,-19],[25,-12],[6,-6],[2,-14],[-1,-19],[2,-17],[10,-7],[2,-7],[1,-15],[0,-29],[1,-14],[4,-8],[5,-8],[5,-13],[-1,-41],[-20,-23],[-56,-21],[-30,-17],[-16,-15],[-13,-20],[-55,25],[-48,41],[-16,5],[-31,-10],[-9,5],[-7,12],[-6,16],[-6,14],[-3,1],[-9,-2],[-3,1],[-2,5],[-5,18],[-4,6],[-22,16],[-6,8],[-12,7],[-84,6],[-63,-17],[-34,-1],[-21,4],[-8,0],[-7,-4],[-5,5],[-16,4],[-17,8],[-16,3],[-17,5],[-25,24],[-12,7],[-15,-4],[-12,-10],[-12,-1],[-15,20],[-35,64],[-14,16],[-20,19],[-1,6],[1,16],[0,6],[-3,6],[-6,3],[-4,5],[-3,12],[-1,14],[-1,11],[-6,5],[-12,3],[-3,9],[2,12],[1,15],[-7,30],[-9,28],[-11,25],[-12,23],[-11,15],[-5,10],[-2,13],[9,9],[8,10],[4,14],[-2,15],[-10,28],[-4,13],[0,16],[5,36],[0,15],[-4,18],[-1,14],[-8,15],[-3,4],[4,9],[7,15],[2,47],[5,20],[11,8],[12,6],[47,51],[26,-16],[27,-31],[40,-66],[30,-40],[5,-12],[4,-20],[8,-23],[10,-22],[10,-15],[13,-9]],[[3116,5223],[23,-34],[9,-15],[-1,-9],[-8,-14],[-7,-7],[-7,-1],[-8,2],[-9,6],[-9,8],[-6,9],[-11,21],[-9,26],[-6,5],[-10,-13],[-11,21],[-11,12],[-9,16],[0,36],[5,36],[10,23],[15,13],[21,3],[22,-9],[6,-22],[-2,-64],[5,-29],[8,-20]],[[2188,6462],[9,-21],[13,-11],[14,-8],[12,-2],[11,-9],[12,-19],[7,-23],[-2,-15],[-12,-2],[-11,12],[-9,19],[-4,18],[-8,-18],[-14,6],[-29,31],[1,-13],[9,-33],[-7,0],[-19,8],[-18,0],[1,9],[-3,20],[-9,19],[-15,8],[-3,5],[-3,9],[-2,10],[3,5],[7,-2],[12,-6],[4,-1],[2,-3],[6,-2],[4,3],[-4,15],[0,8],[5,6],[7,0],[6,-9],[9,-7],[18,-7]],[[2318,6453],[8,0],[6,16],[8,14],[11,-7],[3,5],[2,3],[2,2],[4,1],[6,-27],[10,-18],[30,-22],[3,8],[5,10],[5,8],[5,3],[9,-6],[4,-14],[3,-17],[5,-16],[14,-25],[9,-12],[8,-5],[4,-9],[12,-40],[14,-14],[12,-28],[6,-13],[9,-10],[27,-18],[14,-14],[22,-36],[16,-15],[-8,-27],[6,-25],[12,-12],[11,7],[30,-23],[8,-10],[5,-17],[3,-21],[5,-11],[11,7],[20,-29],[17,14],[14,6],[14,-2],[16,-8],[-9,-8],[2,-12],[8,-12],[7,-5],[13,-3],[7,-6],[4,-12],[4,-17],[13,17],[7,-11],[6,-22],[8,-12],[24,-4],[10,5],[5,18],[14,-12],[5,-6],[6,-10],[12,16],[8,-16],[6,-28],[8,-24],[12,-17],[21,-21],[10,-14],[2,-2],[9,-7],[2,-4],[1,-11],[2,-3],[35,-51],[69,-75],[145,-198],[3,-14],[-16,-30],[-15,-39],[-6,-8],[-7,7],[-8,4],[-9,-2],[-6,-9],[1,-8],[11,-15],[7,-23],[7,0],[7,6],[6,12],[7,-11],[22,-57],[6,-20],[3,-20],[0,-28],[22,-31],[9,-23],[-10,-16],[-7,2],[-24,26],[-3,1],[-9,-2],[-4,1],[-3,7],[-2,9],[-3,9],[-13,9],[-8,25],[-16,9],[-5,9],[-11,25],[-23,34],[-7,14],[-2,13],[1,12],[-1,9],[-12,3],[-8,9],[-35,57],[-63,24],[-27,29],[-18,14],[-17,26],[-12,12],[-13,5],[-10,-6],[-25,27],[-14,8],[-13,-7],[-7,31],[-7,18],[-9,7],[-10,1],[-10,7],[-7,11],[-1,18],[-16,-10],[-17,6],[-15,15],[-70,93],[-5,2],[-11,-3],[-5,1],[-3,9],[-1,12],[0,12],[-1,6],[-6,1],[-3,-5],[-2,-6],[-5,-1],[-20,19],[-5,1],[-11,-2],[-5,1],[0,4],[-3,19],[-2,5],[-8,7],[-7,4],[-4,7],[-1,20],[-5,-6],[-16,-13],[-2,27],[-6,21],[-11,14],[-14,4],[-44,38],[-4,52],[-6,24],[-13,-6],[-23,10],[-11,12],[6,20],[-9,17],[-7,11],[-5,0],[-8,-18],[-8,17],[-10,49],[-9,18],[-11,16],[-13,9],[-13,-7],[-7,25],[-33,33],[-12,18],[-3,19],[0,14],[2,14],[1,14],[0,57],[-58,89],[-39,38],[-7,21],[-1,23],[5,16],[13,-3],[16,-17],[73,-46],[6,-8]],[[2100,6595],[5,-15],[10,5],[8,-6],[18,-27],[9,13],[16,-2],[18,-12],[13,-18],[-7,-7],[-5,-9],[-1,-13],[3,-17],[-33,12],[-9,6],[-6,12],[-6,15],[-8,14],[-23,10],[-16,20],[-10,4],[-5,7],[-1,16],[3,13],[6,1],[7,-9],[8,-5],[6,-8]],[[2073,6721],[0,-37],[3,2],[5,3],[3,3],[-3,-12],[-2,-14],[-1,-14],[0,-15],[-11,12],[-4,7],[-5,-48],[-10,14],[-7,18],[-8,15],[-16,1],[0,8],[10,20],[5,0],[7,-3],[20,34],[14,6]],[[9994,8],[-13,-8],[-3,12],[6,11],[5,10],[8,-1],[2,-19],[-5,-5]],[[8601,878],[-14,-4],[-5,16],[4,22],[10,18],[14,2],[7,-4],[0,-13],[-4,-21],[-12,-16]],[[8530,962],[18,-6],[32,-21],[-9,-44],[1,-19],[-1,-27],[41,6],[4,-54],[-56,-7],[-16,-4],[-12,1],[-22,5],[-11,27],[-9,15],[-10,7],[-4,11],[-13,29],[-10,20],[0,23],[6,14],[8,8],[15,13],[14,0],[17,-2],[17,5]],[[8301,1414],[-5,-33],[-18,-33],[-5,-1],[-8,-4],[-6,3],[-6,7],[0,9],[0,12],[5,10],[3,10],[-1,18],[-11,-11],[-9,-13],[-4,12],[2,31],[10,25],[14,22],[10,6],[11,-8],[12,-17],[6,-19],[0,-26]],[[7923,2245],[13,-12],[2,-7],[3,-6],[22,6],[22,-2],[3,-34],[-8,-15],[-3,-27],[-28,-20],[-9,-15],[-14,-11],[-8,-12],[-14,2],[-5,-2],[-20,13],[-9,-13],[-7,-9],[-8,5],[-27,-8],[-32,-66],[-22,-61],[-10,17],[3,37],[-9,20],[-9,3],[-22,-35],[-22,3],[-10,32],[-3,54],[0,29],[14,18],[7,19],[9,21],[7,1],[-4,-47],[11,-23],[8,-3],[4,17],[0,21],[3,36],[12,32],[23,38],[34,5],[53,-11],[24,19],[26,-9]],[[7686,2564],[-7,-4],[-6,3],[-4,13],[1,15],[2,10],[4,6],[11,-5],[6,-18],[-1,-14],[-6,-6]],[[8059,2839],[0,-66],[5,3],[3,3],[11,1],[10,-13],[-8,-25],[-14,-24],[-10,-3],[-1,20],[1,18],[-5,1],[-2,9],[5,29],[3,29],[-1,26],[3,-8]],[[8773,3288],[-6,-1],[-9,11],[-5,25],[3,12],[8,-1],[8,-18],[3,-18],[-2,-10]],[[8709,3410],[-3,-3],[-9,5],[10,2],[2,-4]],[[1564,4910],[7,-13],[6,-13],[5,-6],[15,-13],[11,-29],[-1,-29],[-20,-13],[-6,4],[-27,25],[-36,17],[-40,31],[-18,24],[2,21],[28,-7],[12,-1],[11,8],[5,-10],[6,18],[4,8],[5,4],[7,-2],[4,-7],[3,-7],[4,-4],[13,-6]],[[2024,4745],[-12,-8],[-15,41],[-3,7],[-8,8],[0,17],[5,35],[4,13],[22,30],[4,12],[3,16],[3,18],[0,20],[5,0],[0,-29],[7,-20],[5,-32],[2,-31],[-3,-19],[21,-15],[-1,-13],[-3,-11],[-4,-7],[-17,-8],[-15,-24]],[[1881,5137],[10,-7],[8,7],[7,13],[8,11],[15,6],[19,0],[16,-9],[6,-21],[2,-29],[-1,-10],[-6,-12],[-7,-4],[-8,1],[-7,-1],[-3,-11],[-4,-5],[-14,-10],[-2,-9],[4,-8],[15,-4],[6,-6],[4,4],[3,3],[8,2],[-9,-23],[-5,-8],[-6,-7],[17,-31],[-7,-32],[-30,-49],[-10,-26],[-3,-7],[-7,-8],[-13,-5],[-6,-6],[-11,-3],[-16,6],[-15,9],[-7,7],[-2,9],[-4,6],[-9,8],[-4,7],[-4,14],[-3,7],[-22,35],[-4,8],[0,70],[5,48],[4,8],[4,8],[3,12],[0,19],[20,-23],[8,0],[3,23],[5,-1],[3,-4],[2,-15],[6,18],[-2,13],[-4,15],[0,21],[5,9],[26,19],[6,2],[2,2],[2,5],[6,-9],[0,-10],[-7,-8],[-2,-11],[1,-11],[5,-12]],[[1404,5104],[-2,-5],[-4,-6],[-4,-1],[-2,2],[-3,0],[-3,0],[-3,4],[-4,2],[-5,-6],[-1,-7],[1,-30],[-6,-40],[-1,-22],[4,-18],[28,-53],[15,-19],[17,-8],[0,-9],[-12,-5],[-14,1],[-13,7],[-10,10],[-9,16],[-5,6],[-7,2],[-6,5],[-2,14],[1,15],[0,13],[-5,15],[-20,50],[-9,10],[-12,7],[-16,17],[-15,21],[-6,17],[2,20],[5,17],[7,13],[7,11],[20,19],[11,15],[14,52],[21,37],[23,26],[14,0],[14,-17],[11,-26],[8,-34],[3,-40],[-3,-8],[-11,-24],[-7,-45],[-2,-7],[-2,-4],[-2,-10]],[[1235,5433],[0,-10],[-22,23],[-11,51],[-14,47],[-30,11],[2,4],[5,10],[3,5],[-12,10],[-13,18],[-6,19],[11,10],[10,-15],[19,-3],[19,-7],[8,-28],[2,-17],[8,-53],[4,-46],[6,-19],[11,-10]],[[1194,5667],[18,-11],[18,2],[16,-2],[15,-25],[7,-29],[5,-36],[4,-39],[-1,-38],[-5,0],[-1,20],[-6,3],[-7,-1],[-7,6],[-3,10],[-7,38],[-7,24],[-8,21],[-11,17],[-15,13],[5,9],[-7,1],[-4,3],[0,6],[1,8]],[[978,5809],[3,-22],[8,3],[12,-60],[-19,2],[-23,35],[3,42],[16,0]],[[1598,5626],[6,-22],[-2,-17],[-4,-16],[2,-16],[6,10],[4,-2],[5,-6],[6,-2],[5,3],[9,11],[7,5],[14,5],[12,0],[12,-7],[22,-29],[3,-5],[4,-11],[1,-9],[3,-29],[10,-23],[19,-24],[8,-19],[-2,-35],[2,-12],[4,-12],[12,-17],[4,-8],[6,-24],[5,-47],[4,-24],[4,-4],[5,-1],[4,-3],[3,-11],[0,-13],[-3,-11],[-14,-45],[-2,-8],[-2,-16],[0,-12],[1,-12],[2,-12],[2,-10],[-46,33],[-20,3],[0,-8],[10,-12],[7,-17],[3,-22],[-5,-25],[-5,0],[-2,23],[-5,14],[-6,9],[-3,6],[-2,20],[-7,19],[-16,32],[-20,19],[-41,7],[-19,15],[-33,42],[-18,39],[-3,4],[-1,20],[-6,31],[-3,19],[0,85],[6,-16],[3,-16],[1,-38],[4,-17],[18,-9],[4,-12],[2,-14],[5,2],[6,13],[2,18],[-4,14],[-18,30],[-7,45],[-8,33],[-12,31],[-13,21],[-9,4],[-7,-4],[-8,-6],[-11,-4],[-5,5],[-6,10],[-5,2],[-5,-17],[-8,9],[-6,0],[-12,-9],[0,-2],[-6,-5],[-4,-2],[-25,-1],[-28,5],[-9,-4],[-6,-20],[-5,-25],[-8,-25],[-17,-15],[-13,5],[-26,27],[-7,-4],[-5,0],[-5,127],[2,9],[11,27],[4,6],[11,1],[9,1],[7,6],[7,12],[4,-18],[5,-5],[6,4],[5,10],[2,12],[1,30],[3,14],[-3,16],[4,19],[17,36],[7,20],[11,46],[8,18],[32,57],[11,14],[12,9],[23,11],[12,8],[-1,-7],[1,-1],[2,-1],[3,-1],[52,-53],[20,-3],[-5,-25],[2,-17],[5,-17],[3,-21],[-1,-53],[1,-25],[5,-21],[-7,-25],[3,-18],[17,-27]],[[823,5623],[-13,-11],[-6,17],[-19,100],[-11,31],[-2,30],[5,66],[-8,70],[0,29],[13,14],[11,-18],[10,-20],[3,-14],[5,-33],[10,-31],[4,-23],[2,-25],[1,-19],[4,-36],[0,-19],[-4,-17],[6,-29],[-2,-34],[-9,-28]],[[1259,5739],[-13,-40],[-16,-18],[-16,33],[-6,-4],[-3,-1],[-6,5],[-38,-6],[-39,58],[-30,82],[-12,65],[2,18],[2,16],[7,31],[18,50],[3,11],[10,18],[23,16],[26,12],[18,5],[34,-33],[25,-48],[16,-59],[7,-66],[1,-65],[-2,-23],[-11,-57]],[[786,6136],[-15,-14],[-14,13],[1,12],[6,13],[8,12],[7,-1],[3,-4],[7,-11],[-3,-20]],[[828,6476],[2,-12],[4,-10],[10,-16],[2,-6],[-1,-7],[0,-4],[12,-2],[2,-4],[1,-8],[0,-11],[3,-5],[7,-6],[7,-9],[7,-32],[7,-9],[15,-9],[-5,-10],[10,-20],[17,12],[17,-11],[16,-18],[14,-10],[6,-20],[-11,-43],[-28,-69],[-14,-21],[-18,-35],[-12,-38],[5,-33],[7,-16],[1,-15],[-6,-12],[-10,-7],[-2,5],[-6,9],[-9,4],[-8,-9],[-5,0],[-2,27],[3,28],[-2,21],[-35,17],[-3,23],[8,57],[-3,11],[-7,9],[-7,8],[-4,5],[-10,37],[-5,10],[-13,8],[0,2],[-6,1],[-1,4],[1,6],[-8,31],[-5,5],[-9,-10],[-14,62],[5,62],[20,51],[30,32],[10,-4],[7,-6],[6,-1],[7,11]],[[47,6814],[12,-11],[8,-15],[7,-16],[9,-14],[-16,-26],[-16,-7],[-38,5],[-13,12],[5,25],[21,47],[5,-3],[4,0],[12,3]],[[232,7229],[22,-10],[6,-5],[10,-24],[0,-18],[-4,-19],[-3,-23],[-6,-9],[-12,-7],[-14,-2],[-9,3],[-13,-20],[-12,1],[-13,3],[-14,-12],[-7,6],[-8,15],[-6,7],[-21,12],[-4,7],[1,19],[20,76],[6,-7],[2,20],[5,25],[12,9],[-2,10],[0,9],[2,9],[10,-7],[26,-41],[26,-27]],[[446,7424],[-3,-17],[11,-30],[4,-8],[6,-6],[14,-7],[6,-5],[-7,-8],[-8,-2],[-8,3],[-8,7],[-2,-21],[-3,-21],[-6,-19],[-10,-14],[-1,10],[-1,6],[-2,4],[-1,8],[-5,-11],[-6,-7],[-7,-3],[-8,1],[12,15],[12,19],[2,11],[-4,9],[-4,10],[-2,12],[3,19],[3,6],[7,3],[5,4],[-4,8],[-7,7],[-2,0],[-10,26],[-7,27],[2,28],[20,23],[16,0],[0,-10],[-10,-5],[-8,-10],[-6,-16],[-2,-20],[7,-12],[13,-6],[9,-8]],[[1502,6721],[20,-7],[21,5],[19,-1],[13,-26],[4,11],[6,-11],[-5,-10],[-1,-15],[1,-15],[5,-15],[-21,27],[0,-20],[-8,-8],[-23,1],[0,8],[4,5],[7,9],[4,5],[-29,16],[-9,8],[-6,2],[-19,-2],[-8,4],[13,16],[12,13]],[[1713,6627],[6,-19],[-20,-2],[-11,1],[-13,10],[-5,-3],[-5,-4],[-6,-2],[-5,3],[-5,13],[-5,3],[-14,15],[7,35],[25,53],[36,-23],[5,-10],[1,-20],[9,-50]],[[806,7738],[7,-1],[8,5],[3,3],[28,-15],[20,-27],[39,-80],[10,-17],[34,-41],[32,-16],[47,-58],[32,-27],[27,-10],[13,-11],[9,-6],[8,-9],[4,-16],[2,-10],[7,-2],[9,1],[7,-3],[8,-11],[16,-41],[28,-42],[7,-21],[2,-24],[1,-58],[8,-62],[13,-52],[7,-18],[8,-14],[7,-18],[4,-25],[11,12],[4,7],[6,-24],[10,-18],[13,-11],[15,-4],[10,-8],[7,-20],[5,-24],[1,-19],[5,-11],[9,-9],[22,-13],[9,-9],[9,-13],[8,-17],[5,-17],[5,0],[2,23],[5,1],[8,-9],[9,-6],[16,0],[7,3],[27,18],[15,1],[10,-13],[4,-32],[0,-33],[-3,-19],[-9,-8],[-21,-1],[-9,-10],[-19,-65],[-2,16],[-3,9],[-5,1],[-6,-6],[-10,14],[-18,8],[-34,5],[-14,9],[-11,17],[-3,26],[7,32],[-6,-4],[-5,-6],[-9,-18],[-6,5],[-3,-4],[-1,-20],[-14,15],[-27,17],[-16,15],[-40,6],[-6,-1],[-5,-13],[-11,0],[-11,14],[-8,42],[-8,20],[-10,18],[-9,8],[-7,-7],[-14,-10],[-11,-3],[6,20],[-7,23],[-11,18],[-14,12],[-17,4],[-10,7],[-12,18],[-70,149],[-3,13],[-2,17],[-3,15],[-7,7],[-10,4],[-6,12],[-8,30],[-29,75],[-9,11],[-8,13],[-26,81],[-12,16],[-36,34],[-11,6],[-9,8],[-84,133],[-2,4],[-15,24],[-5,19],[-6,75],[0,27],[6,19],[25,39],[12,4],[18,-5],[15,-10],[9,-18],[5,-4],[5,-7],[3,-12],[3,-12]],[[4702,2760],[-17,-16],[-5,2],[-3,6],[-1,7],[4,9],[3,13],[-3,6],[-5,2],[-3,2],[-10,34],[0,18],[10,15],[16,-24],[13,-38],[1,-36]],[[4375,2856],[4,-8],[42,14],[79,-64],[81,-91],[44,-66],[14,10],[10,-10],[15,-42],[11,-13],[12,-2],[11,1],[10,-1],[9,-7],[22,-25],[5,-10],[7,-13],[16,-1],[17,7],[11,12],[28,-17],[13,-1],[11,9],[6,-11],[11,-26],[8,-10],[9,-5],[25,5],[12,-5],[4,3],[4,29],[5,7],[6,3],[8,0],[14,-12],[21,-51],[8,-12],[9,-9],[6,-21],[15,-74],[4,-9],[5,-8],[5,-3],[4,-6],[4,-45],[6,-20],[10,-16],[27,-32],[7,-17],[3,-22],[0,-78],[-2,-7],[-5,-3],[-5,-7],[-5,-9],[-3,-8],[5,-34],[24,-7],[30,2],[24,-8],[8,7],[5,-11],[-1,-17],[-10,-8],[-10,3],[-9,5],[-9,1],[-10,-9],[-6,6],[-8,3],[-12,1],[-11,3],[-53,38],[-18,-4],[-16,-28],[-9,8],[-9,3],[-21,-1],[-7,3],[-5,8],[-5,9],[-6,7],[-33,24],[-12,18],[-6,5],[-10,-2],[-22,-14],[-7,3],[-7,7],[-31,17],[1,-3],[-4,-4],[-10,-4],[-5,3],[-8,14],[-2,3],[-8,4],[-5,9],[-6,24],[-4,-20],[-1,-9],[-16,22],[-9,5],[-11,-7],[-2,8],[-5,12],[-3,7],[-7,-5],[-6,9],[-4,14],[2,10],[-7,16],[-9,2],[-10,-3],[-10,5],[-6,13],[-2,16],[-4,12],[-11,6],[-4,8],[-10,17],[-10,12],[-5,-5],[-4,-12],[-8,9],[-14,26],[0,12],[-3,8],[-5,5],[-7,4],[1,10],[-1,10],[-4,10],[-6,8],[-7,-13],[-9,-5],[-10,2],[-11,6],[16,85],[-9,10],[0,13],[0,14],[-4,15],[-5,2],[-15,-6],[-8,-1],[7,16],[5,22],[0,20],[-12,8],[7,34],[5,16],[6,7],[4,3],[-2,6],[-5,9],[-4,2],[-12,-2],[-4,0],[-6,8],[-3,9],[-5,8],[-10,3],[-20,0],[-9,4],[-3,11],[-5,5],[-11,1],[-16,-2],[-4,-4],[-4,-6],[-4,-6],[-5,-3],[-6,2],[-5,6],[-19,5],[-9,6],[-6,10],[-3,20],[7,30],[-4,15],[4,7],[2,4],[1,4],[4,3],[-5,22],[-1,29],[7,26],[14,9],[4,-4],[4,-3],[4,-4]],[[3327,3920],[16,-3],[29,2],[8,2],[7,6],[7,8]]],
transform:{scale:[.0013319203014301542,.0007290361197119726],translate:[155.50798587300008,-12.290622653999918]}},m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();