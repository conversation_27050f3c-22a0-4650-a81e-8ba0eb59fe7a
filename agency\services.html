<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Crafted for agencies and studios specializing in web design and development.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Agency | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Crafted for agencies and studios specializing in web design and development.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Agency | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Crafted for agencies and studios specializing in web design and development.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Agency | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>
</head>

<body class="bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="sticky top-4 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full before:absolute before:inset-0 before:max-w-5xl before:mx-2 lg:before:mx-auto before:rounded-[26px] before:bg-neutral-800/30 before:backdrop-blur-md">
    <nav class="relative max-w-5xl w-full flex flex-wrap md:flex-nowrap basis-full items-center justify-between py-2 ps-5 pe-2 md:py-0 mx-2 lg:mx-auto">
      <div class="flex items-center">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/agency/index.html" aria-label="Preline">
          <svg class="w-28 h-auto" width="116" height="32" viewBox="0 0 116 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M33.5696 30.8182V11.3182H37.4474V13.7003H37.6229C37.7952 13.3187 38.0445 12.9309 38.3707 12.5369C38.7031 12.1368 39.134 11.8045 39.6634 11.5398C40.1989 11.2689 40.8636 11.1335 41.6577 11.1335C42.6918 11.1335 43.6458 11.4044 44.5199 11.946C45.3939 12.4815 46.0926 13.291 46.6158 14.3743C47.139 15.4515 47.4006 16.8026 47.4006 18.4276C47.4006 20.0095 47.1451 21.3452 46.6342 22.4347C46.1295 23.518 45.4401 24.3397 44.5661 24.8999C43.6982 25.4538 42.7256 25.7308 41.6484 25.7308C40.8852 25.7308 40.2358 25.6046 39.7003 25.3523C39.1709 25.0999 38.737 24.7829 38.3984 24.4013C38.0599 24.0135 37.8014 23.6226 37.6229 23.2287H37.5028V30.8182H33.5696ZM37.4197 18.4091C37.4197 19.2524 37.5367 19.9879 37.7706 20.6158C38.0045 21.2436 38.343 21.733 38.7862 22.0838C39.2294 22.4285 39.768 22.6009 40.402 22.6009C41.0421 22.6009 41.5838 22.4254 42.027 22.0746C42.4702 21.7176 42.8056 21.2251 43.0334 20.5973C43.2673 19.9633 43.3842 19.2339 43.3842 18.4091C43.3842 17.5904 43.2704 16.8703 43.0426 16.2486C42.8149 15.6269 42.4794 15.1406 42.0362 14.7898C41.593 14.4389 41.0483 14.2635 40.402 14.2635C39.7618 14.2635 39.2202 14.4328 38.777 14.7713C38.34 15.1098 38.0045 15.59 37.7706 16.2116C37.5367 16.8333 37.4197 17.5658 37.4197 18.4091ZM49.2427 25.5V11.3182H53.0559V13.7926H53.2037C53.4622 12.9124 53.8961 12.2476 54.5055 11.7983C55.1149 11.3428 55.8166 11.1151 56.6106 11.1151C56.8076 11.1151 57.02 11.1274 57.2477 11.152C57.4754 11.1766 57.6755 11.2105 57.8478 11.2536V14.7436C57.6632 14.6882 57.4077 14.639 57.0815 14.5959C56.7553 14.5528 56.4567 14.5312 56.1859 14.5312C55.6073 14.5312 55.0903 14.6574 54.6348 14.9098C54.1854 15.156 53.8284 15.5007 53.5638 15.9439C53.3052 16.3871 53.176 16.898 53.176 17.4766V25.5H49.2427ZM64.9043 25.777C63.4455 25.777 62.1898 25.4815 61.1373 24.8906C60.0909 24.2936 59.2845 23.4503 58.7182 22.3608C58.1519 21.2652 57.8688 19.9695 57.8688 18.4737C57.8688 17.0149 58.1519 15.7346 58.7182 14.6328C59.2845 13.531 60.0816 12.6723 61.1096 12.0568C62.1437 11.4413 63.3563 11.1335 64.7474 11.1335C65.683 11.1335 66.5539 11.2843 67.3603 11.5859C68.1728 11.8814 68.8806 12.3277 69.4839 12.9247C70.0932 13.5218 70.5672 14.2727 70.9057 15.1776C71.2443 16.0762 71.4135 17.1288 71.4135 18.3352V19.4155H59.4384V16.978H67.7111C67.7111 16.4117 67.588 15.91 67.3418 15.473C67.0956 15.036 66.754 14.6944 66.317 14.4482C65.8861 14.1958 65.3844 14.0696 64.812 14.0696C64.2149 14.0696 63.6856 14.2081 63.2239 14.4851C62.7684 14.7559 62.4114 15.1222 62.1529 15.5838C61.8944 16.0393 61.762 16.5471 61.7559 17.1072V19.4247C61.7559 20.1264 61.8851 20.7327 62.1437 21.2436C62.4083 21.7545 62.7807 22.1484 63.2608 22.4254C63.741 22.7024 64.3103 22.8409 64.9689 22.8409C65.406 22.8409 65.8061 22.7794 66.1692 22.6562C66.5324 22.5331 66.8432 22.3485 67.1018 22.1023C67.3603 21.8561 67.5572 21.5545 67.6927 21.1974L71.3304 21.4375C71.1458 22.3116 70.7672 23.0748 70.1948 23.7273C69.6285 24.3736 68.896 24.8783 67.9974 25.2415C67.1048 25.5985 66.0738 25.777 64.9043 25.777ZM77.1335 6.59091V25.5H73.2003V6.59091H77.1335ZM79.5043 25.5V11.3182H83.4375V25.5H79.5043ZM81.4801 9.49006C80.8954 9.49006 80.3937 9.29616 79.9752 8.90838C79.5628 8.51444 79.3566 8.04356 79.3566 7.49574C79.3566 6.95407 79.5628 6.48935 79.9752 6.10156C80.3937 5.70762 80.8954 5.51065 81.4801 5.51065C82.0649 5.51065 82.5635 5.70762 82.9759 6.10156C83.3944 6.48935 83.6037 6.95407 83.6037 7.49574C83.6037 8.04356 83.3944 8.51444 82.9759 8.90838C82.5635 9.29616 82.0649 9.49006 81.4801 9.49006ZM89.7415 17.3011V25.5H85.8083V11.3182H89.5569V13.8203H89.723C90.037 12.9955 90.5632 12.343 91.3019 11.8629C92.0405 11.3767 92.9361 11.1335 93.9887 11.1335C94.9735 11.1335 95.8322 11.349 96.5647 11.7798C97.2971 12.2107 97.8665 12.8262 98.2728 13.6264C98.679 14.4205 98.8821 15.3684 98.8821 16.4702V25.5H94.9489V17.1719C94.9551 16.304 94.7335 15.6269 94.2841 15.1406C93.8348 14.6482 93.2162 14.402 92.4283 14.402C91.8989 14.402 91.4311 14.5159 91.0249 14.7436C90.6248 14.9714 90.3109 15.3037 90.0831 15.7408C89.8615 16.1716 89.7477 16.6918 89.7415 17.3011ZM107.665 25.777C106.206 25.777 104.951 25.4815 103.898 24.8906C102.852 24.2936 102.045 23.4503 101.479 22.3608C100.913 21.2652 100.63 19.9695 100.63 18.4737C100.63 17.0149 100.913 15.7346 101.479 14.6328C102.045 13.531 102.842 12.6723 103.87 12.0568C104.905 11.4413 106.117 11.1335 107.508 11.1335C108.444 11.1335 109.315 11.2843 110.121 11.5859C110.934 11.8814 111.641 12.3277 112.245 12.9247C112.854 13.5218 113.328 14.2727 113.667 15.1776C114.005 16.0762 114.174 17.1288 114.174 18.3352V19.4155H102.199V16.978H110.472C110.472 16.4117 110.349 15.91 110.103 15.473C109.856 15.036 109.515 14.6944 109.078 14.4482C108.647 14.1958 108.145 14.0696 107.573 14.0696C106.976 14.0696 106.446 14.2081 105.985 14.4851C105.529 14.7559 105.172 15.1222 104.914 15.5838C104.655 16.0393 104.523 16.5471 104.517 17.1072V19.4247C104.517 20.1264 104.646 20.7327 104.905 21.2436C105.169 21.7545 105.542 22.1484 106.022 22.4254C106.502 22.7024 107.071 22.8409 107.73 22.8409C108.167 22.8409 108.567 22.7794 108.93 22.6562C109.293 22.5331 109.604 22.3485 109.863 22.1023C110.121 21.8561 110.318 21.5545 110.454 21.1974L114.091 21.4375C113.907 22.3116 113.528 23.0748 112.956 23.7273C112.389 24.3736 111.657 24.8783 110.758 25.2415C109.866 25.5985 108.835 25.777 107.665 25.777Z" class="fill-white" fill="currentColor" />
            <path d="M1 29.5V16.5C1 9.87258 6.37258 4.5 13 4.5C19.6274 4.5 25 9.87258 25 16.5C25 23.1274 19.6274 28.5 13 28.5H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <path d="M5 29.5V16.66C5 12.1534 8.58172 8.5 13 8.5C17.4183 8.5 21 12.1534 21 16.66C21 21.1666 17.4183 24.82 13 24.82H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <circle cx="13" cy="16.5214" r="5" class="fill-white" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->
      </div>

      <!-- Button Group -->
      <div class="md:order-3 flex items-center gap-x-3">
        <div class="md:ps-3">
          <a class="group inline-flex items-center gap-x-2 py-2 px-3 bg-[#ff0] font-medium text-sm text-nowrap text-neutral-800 rounded-full focus:outline-hidden" href="#">
            Request demo
          </a>
        </div>

        <div class="md:hidden">
          <button type="button" class="hs-collapse-toggle size-9 flex justify-center items-center text-sm font-semibold rounded-full bg-neutral-800 text-white disabled:opacity-50 disabled:pointer-events-none" id="hs-pro-an-collapse" aria-expanded="false" aria-controls="hs-pro-an" aria-label="Toggle navigation" data-hs-collapse="#hs-pro-an">
            <svg class="hs-collapse-open:hidden shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="3" x2="21" y1="6" y2="6" />
              <line x1="3" x2="21" y1="12" y2="12" />
              <line x1="3" x2="21" y1="18" y2="18" />
            </svg>
            <svg class="hs-collapse-open:block hidden shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>
      </div>
      <!-- End Button Group -->

      <!-- Collapse -->
      <div id="hs-pro-an" class="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow md:block" aria-labelledby="hs-pro-an-collapse">
        <!-- Nav -->
        <div class="overflow-hidden overflow-y-auto max-h-[75vh] [&::-webkit-scrollbar]:w-0">
          <div class="flex flex-col md:flex-row md:items-center md:justify-end gap-y-3 pt-6 pb-2 md:py-0 md:ps-7">
            <a class="md:px-3 md:py-4 text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" href="index.html" aria-current="page">Home</a>

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-ancpd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Company
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-ancpd">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="about.html">About</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="services.html">Services</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers.html">Careers</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers-job-details.html">Careers: Job Details</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers-apply.html">Careers: Apply</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anstd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Stories
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anstd">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="case-study.html">Case Study</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="case-study-details.html">Case Study Details</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anbld" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                News
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anbld">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="news.html">News</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="news-details.html">News Details</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anpd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Product
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-150 hidden z-10 top-full end-0 rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anpd">
                <div class="flex flex-col gap-y-1">
                  <!-- Grid -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-1">
                    <div class="p-5 min-h-50 flex flex-col justify-between bg-neutral-900 rounded-t-xl md:rounded-tr-none md:rounded-tl-xl">
                      <!-- Heading -->
                      <div class="mb-5">
                        <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                          Build
                          <span class="ms-auto size-6 flex shrink-0 justify-center items-center bg-[#ff0] text-black rounded-sm">
                            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M5 12h14"></path>
                              <path d="m12 5 7 7-7 7"></path>
                            </svg>
                          </span>
                        </a>
                      </div>
                      <!-- End Heading -->

                      <!-- List -->
                      <ul class="flex flex-col">
                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Websites
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Mobile apps
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Pages
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>
                      </ul>
                      <!-- End List -->
                    </div>
                    <!-- End Col -->

                    <div class="p-5 min-h-50 flex flex-col justify-between bg-neutral-900 md:rounded-tr-xl">
                      <!-- Heading -->
                      <div class="mb-5">
                        <a class="group flex items-center gap-x-3 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                          Resources
                          <span class="ms-auto size-6 flex shrink-0 justify-center items-center bg-[#ff0] text-black rounded-sm">
                            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M5 12h14"></path>
                              <path d="m12 5 7 7-7 7"></path>
                            </svg>
                          </span>
                        </a>
                      </div>
                      <!-- End Heading -->

                      <!-- List -->
                      <ul class="flex flex-col">
                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Documentation
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Support
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>
                      </ul>
                      <!-- End List -->
                    </div>
                    <!-- End Col -->
                  </div>
                  <!-- End Grid -->

                  <!-- Footer -->
                  <div class="p-2 bg-neutral-900 rounded-b-xl">
                    <div class="flex flex-wrap justify-between items-center gap-1">
                      <a class="py-1.5 ps-3 pe-2 group flex items-center gap-x-1 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                        Sessions 2025 &dash; Watch the product keynote live
                        <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M5 12h14"></path>
                          <path d="m12 5 7 7-7 7"></path>
                        </svg>
                      </a>

                      <a class="py-1.5 px-3 font-medium text-sm text-[#ff0] rounded-full hover:bg-neutral-800 focus:outline-hidden focus:bg-neutral-800" href="#">
                        Changelog
                      </a>
                    </div>
                  </div>
                  <!-- End Footer -->
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->
          </div>
        </div>
        <!-- End Nav -->
      </div>
      <!-- End Collapse -->
    </nav>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">
    <!-- Hero -->
    <div class="max-w-5xl mx-auto px-4 xl:px-0 pt-24 lg:pt-32 pb-16">
      <h1 class="font-semibold text-white text-5xl md:text-6xl">
        <span class="text-[#ff0]">Our services</span>
      </h1>

      <div class="mt-5 max-w-4xl">
        <p class="text-neutral-400 text-lg">
          We offer a full suite of digital services to help your business thrive in the modern landscape. From web development and UI/UX design to digital marketing and brand strategy, our expert team delivers tailored solutions that drive results and elevate your digital presence.
        </p>
      </div>
    </div>
    <!-- End Hero -->

    <div class="pb-10 lg:pb-20">
      <!-- Services -->
      <div class="max-w-5xl mx-auto px-4 xl:px-0">
        <span class="block font-semibold text-sm uppercase tracking-wider text-white">
          Our services
        </span>

        <!-- Listing -->
        <div class="flex flex-col gap-y-10">
          <!-- Service Listing -->
          <div class="py-4 md:py-6">
            <!-- Grid -->
            <div class="mb-6 grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
              <div class="flex gap-x-5 lg:pe-10">
                <svg class="shrink-0 size-10 text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M10 11h.01" />
                  <path d="M14 6h.01" />
                  <path d="M18 6h.01" />
                  <path d="M6.5 13.1h.01" />
                  <path d="M22 5c0 9-4 12-6 12s-6-3-6-12c0-2 2-3 6-3s6 1 6 3" />
                  <path d="M17.4 9.9c-.8.8-2 .8-2.8 0" />
                  <path d="M10.1 7.1C9 7.2 7.7 7.7 6 8.6c-3.5 2-4.7 3.9-3.7 5.6 4.5 7.8 9.5 8.4 11.2 7.4.9-.5 1.9-2.1 1.9-4.7" />
                  <path d="M9.1 16.5c.3-1.1 1.4-1.7 2.4-1.4" />
                </svg>

                <div class="grow">
                  <h4 class="font-semibold text-white text-xl">
                    Branding
                  </h4>
                  <p class="mt-3 text-sm text-neutral-400">
                    We help you define and communicate your brand's unique value proposition.
                  </p>
                </div>
              </div>
              <!-- End Col -->

              <div class="ps-16 lg:ps-0">
                <!-- List -->
                <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Brand identity development
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Market positioning strategy
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Competitive analysis
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Brand messaging framework
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Visual identity guidelines
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Brand voice development
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Brand strategy workshops
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Brand implementation roadmap
                    </span>
                  </li>
                </ul>
                <!-- End List -->
              </div>
              <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Slider -->
            <div data-hs-carousel='{
              "loadingClasses": "opacity-0",
              "dotsItemClasses": "hs-carousel-active:bg-blue-700 hs-carousel-active:border-blue-700 size-3 border border-gray-400 rounded-full cursor-pointer dark:border-neutral-600 dark:hs-carousel-active:bg-blue-500 dark:hs-carousel-active:border-blue-500",
              "slidesQty": {
                "xs": 1,
                "sm": 2,
                "md": 3
              },
              "isDraggable": true
            }' class="relative">
              <div class="hs-carousel w-full overflow-hidden">
                <div class="relative min-h-40 -mx-1.5 lg:-mx-3">
                  <!-- transition-transform duration-700 -->
                  <div class="hs-carousel-body absolute top-0 bottom-0 start-0 flex flex-nowrap opacity-0 transition-transform duration-700">
                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1707750893158-01b261ba06a4?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1699621106755-4fe40ce95d64?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1633533452148-a9657d2c9a5f?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1633793566063-52465a148cc7?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1567262439850-1d4dc1fefdd0?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="mt-5 flex flex-wrap justify-between gap-3">
                <div>
                  <!-- Link -->
                  <a class="group inline-flex items-center gap-x-2 font-medium text-sm text-[#ff0] decoration-2 hover:underline focus:outline-hidden focus:underline" href="#">
                    Learn more
                    <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </a>
                  <!-- End Link -->
                </div>

                <div class="flex items-center gap-x-2">
                  <!-- Arrows -->
                  <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                      </svg>
                    </span>
                    <span class="sr-only">Previous</span>
                  </button>
                  <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="sr-only">Next</span>
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                      </svg>
                    </span>
                  </button>
                  <!-- End Arrows -->
                </div>
              </div>
              <!-- End Footer -->
            </div>
            <!-- End Slider -->
          </div>
          <!-- End Service Listing -->

          <!-- Service Listing -->
          <div class="py-4 md:py-6">
            <!-- Grid -->
            <div class="mb-6 grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
              <div class="flex gap-x-5 lg:pe-10">
                <svg class="shrink-0 size-10 text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="12" height="12" x="2" y="10" rx="2" ry="2" />
                  <path d="m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6" />
                  <path d="M6 18h.01" />
                  <path d="M10 14h.01" />
                  <path d="M15 6h.01" />
                  <path d="M18 9h.01" />
                </svg>

                <div class="grow">
                  <h4 class="font-semibold text-white text-xl">
                    Content creation
                  </h4>
                  <p class="mt-3 text-sm text-neutral-400">
                    We craft compelling content that tells your story and connects with your audience.
                  </p>
                </div>
              </div>
              <!-- End Col -->

              <div class="ps-16 lg:ps-0">
                <!-- List -->
                <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Blog post writing
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Social media content
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Email newsletter creation
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Video content production
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Infographic design
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Case study development
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Content strategy planning
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Content calendar management
                    </span>
                  </li>
                </ul>
                <!-- End List -->
              </div>
              <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Slider -->
            <div data-hs-carousel='{
              "loadingClasses": "opacity-0",
              "dotsItemClasses": "hs-carousel-active:bg-blue-700 hs-carousel-active:border-blue-700 size-3 border border-gray-400 rounded-full cursor-pointer dark:border-neutral-600 dark:hs-carousel-active:bg-blue-500 dark:hs-carousel-active:border-blue-500",
              "slidesQty": {
                "xs": 1,
                "sm": 2,
                "md": 3
              },
              "isDraggable": true
            }' class="relative">
              <div class="hs-carousel w-full overflow-hidden">
                <div class="relative min-h-40 -mx-1.5 lg:-mx-3">
                  <!-- transition-transform duration-700 -->
                  <div class="hs-carousel-body absolute top-0 bottom-0 start-0 flex flex-nowrap opacity-0 transition-transform duration-700">
                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1726066012749-f81bf4422d4e?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1682695795798-1b31ea040caf?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1627244714766-94dab62ed964?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1552196527-bffef41ef674?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="mt-5 flex flex-wrap justify-between gap-3">
                <div>
                  <!-- Link -->
                  <a class="group inline-flex items-center gap-x-2 font-medium text-sm text-[#ff0] decoration-2 hover:underline focus:outline-hidden focus:underline" href="#">
                    Learn more
                    <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </a>
                  <!-- End Link -->
                </div>

                <div class="flex items-center gap-x-2">
                  <!-- Arrows -->
                  <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                      </svg>
                    </span>
                    <span class="sr-only">Previous</span>
                  </button>
                  <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="sr-only">Next</span>
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                      </svg>
                    </span>
                  </button>
                  <!-- End Arrows -->
                </div>
              </div>
              <!-- End Footer -->
            </div>
            <!-- End Slider -->
          </div>
          <!-- End Service Listing -->

          <!-- Service Listing -->
          <div class="py-4 md:py-6">
            <!-- Grid -->
            <div class="mb-6 grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
              <div class="flex gap-x-5 lg:pe-10">
                <svg class="shrink-0 size-10 text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 11 18-5v12L3 14v-3z" />
                  <path d="M11.6 16.8a3 3 0 1 1-5.8-1.6" />
                </svg>

                <div class="grow">
                  <h4 class="font-semibold text-white text-xl">
                    Digital marketing
                  </h4>
                  <p class="mt-3 text-sm text-neutral-400">
                    From SEO to social media, we drive growth and measurable results.
                  </p>
                </div>
              </div>
              <!-- End Col -->

              <div class="ps-16 lg:ps-0">
                <!-- List -->
                <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      SEO optimization
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Social media management
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      PPC advertising
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Email marketing
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Content marketing
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Analytics & reporting
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Conversion rate optimization
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <span class="size-1 bg-[#ff0] rounded-full"></span>
                    <span class="text-neutral-200">
                      Marketing automation
                    </span>
                  </li>
                </ul>
                <!-- End List -->
              </div>
              <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Slider -->
            <div data-hs-carousel='{
              "loadingClasses": "opacity-0",
              "dotsItemClasses": "hs-carousel-active:bg-blue-700 hs-carousel-active:border-blue-700 size-3 border border-gray-400 rounded-full cursor-pointer dark:border-neutral-600 dark:hs-carousel-active:bg-blue-500 dark:hs-carousel-active:border-blue-500",
              "slidesQty": {
                "xs": 1,
                "sm": 2,
                "md": 3
              },
              "isDraggable": true
            }' class="relative">
              <div class="hs-carousel w-full overflow-hidden">
                <div class="relative min-h-40 -mx-1.5 lg:-mx-3">
                  <!-- transition-transform duration-700 -->
                  <div class="hs-carousel-body absolute top-0 bottom-0 start-0 flex flex-nowrap opacity-0 transition-transform duration-700">
                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1587614313085-5da51cebd8ac?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1560858275-933aa173847b?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1620325867502-221cfb5faa5f?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1647529735400-d4b2caec8906?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->

                    <!-- Slide -->
                    <div class="hs-carousel-slide px-1.5 lg:px-3">
                      <div class="h-40 bg-neutral-800 rounded-xl">
                        <img class="size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1608592922139-5e2b42df53aa?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Slider Image">
                      </div>
                    </div>
                    <!-- End Slide -->
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="mt-5 flex flex-wrap justify-between gap-3">
                <div>
                  <!-- Link -->
                  <a class="group inline-flex items-center gap-x-2 font-medium text-sm text-[#ff0] decoration-2 hover:underline focus:outline-hidden focus:underline" href="#">
                    Learn more
                    <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </a>
                  <!-- End Link -->
                </div>

                <div class="flex items-center gap-x-2">
                  <!-- Arrows -->
                  <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                      </svg>
                    </span>
                    <span class="sr-only">Previous</span>
                  </button>
                  <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                    <span class="sr-only">Next</span>
                    <span class="text-2xl" aria-hidden="true">
                      <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                      </svg>
                    </span>
                  </button>
                  <!-- End Arrows -->
                </div>
              </div>
              <!-- End Footer -->
            </div>
            <!-- End Slider -->
          </div>
          <!-- End Service Listing -->
        </div>
        <!-- End Listing -->
      </div>
      <!-- End Services -->
    </div>

    <!-- Projects -->
    <div class="bg-black">
      <div class="max-w-5xl px-4 xl:px-0 py-10 lg:py-20 mx-auto">
        <!-- Title -->
        <div class="max-w-3xl mb-10 lg:mb-14">
          <h2 class="text-white font-semibold text-2xl md:text-4xl md:leading-tight">Our projects</h2>
          <p class="mt-1 text-neutral-400">Explore our portfolio of innovative projects that showcase our expertise in creating cutting-edge solutions for businesses worldwide.</p>
        </div>
        <!-- End Title -->

        <!-- Card Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M74.2126 66.2965H75.2891V86H72.0656C51.1336 86 39.0226 74.0603 39.0226 54.5676V44.5439L45.8242 42.8336C47.8161 42.3291 49.6264 41.2753 51.0494 39.7914C52.4724 38.3078 53.4506 36.4541 53.8731 34.4411C54.2952 32.4286 54.1445 30.3374 53.4377 28.406C52.7309 26.475 51.4969 24.7811 49.8759 23.5174C48.2545 22.2537 46.3115 21.4709 44.268 21.2584C42.2241 21.0458 40.1622 21.4117 38.3158 22.3147C36.4699 23.2177 34.9142 24.6209 33.8257 26.3655C32.7371 28.1102 32.16 30.1253 32.1596 32.1821V75.1265H11V31.438C10.9987 30.1232 11.0825 28.8101 11.2495 27.506C13.2247 12.0011 26.7506 0 43.1443 0C58.0073 0 70.5177 9.86829 74.2126 23.2744C76.1333 30.2958 75.5102 37.7715 72.4538 44.3773C69.8641 49.9521 65.7128 54.6535 60.5038 57.9109C62.2514 65.0027 65.9687 66.3132 74.2126 66.3132" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Remote
            </h4>

            <p class="mt-2 text-neutral-400">
              Navigate global employment with ease. Rely&nbsp;on us to recruit, pay, and manage international teams across 193 countries.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->

          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M52.0002 56.4378C59.4215 56.4378 65.4377 50.4216 65.4377 43.0002C65.4377 35.5789 59.4215 29.5627 52.0002 29.5627C44.5788 29.5627 38.5627 35.5789 38.5627 43.0002C38.5627 50.4216 44.5788 56.4378 52.0002 56.4378Z" fill="currentColor" />
              <path d="M76.2113 74.8161C77.3546 75.9594 77.2399 77.8519 75.8964 78.7521C69.0618 83.3302 60.8411 86 51.9969 86C43.1526 86 34.9319 83.3302 28.0971 78.7521C26.7538 77.8519 26.639 75.9594 27.7823 74.8161L37.6022 64.9958C38.4898 64.1086 39.8666 63.9681 40.9838 64.5405C44.2869 66.2328 48.0304 67.1874 51.9969 67.1874C55.9633 67.1874 59.7068 66.2328 63.0102 64.5405C64.1273 63.9681 65.5037 64.1086 66.3915 64.9958L76.2113 74.8161Z" fill="currentColor" />
              <path d="M75.8998 7.24822C77.2433 8.14802 77.358 10.0408 76.2146 11.1841L66.3948 21.0041C65.5071 21.8917 64.1301 22.0318 63.0131 21.4596C59.7097 19.7674 55.9665 18.8126 52 18.8126C38.6416 18.8126 27.8125 29.6418 27.8125 43.0002C27.8125 46.9667 28.7673 50.7102 30.4595 54.0136C31.0317 55.1306 30.8916 56.5071 30.004 57.3948L20.184 67.2151C19.0407 68.358 17.148 68.2433 16.2482 66.9002C11.6701 60.0651 9 51.8444 9 43.0002C9 19.2519 28.2518 0 52 0C60.8444 0 69.0651 2.67012 75.8998 7.24822Z" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Clerk
            </h4>

            <p class="mt-2 text-neutral-400">
              Clerk is a complete suite of embeddable UIs, flexible APIs, and admin dashboards to authenticate and manage your users.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->

          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M43 5L86 81H0L43 5Z" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Vercel
            </h4>

            <p class="mt-2 text-neutral-400">
              Vercel provides the developer tools and cloud infrastructure to build, scale, and secure a faster, more personalized web.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->

          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="87" height="86" viewBox="0 0 87 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M48.7857 6H37.1999C16.6612 6.02511 0.0179346 22.6361 0 43.1318C0.0251084 63.6239 16.6648 80.2277 37.1999 80.2528H48.8287C69.3674 80.2349 86.0143 63.6275 86.0394 43.1318C86.0179 22.6146 69.3459 5.99641 48.7857 6ZM4.6056 43.1318C4.6056 25.1793 19.1864 10.6307 37.1748 10.6307C38.9934 10.6307 40.812 10.7778 42.6054 11.0791C58.3018 13.6688 69.7943 27.2382 69.7369 43.1139C69.719 53.3043 62.3478 61.999 52.2794 63.7028C58.8685 58.9035 62.7603 51.2526 62.7532 43.1139C62.7747 31.4671 54.8799 21.291 43.5739 18.3964C41.4827 17.8548 39.3342 17.5858 37.1748 17.5858C23.0711 17.6288 11.6611 29.0424 11.6467 43.1139C11.6109 55.8654 18.1534 67.7345 28.9608 74.5353C14.6526 70.787 4.663 57.8956 4.63071 43.1318H4.6056ZM42.9749 23.0773C51.9314 25.6491 58.0973 33.8309 58.0902 43.1318C58.0938 52.4291 51.9278 60.6037 42.9749 63.1755C34.0219 60.6037 27.8596 52.4291 27.8596 43.1318C27.8524 33.8309 34.0183 25.6491 42.9749 23.0773ZM48.7857 75.6149H48.653C46.8846 75.6042 45.1163 75.4571 43.3694 75.1666C28.0892 72.6378 16.7366 59.6711 16.2774 44.2222V43.1318C16.2882 32.9378 23.6629 24.2395 33.7314 22.5393C27.1422 27.3386 23.2468 34.9895 23.2468 43.1318C23.2325 54.7749 31.1273 64.9474 42.4297 67.8421C44.5173 68.3837 46.6694 68.6563 48.8287 68.6491C62.9469 68.6348 74.3891 57.2212 74.4107 43.1318C74.4501 30.3803 67.9076 18.5076 57.093 11.7104C71.4012 15.4659 81.3764 28.368 81.38 43.1318C81.3764 51.7512 77.9402 60.019 71.8245 66.1096C65.7124 72.2038 57.423 75.6221 48.7857 75.6149Z" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Loops
            </h4>

            <p class="mt-2 text-neutral-400">
              Loops makes email marketing for modern SaaS companies easy. It's the best way to create, send and track beautiful email campaigns.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->

          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M86 42.2235H64.7529V63.4706H86V42.2235Z" fill="currentColor" />
              <path d="M0 63.4706V44.4675L21.4676 23H40.4706V42.0031L19.0031 63.4706H0Z" fill="currentColor" />
              <path d="M32.3765 63.4706V44.4676L53.8438 23H72.8471V42.0031L51.3793 63.4706H32.3765Z" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Mobbin
            </h4>

            <p class="mt-2 text-neutral-400">
              Mobbin is a game-changer for designers looking to step up their understanding of UX and UI design patterns.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->

          <!-- Card -->
          <a class="group p-4 md:p-6 h-full flex flex-col bg-neutral-900 border border-neutral-700 lg:border-transparent rounded-xl transition-all duration-300 lg:hover:border-[#ff0] lg:hover:-rotate-5 focus:outline-hidden lg:focus:border-[#ff0] lg:focus:-rotate-5" href="#">
            <svg class="size-8 mb-5 text-white" width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1059 14.0845C10.6514 14.584 10.6815 15.3504 11.159 15.8279L70.1721 74.8411C70.6496 75.3185 71.416 75.3487 71.9155 74.8942C80.5685 67.0204 86 55.668 86 43.047C86 19.2728 66.7273 0 42.953 0C30.3321 0 18.9797 5.4315 11.1059 14.0845ZM3.73013 25.2842C3.51206 25.765 3.62201 26.3288 3.99531 26.7021L59.2979 82.0047C59.6712 82.378 60.235 82.4879 60.7158 82.2699C61.9934 81.6904 63.2376 81.0503 64.4448 80.3534C65.1624 79.9391 65.2722 78.9601 64.6863 78.3742L7.62575 21.3137C7.0399 20.7278 6.06088 20.8377 5.64663 21.5553C4.94966 22.7624 4.3096 24.0066 3.73013 25.2842ZM0.375099 41.1197C0.116161 40.8608 -0.0216105 40.5039 0.00276617 40.1385C0.116932 38.4273 0.331097 36.7435 0.639469 35.0928C0.824374 34.103 2.03381 33.7595 2.74579 34.4715L51.5286 83.2542C52.2405 83.9662 51.897 85.1756 50.9073 85.3605C49.2566 85.6689 47.5727 85.8831 45.8615 85.9972C45.4961 86.0216 45.1393 85.8838 44.8803 85.6249L0.375099 41.1197ZM3.36877 53.1323C2.47998 52.2435 1.04567 53.0093 1.37117 54.2234C5.33694 69.0149 16.9851 80.6631 31.7766 84.6288C32.9907 84.9544 33.7566 83.52 32.8678 82.6312L3.36877 53.1323Z" fill="currentColor" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Linear
            </h4>

            <p class="mt-2 text-neutral-400">
              Linear is a purpose-built tool for modern product development. Streamline issues, projects, and product roadmaps.
            </p>

            <p class="pt-5 mt-auto">
              <span class="font-medium text-sm text-[#ff0] pb-1 border-b-2 border-neutral-700 group-hover:border-[#ff0] group-focus:border-[#ff0] transition focus:outline-hidden">
                View project
              </span>
            </p>
          </a>
          <!-- End Card -->
        </div>
        <!-- End Card Grid -->

        <div class="mt-5">
          <!-- Quick Action -->
          <a class="py-1 px-3 group inline-flex items-center gap-x-1.5 bg-neutral-900 border border-neutral-700 text-white text-sm rounded-full focus:outline-hidden" href="case-study-details.html">
            <div class="flex items-center -space-x-2">
              <div class="size-6 flex shrink-0 justify-center items-center bg-neutral-900 rounded-full">
                <svg class="shrink-0 size-3.5" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M32.8875 15.3054C32.9242 16.2093 32.8209 17.1099 32.5811 17.9792C32.3447 18.8486 31.9716 19.6695 31.4787 20.4141C30.989 21.1593 30.3861 21.8167 29.6935 22.3607L29.6769 22.3745L23.019 27.563L19.7451 30.1433L17.7501 31.7089C17.6335 31.8024 17.5036 31.8716 17.3671 31.9201C17.2305 31.9686 17.084 31.9929 16.9374 31.9929C16.7942 31.9929 16.6477 31.9686 16.5111 31.9201C16.3745 31.8716 16.2447 31.8024 16.1281 31.7089L14.1331 30.1433L10.8591 27.563L4.24125 22.4057L4.20129 22.378L4.18796 22.3641C3.49187 21.8203 2.88904 21.1623 2.39611 20.4176C1.90319 19.6729 1.53016 18.8486 1.29036 17.9792C1.05056 17.1099 0.947313 16.2059 0.98395 15.3019C1.02392 14.3979 1.20044 13.5078 1.51018 12.6626L1.55348 12.5414L5.90654 0.747936C5.92875 0.69021 5.95539 0.634792 5.98648 0.581684C6.01534 0.528576 6.04976 0.478931 6.08972 0.43275C6.12747 0.38426 6.16855 0.339234 6.21295 0.297671C6.25736 0.258417 6.30399 0.221472 6.35284 0.186836C6.45609 0.121028 6.56267 0.0725381 6.67924 0.0448295C6.79248 0.0136573 6.91238 -0.000196993 7.02895 0.00673016C7.14885 0.0136573 7.26542 0.0379024 7.37533 0.0829289C7.48524 0.124492 7.59181 0.186836 7.68507 0.263035C7.72948 0.302289 7.77278 0.343852 7.81496 0.387724C7.85493 0.433905 7.89046 0.483549 7.92154 0.536658C7.95485 0.587457 7.98371 0.641719 8.00814 0.699446C8.03256 0.754863 8.05254 0.812589 8.06809 0.872625L11.0023 10.2139H22.8792L25.8134 0.872625C25.8289 0.812589 25.85 0.754863 25.8767 0.699446C25.9011 0.644029 25.93 0.589766 25.9633 0.536658C25.9944 0.485858 26.0299 0.437368 26.0699 0.391187C26.1098 0.345006 26.1531 0.302289 26.1997 0.263035C26.293 0.186836 26.3962 0.127955 26.5062 0.0829289C26.6194 0.0413659 26.736 0.0171209 26.8525 0.0101937C26.9724 0.00326659 27.089 0.0136573 27.2056 0.0448295C27.3188 0.0760017 27.4287 0.124492 27.5286 0.1903C27.5797 0.222627 27.6275 0.259571 27.6719 0.301134C27.7163 0.340388 27.7573 0.38426 27.7951 0.43275C27.8328 0.48124 27.8673 0.532039 27.8983 0.585148C27.9272 0.638256 27.9527 0.693673 27.9749 0.751399L32.3213 12.5483L32.3646 12.6696C32.6744 13.5112 32.8509 14.4014 32.8875 15.3054Z" fill="#E24329" />
                  <path d="M32.8909 15.309C32.9275 16.2095 32.8243 17.1135 32.5845 17.9829C32.3447 18.8523 31.9717 19.6766 31.4787 20.4213C30.9858 21.1659 30.383 21.824 29.6902 22.3678L29.6736 22.3816L23.0157 27.5701C23.0157 27.5701 20.1881 25.3499 16.9374 22.7903L26.4795 15.2813C26.9092 14.9453 27.3588 14.6371 27.8218 14.3531C28.2847 14.0656 28.7643 13.8093 29.2539 13.5807C29.7468 13.3521 30.2498 13.1477 30.7593 12.978C31.2722 12.8048 31.7918 12.6628 32.3214 12.5485L32.3647 12.6698C32.6744 13.5149 32.8509 14.405 32.8909 15.309Z" fill="#FC6D26" />
                  <path d="M16.9374 22.7903C20.1881 25.343 23.0191 27.5701 23.0191 27.5701L19.7451 30.1504L17.7501 31.716C17.6335 31.8095 17.5036 31.8788 17.3671 31.9273C17.2305 31.9758 17.084 32 16.9374 32C16.7942 32 16.6477 31.9758 16.5111 31.9273C16.3746 31.8788 16.2447 31.8095 16.1281 31.716L14.1331 30.1504L10.8591 27.5701C10.8591 27.5701 13.6868 25.343 16.9374 22.7903Z" fill="#FCA326" />
                  <path d="M16.9374 22.7834C13.6834 25.343 10.8591 27.5631 10.8591 27.5631L4.24125 22.4059L4.20129 22.3782L4.18796 22.3643C3.49187 21.8205 2.88904 21.1625 2.39611 20.4178C1.90319 19.6731 1.53016 18.8488 1.29036 17.9794C1.05056 17.1101 0.947313 16.2061 0.98395 15.3021C1.02392 14.3981 1.20044 13.5079 1.51018 12.6628L1.55348 12.5416C2.08304 12.6559 2.60261 12.7979 3.11552 12.9711C3.6251 13.1443 4.12801 13.3452 4.62094 13.5772C5.11053 13.8058 5.59014 14.0656 6.05309 14.3496C6.51604 14.6336 6.96233 14.9453 7.39531 15.2813L16.9374 22.7834Z" fill="#FC6D26" />
                </svg>
              </div>
              <div class="size-6 flex shrink-0 justify-center items-center bg-neutral-900 rounded-full">
                <svg class="shrink-0 size-3.5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_11766_122079)">
                    <path d="M16 32C7.16 32 0 24.84 0 16C0 7.16 7.16 0 16 0C24.84 0 32 7.16 32 16C32 24.84 24.84 32 16 32Z" fill="#FFE01B" />
                    <path d="M11.72 19.28C11.74 19.3 11.74 19.34 11.72 19.38C11.64 19.52 11.48 19.6 11.32 19.58C11.02 19.54 10.8 19.3 10.82 19C10.82 18.8 10.86 18.62 10.92 18.42C11.02 18.18 10.92 17.92 10.72 17.78C10.6 17.7 10.44 17.68 10.3 17.7C10.16 17.72 10.04 17.82 9.96001 17.94C9.90001 18.04 9.86001 18.14 9.84001 18.24C9.84001 18.26 9.82001 18.28 9.82001 18.28C9.78001 18.4 9.70001 18.44 9.64001 18.44C9.62001 18.44 9.58001 18.42 9.56001 18.36C9.50001 18.02 9.62001 17.68 9.84001 17.42C10.04 17.2 10.32 17.1 10.62 17.14C10.92 17.18 11.2 17.38 11.32 17.66C11.46 18 11.42 18.38 11.24 18.7C11.22 18.72 11.22 18.76 11.2 18.78C11.14 18.9 11.12 19.06 11.2 19.18C11.26 19.26 11.34 19.3 11.44 19.3C11.48 19.3 11.52 19.3 11.56 19.28C11.64 19.24 11.7 19.24 11.72 19.28ZM24.94 19.6C24.92 20.22 24.78 20.82 24.56 21.4C23.44 24.1 20.76 25.6 17.56 25.5C14.58 25.42 12.04 23.84 10.94 21.26C10.24 21.24 9.56001 20.96 9.06001 20.5C8.52001 20.04 8.18001 19.4 8.10001 18.7C8.04001 18.22 8.10001 17.74 8.28001 17.28L7.66001 16.76C4.78001 14.36 13.72 4.4 16.56 6.9C16.58 6.92 17.54 7.86 17.54 7.86C17.54 7.86 18.06 7.64 18.08 7.64C20.58 6.6 22.62 7.1 22.62 8.76C22.62 9.62 22.08 10.62 21.2 11.54C21.56 11.9 21.8 12.34 21.92 12.82C22.02 13.16 22.06 13.5 22.08 13.86C22.1 14.22 22.12 15.04 22.12 15.04C22.14 15.04 22.4 15.12 22.48 15.14C23 15.26 23.46 15.48 23.86 15.82C24.08 16.02 24.2 16.3 24.26 16.58C24.32 16.96 24.22 17.34 24 17.64C24.06 17.78 24.1 17.9 24.16 18.04C24.24 18.28 24.28 18.48 24.3 18.5C24.52 18.54 24.94 18.86 24.94 19.6ZM12.34 18.12C12.14 16.86 11.3 16.42 10.72 16.38C10.58 16.38 10.44 16.38 10.28 16.42C9.26001 16.62 8.66001 17.5 8.78001 18.64C8.96001 19.7 9.82001 20.5 10.88 20.56C10.98 20.56 11.08 20.56 11.18 20.54C12.24 20.38 12.5 19.24 12.34 18.12ZM14.1 10.12C14.98 9.4 15.9 8.76 16.88 8.2C16.88 8.2 16.1 7.3 15.86 7.22C14.42 6.82 11.3 8.98 9.30001 11.84C8.50001 13 7.34001 15.04 7.90001 16.08C8.10001 16.32 8.32001 16.52 8.56001 16.72C8.92001 16.2 9.48001 15.84 10.12 15.72C10.9 13.54 12.28 11.6 14.1 10.12ZM17.22 20.1C17.3 20.44 17.56 20.72 17.9 20.8C18.08 20.86 18.24 20.92 18.44 20.94C20.72 21.34 22.86 20.02 23.34 19.7C23.38 19.68 23.4 19.7 23.38 19.74C23.36 19.76 23.34 19.78 23.34 19.8C22.76 20.56 21.18 21.44 19.12 21.44C18.22 21.44 17.32 21.12 17 20.64C16.48 19.88 16.98 18.78 17.82 18.9C17.82 18.9 18.12 18.94 18.2 18.94C19.52 19.06 20.86 18.86 22.08 18.32C23.24 17.78 23.68 17.18 23.62 16.7C23.6 16.56 23.52 16.42 23.42 16.3C23.1 16.04 22.72 15.86 22.32 15.78C22.14 15.72 22.02 15.7 21.88 15.66C21.64 15.58 21.52 15.52 21.5 15.06C21.48 14.86 21.46 14.18 21.44 13.88C21.42 13.38 21.36 12.7 20.94 12.42C20.84 12.34 20.7 12.3 20.58 12.3C20.5 12.3 20.44 12.3 20.36 12.32C20.14 12.36 19.96 12.48 19.8 12.64C19.4 13 18.88 13.18 18.34 13.14C18.04 13.12 17.74 13.08 17.38 13.06C17.32 13.06 17.24 13.06 17.18 13.04C16.22 13.06 15.44 13.78 15.32 14.74C15.12 16.16 16.14 16.88 16.44 17.32C16.48 17.38 16.52 17.44 16.52 17.52C16.52 17.6 16.48 17.68 16.42 17.72C15.6 18.64 15.3 19.92 15.62 21.12C15.66 21.26 15.7 21.4 15.76 21.54C16.5 23.28 18.82 24.1 21.08 23.36C21.38 23.26 21.66 23.14 21.94 23C22.44 22.76 22.88 22.42 23.26 22.02C23.84 21.44 24.22 20.68 24.36 19.86C24.42 19.4 24.32 19.24 24.2 19.16C24.1 19.1 24 19.08 23.88 19.1C23.82 18.74 23.72 18.4 23.58 18.08C22.94 18.56 22.2 18.94 21.42 19.16C20.48 19.42 19.52 19.52 18.54 19.48C17.92 19.42 17.5 19.24 17.34 19.76C18.28 20.08 19.28 20.18 20.28 20.06C20.3 20.06 20.34 20.08 20.34 20.1C20.34 20.12 20.32 20.14 20.3 20.16C20.22 20.14 19.06 20.68 17.22 20.1ZM13.84 11.88C14.6 11.34 15.48 10.96 16.38 10.76C17.42 10.52 18.48 10.52 19.52 10.76C19.56 10.76 19.58 10.7 19.54 10.68C19 10.4 18.42 10.24 17.8 10.22C17.78 10.22 17.76 10.2 17.76 10.18V10.16C17.86 10.04 17.96 9.92 18.08 9.84C18.1 9.82 18.1 9.8 18.08 9.8L18.06 9.78C17.32 9.86 16.62 10.1 15.98 10.52C15.96 10.52 15.94 10.52 15.94 10.52V10.5C15.98 10.32 16.06 10.14 16.16 9.96C16.16 9.94 16.16 9.92 16.14 9.92H16.12C15.22 10.42 14.42 11.08 13.76 11.86C13.74 11.88 13.74 11.9 13.76 11.9C13.8 11.9 13.82 11.9 13.84 11.88ZM19.84 16.7C19.96 16.78 20.14 16.76 20.24 16.64C20.3 16.52 20.22 16.38 20.06 16.3C19.94 16.22 19.76 16.24 19.66 16.36C19.6 16.46 19.68 16.62 19.84 16.7ZM20.34 14.88C20.38 15.08 20.46 15.28 20.58 15.44C20.7 15.42 20.84 15.42 20.96 15.44C21.04 15.22 21.04 14.98 20.98 14.76C20.88 14.34 20.76 14.1 20.52 14.14C20.26 14.18 20.24 14.48 20.34 14.88ZM20.88 15.84C20.72 15.8 20.54 15.88 20.48 16.06C20.44 16.22 20.52 16.4 20.7 16.46C20.88 16.52 21.04 16.42 21.1 16.24C21.1 16.22 21.12 16.18 21.12 16.16C21.12 16 21.02 15.86 20.88 15.84Z" fill="black" />
                    <path d="M16.66 15.8C16.62 15.8 16.6 15.78 16.6 15.76C16.58 15.68 16.7 15.58 16.8 15.48C17.14 15.22 17.6 15.18 17.98 15.34C18.16 15.42 18.32 15.54 18.42 15.7C18.46 15.76 18.46 15.82 18.44 15.84C18.4 15.88 18.3 15.84 18.12 15.76C17.92 15.66 17.68 15.6 17.46 15.62C17.2 15.66 16.92 15.72 16.66 15.8ZM18.38 16.16C18.22 16 18 15.92 17.8 15.96C17.64 15.98 17.5 16.04 17.38 16.14C17.32 16.18 17.28 16.24 17.28 16.32C17.28 16.34 17.28 16.36 17.3 16.36C17.32 16.36 17.32 16.38 17.34 16.38C17.4 16.38 17.46 16.36 17.5 16.34C17.74 16.26 17.98 16.22 18.22 16.26C18.34 16.28 18.38 16.28 18.42 16.24C18.4 16.2 18.4 16.18 18.38 16.16Z" fill="black" />
                  </g>
                  <defs>
                    <clipPath id="clip0_11766_122079">
                      <rect width="32" height="32" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
            Explore projects
            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </a>
          <!-- End Quick Action -->
        </div>
      </div>
    </div>
    <!-- End Projects -->

    <!-- Pricing -->
    <div class="bg-neutral-900 bg-linear-to-b from-black to-transparent">
      <div class="max-w-5xl px-4 xl:px-0 py-10 lg:pt-20 mx-auto">
        <!-- Title -->
        <div class="max-w-3xl mb-10 lg:mb-14">
          <h2 class="text-white font-semibold text-2xl md:text-4xl md:leading-tight">Flexible plans</h2>
          <p class="mt-1 text-neutral-400">Whether you're just starting or scaling fast, our pricing adjusts to fit your goals.</p>
        </div>
        <!-- End Title -->

        <!-- Grid -->
        <div class="xl:-mx-6 flex flex-col">
          <div class="relative after:absolute after:top-0 after:inset-x-4 md:after:inset-x-6 after:border-t after:border-neutral-700 first:after:border-t-0 py-3">
            <!-- Pricing Card -->
            <div class="p-4 md:p-6 relative border border-transparent rounded-xl hover:border-[#ff0] before:absolute before:inset-0 before:rounded-b-xl before:bg-linear-to-b hover:before:from-transparent hover:before:via-transparent hover:before:to-[#ff0]/10 before:via-80% before:opacity-0 hover:before:opacity-100">
              <a class="after:absolute after:inset-0 after:z-1" href="#"></a>

              <!-- Grid -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
                <div>
                  <div class="mb-3 flex items-start gap-x-1">
                    <span class="text-[#ff0] text-3xl md:text-4xl">
                      $2,999
                    </span>
                    <span class="mb-1 self-end text-xs text-neutral-400">
                      /month
                    </span>
                  </div>

                  <h4 class="font-semibold text-xl text-white">
                    Starter
                  </h4>

                  <p class="text-sm text-neutral-400">
                    One request at a time, Avg. 24-hour delivery, Up to 2 users.
                  </p>
                </div>
                <!-- End Col -->

                <div>
                  <!-- List -->
                  <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        One request at a time
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Avg. 48 hour delivery
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Unlimited revisions
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Up to 1 users
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Webflow development
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Unlimited stock photos
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Dedicated creative manager
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Cancel or pause anytime
                      </span>
                    </li>
                  </ul>
                  <!-- End List -->
                </div>
                <!-- End Col -->
              </div>
              <!-- End Grid -->
            </div>
            <!-- End Pricing Card -->
          </div>

          <div class="relative after:absolute after:top-0 after:inset-x-4 md:after:inset-x-6 after:border-t after:border-neutral-700 first:after:border-t-0 py-3">
            <!-- Pricing Card -->
            <div class="p-4 md:p-6 relative border border-transparent rounded-xl hover:border-[#ff0] before:absolute before:inset-0 before:rounded-b-xl before:bg-linear-to-b hover:before:from-transparent hover:before:via-transparent hover:before:to-[#ff0]/10 before:via-80% before:opacity-0 hover:before:opacity-100">
              <a class="after:absolute after:inset-0 after:z-1" href="#"></a>

              <!-- Grid -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
                <div>
                  <div class="mb-3 flex items-start gap-x-1">
                    <span class="text-[#ff0] text-3xl md:text-4xl">
                      $4,999
                    </span>
                    <span class="mb-1 self-end text-xs text-neutral-400">
                      /month
                    </span>
                  </div>

                  <h4 class="font-semibold text-xl text-white">
                    Pro
                  </h4>

                  <p class="text-sm text-neutral-400">
                    Everything in Starter + More scale and brand flexibility.
                  </p>
                </div>
                <!-- End Col -->

                <div>
                  <!-- List -->
                  <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Two requests at a time
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Avg. 36 hour delivery
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Unlimited revisions
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Up to 2 users
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Priority Webflow development
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        2 branded stock photo curations
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Dedicated creative manager
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Cancel or pause anytime
                      </span>
                    </li>
                  </ul>
                  <!-- End List -->
                </div>
                <!-- End Col -->
              </div>
              <!-- End Grid -->
            </div>
            <!-- End Pricing Card -->
          </div>

          <div class="relative after:absolute after:top-0 after:inset-x-4 md:after:inset-x-6 after:border-t after:border-neutral-700 first:after:border-t-0 py-3">
            <!-- Pricing Card -->
            <div class="p-4 md:p-6 relative border border-transparent rounded-xl hover:border-[#ff0] before:absolute before:inset-0 before:rounded-b-xl before:bg-linear-to-b hover:before:from-transparent hover:before:via-transparent hover:before:to-[#ff0]/10 before:via-80% before:opacity-0 hover:before:opacity-100">
              <a class="after:absolute after:inset-0 after:z-1" href="#"></a>

              <!-- Grid -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-y-5 gap-x-8">
                <div>
                  <div class="mb-3 flex items-start gap-x-1">
                    <span class="text-[#ff0] text-3xl md:text-4xl">
                      $7,999
                    </span>
                    <span class="mb-1 self-end text-xs text-neutral-400">
                      /month
                    </span>
                  </div>

                  <h4 class="font-semibold text-xl text-white">
                    Elite
                  </h4>

                  <p class="text-sm text-neutral-400">
                    Everything in Pro + Highest flexibility and dedicated support.
                  </p>
                </div>
                <!-- End Col -->

                <div>
                  <!-- List -->
                  <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2.5 text-sm">
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Five requests at a time
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Avg. 24 hour delivery
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Unlimited revisions
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Up to 5 users
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Webflow design system setup
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        5 branded stock photo curations
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Dedicated creative manager
                      </span>
                    </li>
                    <li class="flex items-center gap-x-3">
                      <span class="size-1 bg-[#ff0] rounded-full"></span>
                      <span class="text-neutral-200">
                        Cancel or pause anytime
                      </span>
                    </li>
                  </ul>
                  <!-- End List -->
                </div>
                <!-- End Col -->
              </div>
              <!-- End Grid -->
            </div>
            <!-- End Pricing Card -->
          </div>
        </div>
        <!-- End Grid -->
      </div>
    </div>
    <!-- End Pricing -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== FOOTER ========== -->
  <footer class="relative overflow-hidden bg-neutral-900">
    <svg class="absolute -bottom-20 start-1/2 w-[1900px] transform -translate-x-1/2" width="2745" height="488" viewBox="0 0 2745 488" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0.5 330.864C232.505 403.801 853.749 527.683 1482.69 439.719C2111.63 351.756 2585.54 434.588 2743.87 487" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 308.873C232.505 381.81 853.749 505.692 1482.69 417.728C2111.63 329.765 2585.54 412.597 2743.87 465.009" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 286.882C232.505 359.819 853.749 483.701 1482.69 395.738C2111.63 307.774 2585.54 390.606 2743.87 443.018" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 264.891C232.505 337.828 853.749 461.71 1482.69 373.747C2111.63 285.783 2585.54 368.615 2743.87 421.027" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 242.9C232.505 315.837 853.749 439.719 1482.69 351.756C2111.63 263.792 2585.54 346.624 2743.87 399.036" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 220.909C232.505 293.846 853.749 417.728 1482.69 329.765C2111.63 241.801 2585.54 324.633 2743.87 377.045" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 198.918C232.505 271.855 853.749 395.737 1482.69 307.774C2111.63 219.81 2585.54 302.642 2743.87 355.054" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 176.927C232.505 249.864 853.749 373.746 1482.69 285.783C2111.63 197.819 2585.54 280.651 2743.87 333.063" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 154.937C232.505 227.873 853.749 351.756 1482.69 263.792C2111.63 175.828 2585.54 258.661 2743.87 311.072" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 132.946C232.505 205.882 853.749 329.765 1482.69 241.801C2111.63 153.837 2585.54 236.67 2743.87 289.082" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 110.955C232.505 183.891 853.749 307.774 1482.69 219.81C2111.63 131.846 2585.54 214.679 2743.87 267.091" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 88.9639C232.505 161.901 853.749 285.783 1482.69 197.819C2111.63 109.855 2585.54 192.688 2743.87 245.1" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 66.9729C232.505 139.91 853.749 263.792 1482.69 175.828C2111.63 87.8643 2585.54 170.697 2743.87 223.109" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 44.9819C232.505 117.919 853.749 241.801 1482.69 153.837C2111.63 65.8733 2585.54 148.706 2743.87 201.118" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 22.991C232.505 95.9276 853.749 219.81 1482.69 131.846C2111.63 43.8824 2585.54 126.715 2743.87 179.127" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 1C232.505 73.9367 853.749 197.819 1482.69 109.855C2111.63 21.8914 2585.54 104.724 2743.87 157.136" class="stroke-neutral-700/50" stroke="currentColor" />
    </svg>

    <div class="relative z-10">
      <div class="w-full max-w-5xl px-4 xl:px-0 py-10 lg:pt-16 mx-auto">
        <div class="inline-flex items-center">
          <!-- Logo -->
          <svg class="w-24 h-auto" width="116" height="32" viewBox="0 0 116 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M33.5696 30.8182V11.3182H37.4474V13.7003H37.6229C37.7952 13.3187 38.0445 12.9309 38.3707 12.5369C38.7031 12.1368 39.134 11.8045 39.6634 11.5398C40.1989 11.2689 40.8636 11.1335 41.6577 11.1335C42.6918 11.1335 43.6458 11.4044 44.5199 11.946C45.3939 12.4815 46.0926 13.291 46.6158 14.3743C47.139 15.4515 47.4006 16.8026 47.4006 18.4276C47.4006 20.0095 47.1451 21.3452 46.6342 22.4347C46.1295 23.518 45.4401 24.3397 44.5661 24.8999C43.6982 25.4538 42.7256 25.7308 41.6484 25.7308C40.8852 25.7308 40.2358 25.6046 39.7003 25.3523C39.1709 25.0999 38.737 24.7829 38.3984 24.4013C38.0599 24.0135 37.8014 23.6226 37.6229 23.2287H37.5028V30.8182H33.5696ZM37.4197 18.4091C37.4197 19.2524 37.5367 19.9879 37.7706 20.6158C38.0045 21.2436 38.343 21.733 38.7862 22.0838C39.2294 22.4285 39.768 22.6009 40.402 22.6009C41.0421 22.6009 41.5838 22.4254 42.027 22.0746C42.4702 21.7176 42.8056 21.2251 43.0334 20.5973C43.2673 19.9633 43.3842 19.2339 43.3842 18.4091C43.3842 17.5904 43.2704 16.8703 43.0426 16.2486C42.8149 15.6269 42.4794 15.1406 42.0362 14.7898C41.593 14.4389 41.0483 14.2635 40.402 14.2635C39.7618 14.2635 39.2202 14.4328 38.777 14.7713C38.34 15.1098 38.0045 15.59 37.7706 16.2116C37.5367 16.8333 37.4197 17.5658 37.4197 18.4091ZM49.2427 25.5V11.3182H53.0559V13.7926H53.2037C53.4622 12.9124 53.8961 12.2476 54.5055 11.7983C55.1149 11.3428 55.8166 11.1151 56.6106 11.1151C56.8076 11.1151 57.02 11.1274 57.2477 11.152C57.4754 11.1766 57.6755 11.2105 57.8478 11.2536V14.7436C57.6632 14.6882 57.4077 14.639 57.0815 14.5959C56.7553 14.5528 56.4567 14.5312 56.1859 14.5312C55.6073 14.5312 55.0903 14.6574 54.6348 14.9098C54.1854 15.156 53.8284 15.5007 53.5638 15.9439C53.3052 16.3871 53.176 16.898 53.176 17.4766V25.5H49.2427ZM64.9043 25.777C63.4455 25.777 62.1898 25.4815 61.1373 24.8906C60.0909 24.2936 59.2845 23.4503 58.7182 22.3608C58.1519 21.2652 57.8688 19.9695 57.8688 18.4737C57.8688 17.0149 58.1519 15.7346 58.7182 14.6328C59.2845 13.531 60.0816 12.6723 61.1096 12.0568C62.1437 11.4413 63.3563 11.1335 64.7474 11.1335C65.683 11.1335 66.5539 11.2843 67.3603 11.5859C68.1728 11.8814 68.8806 12.3277 69.4839 12.9247C70.0932 13.5218 70.5672 14.2727 70.9057 15.1776C71.2443 16.0762 71.4135 17.1288 71.4135 18.3352V19.4155H59.4384V16.978H67.7111C67.7111 16.4117 67.588 15.91 67.3418 15.473C67.0956 15.036 66.754 14.6944 66.317 14.4482C65.8861 14.1958 65.3844 14.0696 64.812 14.0696C64.2149 14.0696 63.6856 14.2081 63.2239 14.4851C62.7684 14.7559 62.4114 15.1222 62.1529 15.5838C61.8944 16.0393 61.762 16.5471 61.7559 17.1072V19.4247C61.7559 20.1264 61.8851 20.7327 62.1437 21.2436C62.4083 21.7545 62.7807 22.1484 63.2608 22.4254C63.741 22.7024 64.3103 22.8409 64.9689 22.8409C65.406 22.8409 65.8061 22.7794 66.1692 22.6562C66.5324 22.5331 66.8432 22.3485 67.1018 22.1023C67.3603 21.8561 67.5572 21.5545 67.6927 21.1974L71.3304 21.4375C71.1458 22.3116 70.7672 23.0748 70.1948 23.7273C69.6285 24.3736 68.896 24.8783 67.9974 25.2415C67.1048 25.5985 66.0738 25.777 64.9043 25.777ZM77.1335 6.59091V25.5H73.2003V6.59091H77.1335ZM79.5043 25.5V11.3182H83.4375V25.5H79.5043ZM81.4801 9.49006C80.8954 9.49006 80.3937 9.29616 79.9752 8.90838C79.5628 8.51444 79.3566 8.04356 79.3566 7.49574C79.3566 6.95407 79.5628 6.48935 79.9752 6.10156C80.3937 5.70762 80.8954 5.51065 81.4801 5.51065C82.0649 5.51065 82.5635 5.70762 82.9759 6.10156C83.3944 6.48935 83.6037 6.95407 83.6037 7.49574C83.6037 8.04356 83.3944 8.51444 82.9759 8.90838C82.5635 9.29616 82.0649 9.49006 81.4801 9.49006ZM89.7415 17.3011V25.5H85.8083V11.3182H89.5569V13.8203H89.723C90.037 12.9955 90.5632 12.343 91.3019 11.8629C92.0405 11.3767 92.9361 11.1335 93.9887 11.1335C94.9735 11.1335 95.8322 11.349 96.5647 11.7798C97.2971 12.2107 97.8665 12.8262 98.2728 13.6264C98.679 14.4205 98.8821 15.3684 98.8821 16.4702V25.5H94.9489V17.1719C94.9551 16.304 94.7335 15.6269 94.2841 15.1406C93.8348 14.6482 93.2162 14.402 92.4283 14.402C91.8989 14.402 91.4311 14.5159 91.0249 14.7436C90.6248 14.9714 90.3109 15.3037 90.0831 15.7408C89.8615 16.1716 89.7477 16.6918 89.7415 17.3011ZM107.665 25.777C106.206 25.777 104.951 25.4815 103.898 24.8906C102.852 24.2936 102.045 23.4503 101.479 22.3608C100.913 21.2652 100.63 19.9695 100.63 18.4737C100.63 17.0149 100.913 15.7346 101.479 14.6328C102.045 13.531 102.842 12.6723 103.87 12.0568C104.905 11.4413 106.117 11.1335 107.508 11.1335C108.444 11.1335 109.315 11.2843 110.121 11.5859C110.934 11.8814 111.641 12.3277 112.245 12.9247C112.854 13.5218 113.328 14.2727 113.667 15.1776C114.005 16.0762 114.174 17.1288 114.174 18.3352V19.4155H102.199V16.978H110.472C110.472 16.4117 110.349 15.91 110.103 15.473C109.856 15.036 109.515 14.6944 109.078 14.4482C108.647 14.1958 108.145 14.0696 107.573 14.0696C106.976 14.0696 106.446 14.2081 105.985 14.4851C105.529 14.7559 105.172 15.1222 104.914 15.5838C104.655 16.0393 104.523 16.5471 104.517 17.1072V19.4247C104.517 20.1264 104.646 20.7327 104.905 21.2436C105.169 21.7545 105.542 22.1484 106.022 22.4254C106.502 22.7024 107.071 22.8409 107.73 22.8409C108.167 22.8409 108.567 22.7794 108.93 22.6562C109.293 22.5331 109.604 22.3485 109.863 22.1023C110.121 21.8561 110.318 21.5545 110.454 21.1974L114.091 21.4375C113.907 22.3116 113.528 23.0748 112.956 23.7273C112.389 24.3736 111.657 24.8783 110.758 25.2415C109.866 25.5985 108.835 25.777 107.665 25.777Z" class="fill-white" fill="currentColor" />
            <path d="M1 29.5V16.5C1 9.87258 6.37258 4.5 13 4.5C19.6274 4.5 25 9.87258 25 16.5C25 23.1274 19.6274 28.5 13 28.5H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <path d="M5 29.5V16.66C5 12.1534 8.58172 8.5 13 8.5C17.4183 8.5 21 12.1534 21 16.66C21 21.1666 17.4183 24.82 13 24.82H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <circle cx="13" cy="16.5214" r="5" class="fill-white" fill="currentColor" />
          </svg>
          <!-- End Logo -->

          <div class="border-s border-neutral-700 ps-5 ms-5">
            <p class="text-sm text-neutral-400">
              © 2025 Preline Labs.
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!-- ========== END FOOTER ========== -->

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>

</body>
</html>