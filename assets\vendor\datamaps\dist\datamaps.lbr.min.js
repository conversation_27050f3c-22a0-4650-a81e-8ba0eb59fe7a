!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo={type:"Topology",objects:{lbr:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"River Gee"},id:"LR.RG",arcs:[[0,1,2,3,4]]},{type:"Polygon",properties:{name:"Gbapolu"},id:"LR.GP",arcs:[[5,6,7,8,9]]},{type:"Polygon",properties:{name:"Grand Kru"},id:"LR.GK",arcs:[[10,11,12,-2]]},{type:"Polygon",properties:{name:"Maryland"},id:"LR.MY",arcs:[[13,-11,-1]]},{type:"Polygon",properties:{name:"Sinoe"},id:"LR.SI",arcs:[[-3,-13,14,15,16,17]]},{type:"Polygon",properties:{name:"Bomi"},id:"LR.BM",arcs:[[18,19,20,21,-7]]},{type:"Polygon",properties:{name:"Bong"},id:"LR.BG",arcs:[[22,23,24,25,-19,-6,26,27]]},{type:"Polygon",properties:{name:"Grand Bassa"},id:"LR.MG",arcs:[[28,29,30,31,-24]]},{type:"Polygon",properties:{name:"Grand Cape Mount"},id:"LR.CM",arcs:[[-22,32,-8]]},{type:"Polygon",properties:{name:"Lofa"},id:"LR.LF",arcs:[[-27,-10,33]]},{type:"Polygon",properties:{name:"Montserrado"},id:"LR.MO",arcs:[[34,35,-20,-26]]},{type:"Polygon",properties:{name:"Margibi"},id:"LR.MG",arcs:[[-32,36,-35,-25]]},{type:"Polygon",properties:{name:"Nimba"},id:"LR.NI",arcs:[[37,-17,38,-29,-23,39]]},{type:"Polygon",properties:{name:"River Cess"},id:"LR.RI",arcs:[[-16,40,-30,-39]]},{type:"Polygon",properties:{name:"Grand Gedeh"},id:"LR.GD",arcs:[[41,-4,-18,-38]]}]}},arcs:[[[9457,1114],[-1,1],[-8,6],[-18,9],[-50,12],[-15,6],[-11,10],[-21,38],[-7,8],[-12,8],[-20,6],[-33,4],[-75,39],[-20,5],[-13,-1],[-7,-4],[-8,-4],[-8,-1],[-8,2],[-10,3],[-9,0],[-9,-2],[-8,-5],[-26,-23],[-20,-11],[-9,-3],[-12,-3],[-13,0],[-27,9],[-10,-1],[-16,-4],[-7,-1],[-16,2],[-11,-1],[-11,-2],[-8,2],[-8,8],[-6,20],[-6,35],[-2,7],[-1,9],[1,9],[3,9],[1,8],[-1,9],[-3,10],[-2,18],[2,342],[-4,12],[-6,11],[-11,11],[-9,6],[-18,5],[-397,68]],[[8433,1815],[-341,53],[-81,6],[-15,-8],[-11,0],[-16,4],[-86,56],[-36,11],[-108,14]],[[7739,1951],[6,74],[-3,38],[-10,37],[-11,21],[-75,109],[-172,171],[-12,17],[-13,27],[-21,62],[-8,37],[3,31],[18,87]],[[7441,2662],[86,-32],[269,3],[320,34],[158,31],[1662,66]],[[9936,2764],[4,-3],[-13,-20],[-28,-22],[-18,-35],[2,-89],[-11,-25],[-34,17],[0,-37],[10,-43],[17,-40],[23,-32],[36,-20],[26,5],[18,-4],[13,-44],[7,-46],[-3,-27],[-14,-24],[-30,-41],[-6,-15],[-8,-39],[-13,-15],[-23,-10],[-6,9],[0,18],[-8,15],[-3,12],[13,12],[4,9],[-28,5],[-6,-6],[-64,-33],[-18,-22],[-10,-22],[-26,-78],[20,-112],[-2,-40],[-14,-41],[-66,-116],[-34,13],[-42,1],[-40,-18],[-30,-43],[-6,-41],[19,-137],[-3,-51],[17,-33],[23,-31],[14,-46],[0,-36],[-10,1],[-20,14],[-32,3],[-75,-57],[-1,-22],[17,-73],[-3,-27],[-14,-49],[0,-19]],[[4063,6790],[-34,-32],[-91,-50],[-40,-30],[-47,-56],[-12,-10],[-15,-6],[-11,-13],[-12,-9],[-14,3],[-12,6],[-15,3],[-16,0],[-14,-1],[-58,-16],[-52,-22],[-91,-60],[-76,-73],[-41,-29],[-38,-7],[-27,12],[-7,3],[-6,4],[-1,5],[4,6],[5,4],[3,6],[-1,6],[-7,23],[-4,9],[-7,25],[-4,9],[-7,7],[-5,8],[2,9],[6,9],[11,9],[6,7],[3,14],[1,6],[-2,6],[-4,4],[-34,14],[-85,20],[-64,2],[-26,-9],[-7,-8],[-21,-25],[-160,-126],[-12,-16],[-24,-44],[-39,-48],[-77,-78],[-18,-14],[-19,-6],[-19,-3],[-18,0],[-63,11],[-9,0],[-18,-5],[-82,-36],[-24,-14],[-15,-12],[-4,-8],[-3,-9],[-2,-52],[-8,-40],[-20,-76]],[[2462,5997],[-73,-15],[-268,-1],[-127,-48],[-20,-12],[-25,-21],[-20,-13],[-24,-14],[-45,-18],[-21,-3],[-14,1],[-18,15],[-17,19],[-9,13],[-5,10],[-3,9],[-5,6],[-18,16],[-3,14],[1,6],[6,14],[1,7],[-4,17],[2,8],[5,8],[1,9],[-3,10],[-5,9],[-5,16],[-1,7],[1,7],[4,6],[2,9],[4,8],[4,17],[1,6],[-2,7],[-3,8],[-4,9],[-2,8],[1,6],[5,4],[9,1],[38,-1],[8,2],[3,5],[-6,5],[-4,5],[-3,6],[1,10],[-4,9],[-13,12],[-153,31],[-11,0],[-12,0],[-46,-12],[-52,-4],[-29,3],[-136,46],[-15,0],[-19,-7]],[[1312,6272],[-17,17],[-1,1],[7,11],[18,11],[15,7],[32,5],[16,12],[17,19],[27,41],[23,27],[13,7],[8,6],[16,16],[9,8],[11,5],[39,12],[12,5],[9,9],[20,23],[12,8],[44,24],[12,3],[24,0],[11,3],[10,6],[8,9],[18,15],[21,10],[7,6],[5,6],[38,63],[10,12],[10,9],[49,34],[9,8],[8,9],[9,8],[11,5],[13,4],[59,12],[177,105],[35,31],[11,6],[11,6],[51,15],[11,7],[9,7],[7,9],[18,17],[33,21],[10,15],[3,17],[-12,54],[2,21],[-1,46],[-9,49],[-8,22],[-6,12],[-142,191],[-22,17],[-25,10],[-49,5],[-38,1],[-42,-4],[-15,7],[-20,30],[-36,-4],[-13,-3],[-176,32],[-20,14],[-6,3],[-7,0],[-34,-6],[-14,-1],[-7,2],[-8,5],[-7,16],[-11,7],[-8,4],[-88,19],[-32,4]],[[1496,7577],[326,384],[93,76],[115,60],[18,6],[56,8],[-6,573],[3,28]],[[2101,8712],[126,-105],[245,62],[136,-123],[445,-132],[63,88],[227,-308],[390,88],[617,-247],[27,-158],[-363,-810],[49,-277]],[[8433,1815],[26,-63],[3,-16],[1,-30],[-4,-16],[-6,-14],[-8,-14],[-16,-20],[-58,-94],[-36,-64],[-86,-206],[0,-23],[3,-20],[3,-13],[4,-12],[5,-8],[13,-16],[18,-14],[10,-5],[175,-57],[158,-67],[35,-19],[19,-13],[20,-17],[18,-18],[16,-20],[107,-175],[35,-47],[58,-95],[10,-24],[5,-18],[-3,-20],[-17,-63],[-5,-46],[2,-85],[-3,-17],[-7,-18],[-9,-17],[-35,-55],[-9,-19]],[[8875,257],[-46,14],[-58,44],[-28,10],[-40,5],[-24,12],[-19,16],[-24,16],[-44,17],[-106,27],[-84,12],[-153,58],[-297,35],[-87,29],[-104,112],[-39,19],[-47,8],[-253,89],[-272,186],[-140,76],[-235,83]],[[6775,1125],[64,55],[22,27],[12,37],[5,35],[18,61],[9,13],[9,7],[61,4],[12,4],[21,7],[10,8],[7,9],[35,72],[56,95],[16,20],[13,13],[72,40],[162,121],[9,5],[9,3],[19,3],[106,0],[13,2],[20,8],[12,8],[9,10],[20,33],[78,90],[13,13],[21,11],[31,12]],[[9457,1114],[0,-2],[12,-28],[19,-17],[19,-14],[14,-17],[7,-21],[11,-123],[-11,-508],[4,-21],[6,-22],[5,-25],[-5,-60],[17,-47],[4,-23],[-8,-23],[-25,-44],[-4,-24],[14,-28],[80,-54],[-128,-13],[-37,5],[-57,24],[-31,5],[-167,0],[-20,7],[-11,17],[-8,17],[-11,7],[-40,3],[-96,38],[-30,17],[-27,23],[-15,27],[-13,32],[-23,27],[-27,8]],[[6775,1125],[-106,38],[-76,39],[-172,140],[-63,40],[-76,33],[-25,5],[-50,22],[-75,11],[-60,17],[-58,26],[-42,31],[8,5],[2,2],[1,3],[5,8],[-108,28],[-86,49],[-140,117],[-39,24],[-84,39],[-77,55],[-43,16],[-41,20],[-33,42],[32,0],[-32,43],[-49,26],[-55,20],[-48,24],[-156,142]],[[5029,2190],[3,1],[6,5],[1,3],[-1,4],[-5,6],[-21,16],[-4,3],[-3,4],[-1,4],[-2,9],[-1,14],[0,7],[1,5],[3,5],[6,4],[36,11],[8,3],[5,4],[4,3],[17,15],[7,3],[13,6],[7,7],[5,12],[7,23],[5,11],[7,8],[13,4],[9,1],[10,1],[7,-1],[4,0],[4,2],[10,9],[68,88],[10,20],[10,40],[4,20],[0,12],[-50,217],[-13,36],[-18,29],[-14,31],[-9,34],[51,53],[67,38],[70,66],[39,28],[68,27],[22,12],[75,56],[31,12],[52,8],[24,8],[19,18],[10,26],[6,66],[7,30],[30,58],[40,49],[51,35]],[[5839,3519],[65,15],[284,-6],[39,18],[24,23],[37,53]],[[6288,3622],[637,-345],[140,-58],[156,184],[8,6],[17,5],[24,3],[78,-5],[22,-4],[19,-7],[75,-44],[28,-21],[34,-33],[47,-66],[38,-66],[13,-37],[10,-35],[2,-16],[-1,-25],[-2,-9],[-3,-8],[-4,-8],[-7,-7],[-8,-5],[-9,-5],[-41,-10],[-21,-2],[-79,-2],[-11,-2],[-18,-4],[-9,-3],[-9,-5],[-8,-6],[-6,-8],[-4,-8],[-2,-9],[-4,-26],[-3,-8],[-3,-8],[-52,-70],[-10,-18],[-4,-9],[-3,-10],[0,-11],[3,-13],[10,-19],[10,-9],[10,-6],[38,-8],[34,-12],[13,-8],[8,-9],[4,-8],[1,-9],[0,-9],[-4,-25],[-1,0]],[[2462,5997],[-40,-65],[-15,-18],[-35,-14],[-2,0]],[[2370,5900],[-48,-19],[-67,-7],[-16,-9],[-5,-22],[1,-33],[-3,-29],[-19,-13],[-9,-13],[4,-27],[13,-50],[-9,-21],[-42,-48],[-15,-21],[3,-1],[-3,-46],[-3,-14],[-9,-22],[-4,-14],[-3,-25],[3,-80],[10,-18],[64,-63],[22,-29],[0,-16],[-29,-28],[-57,-42],[-28,-6],[-41,19],[-7,7],[-9,8],[-99,61],[-22,10],[-13,2],[-14,-2],[-17,-6],[-22,-14],[-22,-17],[-39,-35],[-36,-26],[-8,-3],[-58,-10],[-30,-10],[-27,-16],[-13,-13],[-7,-12],[-2,-14],[-1,-23],[2,-19],[3,-19],[24,-65],[5,-20],[-3,-29],[-28,-65],[-1,-1]],[[1636,4872],[-43,83],[-81,84],[-94,62],[-385,202],[-58,19]],[[975,5322],[0,1],[-7,28],[-4,30],[3,18],[8,19],[19,29],[13,14],[12,10],[11,7],[24,9],[26,6],[9,9],[5,15],[3,30],[8,22],[16,25],[12,13],[12,8],[9,6],[9,3],[9,0],[11,-2],[20,-7],[9,2],[6,6],[4,5],[6,16],[19,75],[3,47],[6,36],[-7,28],[-8,13],[-25,27],[-3,13],[2,19],[32,67],[63,167],[3,20],[-4,24],[1,21],[11,45],[-9,26]],[[5791,6769],[-8,-21],[-81,-258],[-43,-59],[-5,-35],[-1,-24],[-12,-29],[-2,-22],[-1,-32],[-1,-1],[-5,-7],[-41,-41],[1,-10],[15,-16],[14,-19],[10,-7],[22,-10],[8,-11],[6,-13],[6,-18],[8,-12],[9,-9],[6,-10],[7,-26],[7,-9],[10,-4],[8,-1],[11,-1],[4,-2],[7,-4],[12,-21],[21,-20],[0,-18],[-14,-67],[-2,-26],[2,-23],[12,-49],[-3,-16],[-7,-12],[-18,-15],[-8,-8],[-10,-8],[-9,-9],[-9,-12],[2,-12],[7,-9],[9,-15],[0,-13],[-5,-10],[-18,-15],[-5,-11],[-5,-12],[-6,-12],[-16,-22],[-1,-12],[5,-15],[-9,-34],[-6,-15],[-8,-13],[-19,-21],[0,-12],[17,-29],[1,-16],[-14,-38],[-11,-12],[-10,-4],[-25,0]],[[5600,5402],[-27,-3],[-40,1],[-232,-56],[-28,2],[-5,13],[-6,11],[-18,7],[-10,-4],[-8,-9],[-15,-13],[-37,-26],[-11,-11],[-6,-14],[-3,-13],[-6,-13],[-9,-12],[-18,-15],[-16,-3],[-22,1],[-12,-5],[-47,-44],[-37,-25],[-13,-19],[-15,-29],[-12,-1],[-13,4],[-21,3],[-14,-4],[-12,-7],[-11,-11],[-13,-1],[-12,2],[-17,1],[-8,-6],[-5,-10],[-2,-11],[-16,-23],[-14,-16],[-9,-5],[-11,-4],[-24,3],[-10,-1],[-21,-2],[-13,0],[-28,10],[-20,2],[-11,-5],[-45,-38],[-67,-37],[-29,-10],[-20,0],[-7,9],[-6,9],[-4,9],[-2,8],[0,9],[1,9],[3,10],[4,9],[23,37],[62,76],[34,32],[30,35],[5,9],[3,8],[2,9],[0,8],[-1,10],[-4,12],[-9,16],[-12,14],[-58,50],[-64,75],[-11,10],[-14,9],[-24,11],[-14,2],[-12,0],[-9,-2],[-121,-13],[-18,1],[-9,5],[-8,5],[-12,16]],[[4229,5463],[50,105],[12,36],[1,14],[-1,16],[-5,24],[-5,16],[-5,13],[-7,11],[-8,10],[-17,17],[-33,25],[-41,25],[-19,9],[-20,6],[-23,5],[-10,1],[-10,-1],[-9,-4],[-10,-6],[-17,-17],[-12,-17],[-5,-9],[-4,-10],[-3,-11],[-2,-11],[0,-21],[7,-55],[-1,-10],[-3,-9],[-6,-9],[-36,-33],[-6,-8],[-9,-18],[-7,-19],[-5,-18],[-4,-35],[-6,-28],[-4,-9],[-8,-9],[-14,-10],[-28,-13],[-17,-2],[-13,1],[-8,5],[-9,6],[-17,19],[-43,56],[-8,7],[-18,12],[-9,5],[-55,18],[-103,24],[-22,1],[-9,-1],[-28,-11],[-56,-29],[-33,-9],[-19,-2],[-20,2],[-301,96],[-21,11],[-21,14],[-21,16],[-41,41],[-12,10],[-17,11],[-33,16],[-20,7],[-16,4],[-37,-2],[-53,-7],[-81,-1]],[[2735,5724],[-40,33],[-25,7],[-12,-1],[-20,-3],[-23,-1],[-106,6],[-29,9],[-25,19],[-8,20],[-77,87]],[[4063,6790],[73,-32],[34,-9],[89,-11],[16,0],[23,5],[21,10],[42,27],[21,10],[0,-14],[34,17],[31,33],[16,36],[-13,27],[14,29],[18,26],[25,19],[68,12],[47,22],[55,12],[39,17],[94,61],[59,61],[-4,8],[-1,9],[-3,8],[-1,0]],[[4860,7173],[2,-1],[20,10],[71,67],[27,39],[47,-81],[28,-23],[37,52],[14,4],[13,8],[5,26],[10,8],[22,4],[41,2],[35,-5],[18,-6],[20,-12],[17,-21],[17,-47],[15,-16],[38,-6],[83,21],[38,-4],[35,-48],[34,-148],[60,-54],[15,-3],[55,2],[13,-9],[2,-41],[19,-15],[47,-24],[9,-31],[-23,-82],[28,24],[19,6]],[[5600,5402],[111,-34],[29,-5],[13,5],[6,4],[21,7],[18,-2],[11,-4],[15,-10],[4,-7],[1,-6],[-6,-17],[-5,-20],[-1,-4],[-2,-5],[-5,-12],[-4,-4],[-5,-3],[-33,-12],[-6,-4],[-4,-3],[-27,-31],[-4,-8],[-8,-21],[-12,-67],[-3,-12],[-21,-39],[-1,-6],[1,-5],[2,-1],[5,-2],[5,0],[6,0],[13,5],[5,0],[7,-4],[10,-7],[3,-9],[1,-8],[-13,-57],[-18,-51],[-3,-14],[-1,-15],[3,-38],[0,-10],[-7,-37],[-2,-70]],[[5699,4759],[-516,-2],[-34,-22],[-18,-7],[-37,-10],[-18,-9],[-12,-4],[-97,-19],[-11,-5],[-9,-6],[-11,-10],[-7,-9],[-5,-8],[-1,-8],[1,-8],[2,-9],[6,-16],[3,-8],[1,-8],[0,-17],[-2,-16],[-17,-60],[-2,-17],[1,-36],[24,-123],[-5,-12],[-10,-15],[-76,-67],[-14,-16],[-53,-83],[-14,-17],[-10,-3],[-10,1],[-8,6],[-6,7],[-12,16],[-8,6],[-9,4],[-17,4],[-35,2],[-28,-1],[-26,-5],[-15,-7],[-11,-9],[-7,-10],[-5,-9],[-3,-10],[-1,-10],[2,-20],[-1,-12],[-7,-16],[-8,-8],[-62,-23],[-14,-10],[-9,-11],[-3,-9],[0,-10],[3,-17],[2,-8],[1,-8],[0,-16],[-1,-8],[-30,-91],[-1,-9],[0,-8],[8,-24],[0,-10],[-3,-9],[-13,-22],[-3,-9],[0,-8],[4,-17],[22,-42],[1,-9],[-1,-8],[-3,-8],[-79,-123],[-8,-17],[-8,-30],[0,-9],[0,-8],[4,-17],[21,-45],[6,-16],[1,-8],[0,-15],[-1,-7],[-2,-6],[-15,-29],[-4,-9],[-1,-10],[0,-9],[1,-10],[3,-9],[3,-9],[17,-26],[8,-18],[17,-73],[0,-11],[-1,-14],[-6,-23],[-8,-13],[-57,-52],[-11,-16],[-6,-14],[-5,-15],[-8,-8],[-12,-6],[-15,-7],[-9,-8],[-5,-8],[-2,-9],[0,-11],[0,-1]],[[4259,2925],[-266,247],[-336,267],[-87,95],[-18,15],[-43,23],[-15,18],[-7,24],[11,30],[-4,28],[-23,37],[-38,31],[-44,21],[-37,9],[-26,13],[-173,133],[-100,105],[-130,79],[-44,19],[-93,17],[-43,24],[-26,32],[7,32],[22,-13],[19,-2],[16,9],[11,22],[-49,11],[-33,25]],[[2710,4276],[1,3],[25,47],[3,12],[8,15],[17,25],[4,12],[-3,11],[-9,23],[-1,14],[9,70],[4,11],[5,11],[37,44],[12,25],[8,7],[15,4],[12,7],[8,14],[1,14],[-5,12],[-8,8],[-5,9],[-1,10],[0,10],[-2,10],[1,10],[9,6],[24,9],[27,14],[42,38],[15,23],[9,19],[0,14],[-12,36],[-2,13],[2,13],[5,11],[76,79],[11,26],[12,20],[7,25],[9,22],[0,11],[-7,8],[-21,10],[-5,11],[-4,22],[-8,6],[-10,3],[-7,6],[-5,18],[11,7],[20,11],[88,16],[40,14],[26,-8],[10,-5],[1,-5],[-1,-6],[-3,-7],[-2,-9],[1,-10],[4,-10],[7,-7],[18,-10],[6,-6],[4,-6],[4,-6],[4,-5],[7,-5],[37,-11],[10,-6],[7,-6],[14,-16],[8,-7],[15,-4],[19,-1],[35,5],[20,6],[19,15],[12,10],[9,12],[20,19],[8,3],[39,-6],[31,6],[35,-1],[67,-20],[20,-3],[19,0],[17,7],[23,14],[64,56],[23,25],[12,9],[14,6],[20,3],[14,-2],[12,-3],[47,-25],[9,-3],[9,-2],[9,-1],[9,1],[10,2],[52,48],[183,213]],[[975,5322],[-381,132],[-293,126],[-53,61],[-3,94],[15,53],[3,22],[1,31],[-34,60],[-9,12],[-46,13],[-48,29],[-41,33],[-24,24],[-62,85],[77,35],[19,11],[-5,60],[10,40],[67,37],[24,39],[15,45],[5,35],[-9,48],[8,16],[54,5],[18,7],[13,13],[5,20],[-8,30],[-35,55],[-6,24],[7,9],[44,40],[85,129],[42,8],[201,88],[21,19],[159,197],[44,54],[363,277],[99,54],[93,21],[33,13],[27,21],[26,30]],[[2101,8712],[10,22],[22,18],[62,33],[26,24],[57,101],[36,43],[57,28],[49,7],[197,1],[50,7],[48,14],[48,26],[60,57],[7,44],[-20,47],[-17,67],[5,53],[43,90],[75,291],[1,123],[27,-2],[26,5],[50,18],[19,-39],[41,22],[83,78],[41,12],[41,0],[140,-34],[36,-20],[17,-35],[-25,-111],[11,-32],[32,-10],[52,8],[99,56],[95,72],[42,20],[39,12],[41,6],[47,0],[12,2],[35,9],[11,-6],[12,-10],[11,-16],[22,7],[45,22],[60,17],[3,15],[-8,18],[0,19],[17,21],[17,18],[12,20],[-1,29],[41,-6],[26,-31],[14,-40],[16,-112],[11,-42],[25,-41],[34,-36],[10,7],[0,34],[6,49],[30,33],[48,-1],[49,-22],[32,-30],[13,-51],[-20,-29],[-32,-21],[-23,-30],[-3,-43],[21,-7],[37,8],[42,1],[20,-2],[21,3],[18,8],[12,16],[17,19],[20,-14],[31,-42],[123,-50],[43,-30],[17,-18],[13,-6],[-30,-17],[-10,-5],[-17,-8],[-15,-12],[-26,-27],[22,-20],[12,-25],[10,-26],[14,-24],[12,-10],[3,-11],[-4,-10],[-11,-11],[-24,-37],[-16,-68],[4,-64],[36,-25],[59,-11],[24,-13],[8,-28],[0,-27],[45,-172],[-5,-25],[-27,-43],[-3,-27],[29,35],[26,19],[26,-2],[29,-33],[12,-25],[5,-25],[-6,-23],[-17,-22],[-4,-8],[-31,-103],[-6,-100],[24,-98],[56,-99],[63,-79],[22,-40],[32,-40],[30,-37],[-27,-61],[-24,-84],[4,-80],[52,-58],[-27,-20],[-21,-30],[-14,-36],[-12,-63],[-17,-27],[-44,-49],[9,-33],[-21,-51],[-55,-80],[-50,-36],[-24,-23],[-9,-30],[-2,-32],[-6,-28],[-16,-24],[-26,-22],[8,-7]],[[2735,5724],[8,-59],[-5,-22],[-123,-228],[-9,-22],[-4,-15],[2,-17],[6,-18],[5,-8],[29,-36],[11,-19],[7,-19],[6,-19],[9,-54],[3,-138],[-4,-36],[-6,-24],[-8,-9],[-18,-16],[-11,-6],[-11,-5],[-85,-27],[-9,-5],[-11,-12],[-13,-20],[-41,-75],[-16,-16],[-9,-7],[-20,-11],[-113,-42],[-19,-9],[-79,-58],[-20,-9],[-29,-9],[-11,-5],[-11,-8],[-18,-15],[-10,-11],[-6,-11],[-51,-160]],[[2051,4444],[-55,13],[-315,155],[-48,43],[18,40],[12,-19],[13,-14],[18,-9],[26,-7],[-41,66],[-8,57],[24,53],[57,54],[-35,0],[-26,-7],[-20,-12],[-20,-15],[-15,30]],[[2710,4276],[-2,2],[-31,32],[-37,27],[-79,48],[-41,16],[-47,3],[0,-18],[127,-63],[56,-44],[18,-55],[-37,44],[-68,40],[-78,31],[-440,105]],[[6982,5086],[-1,0],[-40,-87],[-58,-189],[-147,-285],[-38,-57],[-56,-62],[-64,-51],[-68,-25],[-35,-18],[-31,-77],[-36,-30],[27,-38],[6,-59],[-11,-61],[-22,-45],[-38,-25],[-99,-11],[-42,-14],[-31,-40],[19,-30],[30,-22],[0,-22],[-45,-56],[-14,-54],[13,-25],[36,32],[37,-32],[19,-37],[-5,-44]],[[5839,3519],[11,56],[3,19],[2,5],[20,32],[1,7],[-3,11],[-11,18],[-26,34],[-7,16],[-1,11],[2,11],[8,19],[18,14],[5,2],[4,4],[-1,4],[-8,7],[-5,4],[-5,5],[-8,11],[-6,10],[-7,7],[-4,3],[-5,2],[-5,2],[-4,2],[-3,2],[-2,6],[1,8],[9,30],[3,6],[2,3],[3,0],[5,0],[19,-2],[7,1],[6,1],[9,5],[6,4],[9,9],[3,4],[0,4],[-2,4],[-40,30],[-11,12],[-5,6],[0,3],[1,4],[5,9],[41,47],[2,4],[3,6],[-1,6],[-5,10],[-20,34],[-6,7],[-14,6],[-7,6],[-2,2],[0,5],[1,6],[10,29],[0,5],[-1,33],[-2,7],[-5,8],[-12,11],[-51,33],[-4,4],[-3,5],[2,5],[9,6],[12,5],[2,3],[2,3],[2,11],[3,3],[4,2],[4,5],[1,10],[0,19],[2,10],[2,5],[5,1],[4,-3],[5,-6],[3,-8],[3,-4],[3,-1],[3,1],[4,4],[10,16],[5,6],[3,7],[3,27],[3,8],[5,4],[33,11],[5,4],[2,2],[1,4],[0,8],[-4,26],[-2,12],[-1,7],[0,5],[-1,12],[-8,47],[0,14],[3,11],[13,10],[-4,8],[-15,10],[-46,21],[-58,14],[-8,3],[-24,31],[-47,102]],[[5791,6769],[14,5],[34,5],[33,10],[34,24],[31,27],[33,18],[42,-3],[20,10],[54,16],[11,1],[8,37],[11,4],[26,-11],[13,55],[31,-18],[47,-71],[32,3],[106,39],[27,16],[16,47],[-11,45],[-18,42],[-5,41],[11,25],[22,33],[45,53],[23,14],[39,11],[21,18],[9,21],[4,49],[9,22],[14,12],[69,39],[41,43],[24,43],[10,47],[-3,55],[-6,11],[-11,3],[-10,5],[-3,14],[4,14],[15,24],[3,14],[6,82],[17,49],[64,75],[19,47],[292,-14],[-13,-89],[15,-65],[45,-54],[79,-56],[74,-45],[102,-50],[44,-83],[50,-167],[30,-230],[13,-46],[16,-28],[42,-58],[15,-31],[20,-73],[11,-19],[29,-36],[10,-18],[2,-16],[-6,-30],[2,-15],[9,-15],[36,-35],[15,-24],[7,-23],[3,-57],[42,-226],[-6,-57],[-19,-23],[-50,-37],[-13,-21],[-3,-42],[3,-39],[-2,-37],[-31,-65],[12,-18],[20,-13],[12,-14],[1,-32],[-4,-33],[-20,-71],[-31,-69],[-34,-52],[-220,-237],[-48,-52],[-177,-135],[-33,-39],[-6,1],[-40,-22],[-3,-1],[3,-4],[-11,-33],[-5,-10],[-12,-7],[-29,-4],[-12,-5],[-60,-74]],[[5029,2190],[-129,117],[-17,35],[-26,86],[-22,42],[-26,25],[-40,26],[-42,22],[-36,8],[-7,4],[-69,13],[-15,-1],[-3,23],[29,36],[6,22],[-40,57],[-244,138],[-89,82]],[[6982,5086],[41,-2],[24,-5],[59,1],[28,-8],[28,-23],[72,-94],[45,-15],[31,25],[19,44],[11,43],[12,20],[15,-10],[28,-35],[77,-40],[32,-35],[-3,-14],[-18,-15],[-13,-39],[5,-22],[22,-44],[5,-18],[-12,-21],[-23,-12],[-8,-15],[29,-28],[26,25],[30,11],[30,-1],[26,-11],[32,26],[25,-10],[24,-26],[25,-23],[12,2],[8,10],[11,8],[17,-10],[13,-19],[2,-19],[0,-17],[4,-9],[22,-8],[190,-42],[29,-10],[26,-15],[17,-12],[20,-7],[40,1],[34,8],[54,29],[29,11],[113,16],[66,1],[43,-9],[40,-27],[157,-36],[13,-1],[11,4],[11,3],[15,-5],[4,-8],[7,-27],[8,-12],[109,-84],[38,-45],[6,-32],[-32,-92],[-12,-49],[-6,-54],[7,-48],[26,-33],[1,11],[24,12],[30,8],[22,-1],[24,-21],[10,-22],[8,-54],[8,-9],[13,-3],[12,-7],[6,-15],[-5,-12],[-23,-12],[-5,-11],[-2,-67],[3,-18],[31,-38],[97,-49],[30,-28],[43,-59],[34,16],[29,48],[27,31],[16,-13],[26,-29],[36,-28],[44,-7],[3,-9],[51,-26],[6,-2],[53,-22],[13,-15],[-20,-25],[19,-15],[32,-40],[17,-9],[29,13],[30,25],[18,7],[-8,-40],[38,-6],[16,-31],[12,-34],[24,-14],[18,18],[13,39],[7,71],[78,-33],[11,-83],[-15,-100],[-3,-87],[45,-185],[32,-63],[52,-101],[31,-38],[-51,8],[-17,-28],[12,-45],[33,-42],[-49,-2],[-16,0],[25,-23]]],
transform:{scale:[.00040924767186718163,.00042185820482048555],translate:[-11.476185675999915,4.347235419000043]}},m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo="__ZWE__",m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();