!function(){function a(a,b,c){"undefined"==typeof c&&(c=b,optionsValues=void 0);var d="undefined"!=typeof a?a:b;if("undefined"==typeof d)return null;if("function"==typeof d){var e=[c];return c.geography&&(e=[c.geography,c.data]),d.apply(null,e)}return d}function b(a,b,c){return this.svg=n.select(a).append("svg").attr("width",c||a.offsetWidth).attr("data-width",c||a.offsetWidth).attr("class","datamap").attr("height",b||a.offsetHeight).style("overflow","hidden"),this.options.responsive&&(n.select(this.options.element).style({position:"relative","padding-bottom":100*this.options.aspectRatio+"%"}),n.select(this.options.element).select("svg").style({position:"absolute",width:"100%",height:"100%"}),n.select(this.options.element).select("svg").select("g").selectAll("path").style("vector-effect","non-scaling-stroke")),this.svg}function c(a,b){var c,d,e=b.width||a.offsetWidth,f=b.height||a.offsetHeight,g=this.svg;return b&&"undefined"==typeof b.scope&&(b.scope="world"),"usa"===b.scope?c=n.geo.albersUsa().scale(e).translate([e/2,f/2]):"world"===b.scope&&(c=n.geo[b.projection]().scale((e+1)/2/Math.PI).translate([e/2,f/("mercator"===b.projection?1.45:1.8)])),"orthographic"===b.projection&&(g.append("defs").append("path").datum({type:"Sphere"}).attr("id","sphere").attr("d",d),g.append("use").attr("class","stroke").attr("xlink:href","#sphere"),g.append("use").attr("class","fill").attr("xlink:href","#sphere"),c.scale(250).clipAngle(90).rotate(b.projectionConfig.rotation)),d=n.geo.path().projection(c),{path:d,projection:c}}function d(){n.select(".datamaps-style-block").empty()&&n.select("head").append("style").attr("class","datamaps-style-block").html('.datamap path.datamaps-graticule { fill: none; stroke: #777; stroke-width: 0.5px; stroke-opacity: .5; pointer-events: none; } .datamap .labels {pointer-events: none;} .datamap path {stroke: #FFFFFF; stroke-width: 1px;} .datamaps-legend dt, .datamaps-legend dd { float: left; margin: 0 3px 0 0;} .datamaps-legend dd {width: 20px; margin-right: 6px; border-radius: 3px;} .datamaps-legend {padding-bottom: 20px; z-index: 1001; position: absolute; left: 4px; font-size: 12px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;} .datamaps-hoverover {display: none; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; } .hoverinfo {padding: 4px; border-radius: 1px; background-color: #FFF; box-shadow: 1px 1px 5px #CCC; font-size: 12px; border: 1px solid #CCC; } .hoverinfo hr {border:1px dotted #CCC; }')}function e(b){var c=this.options.fills,d=this.options.data||{},e=this.options.geographyConfig,f=this.svg.select("g.datamaps-subunits");f.empty()&&(f=this.addLayer("datamaps-subunits",null,!0));var g=o.feature(b,b.objects[this.options.scope]).features;e.hideAntarctica&&(g=g.filter(function(a){return"ATA"!==a.id})),e.hideHawaiiAndAlaska&&(g=g.filter(function(a){return"HI"!==a.id&&"AK"!==a.id}));var h=f.selectAll("path.datamaps-subunit").data(g);h.enter().append("path").attr("d",this.path).attr("class",function(a){return"datamaps-subunit "+a.id}).attr("data-info",function(a){return JSON.stringify(d[a.id])}).style("fill",function(b){var e,f=d[b.id];return f&&f.fillKey&&(e=c[a(f.fillKey,{data:d[b.id],geography:b})]),"undefined"==typeof e&&(e=a(f&&f.fillColor,c.defaultFill,{data:d[b.id],geography:b})),e}).style("stroke-width",e.borderWidth).style("stroke",e.borderColor)}function f(){function b(){this.parentNode.appendChild(this)}var c=this.svg,d=this,e=this.options.geographyConfig;(e.highlightOnHover||e.popupOnHover)&&c.selectAll(".datamaps-subunit").on("mouseover",function(f){var g=n.select(this),h=d.options.data[f.id]||{};if(e.highlightOnHover){var i={fill:g.style("fill"),stroke:g.style("stroke"),"stroke-width":g.style("stroke-width"),"fill-opacity":g.style("fill-opacity")};g.style("fill",a(h.highlightFillColor,e.highlightFillColor,h)).style("stroke",a(h.highlightBorderColor,e.highlightBorderColor,h)).style("stroke-width",a(h.highlightBorderWidth,e.highlightBorderWidth,h)).style("fill-opacity",a(h.highlightFillOpacity,e.highlightFillOpacity,h)).attr("data-previousAttributes",JSON.stringify(i)),/((MSIE)|(Trident))/.test(navigator.userAgent)||b.call(this)}e.popupOnHover&&d.updatePopup(g,f,e,c)}).on("mouseout",function(){var a=n.select(this);if(e.highlightOnHover){var b=JSON.parse(a.attr("data-previousAttributes"));for(var c in b)a.style(c,b[c])}a.on("mousemove",null),n.selectAll(".datamaps-hoverover").style("display","none")})}function g(a,b,c){if(b=b||{},this.options.fills){var d="<dl>",e="";b.legendTitle&&(d="<h2>"+b.legendTitle+"</h2>"+d);for(var f in this.options.fills){if("defaultFill"===f){if(!b.defaultFillName)continue;e=b.defaultFillName}else e=b.labels&&b.labels[f]?b.labels[f]:f+": ";d+="<dt>"+e+"</dt>",d+='<dd style="background-color:'+this.options.fills[f]+'">&nbsp;</dd>'}d+="</dl>";n.select(this.options.element).append("div").attr("class","datamaps-legend").html(d)}}function h(a,b){var c=n.geo.graticule();this.svg.insert("path",".datamaps-subunits").datum(c).attr("class","datamaps-graticule").attr("d",this.path)}function i(b,c,d){var e=this;this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - arcs must be an array";for(var f=0;f<c.length;f++)c[f]=l(c[f],c[f].options),delete c[f].options;"undefined"==typeof d&&(d=p.arcConfig);var g=b.selectAll("path.datamaps-arc").data(c,JSON.stringify),h=n.geo.path().projection(e.projection);g.enter().append("svg:path").attr("class","datamaps-arc").style("stroke-linecap","round").style("stroke",function(b){return a(b.strokeColor,d.strokeColor,b)}).style("fill","none").style("stroke-width",function(b){return a(b.strokeWidth,d.strokeWidth,b)}).attr("d",function(b){var c=e.latLngToXY(a(b.origin.latitude,b),a(b.origin.longitude,b)),f=e.latLngToXY(a(b.destination.latitude,b),a(b.destination.longitude,b)),g=[(c[0]+f[0])/2,(c[1]+f[1])/2];if(d.greatArc){var i=n.geo.greatArc().source(function(b){return[a(b.origin.longitude,b),a(b.origin.latitude,b)]}).target(function(b){return[a(b.destination.longitude,b),a(b.destination.latitude,b)]});return h(i(b))}var j=a(b.arcSharpness,d.arcSharpness,b);return"M"+c[0]+","+c[1]+"S"+(g[0]+50*j)+","+(g[1]-75*j)+","+f[0]+","+f[1]}).transition().delay(100).style("fill",function(b){var c=this.getTotalLength();return this.style.transition=this.style.WebkitTransition="none",this.style.strokeDasharray=c+" "+c,this.style.strokeDashoffset=c,this.getBoundingClientRect(),this.style.transition=this.style.WebkitTransition="stroke-dashoffset "+a(b.animationSpeed,d.animationSpeed,b)+"ms ease-out",this.style.strokeDashoffset="0","none"}),g.exit().transition().style("opacity",0).remove()}function j(a,b){var c=this;b=b||{};var d=this.projection([-67.707617,42.722131]);this.svg.selectAll(".datamaps-subunit").attr("data-foo",function(e){var f=c.path.centroid(e),g=7.5,h=5;["FL","KY","MI"].indexOf(e.id)>-1&&(g=-2.5),"NY"===e.id&&(g=-1),"MI"===e.id&&(h=18),"LA"===e.id&&(g=13);var i,j;i=f[0]-g,j=f[1]+h;var k=["VT","NH","MA","RI","CT","NJ","DE","MD","DC"].indexOf(e.id);if(k>-1){var l=d[1];i=d[0],j=l+k*(2+(b.fontSize||12)),a.append("line").attr("x1",i-3).attr("y1",j-5).attr("x2",f[0]).attr("y2",f[1]).style("stroke",b.labelColor||"#000").style("stroke-width",b.lineWidth||1)}return a.append("text").attr("x",i).attr("y",j).style("font-size",(b.fontSize||10)+"px").style("font-family",b.fontFamily||"Verdana").style("fill",b.labelColor||"#000").text(e.id),"bar"})}function k(b,c,d){function e(a){return"undefined"!=typeof a&&"undefined"!=typeof a.latitude&&"undefined"!=typeof a.longitude}var f=this,g=this.options.fills,h=this.options.filters,i=this.svg;if(!c||c&&!c.slice)throw"Datamaps Error - bubbles must be an array";var j=b.selectAll("circle.datamaps-bubble").data(c,d.key);j.enter().append("svg:circle").attr("class","datamaps-bubble").attr("cx",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[0]:void 0}).attr("cy",function(a){var b;return e(a)?b=f.latLngToXY(a.latitude,a.longitude):a.centered&&(b=f.path.centroid(i.select("path."+a.centered).data()[0])),b?b[1]:void 0}).attr("r",function(b){return d.animate?0:a(b.radius,d.radius,b)}).attr("data-info",function(a){return JSON.stringify(a)}).attr("filter",function(b){var c=h[a(b.filterKey,d.filterKey,b)];return c?c:void 0}).style("stroke",function(b){return a(b.borderColor,d.borderColor,b)}).style("stroke-width",function(b){return a(b.borderWidth,d.borderWidth,b)}).style("fill-opacity",function(b){return a(b.fillOpacity,d.fillOpacity,b)}).style("fill",function(b){var c=g[a(b.fillKey,d.fillKey,b)];return c||g.defaultFill}).on("mouseover",function(b){var c=n.select(this);if(d.highlightOnHover){var e={fill:c.style("fill"),stroke:c.style("stroke"),"stroke-width":c.style("stroke-width"),"fill-opacity":c.style("fill-opacity")};c.style("fill",a(b.highlightFillColor,d.highlightFillColor,b)).style("stroke",a(b.highlightBorderColor,d.highlightBorderColor,b)).style("stroke-width",a(b.highlightBorderWidth,d.highlightBorderWidth,b)).style("fill-opacity",a(b.highlightFillOpacity,d.highlightFillOpacity,b)).attr("data-previousAttributes",JSON.stringify(e))}d.popupOnHover&&f.updatePopup(c,b,d,i)}).on("mouseout",function(a){var b=n.select(this);if(d.highlightOnHover){var c=JSON.parse(b.attr("data-previousAttributes"));for(var e in c)b.style(e,c[e])}n.selectAll(".datamaps-hoverover").style("display","none")}),j.transition().duration(400).attr("r",function(b){return a(b.radius,d.radius,b)}),j.exit().transition().delay(d.exitDelay).attr("r",0).remove()}function l(a){return Array.prototype.slice.call(arguments,1).forEach(function(b){if(b)for(var c in b)null==a[c]&&(a[c]=b[c])}),a}function m(a){if("undefined"==typeof n||"undefined"==typeof o)throw new Error("Include d3.js (v3.0.3 or greater) and topojson on this page before creating a new map");return this.options=l(a,p),this.options.geographyConfig=l(a.geographyConfig,p.geographyConfig),this.options.projectionConfig=l(a.projectionConfig,p.projectionConfig),this.options.bubblesConfig=l(a.bubblesConfig,p.bubblesConfig),this.options.arcConfig=l(a.arcConfig,p.arcConfig),n.select(this.options.element).select("svg").length>0&&b.call(this,this.options.element,this.options.height,this.options.width),this.addPlugin("bubbles",k),this.addPlugin("legend",g),this.addPlugin("arc",i),this.addPlugin("labels",j),this.addPlugin("graticule",h),this.options.disableDefaultStyles||d(),this.draw()}var n=window.d3,o=window.topojson,p={scope:"world",responsive:!1,aspectRatio:.5625,setProjection:c,projection:"equirectangular",dataType:"json",data:{},done:function(){},fills:{defaultFill:"#ABDDA4"},filters:{},geographyConfig:{dataUrl:null,hideAntarctica:!0,hideHawaiiAndAlaska:!1,borderWidth:1,borderColor:"#FDFDFD",popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+a.properties.name+"</strong></div>"},popupOnHover:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2},projectionConfig:{rotation:[97,0]},bubblesConfig:{borderWidth:2,borderColor:"#FFFFFF",popupOnHover:!0,radius:null,popupTemplate:function(a,b){return'<div class="hoverinfo"><strong>'+b.name+"</strong></div>"},fillOpacity:.75,animate:!0,highlightOnHover:!0,highlightFillColor:"#FC8D59",highlightBorderColor:"rgba(250, 15, 160, 0.2)",highlightBorderWidth:2,highlightFillOpacity:.85,exitDelay:100,key:JSON.stringify},arcConfig:{strokeColor:"#DD1C77",strokeWidth:1,arcSharpness:1,animationSpeed:600}};m.prototype.resize=function(){var a=this,b=a.options;if(b.responsive){var c=b.element.clientWidth,d=n.select(b.element).select("svg").attr("data-width");n.select(b.element).select("svg").selectAll("g").attr("transform","scale("+c/d+")")}},m.prototype.draw=function(){function a(a){b.options.dataUrl&&n[b.options.dataType](b.options.dataUrl,function(a){if("csv"===b.options.dataType&&a&&a.slice){for(var c={},d=0;d<a.length;d++)c[a[d].id]=a[d];a=c}Datamaps.prototype.updateChoropleth.call(b,a)}),e.call(b,a),f.call(b),(b.options.geographyConfig.popupOnHover||b.options.bubblesConfig.popupOnHover)&&(hoverover=n.select(b.options.element).append("div").attr("class","datamaps-hoverover").style("z-index",10001).style("position","absolute")),b.options.done(b)}var b=this,c=b.options,d=c.setProjection.apply(b,[c.element,c]);return this.path=d.path,this.projection=d.projection,c.geographyConfig.dataUrl?n.json(c.geographyConfig.dataUrl,function(c,d){if(c)throw new Error(c);b.customTopo=d,a(d)}):a(this[c.scope+"Topo"]||c.geographyConfig.dataJson),this},m.prototype.worldTopo="__WORLD__",m.prototype.abwTopo="__ABW__",m.prototype.afgTopo="__AFG__",m.prototype.agoTopo="__AGO__",m.prototype.aiaTopo="__AIA__",m.prototype.albTopo="__ALB__",m.prototype.aldTopo="__ALD__",m.prototype.andTopo="__AND__",m.prototype.areTopo="__ARE__",m.prototype.argTopo="__ARG__",m.prototype.armTopo="__ARM__",m.prototype.asmTopo="__ASM__",m.prototype.ataTopo="__ATA__",m.prototype.atcTopo="__ATC__",m.prototype.atfTopo="__ATF__",m.prototype.atgTopo="__ATG__",m.prototype.ausTopo="__AUS__",m.prototype.autTopo="__AUT__",m.prototype.azeTopo="__AZE__",m.prototype.bdiTopo="__BDI__",m.prototype.belTopo="__BEL__",m.prototype.benTopo="__BEN__",m.prototype.bfaTopo="__BFA__",m.prototype.bgdTopo="__BGD__",m.prototype.bgrTopo="__BGR__",m.prototype.bhrTopo="__BHR__",m.prototype.bhsTopo="__BHS__",m.prototype.bihTopo="__BIH__",m.prototype.bjnTopo="__BJN__",m.prototype.blmTopo="__BLM__",m.prototype.blrTopo="__BLR__",m.prototype.blzTopo="__BLZ__",m.prototype.bmuTopo="__BMU__",m.prototype.bolTopo="__BOL__",m.prototype.braTopo="__BRA__",m.prototype.brbTopo="__BRB__",m.prototype.brnTopo="__BRN__",m.prototype.btnTopo="__BTN__",m.prototype.norTopo="__NOR__",m.prototype.bwaTopo="__BWA__",m.prototype.cafTopo="__CAF__",m.prototype.canTopo="__CAN__",m.prototype.cheTopo="__CHE__",m.prototype.chlTopo="__CHL__",m.prototype.chnTopo="__CHN__",m.prototype.civTopo="__CIV__",m.prototype.clpTopo="__CLP__",m.prototype.cmrTopo="__CMR__",m.prototype.codTopo="__COD__",m.prototype.cogTopo="__COG__",m.prototype.cokTopo="__COK__",m.prototype.colTopo="__COL__",m.prototype.comTopo="__COM__",m.prototype.cpvTopo="__CPV__",m.prototype.criTopo="__CRI__",m.prototype.csiTopo="__CSI__",m.prototype.cubTopo="__CUB__",m.prototype.cuwTopo="__CUW__",m.prototype.cymTopo="__CYM__",m.prototype.cynTopo="__CYN__",m.prototype.cypTopo="__CYP__",m.prototype.czeTopo="__CZE__",m.prototype.deuTopo="__DEU__",m.prototype.djiTopo="__DJI__",m.prototype.dmaTopo="__DMA__",m.prototype.dnkTopo="__DNK__",m.prototype.domTopo="__DOM__",m.prototype.dzaTopo="__DZA__",m.prototype.ecuTopo="__ECU__",m.prototype.egyTopo="__EGY__",m.prototype.eriTopo="__ERI__",m.prototype.esbTopo="__ESB__",m.prototype.espTopo="__ESP__",m.prototype.estTopo="__EST__",m.prototype.ethTopo="__ETH__",m.prototype.finTopo="__FIN__",m.prototype.fjiTopo="__FJI__",m.prototype.flkTopo="__FLK__",m.prototype.fraTopo="__FRA__",m.prototype.froTopo="__FRO__",m.prototype.fsmTopo="__FSM__",m.prototype.gabTopo="__GAB__",m.prototype.psxTopo="__PSX__",m.prototype.gbrTopo="__GBR__",m.prototype.geoTopo="__GEO__",m.prototype.ggyTopo="__GGY__",m.prototype.ghaTopo="__GHA__",m.prototype.gibTopo="__GIB__",m.prototype.ginTopo="__GIN__",m.prototype.gmbTopo="__GMB__",m.prototype.gnbTopo="__GNB__",m.prototype.gnqTopo="__GNQ__",m.prototype.grcTopo="__GRC__",m.prototype.grdTopo="__GRD__",m.prototype.grlTopo="__GRL__",m.prototype.gtmTopo="__GTM__",m.prototype.gumTopo="__GUM__",m.prototype.guyTopo="__GUY__",m.prototype.hkgTopo="__HKG__",m.prototype.hmdTopo="__HMD__",m.prototype.hndTopo="__HND__",m.prototype.hrvTopo="__HRV__",m.prototype.htiTopo="__HTI__",m.prototype.hunTopo="__HUN__",m.prototype.idnTopo="__IDN__",m.prototype.imnTopo="__IMN__",m.prototype.indTopo="__IND__",m.prototype.ioaTopo="__IOA__",m.prototype.iotTopo="__IOT__",m.prototype.irlTopo="__IRL__",m.prototype.irnTopo="__IRN__",m.prototype.irqTopo="__IRQ__",m.prototype.islTopo="__ISL__",m.prototype.isrTopo="__ISR__",m.prototype.itaTopo="__ITA__",m.prototype.jamTopo="__JAM__",m.prototype.jeyTopo="__JEY__",m.prototype.jorTopo="__JOR__",m.prototype.jpnTopo="__JPN__",m.prototype.kabTopo="__KAB__",m.prototype.kasTopo="__KAS__",m.prototype.kazTopo="__KAZ__",m.prototype.kenTopo="__KEN__",m.prototype.kgzTopo="__KGZ__",m.prototype.khmTopo="__KHM__",m.prototype.kirTopo="__KIR__",m.prototype.knaTopo="__KNA__",m.prototype.korTopo="__KOR__",m.prototype.kosTopo="__KOS__",m.prototype.kwtTopo="__KWT__",m.prototype.laoTopo="__LAO__",m.prototype.lbnTopo="__LBN__",m.prototype.lbrTopo="__LBR__",m.prototype.lbyTopo="__LBY__",m.prototype.lcaTopo="__LCA__",m.prototype.lieTopo="__LIE__",m.prototype.lkaTopo="__LKA__",m.prototype.lsoTopo="__LSO__",m.prototype.ltuTopo="__LTU__",m.prototype.luxTopo="__LUX__",m.prototype.lvaTopo="__LVA__",m.prototype.macTopo="__MAC__",m.prototype.mafTopo="__MAF__",m.prototype.marTopo="__MAR__",m.prototype.mcoTopo="__MCO__",m.prototype.mdaTopo="__MDA__",m.prototype.mdgTopo="__MDG__",m.prototype.mdvTopo="__MDV__",m.prototype.mexTopo="__MEX__",m.prototype.mhlTopo="__MHL__",m.prototype.mkdTopo="__MKD__",m.prototype.mliTopo="__MLI__",m.prototype.mltTopo="__MLT__",m.prototype.mmrTopo="__MMR__",m.prototype.mneTopo="__MNE__",m.prototype.mngTopo="__MNG__",m.prototype.mnpTopo="__MNP__",m.prototype.mozTopo="__MOZ__",m.prototype.mrtTopo="__MRT__",m.prototype.msrTopo="__MSR__",m.prototype.musTopo="__MUS__",m.prototype.mwiTopo="__MWI__",m.prototype.mysTopo="__MYS__",m.prototype.namTopo="__NAM__",m.prototype.nclTopo="__NCL__",m.prototype.nerTopo="__NER__",m.prototype.nfkTopo="__NFK__",m.prototype.ngaTopo="__NGA__",m.prototype.nicTopo="__NIC__",m.prototype.niuTopo="__NIU__",m.prototype.nldTopo="__NLD__",m.prototype.nplTopo="__NPL__",m.prototype.nruTopo="__NRU__",m.prototype.nulTopo="__NUL__",m.prototype.nzlTopo="__NZL__",m.prototype.omnTopo="__OMN__",m.prototype.pakTopo="__PAK__",m.prototype.panTopo="__PAN__",m.prototype.pcnTopo="__PCN__",m.prototype.perTopo="__PER__",m.prototype.pgaTopo="__PGA__",m.prototype.phlTopo="__PHL__",m.prototype.plwTopo="__PLW__",m.prototype.pngTopo="__PNG__",m.prototype.polTopo="__POL__",m.prototype.priTopo="__PRI__",m.prototype.prkTopo="__PRK__",m.prototype.prtTopo="__PRT__",m.prototype.pryTopo="__PRY__",m.prototype.pyfTopo="__PYF__",m.prototype.qatTopo="__QAT__",m.prototype.rouTopo="__ROU__",m.prototype.rusTopo="__RUS__",m.prototype.rwaTopo="__RWA__",m.prototype.sahTopo="__SAH__",m.prototype.sauTopo="__SAU__",m.prototype.scrTopo="__SCR__",m.prototype.sdnTopo="__SDN__",m.prototype.sdsTopo="__SDS__",m.prototype.senTopo="__SEN__",m.prototype.serTopo="__SER__",m.prototype.sgpTopo="__SGP__",m.prototype.sgsTopo="__SGS__",m.prototype.shnTopo="__SHN__",m.prototype.slbTopo="__SLB__",m.prototype.sleTopo="__SLE__",m.prototype.slvTopo="__SLV__",m.prototype.smrTopo="__SMR__",m.prototype.solTopo="__SOL__",m.prototype.somTopo="__SOM__",m.prototype.spmTopo="__SPM__",m.prototype.srbTopo="__SRB__",m.prototype.stpTopo="__STP__",m.prototype.surTopo="__SUR__",m.prototype.svkTopo="__SVK__",m.prototype.svnTopo="__SVN__",m.prototype.sweTopo="__SWE__",m.prototype.swzTopo="__SWZ__",m.prototype.sxmTopo="__SXM__",m.prototype.sycTopo="__SYC__",m.prototype.syrTopo="__SYR__",m.prototype.tcaTopo="__TCA__",m.prototype.tcdTopo="__TCD__",m.prototype.tgoTopo="__TGO__",m.prototype.thaTopo="__THA__",m.prototype.tjkTopo="__TJK__",m.prototype.tkmTopo="__TKM__",m.prototype.tlsTopo="__TLS__",m.prototype.tonTopo="__TON__",m.prototype.ttoTopo="__TTO__",m.prototype.tunTopo="__TUN__",m.prototype.turTopo="__TUR__",m.prototype.tuvTopo="__TUV__",m.prototype.twnTopo="__TWN__",m.prototype.tzaTopo="__TZA__",m.prototype.ugaTopo="__UGA__",m.prototype.ukrTopo="__UKR__",m.prototype.umiTopo="__UMI__",m.prototype.uryTopo="__URY__",m.prototype.usaTopo="__USA__",m.prototype.usgTopo="__USG__",m.prototype.uzbTopo="__UZB__",m.prototype.vatTopo="__VAT__",m.prototype.vctTopo="__VCT__",m.prototype.venTopo="__VEN__",m.prototype.vgbTopo="__VGB__",m.prototype.virTopo="__VIR__",m.prototype.vnmTopo="__VNM__",m.prototype.vutTopo="__VUT__",m.prototype.wlfTopo="__WLF__",m.prototype.wsbTopo="__WSB__",m.prototype.wsmTopo="__WSM__",m.prototype.yemTopo="__YEM__",m.prototype.zafTopo="__ZAF__",m.prototype.zmbTopo="__ZMB__",m.prototype.zweTopo={type:"Topology",objects:{zwe:{type:"GeometryCollection",geometries:[{type:"Polygon",properties:{name:"Mashonaland Central"},id:"ZW.MC",arcs:[[0,1,2,3]]},{type:"Polygon",properties:{name:"Harare"},id:"ZW.HA",arcs:[[4,-2,5]]},{type:"Polygon",properties:{name:"Matabeleland North"},id:"ZW.MN",arcs:[[6,7,8,9,10,11]]},{type:"Polygon",properties:{name:"Midlands"},id:"ZW.MI",arcs:[[12,13,14,-7,15]]},{type:"Polygon",properties:{name:"Mashonaland East"},id:"ZW.ME",arcs:[[16,17,-13,18,-6,-1,19]]},{type:"Polygon",properties:{name:"Manicaland"},id:"ZW.MA",arcs:[[20,-17,21]]},{type:"Polygon",properties:{name:"Matabeleland South"},id:"ZW.MS",arcs:[[22,23,-10,24,-8,-15]]},{type:"Polygon",properties:{name:"Bulawayo"},id:"ZW.BU",arcs:[[-25,-9]]},{type:"Polygon",properties:{name:"Masvingo"},id:"ZW.MV",arcs:[[-21,25,-23,-14,-18]]},{type:"Polygon",properties:{name:"Mashonaland West"},id:"ZW.MW",arcs:[[-3,-5,-19,-16,-12,26]]}]}},arcs:[[[9600,8386],[-4,-3],[-34,-36],[-22,-11],[-24,-3],[-73,7],[-11,4],[-22,13],[-11,4],[-173,24],[-32,-4],[-33,-13],[-61,-41],[-33,-13],[-18,-2],[-52,2],[-14,-3],[-11,-6],[-21,-17],[-25,-15],[-29,-12],[-30,-8],[-27,-1],[-12,2],[-21,10],[-12,3],[-19,-4],[-20,-9],[-36,-25],[-13,-16],[-12,-21],[-9,-24],[-14,-70],[-7,-15],[-12,-12],[-41,-23],[-19,-17],[-16,-22],[-13,-26],[-8,-27],[-8,-48],[-5,-15],[-29,-53],[-17,-24],[-21,-21],[-25,-16],[-7,-6],[-7,-10],[-11,-22],[-7,-11],[-14,-14],[-33,-22],[-14,-12],[-11,-21],[-3,-27],[3,-27],[4,-25],[2,-40],[-13,-33],[-20,-31],[-109,-119],[-21,-37],[-3,-13],[-23,-24],[-116,-69],[-6,-8],[-14,-26],[-13,-29],[-8,-13],[-9,-12],[-7,3],[-9,4],[-7,0],[-14,-11],[-67,-72],[-14,-12],[-14,-7],[-44,20],[-15,-5],[-28,-17],[-12,-3],[-9,12],[-6,14],[-4,16],[-2,15],[2,16],[43,125],[3,9],[-1,9],[-10,3],[-202,19],[-17,-10],[-17,-18],[-20,-48],[-27,-34],[-2,-8],[8,-19],[9,-6],[8,-1],[3,-12],[1,-12],[-25,-123]],[[7502,6975],[-4,-64],[-10,-23],[-9,-9],[-8,-5],[-8,-2],[-24,0],[-12,-5],[-16,-2],[-12,0],[-6,2],[-7,5],[-21,21]],[[7365,6893],[-35,65],[-24,10],[-8,-5],[-8,-9],[-6,-9],[-8,-7],[-8,0],[-9,5],[-17,13],[-60,36],[-71,31],[-12,9],[-4,9],[0,14],[2,11],[10,28],[4,26],[-2,6],[-7,2],[-180,-1],[-5,3],[-7,6],[0,35],[16,123],[-1,20],[-2,24],[-17,83],[3,10],[2,8],[19,31],[48,102],[1,8],[0,7],[-5,8],[-6,5],[-5,6],[-1,6],[4,8],[5,6],[5,5],[19,20],[17,21],[15,25],[3,6],[5,15],[14,121],[0,10],[-1,8],[-7,8],[-7,4],[-15,5],[-7,2],[-8,0],[-6,-2],[-6,-3],[-11,-8],[-6,-4],[-5,0],[-5,5],[-2,15],[-4,9],[-7,6],[-7,-1],[-7,-3],[-17,-11],[-15,-13],[-6,-2],[-7,0],[-6,3],[-5,4],[-6,2],[-8,1],[-32,-3],[-8,0],[-7,2],[-5,4],[-4,7],[-5,30],[-1,38],[0,5],[5,4],[5,3],[5,4],[4,4],[1,7],[-5,8],[-4,6],[-21,19],[-4,5],[-3,6],[-3,12],[-8,130],[2,9],[1,9],[3,8],[3,6],[20,29],[6,14],[2,9],[-1,11],[-3,12],[-9,19],[-6,10],[-7,6],[-6,4],[-7,3],[-6,4],[-5,5],[3,8],[2,7],[3,9],[-1,10],[-19,49],[-4,4],[-3,2],[-10,6],[-41,27],[-7,3],[-15,3],[-6,3],[-11,9],[-6,4],[-21,9],[-5,4],[-5,7],[-10,22],[-12,21],[-2,10],[-1,13],[9,62],[11,34],[3,7],[7,13],[6,15],[8,46],[3,37],[2,6],[5,13],[16,33],[3,8],[0,12],[-2,17],[-9,33],[-6,18],[-10,20],[-9,5],[-9,3],[-73,-5],[-6,2],[-6,3],[-3,3],[-23,28],[-4,10],[-12,39],[-5,12],[-6,7],[-8,3],[-17,2],[-8,0],[-46,-6],[-13,-6],[-5,-4],[-4,-6],[-3,-7],[-1,-8],[1,-9],[6,-30],[1,-9],[-2,-7],[-3,-7],[-4,-6],[-4,-6],[-6,-4],[-5,-3],[-29,-7],[-6,-4],[-5,-4],[-8,-12],[-4,-5],[-6,-3],[-7,1],[-28,7],[-8,0],[-7,-1],[-13,-6],[-5,-4],[-4,-5],[-3,-7],[-6,-16],[-3,-6],[-5,-4],[-7,-1],[-7,1],[-15,5],[-6,3],[-6,4],[-3,8],[2,15],[22,57],[8,13],[6,7],[16,12],[4,6],[4,9],[2,12],[0,20],[-6,48],[0,9],[2,12],[4,12],[16,29],[6,7],[33,24],[4,6],[3,9],[-1,14],[-3,9],[-6,4],[-7,1],[-7,-1],[-7,-1],[-6,2],[-5,4],[-5,5],[-8,12],[-1,7],[0,9],[0,8],[-1,5],[-1,4],[-9,9],[-4,6],[-3,6],[-2,7],[-3,16],[0,13],[5,38],[-1,13],[-4,8],[-27,11],[-6,4],[-10,10],[-3,6],[-3,6],[-2,8],[-1,8],[1,6],[2,4],[5,5],[333,322],[10,19],[5,21],[-10,32],[-6,15],[-16,20],[-5,21],[-1,59],[4,16]],[[6489,9963],[40,-19],[37,1],[51,23],[1,-119],[2,-141],[3,-176],[2,-103],[143,2],[92,0],[208,2],[138,1],[57,-13],[51,-41],[21,-24],[19,-16],[21,-4],[29,14],[14,15],[24,29],[19,11],[11,-3],[10,-8],[10,-1],[10,21],[32,22],[57,-5],[129,-34],[25,-11],[21,-16],[18,-26],[24,-50],[16,-20],[25,-15],[12,-10],[6,-14],[4,-14],[8,-9],[5,-1],[10,2],[5,1],[6,-4],[0,-7],[-2,-7],[2,-4],[25,-4],[28,0],[25,-4],[19,-15],[50,-28],[213,-15],[32,-16],[35,-32],[77,-95],[25,-23],[53,-31],[16,-14],[11,-26],[6,-57],[11,-22],[21,-11],[132,-24],[253,7],[100,-17],[132,-59],[203,-91],[153,-68],[14,-15],[6,-21],[1,-34],[13,-58],[34,-29],[7,-4]],[[7234,6558],[34,8],[42,16],[5,7],[2,7],[-4,15],[-6,10],[-6,9],[-27,16],[-7,6],[-3,9],[2,13],[8,19],[0,17],[-3,9],[-5,7],[-4,8],[0,12],[3,17],[3,10],[6,6],[12,3],[14,7],[15,12],[35,46],[15,46]],[[7502,6975],[23,-2],[6,-2],[4,-5],[3,-12],[3,-11],[6,-9],[9,-5],[23,-5],[42,2],[12,-4],[6,-7],[5,-16],[1,-16],[-1,-16],[-5,-25],[0,-10],[2,-10],[5,-10],[4,-8],[2,-10],[-1,-16],[0,-9],[3,-15],[4,-13],[12,-28],[2,-7],[0,-5],[-3,-8],[-6,-9],[-12,-14],[-5,-10],[-2,-10],[-2,-15],[-6,-9],[-11,-8],[-47,-23],[-2,-4],[0,-5],[4,-16],[1,-9],[1,-16],[0,-7],[-5,-25],[-13,-16],[-18,-9],[-4,-11],[-1,-7],[4,-6],[12,-10],[1,-4],[-4,-6],[-22,-18],[-7,-3],[-16,-2],[-8,-2],[-7,-4],[-11,-8],[-4,-2],[-5,-1],[-5,-1],[-8,2],[-11,6],[-29,28],[-63,45],[-49,23],[-16,5],[-25,11],[-34,35]],[[3870,7353],[-3,-54],[-3,-13],[-10,-30],[-1,-19],[-2,-7],[-3,-5],[-4,-5],[-1,0],[0,-1],[-7,-26],[-9,-24],[-2,-8],[0,-11],[1,-12],[4,-19],[1,-12],[-1,-10],[-2,-7],[-7,-12],[-3,-7],[-7,-35],[-7,-24],[0,-10],[8,-34],[1,-12],[-2,-9],[-35,-42],[-20,-19],[-9,-11],[-5,-4],[-5,-2],[-6,2],[-4,4],[-5,5],[-6,3],[-6,1],[-6,-2],[-5,-4],[-34,-33],[-2,-8],[-4,-17],[-6,-15],[-1,-9],[0,-11],[15,-69],[-1,-14],[-1,-10],[-16,-34],[-11,-31],[0,-8],[0,-11],[2,-14],[-1,-9],[-4,-6],[-6,-4],[-5,-3],[-5,-5],[-4,-6],[-2,-7],[-3,-18],[-3,-8],[-3,-6],[-4,-6],[-5,-5],[-21,-17],[-4,-5],[-4,-6],[0,-1],[-1,-3],[0,-7],[1,-8],[7,-24],[3,-20],[3,-10],[6,-7],[12,-9],[8,-1],[8,2],[6,2],[6,2],[7,-2],[5,-5],[3,-11],[-1,-30],[2,-17],[7,-21],[2,-7],[-1,-9],[-2,-7],[-4,-7],[-5,-5],[-10,-8],[-5,-5],[-4,-6],[-2,-7],[2,-9],[5,-9],[13,-10],[10,-5],[27,-6],[7,-4],[6,-6],[4,-11],[-3,-5],[-6,-2],[-69,-9],[-35,-9],[-7,-4],[-5,-8],[-5,-432],[2,-21],[5,-20],[14,-14],[10,-7],[9,-4],[9,-1],[16,-1],[68,10],[8,0],[7,-2],[20,-10],[16,-4],[52,-3],[17,-4],[23,-7],[128,-5],[16,-3],[8,-1],[16,1],[29,6],[66,0],[25,-5],[17,-1],[53,3],[11,2],[40,14],[8,1],[81,0],[6,1],[23,2],[15,3],[26,10],[8,1],[8,0],[8,-1],[7,-3],[13,-6],[24,-8],[18,-2],[478,5],[12,-2],[9,-6],[17,-35],[6,-17],[0,-13],[-6,-16],[-11,-10],[-224,-104],[-70,-19],[-12,-6],[-9,-13],[-1,-27],[75,-373],[5,-17],[7,-17],[11,-11],[8,-5],[16,-7],[9,-10],[10,-15],[27,-51],[5,-13],[0,-13],[-2,-11],[-1,-9],[-4,-8],[-8,-11],[-6,-7],[-3,-7],[-1,-7],[0,-42],[0,-3],[2,-6],[11,-18],[15,-12],[116,-137],[8,-14],[3,-10],[0,-18],[-3,-11],[-28,-49],[6,-11],[160,-168]],[[5118,4290],[-11,-26],[-6,-8],[-13,-10],[-10,-2],[-11,2],[-9,6],[-6,6],[-12,8],[-13,5],[-42,9],[-8,0],[-9,-1],[-8,-2],[-7,-6],[-4,-8],[0,-16],[4,-11],[7,-10],[19,-19],[8,-12],[5,-11],[1,-11],[0,-12],[-5,-23],[-6,-13],[-11,-16],[-57,-54],[-5,-7],[-3,-8],[3,-11],[6,-9],[17,-18],[9,-8],[10,-6],[7,-8],[4,-11],[2,-16],[-2,-59],[-2,-12],[-5,-22],[-10,-28],[-1,-9],[-3,-11],[-9,-12],[-29,-24],[-17,-7],[-15,-4],[-76,16],[-7,0],[-4,-5],[-1,-10],[2,-8],[5,-15],[18,-76],[12,-33],[1,-5],[-1,-6],[-3,-7],[-22,-39],[-15,-14],[-90,-55],[-55,-26],[-6,-3],[-4,-7],[-4,-11],[0,-10],[1,-8],[8,-29],[0,-8],[-4,-10],[-125,-165],[-24,-25],[-21,-17],[-9,-22],[-33,-40],[-18,-2],[-9,2],[-66,58]],[[4321,3185],[85,21],[21,10],[9,8],[7,8],[5,9],[2,8],[0,7],[-2,7],[-5,6],[-25,18],[-5,5],[-4,6],[0,7],[1,6],[1,4],[8,18],[4,10],[2,10],[0,7],[-5,5],[-8,3],[-9,4],[-4,5],[-2,6],[-2,17],[-5,6],[-9,-1],[-17,-9],[-9,-3],[-10,1],[-8,4],[-14,12],[-6,4],[-7,1],[-7,1],[-5,1],[-3,3],[-4,4],[-1,4],[0,4],[2,5],[0,18],[2,7],[4,12],[-1,4],[-4,2],[-8,0],[-25,-9],[-28,-5],[-8,-4],[-16,-15],[-8,-5],[-10,0],[-13,4],[-72,42],[-10,3],[-5,0],[-4,-4],[-2,-7],[1,-18],[13,-53],[-1,-14],[-2,-8],[-5,-14],[-2,-10],[-1,-11],[0,-16],[-4,-9],[-8,-7],[-17,-11],[-4,-6],[1,-7],[6,-7],[16,-5],[10,-2],[16,1],[10,-1],[21,-6],[9,-1],[7,2],[15,7],[10,1],[10,-3],[9,-17],[12,-15],[3,-14],[-16,-41]],[[4193,3195],[-33,-15],[-173,-141],[-8,-2],[-7,8],[-13,29],[-6,22],[-5,15],[-3,6],[-16,23],[-14,16],[-3,6],[-2,8],[-3,7],[-5,4],[-6,4],[-6,4],[-6,5],[-4,5],[-3,6],[-2,9],[-3,6],[-26,24],[-27,31],[-3,7],[-2,17],[-2,7],[-3,7],[-8,12],[-2,7],[-2,9],[-1,7],[-4,6],[-5,5],[-7,4],[-18,8],[-6,5],[-5,5],[-3,6],[-6,15],[-3,5],[-3,4],[-4,0],[-6,-2],[-43,-19],[-13,-2],[-55,-2],[-11,-5],[-47,-26],[-6,0],[-7,4],[-44,77],[-7,8],[-6,-5],[-4,-11],[-18,-59],[-8,-46],[-3,-42],[-3,-15],[-6,-8],[-41,0],[-52,10],[-17,6],[-15,11],[-6,7],[-4,7],[-5,5],[-5,5],[-29,18],[-21,18],[-95,100],[-21,17],[-23,16],[-10,9],[-45,54],[-25,43],[-8,8],[-8,7],[-9,10],[-3,7],[-2,7],[-4,27],[-1,18],[-1,9],[-2,8],[-4,7],[-4,6],[-17,12],[-8,11],[-4,7],[-8,12],[-10,5],[-16,4],[-34,5],[-28,8],[-19,11],[-44,33],[-24,14],[-25,7],[-69,10],[-10,-1],[-9,-2],[-11,-8],[-18,-16],[-9,-3],[-11,-2],[-21,2],[-23,5],[-20,-1],[-14,-3],[-14,3],[-8,5],[-7,4],[-7,3],[-8,2],[-8,0],[-32,6],[-178,-24],[-28,6],[-22,2],[-24,-1],[-250,-66],[-20,-8],[-41,-25],[-23,-14]],[[1863,3701],[-4,5],[-18,11],[-58,19],[-24,11],[-18,20],[-9,33],[-10,28],[-22,25],[-52,36],[-24,11],[-51,13],[-23,10],[-25,25],[-35,59],[-29,17],[-39,8],[-10,5],[-15,11],[0,3],[9,5],[9,18],[9,31],[-3,12],[-13,19],[-10,11],[-12,10],[-14,7],[-69,2],[-57,17],[-50,34],[-32,53],[-122,379],[-30,65],[-38,56],[-32,58],[-10,28],[-5,36],[4,30],[21,55],[5,30],[-1,2],[-34,116],[-160,158],[-46,111],[-8,107],[-15,52],[-32,32],[-49,28],[-37,35],[-60,96],[-18,20],[-43,32],[-84,98],[-17,30],[-6,20],[-12,62],[-11,29],[-41,74],[-41,114],[-27,55],[-39,34],[-43,28],[-35,42],[-52,99],[-37,102],[-9,35],[0,42],[52,126],[9,-10],[23,-12],[39,-34],[25,-13],[13,-2],[40,2],[42,-18],[14,-2],[96,0],[7,-2],[12,-8],[8,-1],[7,3],[11,13],[8,4],[86,18],[67,33],[32,4],[16,-12],[15,-15],[48,-15],[29,-15],[25,-18],[11,-16],[13,-22],[58,-28],[17,-25],[-20,-8],[-2,-21],[9,-24],[13,-17],[78,-41],[55,-2],[14,2],[71,41],[9,-11],[7,18],[21,5],[23,1],[18,6],[2,6],[-3,18],[1,6],[8,3],[22,7],[22,13],[29,6],[12,7],[45,39],[12,6],[11,-5],[9,-12],[8,-14],[6,-9],[12,-5],[59,-7],[12,-6],[10,-9],[8,-9],[10,-2],[47,8],[59,-12],[98,-60],[53,-19],[34,-6],[22,-10],[16,-15],[20,-24],[18,-17],[20,-12],[73,-26],[18,-3],[16,5],[36,37],[17,11],[20,6],[32,4],[120,61],[30,-11],[47,35],[14,6],[60,3],[19,6],[35,21],[38,40],[47,51],[43,59],[-3,35],[-1,36],[2,15],[13,22],[338,390],[131,132],[68,77],[34,74],[26,117],[21,51],[174,291],[51,61],[66,45],[197,94]],[[3583,8155],[4,-11],[21,-107],[-8,-55],[-4,-14],[-1,-8],[0,-2],[0,-1],[5,-6],[21,-12],[7,-9],[1,-14],[-17,-51],[-3,-26],[-1,-7],[2,-6],[5,-6],[10,-7],[18,-8],[7,-6],[5,-9],[6,-17],[4,-11],[6,-8],[9,-3],[8,0],[6,2],[7,1],[7,-1],[14,-6],[6,-4],[3,-6],[0,-10],[-5,-18],[-1,-9],[2,-9],[3,-9],[8,-13],[4,-10],[-1,-15],[-6,-16],[1,-9],[3,-8],[8,-12],[5,-9],[1,-12],[0,-25],[6,-25],[2,-15],[3,-12],[5,-9],[25,-21],[12,-17],[15,-10],[10,-5],[10,-3],[8,-10],[6,-8],[15,-65]],[[6574,5213],[69,-19],[22,-11],[20,-22],[32,-25],[55,-26],[25,-7],[19,-9],[27,-27],[9,-4],[8,-2],[12,-2],[20,-5],[35,0],[27,-4],[9,-5],[11,-6],[8,-4],[9,-3],[-3,-8],[-13,-23],[-2,-6],[1,-9],[4,-14],[26,-50],[31,-45],[6,-13],[4,-11],[2,-10],[1,-9],[-1,-6],[-5,-2],[-5,-1],[-5,-4],[-6,-21],[-1,-6],[0,-4],[0,-4],[1,-4],[1,-4],[1,-3],[1,-3],[-1,-4],[-1,-5],[2,-8],[4,-5],[26,-23],[19,-23],[25,-24],[19,-26],[17,-17],[5,-2],[32,-8],[32,-20]],[[7208,4607],[-178,-160],[-10,-11],[-3,-8],[5,-5],[8,-4],[40,-9],[9,-6],[8,-9],[1,-10],[-6,-12],[-17,-11],[-14,-7],[-8,-7],[-2,-12],[-3,-121],[-13,-78],[4,-24],[5,-21],[2,-15],[-2,-15],[-22,-119],[-1,-12],[1,-36],[-1,-9],[-14,-46],[-4,-28],[1,-19],[2,-16],[1,-6],[2,-4],[4,-5],[4,-3],[2,-6],[-2,-7],[-10,-6],[-9,-4],[-38,-10],[-16,-1],[-16,2],[-38,11],[-35,21],[-10,4],[-12,1],[-109,-4],[-12,-8],[-14,-30],[-1,-7],[1,-4],[2,-4],[1,-6],[1,-2],[1,-2],[2,-2],[1,-3],[1,-4],[0,-8],[2,-5],[2,-4],[2,-3],[1,-3],[1,-3],[-1,-5],[-4,-6],[-7,-10],[-30,-25],[-4,-7],[-2,-14],[0,-6],[1,-5],[1,-2],[1,-4],[-1,-5],[-3,-8],[-1,-5],[0,-3],[2,-2],[4,0],[9,-1],[-6,-7],[-7,-2],[-9,-2],[-199,-9],[-8,-1],[-7,-2],[-96,-51],[-10,-8],[-4,-8],[7,-10],[3,-11],[2,-20],[-9,-52],[-15,-32],[-7,-60],[1,-42],[3,-4],[9,4],[22,16],[21,-2],[21,-7],[17,-12],[7,-10],[6,-19],[4,-24],[2,-29],[-1,-30],[-10,-36],[0,-15],[3,-9],[6,-3],[5,-5],[17,-33],[5,-5],[10,-9],[5,-4],[7,-3],[14,-5],[25,-2],[6,-1],[6,-4],[2,-6],[0,-8],[-1,-8],[1,-8],[2,-8],[3,-7],[8,-11],[5,-5],[6,-4],[7,-3],[15,-3],[7,-2],[5,-5],[3,-6],[3,-7],[3,-8],[3,-7],[5,-5],[5,-5],[28,-21],[5,-5],[3,-6],[3,-8],[1,-18],[2,-7],[2,-8],[1,-1],[53,-64],[2,-8],[-2,-12],[-5,-16],[-2,-13],[0,-10],[5,-15],[2,-8],[1,-9],[-2,-27],[0,-9],[2,-8],[2,-8],[4,-6],[4,-6],[5,-5],[11,-9],[4,-5],[2,-8],[3,-18],[3,-8],[2,-7],[0,-9],[-2,-7],[-17,-32],[-3,-9],[-2,-9],[0,-8],[6,-31],[0,-8],[-2,-9],[-14,-29],[-6,-35],[-4,-9],[-5,-9],[-14,-18],[-4,-8],[-2,-8],[7,-108],[-3,-23],[-10,-16],[-127,-127],[-11,-2],[-9,2],[-8,3],[-9,3],[-24,15],[-7,0],[-4,-1],[-13,-10],[-6,-3],[-6,-3],[-4,-2],[-4,-4],[-7,-3],[-9,0],[-34,6],[-24,0],[-32,5],[-12,3],[-7,4],[-6,4],[-7,7],[-8,10],[-5,5],[-7,4],[-29,10],[-16,10],[-12,12],[-20,29],[-5,5],[-9,5],[-19,8],[-6,4],[-9,7],[-8,8],[-10,9],[-9,6],[-9,8],[-6,7],[-9,6],[-9,3],[-16,1],[-13,-2],[-12,-5],[-17,-10],[-151,-137],[-26,-14],[-15,-2],[-10,-1],[-65,9]],[[5762,2024],[-20,1],[-40,-6],[-89,-22],[-52,1],[-48,13],[-34,2],[-7,1],[-25,9],[-13,7],[-4,5],[-1,6],[17,16],[182,135],[5,8],[4,13],[67,344],[0,10],[-8,0],[-54,-19],[-12,-2],[-7,4],[-5,17],[-5,12],[-8,13],[-12,12],[-4,7],[-1,8],[3,13],[2,8],[4,7],[7,3],[9,7],[9,9],[-1,18],[12,32],[3,16],[0,77],[9,2],[9,-3],[43,-33],[12,-5],[11,6],[9,9],[21,30],[10,21],[10,28],[3,15],[1,12],[-2,48],[0,12],[2,9],[76,201],[2,10],[6,70],[2,8],[85,196],[4,15],[0,14],[-40,74],[-4,1],[-6,-1],[-18,-13],[-5,0],[-5,2],[-8,37],[-9,16],[-18,1],[-90,-24],[-10,4],[-7,18],[-8,44],[-5,16],[-7,2],[-7,-2],[-13,-7],[-7,-2],[-57,-14],[-7,1],[-8,4],[-58,48],[-84,46],[-11,3],[-19,2],[-22,-11],[-22,-7],[-13,4],[-14,29],[-4,16],[-3,14],[0,18],[14,71],[-2,12],[-3,14],[-11,26],[-8,16],[-34,37],[-11,19],[-10,33],[-1,11],[-4,10],[-6,10],[-30,29],[-5,7],[-5,9],[-30,64],[-13,39],[-18,24],[-65,66]],[[3870,7353],[42,-8],[17,4],[16,9],[129,34],[21,3],[19,-2],[29,-8],[8,1],[7,4],[7,5],[7,-1],[6,-5],[6,-6],[11,-8],[7,0],[6,3],[9,15],[7,8],[23,16],[6,6],[50,69],[13,30],[14,27],[50,66],[12,11],[9,5],[10,1],[27,-4],[11,0],[7,4],[5,6],[10,20],[8,9],[11,10],[36,25],[11,5],[59,20],[16,3],[8,0],[35,-6],[11,0],[8,2],[6,4],[39,33],[7,2],[6,-2],[7,-8],[6,-8],[6,-13],[5,-23],[3,-6],[11,-19],[3,-6],[8,-21],[7,-12],[8,-11],[5,-5],[6,-4],[88,-33],[36,-33],[7,-4],[21,-4],[8,-3],[7,-3],[5,-5],[5,-5],[5,-5],[6,-4],[81,-31],[7,-2],[15,-1],[11,3],[13,4],[10,2],[9,-2],[8,-4],[8,-6],[13,-10],[4,-5],[5,-5],[9,-20],[4,-15],[6,-32],[2,-7],[2,-7],[4,-6],[85,-109],[9,-6],[11,-6],[23,-8],[9,-7],[3,-7],[-9,-25],[-3,-6],[-4,-5],[-5,-4],[-22,-16],[-10,-9],[-8,-12],[-3,-7],[-6,-15],[-7,-13],[-10,-9],[-6,-5],[-5,-4],[-4,-6],[-4,-6],[-2,-7],[0,-9],[4,-9],[15,-17],[4,-7],[2,-8],[3,-34],[-5,-36],[-1,-36],[-1,-9],[-3,-8],[-3,-8],[-3,-6],[-4,-6],[-21,-17],[-5,-6],[-1,-7],[2,-9],[12,-15],[6,-9],[3,-10],[-2,-7],[-7,-14],[-8,-11],[-16,-14],[-5,-5],[-4,-6],[-2,-8],[-6,-38],[-3,-8],[-3,-7],[-17,-23],[-3,-7],[-9,-23],[-14,-27],[-34,-92],[-8,-12],[-3,-6],[-2,-8],[-2,-16],[-2,-6],[-2,-4],[-1,-1],[-1,-2],[0,-3],[0,-7],[3,-14],[3,-7],[3,-16],[0,-8],[-1,-17],[1,-8],[2,-7],[3,-7],[3,-7],[3,-16],[4,-8],[6,-9],[18,-20],[5,-8],[1,-8],[0,-9],[1,-8],[2,-7],[4,-6],[34,-35],[4,-6],[3,-7],[6,-16],[6,-9],[32,-39],[10,-8],[28,-15],[8,-7],[6,-7],[11,-19],[12,-15],[10,-6],[20,-6],[14,-5],[175,-125],[48,-24],[7,-7],[4,-9],[1,-8],[-1,-9],[-2,-9],[-14,-39],[-2,-8],[0,-9],[1,-16],[-1,-6],[-2,-4],[-4,-5],[-9,-9],[-4,-6],[-2,-7],[-1,-8],[5,-69],[-3,-28],[0,-9],[0,-9],[3,-11],[5,-12],[9,-18],[9,-7],[8,-4],[13,-4],[16,-7],[49,-33],[13,-6],[7,1],[12,6],[7,0],[32,-6],[8,-1],[10,-3],[11,-4],[16,-10],[9,-9],[10,-12],[7,-5],[7,-1],[6,3],[8,1],[9,-1],[28,-14],[12,-3],[5,-3],[5,-4],[3,-8],[7,-11],[7,-6],[7,-3],[19,-4],[43,-22],[7,-3],[48,-10],[7,0],[6,4],[10,9],[5,4],[21,7],[9,1],[11,-2],[42,-23],[25,-2],[8,-2],[31,-18],[7,-3],[16,-4],[17,-2],[33,2],[8,-1],[44,-12],[17,-7],[17,-11],[10,-10],[27,-32],[9,-7],[10,-5],[19,-6],[30,-15],[29,-7],[17,-7],[9,-5],[11,-10],[45,-47]],[[9905,7565],[-1,0],[-7,-1],[-27,-8],[-95,-42],[-12,-10],[-30,-31],[-86,-69],[-6,-7],[-4,-8],[-38,-84],[-2,-7],[0,-18],[-2,-10],[-7,-13],[-8,-9],[-8,-5],[-19,-10],[-13,-4],[-40,-17],[-8,-7],[-5,-7],[-2,-8],[-2,-27],[-1,-9],[-3,-9],[-4,-8],[-53,-63],[-14,-10],[-11,-7],[-7,-1],[-14,-5],[-12,-6],[-7,-3],[-23,-3],[-45,-22],[-7,-7],[-16,-25],[-19,-23],[-12,-8],[-9,-2],[-14,5],[-7,2],[-9,1],[-11,-2],[-14,-5],[-23,-12],[-20,-15],[-52,-58],[-13,-10],[-29,-18],[-11,-10],[-6,-7],[-7,-12],[-12,-26],[-10,-15],[-7,-8],[-6,-5],[-27,-12],[-27,-16],[-35,-15],[-8,-6],[-6,-6],[-4,-7],[-2,-8],[-9,-56],[-1,-19],[3,-24],[-1,-7],[-5,-8],[-300,-335],[-1,-10],[0,-9],[3,-26],[-5,-15],[-10,-20],[-47,-70],[-3,-8],[-1,-9],[-3,-48],[4,-26],[4,-14],[2,-6],[1,-15],[-18,-41],[-5,-16],[4,-28],[-2,-15],[-9,-7],[-13,-6],[-56,-14],[-12,-5],[-10,-7],[-5,-12],[-2,-14],[-1,-15],[-3,-14],[-3,-13],[-7,-6],[-5,-4],[-4,-4],[-8,-9],[6,-12],[14,-25],[4,-23],[11,-26],[1,-11],[-5,-16],[-7,-19],[0,-11],[3,-8],[3,-6],[3,-16],[2,-7],[12,-18],[12,-26],[4,-6],[4,-6],[14,-15],[11,-19],[12,-17],[3,-6],[3,-9],[6,-29],[5,-17],[3,-6],[5,-6],[4,-4],[7,-4],[7,-2],[23,-1],[9,-2],[7,-2],[6,-4],[5,-5],[4,-6],[28,-78],[23,-127],[2,-8],[3,-6],[5,-6],[5,-4],[12,-8],[4,-6],[2,-7],[2,-8],[2,-7],[4,-6],[12,-18],[6,-12],[6,-14],[1,-7],[1,-7],[-5,-62],[1,-28],[-3,-28],[-1,-20],[6,-36],[-8,-25],[-8,6],[-9,4],[-13,3],[-10,0],[-22,-8],[-12,-2],[-26,7],[-51,27],[-47,13],[-20,17],[-48,55],[-18,12],[-21,8],[-22,-2],[-63,-11],[-24,2],[-43,21],[-28,8],[-12,0],[-5,-5],[3,-6],[8,-11],[3,-6],[2,-7],[-2,-7],[-9,-21],[-9,-31],[-18,-42],[-11,-20],[-8,-11],[-23,-26],[-9,-3],[-8,1],[-6,4],[-6,4],[-6,4],[-9,3],[-14,0],[-10,2],[-7,3],[-3,7],[-1,8],[0,8],[-2,8],[-3,6],[-24,26],[-7,5],[-11,5],[-7,-1],[-6,-4],[-10,-12],[-7,-6],[-8,-2],[-8,1],[-13,6],[-12,7],[-12,3],[-17,1],[-34,-2],[-13,-8],[-6,-8],[-1,-7],[-5,-8],[-1,-59],[-13,-35],[0,-72],[-15,-8],[-125,-16]],[[7642,4642],[-200,-5],[-56,8],[-4,8],[-2,8],[-1,17],[1,19],[4,16],[2,7],[2,3],[2,5],[-1,3],[-1,2],[0,1],[1,1],[4,8],[3,10],[1,11],[-1,7],[-5,3],[-4,1],[-44,4],[-14,-2],[-8,-7],[-14,-23],[-8,-11],[-11,-6],[-19,-3],[-7,-2],[-3,-6],[2,-11],[5,-14],[-2,-31],[-56,-56]],[[6574,5213],[23,24],[9,22],[10,69],[3,12],[55,124],[11,14],[7,2],[6,-4],[3,-6],[3,-6],[14,-44],[3,-6],[3,-5],[3,-4],[20,-53],[7,-2],[10,2],[26,10],[10,7],[5,6],[-1,6],[-4,15],[-1,7],[1,7],[5,3],[7,-1],[28,-10],[5,-4],[11,-10],[7,-4],[9,-1],[14,2],[5,6],[2,4],[-1,6],[-6,23],[0,9],[2,9],[6,2],[7,-1],[29,-8],[4,-2],[4,-4],[5,-9],[2,3],[4,4],[58,111],[55,59],[9,12],[5,12],[3,18],[9,204],[2,12],[4,15],[6,10],[8,7],[13,11],[5,9],[2,8],[-2,7],[-6,14],[-7,12],[-35,43],[-53,54],[-8,11],[-4,6],[-2,7],[-2,8],[-2,49],[1,13],[4,8],[5,5],[12,7],[7,3],[9,10],[12,16],[22,38],[7,18],[3,14],[-3,7],[-1,8],[0,8],[12,47],[6,12],[6,9],[18,13],[3,9],[1,7],[-19,49],[-2,9],[-1,12],[3,18],[3,9],[4,6],[1,1],[1,1],[20,9],[6,4],[7,7],[3,7],[1,8],[-2,8],[-5,14],[-2,10],[-1,12],[3,20],[5,11],[5,2],[4,-1],[10,-5],[35,-15],[17,-11],[7,-5]],[[9600,8386],[1,0],[11,8],[18,8],[20,4],[40,-3],[78,-19],[40,-3],[21,6],[38,27],[21,10],[16,2],[-9,-42],[-36,-155],[-22,-48],[-20,-29],[-91,-99],[1,-10],[73,-142],[53,-106],[33,-85],[17,-90],[2,-55]],[[9188,1632],[-122,85],[-20,29],[1,22],[-45,119],[-9,35],[-7,7],[-90,49],[15,22],[-4,15],[-28,24],[-7,14],[-4,13],[-12,69],[-5,13],[-12,18],[0,12],[18,34],[5,14],[6,32],[46,94],[24,76],[2,30],[5,26],[12,22],[27,38],[13,87],[19,29],[10,71],[7,207],[63,415],[1,147],[-8,13],[-3,14],[-23,16],[-67,36],[-85,72],[-17,7],[-12,4],[-10,1],[-25,-1],[-29,-5],[-8,3],[-8,6],[-11,13],[-8,8],[-10,7],[-14,4],[-11,2],[-14,4],[-16,8],[-28,22],[-19,21],[-10,13],[-10,22],[-6,7],[-8,7],[-34,17],[-7,5],[-7,7],[-10,14],[-4,10],[-4,9],[-12,44],[-7,10],[-9,12],[-40,40],[-30,38],[-4,7],[-2,7],[-9,29],[-4,7],[-8,9],[-16,12],[-59,35],[-8,8],[-11,13],[-6,10],[-3,9],[-1,8],[-2,8],[-2,7],[-5,6],[-39,42],[-7,13],[-3,12],[0,16],[0,5],[-5,5],[-9,4],[-31,5],[-13,4],[-18,8],[-11,7],[-10,9],[-6,4],[-10,1],[-14,-6],[-6,-1],[-8,2],[-8,5],[-8,11],[-5,8],[-7,10],[-11,10],[-33,26],[-13,7],[-24,6],[-8,3],[-6,4],[-14,15],[-5,4],[-7,3],[-14,1],[-9,-2],[-7,-3],[-6,-5],[-6,-2],[-8,1],[-15,8],[-16,12],[-47,43],[-207,139],[-17,23]],[[9905,7565],[0,-15],[5,-32],[13,-30],[12,-10],[28,-17],[9,-14],[1,-23],[-8,-23],[-6,-10],[-18,-31],[-50,-108],[-8,-12],[-12,-8],[-8,-10],[0,-16],[14,-50],[6,-12],[22,-19],[48,-25],[17,-26],[6,-30],[-5,-28],[-21,-55],[-5,-57],[3,-64],[-5,-54],[-32,-24],[-22,-11],[-14,-25],[-8,-30],[-3,-29],[2,-29],[15,-41],[2,-26],[-5,-19],[-10,-10],[-10,-8],[-6,-11],[2,-14],[15,-21],[-1,-12],[-7,-30],[3,-33],[45,-152],[4,-49],[-1,-11],[-11,-31],[-10,-20],[-8,-11],[-2,-12],[6,-22],[19,-31],[60,-54],[23,-28],[10,-28],[-6,-17],[-37,-30],[-27,-43],[-3,1],[1,-15],[17,-21],[4,-15],[-8,-30],[-23,-19],[-28,-15],[-25,-16],[-23,-13],[-24,-8],[-22,-10],[-16,-20],[-3,-25],[4,-30],[17,-53],[38,-84],[10,-40],[-3,-49],[-9,-39],[-13,-32],[-22,-20],[-34,-3],[-52,4],[-39,-6],[-123,-63],[-2,-13],[9,-80],[8,-22],[16,-11],[-14,-12],[-16,-10],[-12,-12],[-2,-18],[9,-33],[2,-16],[-2,-19],[3,-20],[11,-11],[14,-6],[17,-1],[80,13],[22,-2],[14,-7],[7,-6],[4,-10],[3,-18],[6,-18],[10,-11],[12,-11],[11,-14],[8,-51],[-37,-182],[-1,-37],[-5,-26],[-10,-25],[-18,-32],[-48,-60],[-3,-14],[2,-43],[-8,-60],[2,-31],[13,-17],[25,-1],[23,3],[18,-7],[9,-32],[0,-27],[-9,-107],[1,-13],[3,-15],[2,-19],[-6,-14],[-7,-13],[-4,-15],[4,-33],[14,-16],[24,-5],[29,-2],[29,9],[38,42],[24,9],[22,-14],[2,-30],[-10,-57],[10,-26],[22,-23],[27,-19],[28,-13],[13,-17],[-4,-28],[-8,-35],[-2,-60],[-25,-87],[-5,-109],[8,-35],[4,-11],[-69,3],[-17,-17],[-8,-45],[-10,-21],[-20,-7],[-21,-4],[-11,-14],[-10,-71],[-6,-23],[-18,-35],[-2,-14],[1,-11],[9,-19],[1,-12],[-10,-45],[-7,-21],[-9,-19],[-57,-77],[-83,-111],[-40,-85],[-42,-89],[-33,-38],[-54,-10],[-60,8],[-56,-8],[-40,-57],[-14,-62],[-2,-61],[18,-159],[18,-153],[-8,-56],[-30,-65],[-65,-89],[-98,-138],[7,-13],[18,-12],[12,-17],[5,-1],[6,0],[4,-3],[0,-10],[-5,-9],[-5,-8],[-2,-6],[94,-172],[1,-7],[2,-6],[-50,34]],[[5762,2024],[20,-70],[9,-21],[58,-73],[6,-5],[10,-5],[31,-19],[5,-5],[1,-4],[-3,-3],[0,-8],[3,-13],[13,-31],[7,-13],[6,-7],[11,-2],[7,-3],[13,-9],[5,-7],[2,-11],[2,-40],[3,-10],[3,-4],[5,-1],[7,-1],[10,-5],[4,-6],[3,-7],[0,-8],[2,-10],[4,-5],[5,-4],[8,-3],[3,-3],[3,-4],[19,-50],[7,-13],[6,-9],[6,-4],[13,-7],[11,-9],[5,-5],[5,-5],[15,-9],[6,-5],[14,-15],[21,-40],[6,-7],[14,-9],[23,-23],[10,-19],[8,-10],[7,-7],[8,-3],[10,-7],[5,-8],[6,-13],[5,-4],[5,-1],[3,1],[2,0],[5,1],[5,-1],[17,-5],[8,-2],[17,0],[11,-5],[5,-9],[5,-20],[5,-6],[7,-4],[11,-3],[26,-16],[9,-4],[11,-3],[6,-5],[4,-6],[2,-7],[1,-9],[-3,-19],[4,-6],[6,-5],[8,-2],[103,-14],[15,-5],[7,-1],[6,1],[7,4],[6,2],[8,1],[30,-16],[31,-24],[28,-14],[10,-10],[39,-16],[26,-2],[10,-4],[4,-5],[0,-7],[-8,-11],[-2,-6],[1,-5],[11,-17],[26,-31],[7,-10],[10,-20],[6,-2],[7,-1],[7,1],[7,-2],[16,-10],[29,-13],[8,0],[7,2],[13,6],[8,-2],[5,-5],[0,-7],[-1,-8],[-1,-9],[5,-6],[18,-9],[4,-7],[2,-9],[-1,-8],[1,-8],[9,-32],[4,-10],[5,-7],[32,-20],[8,-8],[5,-9],[4,-9],[58,-79],[2,-6],[-2,-17],[1,-10],[9,-16],[9,-9],[10,-6],[13,-7],[3,-7],[1,-7],[-3,-7],[0,-8],[0,-14],[-2,-4],[-1,-2],[-1,-1],[-1,-2],[-3,-4],[-1,-7],[3,-4],[7,-1],[8,0],[10,-3],[12,-5],[18,-13],[9,-9],[8,-13],[7,-9],[14,-11],[6,-10],[7,-21],[5,-5],[7,-2],[17,3],[12,-1],[5,-4],[4,-8],[0,-17],[2,-7],[4,-8],[11,-14],[4,-7],[2,-8],[3,-20],[5,-14],[6,-7],[6,-5],[20,-4],[13,-5],[6,-6],[2,-7],[-2,-17],[5,-4],[7,-1],[16,1],[9,-1],[10,-4],[5,-7],[1,-8],[1,-11],[3,-11],[9,-18],[4,-10],[1,-18],[2,-10],[15,-23],[3,-9],[3,-19],[3,-10],[7,-6],[9,-4],[8,-1],[7,1],[8,-1],[5,-7],[18,-37]],[[7512,92],[-12,-3],[-22,5],[-44,21],[-139,35],[-77,9],[-37,11],[-42,-18],[-142,-13],[-25,-8],[-34,-26],[-20,-7],[-8,3],[-20,15],[-12,3],[-37,0],[-82,10],[-25,-1],[-24,-7],[-48,-23],[-24,-9],[-52,-10],[-48,-1],[-44,11],[-40,30],[-18,18],[-19,12],[-24,8],[-31,3],[-57,-9],[-22,2],[-31,17],[-36,28],[-19,9],[-38,5],[-3,4],[-2,7],[-24,28],[-13,7],[-28,7],[-12,5],[-35,23],[-19,6],[-46,5],[-31,17],[-44,11],[-74,53],[-26,8],[-86,-5],[-15,-6],[-24,17],[-26,-4],[-47,-23],[-43,5],[-25,-6],[-11,-25],[-13,-14],[-32,3],[-64,17],[-27,-7],[-46,-28],[-28,-16],[-19,1],[-8,2],[-9,7],[-97,90],[-8,14],[-10,29],[-7,13],[-13,17],[-6,5],[-121,-4],[-47,9],[-48,27],[-38,44],[-24,56],[-10,63],[5,62],[14,32],[21,35],[16,35],[-3,28],[-22,18],[-51,17],[-22,17],[-37,9],[-78,5],[-39,11],[-187,95],[-59,20],[-49,42],[-18,6],[-38,4],[-41,12],[-14,-3],[-14,-7],[-44,-13],[-21,-8],[-21,-4],[-28,6],[-104,58],[-51,19],[-47,10],[-152,3],[-96,20],[-74,5],[-21,7],[-18,13],[-15,18],[-8,14],[-11,30],[-7,12],[-8,5],[-8,-1],[-6,1],[-5,15],[7,19],[-5,8],[-10,4],[-4,2],[3,14],[10,18],[4,12],[-4,15],[-38,84],[-21,24],[-8,14],[-2,11],[1,23],[-3,12],[-12,21],[-46,59],[-33,56],[-38,51],[-88,70],[-20,23],[-44,65],[-10,28],[0,26],[16,78],[1,31],[-9,83],[-1,14],[4,33],[-1,17],[2,20],[7,13],[9,13],[7,16],[20,120],[-3,59],[-32,117],[10,53],[16,52],[4,58],[-10,26],[-18,19],[-23,10],[-51,1],[-45,22],[-73,-14],[-102,14],[-145,1],[-44,-6],[-48,-28],[20,213],[-23,173],[-66,182],[-16,26],[-23,16],[-27,8],[-27,5],[-15,1],[-13,-2],[-13,1],[-16,7],[-14,12],[-22,34],[-11,15],[-43,25],[-41,5],[-43,0],[-46,9],[-145,80],[-47,10],[-31,0],[-25,6],[-22,12],[-19,22],[-18,27],[-10,11]],[[4193,3195],[22,-28],[6,-1],[7,1],[41,37],[5,1],[6,-2],[9,-5],[15,-4],[17,-9]],[[9188,1632],[-45,-55],[-62,-75],[-67,-80],[-67,-80],[-67,-80],[-67,-80],[-67,-80],[-67,-81],[-67,-80],[-67,-80],[-67,-80],[-67,-80],[-67,-80],[-67,-81],[-67,-80],[-67,-80],[-67,-80],[-67,-80],[-66,-80],[-84,-63],[-102,-77],[-29,47],[-13,11],[-13,1],[-20,-10],[-11,-1],[-10,4],[-20,14],[-9,3],[-9,7],[-26,34],[-14,9],[-19,-3],[-42,-22],[-10,-2]],[[3583,8155],[117,56],[126,117],[86,62],[464,203],[61,12],[36,0],[18,3],[10,11],[26,27],[10,26],[51,42],[17,23],[9,53],[5,12],[31,57],[-1,33],[-21,62],[-5,26],[5,31],[30,79],[-21,42],[6,59],[17,61],[7,51],[-3,7],[-6,8],[-6,11],[-2,13],[3,16],[18,30],[4,11],[28,39],[43,47],[18,9],[6,3],[27,6],[58,1],[31,6],[16,18],[28,57],[12,16],[21,20],[25,18],[25,7],[12,8],[45,53],[282,145],[19,5],[111,11],[23,16],[47,45],[31,10],[27,-4],[25,-8],[26,-4],[31,5],[73,27],[55,10],[53,27],[29,7],[57,-6],[110,-33],[55,-7],[50,9],[51,16],[51,8],[51,-13],[33,-25],[15,-6],[20,6],[10,7],[20,19],[11,4],[32,-5],[21,-10]]],
transform:{scale:[.0007824180856085748,.0006783210059005959],translate:[25.219369751000045,-22.39733978299992]}},m.prototype.latLngToXY=function(a,b){return this.projection([b,a])},m.prototype.addLayer=function(a,b,c){var d;return d=c?this.svg.insert("g",":first-child"):this.svg.append("g"),d.attr("id",b||"").attr("class",a||"")},m.prototype.updateChoropleth=function(a){var b=this.svg;for(var c in a)if(a.hasOwnProperty(c)){var d,e=a[c];if(!c)continue;if(d="string"==typeof e?e:"string"==typeof e.color?e.color:this.options.fills[e.fillKey],e===Object(e)){this.options.data[c]=l(e,this.options.data[c]||{});this.svg.select("."+c).attr("data-info",JSON.stringify(this.options.data[c]))}b.selectAll("."+c).transition().style("fill",d)}},m.prototype.updatePopup=function(a,b,c){var d=this;a.on("mousemove",null),a.on("mousemove",function(){var e=n.mouse(d.options.element);n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("top",e[1]+30+"px").html(function(){var d=JSON.parse(a.attr("data-info"));try{return c.popupTemplate(b,d)}catch(e){return""}}).style("left",e[0]+"px")}),n.select(d.svg[0][0].parentNode).select(".datamaps-hoverover").style("display","block")},m.prototype.addPlugin=function(a,b){var c=this;"undefined"==typeof m.prototype[a]&&(m.prototype[a]=function(d,e,f,g){var h;"undefined"==typeof g&&(g=!1),"function"==typeof e&&(f=e,e=void 0),e=l(e||{},c.options[a+"Config"]),!g&&this.options[a+"Layer"]?(h=this.options[a+"Layer"],e=e||this.options[a+"Options"]):(h=this.addLayer(a),this.options[a+"Layer"]=h,this.options[a+"Options"]=e),b.apply(this,[h,d,e]),f&&f(h)})},"object"==typeof exports?(n=require("d3"),o=require("topojson"),module.exports=m):"function"==typeof define&&define.amd?define("datamaps",["require","d3","topojson"],function(a){return n=a("d3"),o=a("topojson"),m}):window.Datamap=window.Datamaps=m,window.jQuery&&(window.jQuery.fn.datamaps=function(a,b){a=a||{},a.element=this[0];var c=new m(a);return"function"==typeof b&&b(c,a),this})}();