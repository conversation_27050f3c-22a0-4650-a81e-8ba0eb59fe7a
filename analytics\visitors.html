<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Transform any data into visual insights and actionable intelligence with intuitive charts.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Analytics | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>

  <link rel="stylesheet" href="../assets/vendor/apexcharts/dist/apexcharts.css">
  <style type="text/css">
    .apexcharts-tooltip.apexcharts-theme-light
    {
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  </style>
</head>

<body class="bg-gray-50 dark:bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="lg:ms-65 lg:hidden fixed top-0 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 bg-white border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
    <div class="flex justify-between xl:grid xl:grid-cols-3 basis-full items-center w-full py-2.5 px-2 sm:px-5">
      <!-- Logo & Sidebar Toggle -->
      <div class="flex items-center gap-x-3">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
          <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->

        <div class="lg:hidden">
          <!-- Sidebar Toggle -->
          <button type="button" class="w-7 h-9.5 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13" />
            </svg>
          </button>
          <!-- End Sidebar Toggle -->
        </div>

        <div class="lg:hidden">
          <!-- Templates Dropdown -->
          <div class="hs-dropdown  relative  [--scope:window] [--auto-close:inside] inline-flex">
            <button id="hs-dropdown-preview-navbar" type="button" class="hs-dropdown-toggle  group relative flex justify-center items-center size-8 text-xs rounded-md text-gray-800 hover:bg-gray-100 focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <span class="">
                <svg class=" size-4 shrink-0" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </span>

              <span class="absolute -top-0.5 -end-0.5">
                <span class="relative flex">
                  <span class="animate-ping absolute inline-flex size-full rounded-full bg-red-400 dark:bg-red-600 opacity-75"></span>
                  <span class="relative inline-flex size-2 bg-red-500 rounded-full"></span>
                  <span class="sr-only">Notification</span>
                </span>
              </span>
            </button>

            <!-- Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-full min-w-90 md:w-125 transition-[opacity,margin] duration opacity-0 hidden z-61 overflow-hidden border border-gray-200 bg-white rounded-xl shadow-xl dark:bg-neutral-800 dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-preview-navbar">
              <!-- Tab -->
              <div class="p-3 pb-0 flex flex-wrap justify-between items-center gap-3 border-b border-gray-200 dark:border-neutral-700">
                <!-- Nav Tab -->
                <nav class="flex gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pmn-item-pro" aria-selected="true" data-hs-tab="#hs-pmn-pro" aria-controls="hs-pmn-pro" role="tab">
                    Pro
                  </button>
                  <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pmn-item-free" aria-selected="false" data-hs-tab="#hs-pmn-free" aria-controls="hs-pmn-free" role="tab">
                    Free
                  </button>
                </nav>
                <!-- End Nav Tab -->

                <!-- Switch/Toggle -->
                <div class="mb-2 flex items-center gap-x-0.5">
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:hidden flex shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="dark">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                    </svg>
                    Switch to Dark
                  </button>
                  <button type="button" class="hs-dark-mode hs-dark-mode-active:flex hidden shrink-0 justify-center items-center gap-x-1 text-xs text-gray-500 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200" data-hs-theme-click-value="light">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="4"></circle>
                      <path d="M12 2v2"></path>
                      <path d="M12 20v2"></path>
                      <path d="m4.93 4.93 1.41 1.41"></path>
                      <path d="m17.66 17.66 1.41 1.41"></path>
                      <path d="M2 12h2"></path>
                      <path d="M20 12h2"></path>
                      <path d="m6.34 17.66-1.41 1.41"></path>
                      <path d="m19.07 4.93-1.41 1.41"></path>
                    </svg>
                    Switch to Light
                  </button>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <!-- End Tab -->

              <!-- Tab Content -->
              <div id="hs-pmn-pro" class="" role="tabpanel" aria-labelledby="hs-pmn-item-pro">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (21)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../pro/pricing.html">
                      Purchase
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/dashboard/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Dashboard
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img55.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img55.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img58.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img58.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/video-call/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img61.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img61.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Video Call
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/startup/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img32.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img32.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Startup
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/payment/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img8.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img8.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Payment
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img16.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img16.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Chat
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img21.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img21.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/ecommerce/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        E-Commerce
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img46.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img46.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/crm/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img11.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img11.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CRM
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/workspace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img18.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img18.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Workspace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 bg-gray-100 dark:bg-neutral-700 transition" href="../../pro/analytics/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img9.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img9.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Analytics
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/calendars/day.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img14.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img14.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Calendars
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/smart-home/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img35.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img35.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Smart Home
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img52.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img52.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/inbox/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img26.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img26.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Inbox
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/project/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img10.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img10.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Project
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/shop-marketplace/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img29.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img29.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Shop Marketplace
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img49.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img49.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../pro/files/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews/img12.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/demo-previews-dark/img12.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Files
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                      Examples (780<!-- (780) -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                      Templates (21)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->

              <!-- Tab Content -->
              <div id="hs-pmn-free" class="hidden" role="tabpanel" aria-labelledby="hs-pmn-item-free">
                <!-- Header -->
                <div class="p-3 flex flex-wrap justify-between items-center gap-3">
                  <span class="block font-semibold text-sm text-gray-800 dark:text-neutral-200">Templates (5)</span>

                  <div class="ms-auto">
                    <a class="group py-2 px-2.5 rounded-md flex items-center gap-x-1 text-[13px] bg-gray-800 text-white hover:bg-gray-900 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 dark:text-neutral-800" href="../../templates.html">
                      Free download
                      <svg class="hidden md:inline-block shrink-0 size-3.5 group-hover:translate-x-0.5 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:group-focus:opacity-100 lg:group-focus:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:group-focus:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="px-3 max-h-64 sm:max-h-100 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                  <!-- Grid -->
                  <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/agency/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img1.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img1.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Agency
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/ai-chat/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img4.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img4.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        AI Chat
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/cms/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img5.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img5.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        CMS
                      </p>

                      <div class="absolute -top-px end-[3px]">
                        <span class="py-0.5 px-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full dark:bg-emerald-900 dark:text-emerald-500">New</span>
                      </div>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/coffee-shop/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img2.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img2.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Coffee Shop
                      </p>
                    </a>
                    <!-- End Link -->

                    <!-- Link -->
                    <a class="p-3 relative flex flex-col justify-center items-center gap-y-3 rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  transition" href="../../templates/personal/index.html">
                      <img class="dark:hidden w-28 rounded-lg shadow-xs" src="../assets/img/template-previews/img3.webp" alt="Main Page">
                      <img class="hidden dark:block w-28 rounded-lg shadow-xs" src="../assets/img/template-previews-dark/img3.webp" alt="Main Page">

                      <p class="text-sm text-center text-gray-800 dark:text-neutral-400">
                        Personal
                      </p>
                    </a>
                    <!-- End Link -->
                  </div>
                  <!-- End Grid -->
                </div>
                <!-- Body -->

                <!-- Footer -->
                <div class="p-3 flex flex-wrap justify-center items-center gap-0.5">
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../index.html">
                      Main page
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../examples.html">
                      Examples (220+<!-- 222 -->)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                  <div class="relative ps-2 ms-1 first:ps-0 first:ms-0 first:before:hidden before:hidden md:before:block before:absolute before:top-1/2 before:start-0 before:w-px before:h-4 before:bg-gray-200 before:-translate-y-1/2 dark:before:bg-neutral-700">
                    <a class="group flex items-center gap-x-1.5 py-1.5 px-2 rounded-md text-[13px] text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="../../templates.html">
                      Templates (5)
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
                <!-- End Footer -->
              </div>
              <!-- End Tab Content -->
            </div>
            <!-- End Dropdown -->
          </div>
          <!-- End Templates Dropdown -->
        </div>
      </div>
      <!-- End Logo & Sidebar Toggle -->

      <div class="lg:hidden h-9.5 flex justify-end items-center gap-x-2">
        <!-- Account Dropdown -->
        <div class="hs-dropdown inline-flex   [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
          <button id="hs-pro-dnad" type="button" class="inline-flex shrink-0 items-center gap-x-3 text-start rounded-full focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
            <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
          </button>

          <!-- Account Dropdown -->
          <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnad">
            <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
              <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                <div class="grow">
                  <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                    James Collison
                  </span>
                  <p class="text-xs text-gray-500 dark:text-neutral-500">
                    Preline@HS
                  </p>
                </div>
              </a>
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
                Billing
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                Settings
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                  <circle cx="12" cy="7" r="4" />
                </svg>
                My account
              </a>
            </div>
            <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
              <!-- Switch/Toggle -->
              <div class="flex flex-wrap justify-between items-center gap-2">
                <label for="hs-pro-dnaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                <label for="hs-pro-dnaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                  <input data-hs-theme-switch type="checkbox" id="hs-pro-dnaddm" class="peer sr-only">
                  <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                  <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                </label>
              </div>
              <!-- End Switch/Toggle -->
            </div>
            <div class="p-1">
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Customization
                <div class="ms-auto">
                  <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                    New
                  </span>
                </div>
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Manage team
              </a>
              <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                Sign out
              </a>
            </div>
            <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
              <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M8 12h8" />
                  <path d="M12 8v8" />
                </svg>
                Add team account
              </button>
            </div>
          </div>
          <!-- End Account Dropdown -->
        </div>
        <!-- End Account Dropdown -->
      </div>
    </div>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN SIDEBAR ========== -->
  <aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
    hs-overlay-open:translate-x-0
    -translate-x-full transition-all duration-300 transform
    w-82 h-full
    hidden
    fixed inset-y-0 start-0 z-60
    bg-white
    lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
    dark:bg-neutral-800" tabindex="-1" aria-label="Sidebar">
    <div class="h-full flex">
      <!-- Nav Sidebar -->
      <div class="w-16 flex flex-col h-full max-h-full">
        <div class="p-4 flex flex-col items-center">
          <!-- Logo -->
          <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/analytics/index.html" aria-label="Preline">
            <svg class="w-7 h-auto" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0835 3.23358C9.88316 3.23358 3.23548 9.8771 3.23548 18.0723V35.5832H0.583496V18.0723C0.583496 8.41337 8.41851 0.583252 18.0835 0.583252C27.7485 0.583252 35.5835 8.41337 35.5835 18.0723C35.5835 27.7312 27.7485 35.5614 18.0835 35.5614H16.7357V32.911H18.0835C26.2838 32.911 32.9315 26.2675 32.9315 18.0723C32.9315 9.8771 26.2838 3.23358 18.0835 3.23358Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0833 8.62162C12.8852 8.62162 8.62666 12.9245 8.62666 18.2879V35.5833H5.97468V18.2879C5.97468 11.5105 11.3713 5.97129 18.0833 5.97129C24.7954 5.97129 30.192 11.5105 30.192 18.2879C30.192 25.0653 24.7954 30.6045 18.0833 30.6045H16.7355V27.9542H18.0833C23.2815 27.9542 27.54 23.6513 27.54 18.2879C27.54 12.9245 23.2815 8.62162 18.0833 8.62162Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
              <path d="M24.8225 18.1012C24.8225 21.8208 21.8053 24.8361 18.0833 24.8361C14.3614 24.8361 11.3442 21.8208 11.3442 18.1012C11.3442 14.3815 14.3614 11.3662 18.0833 11.3662C21.8053 11.3662 24.8225 14.3815 24.8225 18.1012Z" class="fill-indigo-600 dark:fill-indigo-500" fill="currentColor" />
            </svg>
          </a>
          <!-- End Logo -->
        </div>

        <!-- Content -->
        <div class="h-full px-4 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <!-- Nav -->
          <ul class="flex flex-col items-center space-y-1">
            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                  <polyline points="9 22 9 12 15 12 15 22" />
                </svg>
                <span class="sr-only">Dashboard</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Dashboard
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
                <span class="sr-only">Users</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Users
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 9 5 12 1.8-5.2L21 14Z" />
                  <path d="M7.2 2.2 8 5.1" />
                  <path d="m5.1 8-2.9-.8" />
                  <path d="M14 4.1 12 6" />
                  <path d="m6 12-1.9 2" />
                </svg>
                <span class="sr-only">Events</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Events
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2" />
                  <rect width="18" height="18" x="3" y="4" rx="2" />
                  <circle cx="12" cy="10" r="2" />
                  <line x1="8" x2="8" y1="2" y2="4" />
                  <line x1="16" x2="16" y1="2" y2="4" />
                </svg>
                <span class="sr-only">Attributes</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Attributes
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4 text-indigo-600 dark:text-indigo-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <path d="m16 12-4-4-4 4" />
                  <path d="M12 16V8" />
                </svg>
                <span class="sr-only">Upgrade</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Upgrade
              </span>
            </li>
            <!-- End Item -->

            <!-- Item -->
            <li class="hs-tooltip [--placement:right] inline-block">
              <a class="hs-tooltip-toggle flex justify-center items-center gap-x-3 size-10 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 22v-5" />
                  <path d="M9 8V2" />
                  <path d="M15 8V2" />
                  <path d="M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z" />
                </svg>
                <span class="sr-only">Integrations</span>
              </a>
              <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg whitespace-nowrap dark:bg-neutral-700" role="tooltip">
                Integrations
              </span>
            </li>
            <!-- End Item -->
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Content -->

        <footer class="hidden lg:block text-center border-t border-gray-200 dark:border-neutral-700">
          <!-- Account Dropdown -->
          <div class="hs-dropdown  inline-flex  [--strategy:absolute] [--auto-close:inside] [--placement:bottom-right] relative text-start">
            <button id="hs-pro-dsad" type="button" class="w-full flex items-center gap-x-3 text-start py-4 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">
            </button>

            <!-- Account Dropdown -->
            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-20 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dsad">
              <div class="p-1 border-b border-gray-200 dark:border-neutral-800">
                <a class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../pro/dashboard/user-profile-my-profile.html">
                  <img class="shrink-0 size-8 rounded-full" src="https://images.unsplash.com/photo-*************-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80" alt="Avatar">

                  <div class="grow">
                    <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                      James Collison
                    </span>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                      Preline@HS
                    </p>
                  </div>
                </a>
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <line x1="2" x2="22" y1="10" y2="10" />
                  </svg>
                  Billing
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  Settings
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                  My account
                </a>
              </div>
              <div class="px-4 py-3.5 border-y border-gray-200 dark:border-neutral-800">
                <!-- Switch/Toggle -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                  <label for="hs-pro-dsaddm" class="flex-1 cursor-pointer text-sm text-gray-800 dark:text-neutral-300">Dark mode</label>
                  <label for="hs-pro-dsaddm" class="relative inline-block w-11 h-6 cursor-pointer">
                    <input data-hs-theme-switch type="checkbox" id="hs-pro-dsaddm" class="peer sr-only">
                    <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
                    <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
                  </label>
                </div>
                <!-- End Switch/Toggle -->
              </div>
              <div class="p-1">
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Customization
                  <div class="ms-auto">
                    <span class="ms-auto inline-flex items-center gap-1.5 py-px px-1.5 rounded-sm text-[10px] leading-4 font-medium bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-neutral-300">
                      New
                    </span>
                  </div>
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Manage team
                </a>
                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#">
                  Sign out
                </a>
              </div>
              <div class="p-1 border-t border-gray-200 dark:border-neutral-800">
                <button type="button" class="flex mt-0.5 gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" data-hs-overlay="#hs-pro-dasadam">
                  <svg class="shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M8 12h8" />
                    <path d="M12 8v8" />
                  </svg>
                  Add team account
                </button>
              </div>
            </div>
            <!-- End Account Dropdown -->
          </div>
          <!-- End Account Dropdown -->
        </footer>

        <!-- Sidebar Close -->
        <div class="lg:hidden absolute top-4 -end-6 z-10">
          <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-e-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="7 8 3 12 7 16" />
              <line x1="21" x2="11" y1="12" y2="12" />
              <line x1="21" x2="11" y1="6" y2="6" />
              <line x1="21" x2="11" y1="18" y2="18" />
            </svg>
          </button>
        </div>
        <!-- End Sidebar Close -->
      </div>
      <!-- End Nav Sidebar -->

      <!-- Secondary Sidebar -->
      <div class="w-72 bg-white border-x border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
        <div class="flex flex-col h-full max-h-full">
          <!-- Quick Actions -->
          <div class="py-3 px-2 border-b border-gray-200 dark:border-neutral-700">
            <button type="button" class="py-1.5 ps-3 pe-1.5 w-full inline-flex items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700/50">
              Quick actions
              <span class="ms-auto flex items-center gap-x-1 py-px px-1.5 border border-gray-200 rounded-md dark:border-neutral-700">
                <svg class="shrink-0 size-2.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
                </svg>
                <span class="text-[11px] uppercase">k</span>
              </span>
            </button>
          </div>
          <!-- End Quick Actions -->

          <!-- Content -->
          <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <!-- Nav -->
            <nav class="hs-accordion-group size-full flex flex-col justify-between" data-hs-accordion-always-open>
              <!-- Body -->
              <div>
                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/index.html">
                      Overview
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 bg-gray-100 focus:bg-gray-200 dark:bg-neutral-700 dark:focus:bg-neutral-600" href="../../pro/analytics/visitors.html">
                      Visitors
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/survey.html">
                      Survey
                      <div class="ms-auto">
                        <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-md text-[10px] leading-4 font-medium bg-white border border-gray-200 text-gray-800 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                          Beta
                        </span>
                      </div>
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/incidents.html">
                      Incidents
                    </a>
                  </li>
                  <!-- End Link -->

                  <!-- Link -->
                  <li>
                    <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 " href="../../pro/analytics/empty-states.html">
                      Empty Contents
                    </a>
                  </li>
                  <!-- End Link -->
                </ul>

                <ul class="p-2 flex flex-col gap-y-1">
                  <!-- Link -->
                  <li class="hs-accordion active" id="projects-accordion">
                    <button type="button" class="hs-accordion-toggle py-1 px-3 flex justify-center items-center gap-x-1 text-xs text-gray-500 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-500 dark:focus:bg-neutral-700" aria-expanded="true" aria-controls="projects-accordion-sub">
                      <svg class="hs-accordion-active:rotate-90 shrink-0 size-3 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                      Channels
                    </button>

                    <div id="projects-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="projects-accordion">
                      <ul class="hs-accordion-group mt-1 flex flex-col" data-hs-accordion-always-open>
                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/index.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />
                                <path d="m3.3 7 8.7 5 8.7-5" />
                                <path d="M12 22V12" />
                              </svg>
                            </span>
                            PRO
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/examples.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="18" height="7" x="3" y="3" rx="1" />
                                <rect width="9" height="7" x="3" y="14" rx="1" />
                                <rect width="5" height="7" x="16" y="14" rx="1" />
                              </svg>
                            </span>
                            Examples
                          </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        <li>
                          <a class="flex items-center gap-x-2 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="../../pro/templates.html">
                            <span class="flex justify-center items-center size-6 bg-indigo-600 text-white rounded-md dark:bg-indigo-500">
                              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="4" width="20" height="16" rx="2" />
                                <path d="M10 4v4" />
                                <path d="M2 8h20" />
                                <path d="M6 4v4" />
                              </svg>
                            </span>
                            Templates
                          </a>
                        </li>
                        <!-- End Link -->
                      </ul>
                    </div>
                  </li>
                  <!-- End Link -->
                </ul>
              </div>
              <!-- End Body -->

              <!-- Footer -->
              <footer class="p-2 pb-4 flex flex-col gap-y-3">
                <!-- Credits Card -->
                <a class="p-3 group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="flex justify-between items-center gap-x-1">
                    <div class="grow">
                      <p class="font-medium text-sm text-gray-800 dark:text-neutral-200">
                        65/100 credits used
                      </p>
                      <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                        Invite team members
                      </p>
                      <p class="mt-2 flex items-center gap-1 text-xs text-indigo-600 dark:text-indigo-500">
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
                        </svg>
                        Upgrade now
                        <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                          <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                        </svg>
                      </p>
                    </div>

                    <!-- Circular Progress -->
                    <div class="relative size-14">
                      <svg class="size-full -rotate-90" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-100 dark:text-indigo-500/40" stroke-width="4"></circle>
                        <!-- Progress Circle -->
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-indigo-600 dark:text-indigo-500" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="35" stroke-linecap="round"></circle>
                      </svg>
                    </div>
                    <!-- End Circular Progress -->
                  </div>
                </a>
                <!-- End Credits Card -->

                <!-- News Card -->
                <a class="group flex flex-col bg-white border border-gray-200 rounded-xl hover:border-gray-300 focus:outline-hidden focus:border-gray-300 transition duration-300 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:border-neutral-600 dark:focus:border-neutral-600" href="#">
                  <div class="p-2 w-full h-32 rounded-md">
                    <img class="size-full object-cover rounded-md dark:hidden" src="../assets/img/demo-previews/img32.webp" alt="Card Image">
                    <img class="size-full object-cover rounded-md dark:block hidden" src="../assets/img/demo-previews-dark/img32.webp" alt="Card Image">
                  </div>

                  <div class="p-3">
                    <div class="flex items-center gap-2">
                      <div class="grow">
                        <p class="text-wrap text-xs text-gray-500 dark:text-neutral-500">
                          New
                        </p>
                        <p class="text-wrap font-medium text-sm text-gray-800 group-hover:text-indigo-600 group-focus:text-indigo-600 dark:text-neutral-200 dark:group-hover:text-white dark:group-focus:text-white">
                          Preline Startup Demo
                        </p>
                      </div>

                      <svg class="shrink-0 size-3.5 text-indigo-600 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition duration-300 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path class="lg:opacity-0 lg:-translate-x-1 lg:group-hover:opacity-100 lg:group-hover:translate-x-0 lg:transition" d="M5 12h14" />
                        <path class="lg:-translate-x-1.5 lg:group-hover:translate-x-0 lg:transition" d="m12 5 7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </a>
                <!-- End News Card -->
              </footer>
              <!-- End Footer -->
            </nav>
            <!-- End Nav -->
          </div>
          <!-- End Content -->
        </div>
      </div>
      <!-- End Secondary Sidebar -->
    </div>
  </aside>
  <!-- ========== END MAIN SIDEBAR ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" class="lg:ps-82 pt-15 lg:pt-0">
    <div class="pt-3.5 pb-10 px-2 sm:px-5 flex flex-col gap-y-5">
      <!-- Header -->
      <div class="flex flex-wrap justify-between items-center gap-2">
        <div class="flex flex-wrap items-center gap-3">
          <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Visitors
          </h1>
          <div class="inline-flex items-center">
            <span class="shrink-0 size-2 inline-block bg-green-500 rounded-full me-1"></span>
            <span class="text-sm text-gray-600 dark:text-neutral-400">
              46 current visitors
            </span>
          </div>
        </div>

        <!-- Button Group -->
        <div class="flex flex-wrap items-center gap-3">
          <!-- Switch/Toggle -->
          <div class="flex flex-wrap justify-between items-center gap-2">
            <label for="hs-pro-avdchli" class="flex-1 cursor-pointer text-sm text-gray-500 dark:text-neutral-500">Display chart</label>
            <label for="hs-pro-avdchli" class="relative inline-block w-9 h-5 cursor-pointer">
              <input type="checkbox" id="hs-pro-avdchli" class="peer sr-only" checked>
              <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-indigo-600 dark:bg-neutral-700 dark:peer-checked:bg-indigo-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
              <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-4 bg-white rounded-full shadow-sm !transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
            </label>
          </div>
          <!-- End Switch/Toggle -->

          <!-- Calendar Dropdown -->
          <div class="hs-dropdown [--auto-close:inside] [--placement:top-right] inline-flex">
            <button id="hs-pro-dnic" type="button" class="py-1.5 px-2.5 inline-flex items-center text-[13px] rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
              <svg class="shrink-0 me-2 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                <line x1="16" x2="16" y1="2" y2="6" />
                <line x1="8" x2="8" y1="2" y2="6" />
                <line x1="3" x2="21" y1="10" y2="10" />
                <path d="M8 14h.01" />
                <path d="M12 14h.01" />
                <path d="M16 14h.01" />
                <path d="M8 18h.01" />
                <path d="M12 18h.01" />
                <path d="M16 18h.01" />
              </svg>
              Last 30 days
              <svg class="shrink-0 ms-1.5 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>

            <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-79.5 sm:w-159 transition-[opacity,margin] duration opacity-0 hidden z-50 bg-white rounded-xl shadow-xl dark:bg-neutral-900" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dnic">
              <!-- Calendar -->
              <div class="sm:flex">
                <!-- Calendar -->
                <div class="p-3 space-y-0.5">
                  <!-- Months -->
                  <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                    <!-- Prev Button -->
                    <div class="col-span-1">
                      <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="m15 18-6-6 6-6" />
                        </svg>
                      </button>
                    </div>
                    <!-- End Prev Button -->

                    <!-- Month / Year -->
                    <div class="col-span-3 flex justify-center items-center gap-x-1">
                      <div class="relative">
                        <select data-hs-select='{
                            "placeholder": "Select month",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                          <option value="0">January</option>
                          <option value="1">February</option>
                          <option value="2">March</option>
                          <option value="3">April</option>
                          <option value="4">May</option>
                          <option value="5">June</option>
                          <option value="6" selected>July</option>
                          <option value="7">August</option>
                          <option value="8">September</option>
                          <option value="9">October</option>
                          <option value="10">November</option>
                          <option value="11">December</option>
                        </select>
                      </div>

                      <span class="text-gray-800 dark:text-neutral-200">/</span>

                      <div class="relative">
                        <select data-hs-select='{
                            "placeholder": "Select year",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                          <option selected>2023</option>
                          <option>2024</option>
                          <option>2025</option>
                          <option>2026</option>
                          <option>2027</option>
                        </select>
                      </div>
                    </div>
                    <!-- End Month / Year -->

                    <!-- Next Button -->
                    <div class="col-span-1 flex justify-end">
                      <button type="button" class="opacity-0 pointer-events-none size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                        <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="m9 18 6-6-6-6" />
                        </svg>
                      </button>
                    </div>
                    <!-- End Next Button -->
                  </div>
                  <!-- Months -->

                  <!-- Weeks -->
                  <div class="flex pb-1.5">
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Mo
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Tu
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      We
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Th
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Fr
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Sa
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Su
                    </span>
                  </div>
                  <!-- Weeks -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                        26
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                        27
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                        28
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                        29
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200" disabled>
                        30
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        1
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        2
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        3
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        4
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        5
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        6
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        7
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        8
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        9
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        10
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        11
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        12
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        13
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        14
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        15
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        16
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        17
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        18
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        19
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        20
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        21
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        22
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        23
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        24
                      </button>
                    </div>
                    <div class="bg-gray-100 rounded-s-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                        25
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        26
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        27
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        28
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        29
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        30
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        31
                      </button>
                    </div>
                    <div class="bg-linear-to-r from-gray-100 dark:from-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        1
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        2
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        3
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        4
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        5
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        6
                      </button>
                    </div>
                  </div>
                  <!-- Days -->
                </div>
                <!-- End Calendar -->

                <!-- Calendar -->
                <div class="p-3 space-y-0.5">
                  <!-- Months -->
                  <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                    <!-- Prev Button -->
                    <div class="col-span-1">
                      <button type="button" class="opacity-0 pointer-events-none size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Previous">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="m15 18-6-6 6-6" />
                        </svg>
                      </button>
                    </div>
                    <!-- End Prev Button -->

                    <!-- Month / Year -->
                    <div class="col-span-3 flex justify-center items-center gap-x-1">
                      <div class="relative">
                        <select data-hs-select='{
                            "placeholder": "Select month",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-32 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                          <option value="0">January</option>
                          <option value="1">February</option>
                          <option value="2">March</option>
                          <option value="3">April</option>
                          <option value="4">May</option>
                          <option value="5">June</option>
                          <option value="6" selected>July</option>
                          <option value="7">August</option>
                          <option value="8">September</option>
                          <option value="9">October</option>
                          <option value="10">November</option>
                          <option value="11">December</option>
                        </select>
                      </div>

                      <span class="text-gray-800 dark:text-neutral-200">/</span>

                      <div class="relative">
                        <select data-hs-select='{
                            "placeholder": "Select year",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative flex text-nowrap w-full cursor-pointer text-start font-medium text-gray-800 hover:text-gray-600 focus:outline-hidden focus:text-gray-600 before:absolute before:inset-0 before:z-1 dark:text-neutral-200 dark:hover:text-neutral-300 dark:focus:text-neutral-300",
                            "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "p-2 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-gray-800 dark:text-neutral-200\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                          }' class="hidden">
                          <option selected>2023</option>
                          <option>2024</option>
                          <option>2025</option>
                          <option>2026</option>
                          <option>2027</option>
                        </select>
                      </div>
                    </div>
                    <!-- End Month / Year -->

                    <!-- Next Button -->
                    <div class="col-span-1 flex justify-end">
                      <button type="button" class="size-8 flex justify-center items-center text-gray-800 hover:bg-gray-100 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-label="Next">
                        <svg class="shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="m9 18 6-6-6-6" />
                        </svg>
                      </button>
                    </div>
                    <!-- End Next Button -->
                  </div>
                  <!-- Months -->

                  <!-- Weeks -->
                  <div class="flex pb-1.5">
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Mo
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Tu
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      We
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Th
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Fr
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Sa
                    </span>
                    <span class="m-px w-10 block text-center text-sm text-gray-500 dark:text-neutral-500">
                      Su
                    </span>
                  </div>
                  <!-- Weeks -->

                  <!-- Days -->
                  <div class="flex">
                    <div class="bg-linear-to-l from-gray-100 dark:from-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        31
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                        1
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                        2
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                        3
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700">
                        4
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        5
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        6
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        7
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        8
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        9
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        10
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        11
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        12
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        13
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        14
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        15
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        16
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        17
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        18
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        19
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        20
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        21
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        22
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        23
                      </button>
                    </div>
                    <div class="bg-gray-100 first:rounded-s-full last:rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        24
                      </button>
                    </div>
                    <div class="bg-gray-100 rounded-e-full dark:bg-neutral-800">
                      <button type="button" class="m-px size-10 flex justify-center items-center bg-indigo-600 border border-transparent text-sm font-medium text-white hover:border-indigo-600 rounded-full dark:bg-indigo-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hover:border-neutral-700">
                        25
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        26
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        27
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        28
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        29
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        30
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 rounded-full hover:border-indigo-600 hover:text-indigo-600 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:border-indigo-600 focus:text-indigo-600 dark:text-neutral-200">
                        31
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        1
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        2
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        3
                      </button>
                    </div>
                  </div>
                  <!-- Days -->

                  <!-- Days -->
                  <div class="flex">
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        4
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        5
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        6
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        7
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        8
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        9
                      </button>
                    </div>
                    <div>
                      <button type="button" class="m-px size-10 flex justify-center items-center border border-transparent text-sm text-gray-800 hover:border-indigo-600 hover:text-indigo-600 rounded-full disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:border-neutral-500 dark:focus:bg-neutral-700" disabled>
                        10
                      </button>
                    </div>
                  </div>
                  <!-- Days -->
                </div>
                <!-- End Calendar -->
              </div>
              <!-- End Calendar -->
            </div>
          </div>
          <!-- End Calendar Dropdown -->
        </div>
        <!-- End Button Group -->
      </div>
      <!-- End Header -->

      <!-- Card -->
      <div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
          <div class="flex flex-wrap items-center gap-2">
            <h2 class="font-medium text-gray-800 dark:text-neutral-200">
              Top sources
            </h2>
          </div>

          <div class="flex flex-wrap items-center gap-2">
            <button type="button" class="py-1.5 px-2 inline-flex items-center gap-1 text-xs rounded-full bg-white border border-dashed border-gray-300 text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
              Add filter
            </button>

            <button type="button" class="py-1.5 px-2 inline-flex items-center gap-1 text-xs rounded-full bg-white border border-dashed border-gray-300 text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
              Add comparison
            </button>
          </div>
        </div>
        <!-- End Header -->

        <div class="px-3 border-b border-gray-200 dark:border-neutral-700">
          <!-- Apex Bar Chart -->
          <div id="hs-pro-smpc" class="min-h-[215px] -ms-2"></div>
        </div>

        <!-- Table Content -->
        <div class="overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full">
              <thead>
                <tr>
                  <th scope="col" class="py-2 px-5 min-w-62.5 text-start">
                    <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                      Source
                    </span>
                  </th>

                  <th scope="col" class="py-2 px-5 min-w-48 text-end">
                    <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                      Visitors
                    </span>
                  </th>

                  <th scope="col" class="py-2 px-5 min-w-36 text-end">
                    <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                      Bounce Rate
                    </span>
                  </th>

                  <th scope="col" class="py-2 px-5 min-w-28 text-end">
                    <span class="font-medium text-xs uppercase text-gray-800 dark:text-neutral-200">
                      Visit Duration
                    </span>
                  </th>
                </tr>
              </thead>

              <tbody>
                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Direct / None
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        25.5k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        57%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        3m 12s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_4132_5805adfqfqdq121)">
                            <path d="M32.2566 16.36C32.2566 15.04 32.1567 14.08 31.9171 13.08H16.9166V19.02H25.7251C25.5454 20.5 24.5866 22.72 22.4494 24.22L22.4294 24.42L27.1633 28.1L27.4828 28.14C30.5189 25.34 32.2566 21.22 32.2566 16.36Z" fill="#4285F4" />
                            <path d="M16.9166 32C21.231 32 24.8463 30.58 27.5028 28.12L22.4694 24.2C21.1111 25.14 19.3135 25.8 16.9366 25.8C12.7021 25.8 9.12677 23 7.84844 19.16L7.66867 19.18L2.71513 23L2.65521 23.18C5.2718 28.4 10.6648 32 16.9166 32Z" fill="#34A853" />
                            <path d="M7.82845 19.16C7.48889 18.16 7.28915 17.1 7.28915 16C7.28915 14.9 7.48889 13.84 7.80848 12.84V12.62L2.81499 8.73999L2.6552 8.81999C1.55663 10.98 0.937439 13.42 0.937439 16C0.937439 18.58 1.55663 21.02 2.63522 23.18L7.82845 19.16Z" fill="#FBBC05" />
                            <path d="M16.9166 6.18C19.9127 6.18 21.9501 7.48 23.0886 8.56L27.6027 4.16C24.8263 1.58 21.231 0 16.9166 0C10.6648 0 5.27181 3.6 2.63525 8.82L7.80851 12.84C9.10681 8.98 12.6821 6.18 16.9166 6.18Z" fill="#EB4335" />
                          </g>
                          <defs>
                            <clipPath id="clip0_4132_5805adfqfqdq121">
                              <rect width="32" height="32" fill="white" transform="translate(0.937439)" />
                            </clipPath>
                          </defs>
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Google
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        20.5k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        51%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 45s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-black dark:text-white" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Github
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        7.3k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        52%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 18s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z" fill="#FF4500" />
                          <path d="M13.336 8C13.3376 8.1096 13.324 8.219 13.2952 8.3248C13.2664 8.4306 13.2228 8.532 13.1656 8.6256C13.1086 8.7192 13.0386 8.8044 12.9578 8.8786C12.877 8.9528 12.7862 9.0152 12.688 9.064C12.6902 9.0932 12.692 9.1226 12.693 9.152C12.6954 9.21064 12.6954 9.26936 12.693 9.328C12.692 9.3574 12.6902 9.3868 12.688 9.416C12.688 11.208 10.6 12.664 8.024 12.664C5.448 12.664 3.36 11.208 3.36 9.416C3.3578 9.3868 3.356 9.3574 3.355 9.328C3.35259 9.26936 3.35259 9.21064 3.355 9.152C3.356 9.1226 3.3578 9.0932 3.36 9.064C3.20615 8.99328 3.0695 8.88998 2.95952 8.76124C2.84953 8.6325 2.76883 8.4814 2.723 8.3184C2.67725 8.15532 2.6675 7.98425 2.69444 7.81703C2.72137 7.64982 2.78435 7.49046 2.879 7.35C2.97357 7.20957 3.09756 7.09139 3.24237 7.00367C3.38718 6.91594 3.54934 6.86078 3.7176 6.842C3.88588 6.82314 4.05626 6.84109 4.21691 6.89462C4.37757 6.94815 4.52466 7.03597 4.648 7.152C4.87697 6.99681 5.11686 6.85838 5.3658 6.7378C5.6148 6.6174 5.8722 6.515 6.136 6.4318C6.3998 6.3486 6.6694 6.2848 6.9424 6.2406C7.2154 6.1964 7.4914 6.1722 7.768 6.168L8.36 3.392C8.3666 3.3598 8.3796 3.3294 8.3982 3.3022C8.4166 3.2752 8.4404 3.252 8.4678 3.2342C8.4954 3.2164 8.5262 3.2042 8.5586 3.1982C8.5908 3.1924 8.624 3.193 8.656 3.2L10.616 3.592C10.8138 3.2522 11.229 3.1066 11.5956 3.2482C11.9622 3.3898 12.1718 3.7768 12.0898 4.1612C12.008 4.5456 11.6588 4.8136 11.2664 4.7934C10.8738 4.7732 10.554 4.4708 10.512 4.08L8.8 3.72L8.28 6.216C8.5532 6.2218 8.8258 6.2474 9.0954 6.2924C9.63529 6.38239 10.1594 6.54985 10.6514 6.7896C10.8972 6.9092 11.134 7.04635 11.36 7.2C11.5225 7.04396 11.7267 6.93831 11.948 6.8958C12.1693 6.85314 12.3981 6.87544 12.607 6.96C12.8159 7.04452 12.9959 7.18768 13.1252 7.3722C13.2546 7.55674 13.3278 7.77477 13.336 8ZM5.3968 9.1062C5.4372 9.2032 5.496 9.2914 5.5704 9.3656C5.6446 9.44 5.7328 9.4988 5.8298 9.5392C5.92689 9.57935 6.03093 9.60001 6.136 9.6C6.4596 9.6 6.7512 9.405 6.8752 9.1062C6.999 8.8072 6.9304 8.4632 6.7016 8.2344C6.4728 8.0056 6.1288 7.937 5.8298 8.0608C5.531 8.1848 5.336 8.4764 5.336 8.8C5.336 8.905 5.3566 9.009 5.3968 9.1062ZM9.9788 11.0266C10.0198 10.9866 10.0432 10.932 10.044 10.8748C10.0448 10.8174 10.0226 10.7622 9.9826 10.7212C9.9426 10.6802 9.888 10.6568 9.8308 10.656C9.7734 10.6552 9.7182 10.6774 9.672 10.712C9.5517 10.7983 9.42432 10.8742 9.2912 10.939C9.15808 11.0038 9.01972 11.0573 8.8776 11.0988C8.7356 11.1402 8.5902 11.1696 8.4432 11.1866C8.296 11.2036 8.1478 11.208 8 11.2C7.8524 11.2066 7.7044 11.2006 7.5578 11.1822C7.41116 11.164 7.26637 11.1332 7.125 11.0902C6.9834 11.0474 6.846 10.9926 6.7138 10.9266C6.5816 10.8606 6.4552 10.7834 6.336 10.696C6.2946 10.662 6.242 10.6446 6.1884 10.6472C6.1348 10.6498 6.0842 10.6724 6.0462 10.7102C6.0084 10.7482 5.9858 10.7988 5.9832 10.8524C5.9806 10.906 5.998 10.9586 6.032 11C6.1726 11.106 6.322 11.1996 6.4786 11.28C6.6352 11.3604 6.7986 11.427 6.9666 11.4794C7.1346 11.5318 7.3068 11.5696 7.4814 11.5926C7.656 11.6154 7.8322 11.6232 8.008 11.616C8.1838 11.6232 8.36 11.6154 8.5346 11.5926C8.8843 11.5466 9.2235 11.4409 9.5374 11.28C9.694 11.1996 9.8434 11.106 9.984 11L9.9788 11.0266ZM9.832 9.664C9.9404 9.6652 10.048 9.6442 10.1482 9.6026C10.2484 9.5608 10.339 9.4992 10.4146 9.4214C10.4902 9.3436 10.549 9.2512 10.5878 9.1498C10.6266 9.04852 10.6444 8.94038 10.64 8.832C10.64 8.5084 10.445 8.2168 10.1462 8.0928C9.8472 7.969 9.5032 8.0376 9.2744 8.2664C9.0456 8.4952 8.977 8.8392 9.1008 9.1382C9.2248 9.437 9.5164 9.632 9.84 9.632L9.832 9.664Z" fill="white" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Reddit
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        6.1k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        53%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 01s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8.00083 16C3.57651 16 0 12.4252 0 8.00083C0 3.57651 3.57651 0 8.00083 0C12.4252 0 16 3.57651 16 8.00083C16 12.4252 12.4252 16 8.00083 16Z" fill="#DE5833" />
                          <path d="M15.3475 8.00085C15.3475 9.00054 15.1605 9.96017 14.7734 10.8664C14.4012 11.7342 13.8805 12.5203 13.2012 13.2012C12.5337 13.8671 11.7476 14.4012 10.8664 14.7734C9.96017 15.1472 9.00054 15.3475 8.00085 15.3475C6.9995 15.3475 6.03986 15.1605 5.13364 14.7734C4.2658 14.4012 3.47973 13.8805 2.79881 13.2012C2.13291 12.5337 1.59885 11.7476 1.22668 10.8664C0.85284 9.96017 0.652569 8.98719 0.652569 8.00085C0.652569 6.9995 0.839489 6.03986 1.22668 5.13364C1.59885 4.2658 2.11956 3.47973 2.79881 2.79881C3.46638 2.13291 4.25244 1.59885 5.13364 1.22668C6.03986 0.85284 6.9995 0.652569 8.00085 0.652569C9.00054 0.652569 9.96017 0.839489 10.8664 1.22668C11.7342 1.59885 12.5203 2.11956 13.2012 2.79881C13.8671 3.46638 14.4012 4.25244 14.7734 5.13364C15.1605 6.03986 15.3475 6.9995 15.3475 8.00085ZM14.7867 7.9875C14.7867 4.22574 11.7476 1.18663 7.9875 1.18663C4.22574 1.18663 1.18663 4.23909 1.18663 7.9875C1.18663 10.9332 3.06584 13.4399 5.67938 14.3878C5.33391 12.9342 4.3459 8.77357 4.11893 6.44041C3.96038 4.7865 4.85326 3.89362 6.57392 3.66665C5.82624 3.42633 4.50612 3.49308 4.50612 3.49308C4.53282 3.03913 5.20039 2.94567 5.20039 2.94567C4.85326 2.77377 4.54618 2.68032 4.54618 2.68032C6.387 2.42664 7.54691 3.05249 8.03924 3.70671C10.0403 4.18569 10.3607 6.48046 10.3607 7.13301C10.3607 9.88006 8.05259 6.93274 8.05259 10.3741C8.05259 11.8678 9.17411 13.8805 9.59969 14.5998C12.5737 13.8805 14.7867 11.2002 14.7867 7.9875Z" fill="white" />
                          <path d="M7.62701 9.10735C7.62701 8.22616 8.82697 7.94745 9.28092 7.94745C10.5076 7.94745 12.2399 7.15971 12.6672 7.17307C13.1078 7.18642 13.3865 7.35999 13.3865 7.56026C13.3865 7.85399 10.9332 8.96049 9.98688 8.86703C9.08065 8.78692 8.86703 8.88038 8.86703 9.25422C8.86703 9.57298 9.51958 9.86671 10.2406 9.86671C11.3203 9.86671 12.3734 9.38606 12.6939 9.61304C12.9743 9.81331 11.9595 10.5343 10.7996 10.5343C9.63974 10.5343 7.62701 9.98688 7.62701 9.10735Z" fill="#FED30A" />
                          <path d="M9.7599 5.37396C9.43947 4.96007 8.86703 4.94672 8.66675 5.4257C8.97384 5.18704 9.34601 5.13364 9.7599 5.37396ZM6.18673 5.38731C5.74613 5.12029 5.01347 5.09358 5.05353 5.93305C5.2805 5.41235 5.57256 5.32056 6.18673 5.38731ZM9.93347 6.60062C9.93347 6.83928 9.7332 7.03955 9.49287 7.03955C9.25422 7.03955 9.05395 6.83928 9.05395 6.60062C9.05395 6.3603 9.25422 6.16003 9.49287 6.16003C9.7332 6.16003 9.93347 6.3603 9.93347 6.60062ZM9.7866 6.42706C9.7866 6.34695 9.7332 6.29354 9.65309 6.29354C9.57298 6.29354 9.51958 6.3603 9.51958 6.42706C9.51958 6.50716 9.58633 6.56057 9.65309 6.56057C9.71985 6.56057 9.77325 6.50716 9.7866 6.42706ZM6.58727 6.90604C6.58727 7.18642 6.3603 7.41339 6.07992 7.41339C5.79954 7.41339 5.57256 7.18642 5.57256 6.90604C5.57256 6.62733 5.79954 6.40035 6.07992 6.40035C6.3603 6.40035 6.58727 6.62733 6.58727 6.90604ZM6.4137 6.71912C6.4137 6.63901 6.34695 6.57392 6.26684 6.57392C6.18506 6.57392 6.11997 6.63901 6.11997 6.71912C6.11997 6.8009 6.18506 6.86598 6.26684 6.86598C6.34695 6.86598 6.4137 6.8009 6.4137 6.71912Z" fill="#2D4F8D" />
                          <path d="M4.35926 6.05322C4.8132 9.34601 5.83959 13.6402 6.05322 14.5213L5.69273 14.4012C5.34726 12.9476 4.35926 8.77357 4.13228 6.44041C4.04049 5.4257 4.33255 4.70639 4.97342 4.23909C4.57288 4.65299 4.23909 5.17369 4.35926 6.05322ZM4.51947 3.49308C4.51947 3.42633 4.54618 3.35957 4.57288 3.30616C4.86661 3.30616 5.95976 3.31951 6.65403 3.6533C6.65403 3.6533 6.61398 3.66665 6.56057 3.66665C5.79954 3.42633 4.50612 3.50643 4.51947 3.49308ZM5.17369 2.94567H5.20039C4.85326 2.77377 4.54618 2.68032 4.54618 2.68032C4.63963 2.66696 4.71974 2.66696 4.8132 2.65361C4.92001 2.70702 5.09358 2.79881 5.30721 2.94567H5.17369Z" fill="#D5D7D8" />
                          <path d="M10.7729 13.9606C10.5877 14.0273 9.69314 13.7069 9.28092 13.5467C9.26757 13.5734 9.25422 13.6001 9.22751 13.6135C9.04059 13.7336 8.51989 13.8004 8.23951 13.7336H8.22616C7.77388 13.9873 6.86598 14.4813 6.70743 14.3878C6.49381 14.2677 6.45376 12.6004 6.49381 12.1999C6.50716 11.8945 7.57361 12.3868 8.106 12.6405C8.30627 12.4268 9.16076 12.3467 9.20081 12.4803C9.21416 12.507 9.22751 12.5604 9.24087 12.6271C9.57298 12.3868 10.4542 11.7609 10.6795 11.8144C10.9599 11.8811 11.0133 13.8938 10.7729 13.9606Z" fill="#67BD47" />
                          <path d="M9.20081 13.6268C9.18746 13.6936 9.17411 13.7336 9.13406 13.7603C8.92043 13.8805 8.39973 13.9472 8.11935 13.8805C7.82729 13.8404 7.9341 13.5467 7.9341 12.8941C7.9341 12.8407 7.9875 12.7873 8.05259 12.7473C8.05259 13.3998 7.9608 13.6802 8.23951 13.7336C8.50654 13.8004 9.00054 13.747 9.20081 13.6268Z" fill="#43A347" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            DuckDuckGo
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        3.1k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        52%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 16s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-black dark:text-white" width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M14.176 0.000793457H16.9362L10.9061 6.77754L18 15.9992H12.4456L8.0951 10.4063L3.11724 15.9992H0.355428L6.80519 8.75067L0 0.000793457H5.69542L9.62784 5.1129L14.176 0.000793457ZM13.2074 14.3747H14.7367L4.86443 1.53991H3.22319L13.2074 14.3747Z" fill="currentColor" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            X (Twitter)
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2.5k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        59%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 13s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.35203 4.14767L6.98285 7.68111L9.38842 8.76618L0.705699 13.3333L4.26482 10.1659V1.1236L0.60083 0V13.4489L4.24342 16L13.3991 10.5425V6.61102L5.35203 4.14767Z" fill="#00897B" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Bing
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1.7k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        52%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 28s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M0.04 8C0.04 3.5816 3.6208 0 8.04 0C12.4576 0 16.04 3.5816 16.04 8C16.04 12.4184 12.4576 16 8.04 16C3.6208 16 0.04 12.4184 0.04 8Z" fill="#FC3F1D" />
                          <path d="M9.064 4.5328H8.3248C6.9696 4.5328 6.2568 5.2192 6.2568 6.2312C6.2568 7.3752 6.7496 7.9112 7.7616 8.5984L8.5976 9.1616L6.1952 12.7512H4.4L6.556 9.54C5.316 8.6512 4.62 7.788 4.62 6.328C4.62 4.4976 5.896 3.248 8.316 3.248H10.7184V12.7424H9.064V4.5328Z" fill="white" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Yandex
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1.7k
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        53%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        2m 42s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-black dark:text-white" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15.2709 6.77075C15.4526 6.23217 15.5157 5.66147 15.4559 5.09688C15.396 4.53229 15.2146 3.98683 14.9238 3.49702C14.4927 2.75612 13.8341 2.16954 13.0432 1.82192C12.2522 1.4743 11.3698 1.38361 10.5232 1.56294C10.1414 1.13833 9.67212 0.799092 9.14671 0.567919C8.6213 0.336746 8.05191 0.218975 7.47664 0.222488C6.61117 0.220437 5.76738 0.489603 5.06692 0.991187C4.36646 1.49277 3.8455 2.20087 3.57913 3.0134C3.01522 3.12725 2.48247 3.35866 2.01655 3.69215C1.55063 4.02564 1.16229 4.45349 0.877547 4.94708C0.443082 5.68594 0.257654 6.54187 0.348002 7.39143C0.438349 8.24099 0.799802 9.04029 1.38024 9.67407C1.19845 10.2127 1.13537 10.7835 1.19521 11.3481C1.25506 11.9128 1.43646 12.4583 1.72726 12.9482C2.15848 13.6891 2.81705 14.2756 3.60798 14.6232C4.39892 14.9708 5.28131 15.0615 6.12788 14.8823C6.50977 15.3068 6.97917 15.646 7.50464 15.8771C8.03012 16.1082 8.59957 16.2259 9.17487 16.2223C10.0408 16.2246 10.885 15.9553 11.5857 15.4534C12.2865 14.9515 12.8075 14.2429 13.0736 13.4298C13.6374 13.3159 14.1701 13.0845 14.636 12.751C15.1018 12.4175 15.4901 11.9897 15.7748 11.4961C16.2088 10.7573 16.3938 9.90166 16.3032 9.05242C16.2127 8.20318 15.8512 7.40424 15.2709 6.77075ZM9.17608 15.1775C8.46514 15.1785 7.77647 14.9329 7.23057 14.4835C7.2553 14.4703 7.29867 14.4471 7.32665 14.4299L10.5553 12.5898C10.6364 12.5443 10.7038 12.4783 10.7505 12.3986C10.7971 12.3188 10.8214 12.2282 10.8208 12.1362V7.64439L12.1854 8.42202C12.1925 8.42551 12.1987 8.43069 12.2034 8.43711C12.2081 8.44353 12.2111 8.45099 12.2121 8.45882V12.1786C12.2112 12.9731 11.891 13.7349 11.3218 14.2969C10.7527 14.859 9.98092 15.1755 9.17568 15.1771L9.17608 15.1775ZM2.64671 12.4254C2.2907 11.8183 2.1624 11.1071 2.28428 10.4161C2.30861 10.4305 2.34996 10.4557 2.38036 10.4725L5.60897 12.3126C5.68943 12.3589 5.78093 12.3834 5.8741 12.3834C5.96727 12.3834 6.05877 12.3589 6.13923 12.3126L10.0813 10.0669V11.6221C10.0818 11.6301 10.0803 11.638 10.0769 11.6452C10.0735 11.6524 10.0683 11.6586 10.0619 11.6633L6.79801 13.523C6.0998 13.9198 5.27058 14.0271 4.49231 13.8213C3.71403 13.6155 3.05029 13.1135 2.64671 12.4254ZM1.7974 5.4707C2.15203 4.86272 2.71217 4.39726 3.37967 4.15585C3.37967 4.18305 3.37805 4.23185 3.37805 4.26545V7.946C3.37752 8.03791 3.40181 8.12829 3.4484 8.20788C3.495 8.28746 3.56223 8.35336 3.64318 8.39882L7.58489 10.6445L6.22031 11.4221C6.21355 11.4266 6.20579 11.4292 6.19772 11.4299C6.18965 11.4306 6.18153 11.4293 6.1741 11.4261L2.90941 9.56526C2.21244 9.16699 1.70397 8.51224 1.49551 7.7446C1.28706 6.97696 1.39562 6.15948 1.7974 5.4707ZM13.0099 8.0456L9.06784 5.79951L10.4328 5.02228C10.4395 5.01797 10.4472 5.01535 10.4552 5.01466C10.4632 5.01396 10.4712 5.0152 10.4786 5.01828L13.7429 6.87756C14.243 7.16261 14.6504 7.58233 14.9175 8.08758C15.1846 8.59283 15.3003 9.1627 15.251 9.73047C15.2018 10.2982 14.9896 10.8404 14.6394 11.2935C14.2891 11.7466 13.8154 12.0919 13.2735 12.289V8.49842C13.2742 8.40666 13.2502 8.31637 13.2039 8.23679C13.1576 8.15722 13.0906 8.09123 13.0099 8.0456ZM14.368 6.02832C14.3441 6.01352 14.3024 5.98872 14.2724 5.97192L11.0434 4.13185C10.9629 4.08553 10.8714 4.06112 10.7782 4.06112C10.6851 4.06112 10.5936 4.08553 10.5131 4.13185L6.57139 6.37754V4.82227C6.57093 4.81435 6.57247 4.80645 6.57586 4.79926C6.57926 4.79207 6.58441 4.78582 6.59085 4.78107L9.85431 2.923C10.3544 2.63835 10.9263 2.50013 11.503 2.5245C12.0798 2.54886 12.6376 2.73482 13.1112 3.06059C13.5848 3.38637 13.9545 3.8385 14.1772 4.36407C14.3998 4.88964 14.4658 5.4669 14.368 6.02832ZM5.8291 8.80003L4.46412 8.0224C4.45701 8.01886 4.45089 8.01366 4.44629 8.00724C4.4417 8.00083 4.43877 7.99339 4.43777 7.9856V4.26585C4.43825 3.69622 4.60313 3.13851 4.91313 2.65797C5.22313 2.17744 5.66542 1.79396 6.18824 1.55241C6.71106 1.31086 7.29279 1.22122 7.86535 1.294C8.43792 1.36678 8.97764 1.59895 9.42135 1.96336C9.38911 1.98061 9.35721 1.99848 9.32567 2.01696L6.09666 3.85703C6.01571 3.90249 5.94848 3.96839 5.90188 4.04798C5.85529 4.12756 5.831 4.21795 5.83153 4.30985L5.8291 8.80003ZM6.57058 7.22277L8.32596 6.22233L10.0821 7.22237V9.22245L8.32636 10.2225L6.57058 9.22245V7.22277Z" fill="currentColor" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            chatgpt.com
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        854
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        50%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        4m 07s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-black dark:text-white" width="34" height="16" viewBox="0 0 34 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M34 7.46667H25.4669V16H34V7.46667Z" fill="currentColor" />
                          <path d="M0 16V8.48713L8.48696 0H15.9996V7.51283L7.51266 16H0Z" fill="currentColor" />
                          <path d="M12.7995 16V8.48715L21.2865 0H28.7991V7.51283L20.3122 16H12.7995Z" fill="currentColor" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Mobbin
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        690
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        62%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1m 32s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            ProductHunt
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        697
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        77%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        49s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M11.1987 1.62133L7.00044 5.84533H11.1987V1.62133ZM11.1987 1.62133V2.77778M6.99071 1V17M11.1987 10.0684L7.00044 5.84444M11.1987 10.0684V16.124L7.00044 11.9M11.1987 10.0684L7 5.84444M11.1987 10.0684L11.1982 11.8756H13V5.84444H7M7.00044 5.84444V11.9M7.00044 5.84444L2.80177 10.0684M7.00044 11.9L2.80177 16.124V10.0684M2.80177 10.0684L2.80133 11.8756H1V5.84444H7M2.80177 10.0684L7 5.84444M7 5.84533L2.80133 1.62133V5.84533H7Z" stroke="#20808D" stroke-miterlimit="10" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Perplexity
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        613
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        66%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1m 05s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_11766_122209asdasd)">
                            <path d="M3.11931 28.4817H8.21019V16.1181L0.937439 10.6636V26.3C0.937439 27.5054 1.91381 28.4819 3.11931 28.4819V28.4817Z" fill="#4285F4" />
                            <path d="M25.6647 28.4817H30.7556C31.961 28.4817 32.9374 27.5054 32.9374 26.2999V10.6636L25.6647 16.1181V28.4817Z" fill="#34A853" />
                            <path d="M25.6647 6.66356V16.1181L32.9374 10.6636V7.7545C32.9374 5.05812 29.8593 3.51812 27.701 5.13631L25.6647 6.66356Z" fill="#FBBC04" />
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21021 16.1181V6.66356L16.9375 13.2091L25.6647 6.66356V16.1181L16.9375 22.6636L8.21021 16.1181Z" fill="#EA4335" />
                            <path d="M0.937439 7.7545V10.6636L8.21019 16.1181V6.66356L6.17381 5.13631C4.01556 3.51813 0.937439 5.05813 0.937439 7.75438V7.7545Z" fill="#C5221F" />
                          </g>
                          <defs>
                            <clipPath id="clip0_11766_122209asdasd">
                              <rect width="32" height="32" fill="white" transform="translate(0.937439)" />
                            </clipPath>
                          </defs>
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Gmail
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        523
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        45%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        49s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M11.7438 0.940745C6.84695 1.30308 2.6841 1.63631 2.48837 1.67533C1.9396 1.77319 1.44038 2.14544 1.20563 2.63537L1 3.06646L1.01982 13.3407L1.04893 23.615L1.36234 24.2517C1.53886 24.6042 2.73365 26.2499 4.0362 27.9439C6.61221 31.2836 6.79802 31.47 7.77726 31.5679C8.06156 31.597 10.1966 31.4991 12.5081 31.3622C14.8295 31.2154 18.5508 30.99 20.7842 30.863C30.3233 30.2839 29.8334 30.3328 30.3815 29.8627C31.0672 29.2947 31.0183 30.2251 31.0474 17.7377C31.0672 7.15003 31.0573 6.45509 30.9006 6.13177C30.7148 5.76943 30.3815 5.51487 26.0329 2.45885C23.1243 0.421704 22.9186 0.313932 21.6155 0.294111C21.0772 0.274911 16.6307 0.568497 11.7438 0.940745ZM22.752 2.28232C23.1633 2.46814 26.1704 4.56412 26.6108 4.9661C26.7284 5.08378 26.7675 5.18164 26.7086 5.24048C26.5717 5.35817 7.96245 6.465 7.42421 6.38634C7.17956 6.34732 6.81722 6.20052 6.61159 6.06302C5.75932 5.48514 3.64413 3.75149 3.64413 3.62452C3.64413 3.29129 3.57538 3.29129 11.8714 2.69421C13.4582 2.58644 16.0633 2.39071 17.6502 2.26312C21.0871 1.98874 22.1159 1.99865 22.752 2.28232ZM28.6677 7.63996C28.8046 7.77685 28.9223 8.04132 28.9613 8.29589C28.9904 8.53125 29.0102 12.9189 28.9904 18.0313C28.9613 26.8067 28.9514 27.3555 28.7848 27.61C28.6869 27.7667 28.4912 27.9333 28.3438 27.9823C27.9331 28.1489 8.43318 29.2557 8.03183 29.138C7.84601 29.0891 7.59083 28.9324 7.45394 28.7955L7.21858 28.541L7.18947 19.0799C7.16965 12.4395 7.18947 9.5012 7.26813 9.23672C7.32697 9.041 7.47376 8.80564 7.60136 8.72759C7.77788 8.60991 8.93364 8.51205 12.9101 8.2773C15.7016 8.1206 20.0206 7.85613 22.4987 7.70933C28.3933 7.34638 28.3741 7.34638 28.6677 7.63996Z" class="fill-black dark:fill-neutral-200" fill="currentColor" />
                          <path d="M23.4277 10.8818C22.3698 10.9506 21.4296 11.0484 21.3218 11.1073C20.9985 11.2739 20.8028 11.5483 20.7638 11.8617C20.7347 12.185 20.8325 12.224 21.8898 12.3516L22.35 12.4104V16.5925C22.35 19.0799 22.311 20.7256 22.2621 20.6767C22.2131 20.6178 20.8226 18.5027 19.167 15.9756C17.512 13.4392 16.1407 11.3525 16.1209 11.3333C16.1011 11.3135 15.024 11.3724 13.7313 11.4609C12.1445 11.5687 11.273 11.6666 11.0965 11.7644C10.8122 11.9112 10.4988 12.4303 10.4988 12.7734C10.4988 12.979 10.871 13.0868 11.6545 13.0868H12.0658V25.1139L11.4 25.3196C10.8809 25.4763 10.7044 25.5741 10.6165 25.7698C10.4598 26.1031 10.4697 26.4066 10.6264 26.4066C10.6852 26.4066 11.792 26.3378 13.0649 26.2598C15.582 26.113 15.8657 26.0442 16.1302 25.5252C16.2088 25.3685 16.277 25.2019 16.277 25.1529C16.277 25.1139 15.9345 24.9962 15.5226 24.8984C15.1014 24.8005 14.6802 24.7027 14.5923 24.6828C14.4257 24.6339 14.4157 24.3304 14.4157 20.1186V15.6033L17.3931 20.2753C20.5173 25.1721 20.9093 25.7308 21.3893 25.9755C21.987 26.2889 23.5051 26.0733 24.2688 25.5741L24.5042 25.4273L24.524 18.7479L24.5531 12.0586L25.0722 11.9608C25.6891 11.8431 25.9734 11.5594 25.9734 11.0695C25.9734 10.7561 25.9536 10.7362 25.66 10.7462C25.4847 10.7542 24.4757 10.813 23.4277 10.8818Z" class="fill-black dark:fill-neutral-200" fill="currentColor" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Notion
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        500
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        41%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        48s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6.875 18C8.531 18 9.875 16.656 9.875 15V12H6.875C5.219 12 3.875 13.344 3.875 15C3.875 16.656 5.219 18 6.875 18Z" fill="#0ACF83" />
                          <path d="M3.875 9C3.875 7.344 5.219 6 6.875 6H9.875V12H6.875C5.219 12 3.875 10.656 3.875 9Z" fill="#A259FF" />
                          <path d="M3.875 3C3.875 1.344 5.219 0 6.875 0H9.875V6H6.875C5.219 6 3.875 4.656 3.875 3Z" fill="#F24E1E" />
                          <path d="M9.87501 0H12.875C14.531 0 15.875 1.344 15.875 3C15.875 4.656 14.531 6 12.875 6H9.87501V0Z" fill="#FF7262" />
                          <path d="M15.875 9C15.875 10.656 14.531 12 12.875 12C11.219 12 9.87501 10.656 9.87501 9C9.87501 7.344 11.219 6 12.875 6C14.531 6 15.875 7.344 15.875 9Z" fill="#1ABCFE" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Figma
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        387
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        29%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        31s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="17" height="22" viewBox="0 0 17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M5.91125 13.5019L11.7779 16.2307C12.4583 16.5413 12.9993 17.0966 13.2952 17.788C13.9952 19.438 13.0379 21.1245 11.5342 21.7314C10.0305 22.3382 8.4259 21.9468 7.69769 20.2318L5.14394 14.2095C4.94666 13.7429 5.44395 13.2837 5.91125 13.5019ZM6.26399 11.7365L12.3188 9.43297C14.3316 8.66758 16.5299 10.1159 16.5008 12.2195L16.499 12.302C16.4553 14.3516 14.318 15.7293 12.3497 15.0042L6.26945 12.7649C6.16331 12.7284 6.07107 12.6595 6.00553 12.5678C5.94 12.476 5.90441 12.366 5.90371 12.253C5.90301 12.1399 5.93723 12.0294 6.00163 11.9369C6.06603 11.8443 6.15832 11.7743 6.26399 11.7365ZM5.92398 10.939L11.877 8.39351C13.8552 7.54838 14.3571 5.01024 12.8079 3.54455L12.747 3.48589C11.2278 2.06787 8.71683 2.56743 7.85224 4.43643L5.18031 10.2084C4.96757 10.6686 5.44941 11.1425 5.92398 10.9399M4.39209 9.93253L6.55582 3.96345C6.8109 3.26695 6.79006 2.49813 6.49764 1.81671C5.79488 0.1677 3.89298 -0.36486 2.38836 0.242864C0.88648 0.852421 0.0364424 2.23194 0.767384 3.94604L3.33659 9.96094C3.5366 10.4275 4.22026 10.4092 4.393 9.93345" fill="#FFC233" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Lemon Squeezy
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        352
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        25%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        30s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Newsletter
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        320
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        23%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        49s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="22" height="17" viewBox="0 0 22 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18.3512 1.97644C20.7229 5.46326 21.8942 9.39631 21.4563 13.9241C21.4545 13.9433 21.4445 13.9609 21.4288 13.9724C19.6328 15.2912 17.8926 16.0915 16.1769 16.6222C16.1636 16.6263 16.1493 16.626 16.1361 16.6216C16.1228 16.6171 16.1113 16.6086 16.1032 16.5973C15.7068 16.0458 15.3467 15.4644 15.0311 14.8539C15.013 14.8179 15.0296 14.7746 15.0668 14.7604C15.6388 14.5449 16.1827 14.2866 16.7059 13.9808C16.7471 13.9567 16.7497 13.8976 16.7117 13.8693C16.6006 13.7869 16.4906 13.7002 16.3854 13.6136C16.3657 13.5976 16.3392 13.5945 16.3169 13.6052C12.9204 15.1736 9.19971 15.1736 5.76307 13.6052C5.74075 13.5952 5.71424 13.5987 5.69508 13.6144C5.59008 13.701 5.47983 13.7869 5.36984 13.8693C5.33178 13.8976 5.33493 13.9567 5.3764 13.9808C5.89957 14.2809 6.44346 14.5449 7.01466 14.7615C7.05168 14.7756 7.06926 14.8179 7.05089 14.8539C6.74219 15.4652 6.38204 16.0466 5.97832 16.5981C5.96073 16.6204 5.93185 16.6306 5.90455 16.6222C4.19699 16.0915 2.45688 15.2912 0.660862 13.9724C0.6459 13.9609 0.635137 13.9425 0.633562 13.9234C0.267638 10.0068 1.0134 6.04125 3.73578 1.97565C3.74234 1.96489 3.75232 1.95649 3.76387 1.9515C5.1034 1.33677 6.53849 0.884512 8.03841 0.626229C8.06571 0.622029 8.09301 0.634628 8.10719 0.658776C8.29251 0.98688 8.50435 1.40764 8.64767 1.75149C10.2287 1.51001 11.8344 1.51001 13.4485 1.75149C13.5919 1.41499 13.7963 0.98688 13.9809 0.658776C13.9874 0.646797 13.9976 0.637199 14.01 0.631354C14.0223 0.625509 14.0362 0.623715 14.0497 0.626229C15.5504 0.885299 16.9854 1.33756 18.3239 1.9515C18.3357 1.95649 18.3455 1.96489 18.3512 1.97644ZM9.45145 9.42308C9.46799 8.26527 8.62379 7.30721 7.56408 7.30721C6.51303 7.30721 5.67697 8.25687 5.67697 9.42308C5.67697 10.589 6.52956 11.5387 7.56408 11.5387C8.61539 11.5387 9.45145 10.589 9.45145 9.42308ZM16.4292 9.42308C16.4457 8.26527 15.6015 7.30721 14.5421 7.30721C13.4908 7.30721 12.6547 8.25687 12.6547 9.42308C12.6547 10.589 13.5073 11.5387 14.5421 11.5387C15.6015 11.5387 16.4292 10.589 16.4292 9.42308Z" fill="#5865F2" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Discord
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        345
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        24%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        33s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.08321 16.8093C12.9033 16.8093 16 13.7267 16 9.92405C16 6.12141 12.9033 3.03873 9.08321 3.03873C5.26316 3.03873 2.16637 6.12141 2.16637 9.92405C2.16637 13.7267 5.26316 16.8093 9.08321 16.8093Z" fill="black" />
                          <path d="M7.29813 15.3664C11.3159 15.3664 14.5963 12.1208 14.5963 8.08788C14.5963 4.055 11.3159 0.809326 7.29813 0.809326C3.2804 0.809326 0 4.055 0 8.08788C0 12.1208 3.2804 15.3664 7.29813 15.3664Z" fill="#FF90E8" />
                          <path d="M6.7492 11.76C4.70668 11.76 3.50523 10.0582 3.50523 7.94136C3.50523 5.74144 4.82681 3.95666 7.34985 3.95666C9.95306 3.95666 10.8341 5.783 10.8741 6.82062H8.99186C8.95185 6.23955 8.47123 5.36791 7.30984 5.36791C6.06837 5.36791 5.26737 6.4886 5.26737 7.85834C5.26737 9.22808 6.06837 10.3487 7.30984 10.3487C8.43122 10.3487 8.91179 9.43557 9.11204 8.52248H7.30984V7.77532H11.0914V11.5939H9.43243V9.18652C9.31225 10.0582 8.79162 11.76 6.7492 11.76Z" fill="black" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Gumroad
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        341
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        31%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1m 50s
                      </span>
                    </div>
                  </td>
                </tr>

                <tr class="odd:bg-gray-100 cursor-pointer dark:odd:bg-neutral-800">
                  <td class="size-px whitespace-nowrap">
                    <div class="py-2 px-5">
                      <div class="flex items-center gap-3">
                        <svg class="shrink-0 size-4" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58173 12.4183 0 8 0C3.58173 0 0 3.58173 0 8C0 12.4183 3.58173 16 8 16Z" fill="#34AADF" />
                          <path d="M3.3295 7.93062C3.3295 7.93062 7.3295 6.28902 8.71677 5.71098C9.24857 5.47978 11.052 4.73987 11.052 4.73987C11.052 4.73987 11.8844 4.41619 11.815 5.2023C11.7919 5.52602 11.6069 6.65894 11.422 7.88438C11.1445 9.6185 10.8439 11.5144 10.8439 11.5144C10.8439 11.5144 10.7977 12.0462 10.4046 12.1387C10.0116 12.2312 9.36416 11.815 9.24858 11.7225C9.15606 11.6532 7.51446 10.6127 6.91331 10.104C6.75146 9.96531 6.5665 9.68787 6.93642 9.36416C7.7688 8.60115 8.76301 7.65318 9.36416 7.05203C9.64163 6.77456 9.91907 6.12717 8.76301 6.91328C7.12141 8.04624 5.50291 9.10982 5.50291 9.10982C5.50291 9.10982 5.13296 9.34102 4.43933 9.13293C3.74566 8.92486 2.93642 8.64739 2.93642 8.64739C2.93642 8.64739 2.38154 8.30058 3.3295 7.93062Z" fill="white" />
                        </svg>
                        <div class="grow">
                          <span class="text-sm text-gray-600 dark:text-neutral-400">
                            Telegram
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        208
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        73%
                      </span>
                    </div>
                  </td>
                  <td class="size-px whitespace-nowrap text-end">
                    <div class="py-2 px-5">
                      <span class="text-sm text-gray-600 dark:text-neutral-400">
                        1m 22s
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- End Table -->
          </div>
        </div>
        <!-- End Table Content -->

        <!-- Footer -->
        <div class="py-3 px-5 border-t border-gray-200 dark:border-neutral-800">
          <!-- Footer -->
          <div class="grid grid-cols-2 items-center gap-y-2 sm:gap-y-0 sm:gap-x-5">
            <p class="text-sm text-gray-800 dark:text-neutral-200">
              <span class="font-medium">20</span>
              <span class="text-gray-500 dark:text-neutral-500">results</span>
            </p>

            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
              <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Previous">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg>
                <span class="sr-only">Previous</span>
              </button>
              <div class="flex items-center gap-x-1">
                <span class="min-h-9.5 min-w-9.5 flex justify-center items-center bg-gray-100 text-gray-800 py-2 px-3 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-white" aria-current="page">1</span>
                <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                <span class="min-h-9.5 flex justify-center items-center text-gray-500 py-2 px-1.5 text-sm dark:text-neutral-500">3</span>
              </div>
              <button type="button" class="min-h-9.5 min-w-9.5 py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700" aria-label="Next">
                <span class="sr-only">Next</span>
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </button>
            </nav>
            <!-- End Pagination -->
          </div>
          <!-- End Footer -->
        </div>
        <!-- End Footer -->
      </div>
      <!-- End Card -->
    </div>
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <!-- Required -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>
  <!-- Apexcharts -->
  <script src="../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../assets/vendor/apexcharts/dist/apexcharts.min.js"></script>
  <script src="../assets/vendor/preline/dist/helper-apexcharts.js"></script>

  <!-- JS INITIALIZATIONS -->
  <script>
    window.addEventListener('load', () => {
      // Apex Visitors Bar Chart
      (function () {
        const now = new Date();
        const categories = Array.from({ length: 120 }, (_, i) => {
          const time = new Date(now.getTime() - (119 - i) * 10 * 60 * 1000);
          return time.toISOString();
        });

        const data = Array.from({ length: 120 }, () => Math.floor(Math.random() * 4000) + 1000);

        buildChart('#hs-pro-smpc', (mode) => ({
          chart: {
            type: 'bar',
            height: 200,
            toolbar: { show: false },
            zoom: { enabled: false },
            events: {
              mounted: (chart) => fullBarHoverEffect(chart, {
                shadowClasses: "fill-gray-200 dark:fill-neutral-700"
              }),
              updated: (chart) => fullBarHoverEffect(chart, {
                shadowClasses: "fill-gray-200 dark:fill-neutral-700"
              })
            },
          },
          series: [{
            name: 'Total',
            data
          }],
          colors: ['#8b5cf6'],
          plotOptions: {
            bar: {
              horizontal: false,
              columnWidth: '4px',
              borderRadius: 0,
            }
          },
          legend: { show: false },
          dataLabels: { enabled: false },
          stroke: {
            show: true,
            width: 1,
            colors: ['transparent']
          },
          xaxis: {
            type: 'datetime',
            categories,
            labels: {
              format: 'dd. MMM',
              style: {
                colors: 'oklch(0.707 0.022 261.325)',
                fontSize: '13px',
                fontFamily: 'Inter, ui-sans-serif',
                fontWeight: 400
              }
            },
            axisBorder: { show: false },
            axisTicks: { show: false },
            crosshairs: { show: false }
          },
          yaxis: {
            labels: {
              align: 'left',
              minWidth: 0,
              maxWidth: 140,
              style: {
                colors: 'oklch(0.707 0.022 261.325)',
                fontSize: '13px',
                fontFamily: 'Inter, ui-sans-serif',
                fontWeight: 400
              },
              formatter: (value) => value >= 1000 ? `${Math.round(value / 1000)}k` : value
            }
          },
          tooltip: {
            shared: true,
            intersect: false,
            custom: function (props) {
              const { categories } = props.ctx.opts.xaxis;
              const { dataPointIndex } = props;

              return buildTooltip(props, {
                title: new Date(categories[dataPointIndex]).toLocaleString(),
                mode,
                hasCategory: false,
                hasTextLabel: true,
                wrapperExtClasses: 'min-w-36',
                isValueDivided: false,
                valuePrefix: '',
                labelDivider: ':',
                labelExtClasses: 'ms-2'
              });
            }
          },
          states: {
            hover: {
              filter: {
                type: 'darken',
                value: 0.9
              }
            }
          },
          responsive: [{
            breakpoint: 568,
            options: {
              chart: { height: 170 },
              plotOptions: {
                bar: { columnWidth: '2px' }
              },
              stroke: { width: 10 },
              yaxis: {
                labels: {
                  style: {
                    fontSize: '11px',
                    fontFamily: 'Inter, ui-sans-serif',
                    fontWeight: 400
                  },
                  formatter: (value) => value >= 1000 ? `${Math.round(value / 1000)}k` : value
                }
              }
            }
          }]
        }), {
          colors: ['oklch(0.511 0.262 276.966)'],
          grid: {
            borderColor: 'oklch(0.967 0.003 264.542)'
          }
        }, {
          colors: ['oklch(0.585 0.233 277.117)'],
          grid: {
            borderColor: 'oklch(0.371 0 0)'
          }
        });
      })();
    });
  </script>

</body>
</html>