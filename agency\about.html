<!DOCTYPE html>
<html lang="en" class="relative min-h-full">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Crafted for agencies and studios specializing in web design and development.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Agency | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Crafted for agencies and studios specializing in web design and development.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Agency | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Crafted for agencies and studios specializing in web design and development.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Agency | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link rel="stylesheet" href="../assets/css/main.min.css?v=3.0.1">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>
</head>

<body class="bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="sticky top-4 inset-x-0 flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full before:absolute before:inset-0 before:max-w-5xl before:mx-2 lg:before:mx-auto before:rounded-[26px] before:bg-neutral-800/30 before:backdrop-blur-md">
    <nav class="relative max-w-5xl w-full flex flex-wrap md:flex-nowrap basis-full items-center justify-between py-2 ps-5 pe-2 md:py-0 mx-2 lg:mx-auto">
      <div class="flex items-center">
        <!-- Logo -->
        <a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-hidden focus:opacity-80" href="../../pro/agency/index.html" aria-label="Preline">
          <svg class="w-28 h-auto" width="116" height="32" viewBox="0 0 116 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M33.5696 30.8182V11.3182H37.4474V13.7003H37.6229C37.7952 13.3187 38.0445 12.9309 38.3707 12.5369C38.7031 12.1368 39.134 11.8045 39.6634 11.5398C40.1989 11.2689 40.8636 11.1335 41.6577 11.1335C42.6918 11.1335 43.6458 11.4044 44.5199 11.946C45.3939 12.4815 46.0926 13.291 46.6158 14.3743C47.139 15.4515 47.4006 16.8026 47.4006 18.4276C47.4006 20.0095 47.1451 21.3452 46.6342 22.4347C46.1295 23.518 45.4401 24.3397 44.5661 24.8999C43.6982 25.4538 42.7256 25.7308 41.6484 25.7308C40.8852 25.7308 40.2358 25.6046 39.7003 25.3523C39.1709 25.0999 38.737 24.7829 38.3984 24.4013C38.0599 24.0135 37.8014 23.6226 37.6229 23.2287H37.5028V30.8182H33.5696ZM37.4197 18.4091C37.4197 19.2524 37.5367 19.9879 37.7706 20.6158C38.0045 21.2436 38.343 21.733 38.7862 22.0838C39.2294 22.4285 39.768 22.6009 40.402 22.6009C41.0421 22.6009 41.5838 22.4254 42.027 22.0746C42.4702 21.7176 42.8056 21.2251 43.0334 20.5973C43.2673 19.9633 43.3842 19.2339 43.3842 18.4091C43.3842 17.5904 43.2704 16.8703 43.0426 16.2486C42.8149 15.6269 42.4794 15.1406 42.0362 14.7898C41.593 14.4389 41.0483 14.2635 40.402 14.2635C39.7618 14.2635 39.2202 14.4328 38.777 14.7713C38.34 15.1098 38.0045 15.59 37.7706 16.2116C37.5367 16.8333 37.4197 17.5658 37.4197 18.4091ZM49.2427 25.5V11.3182H53.0559V13.7926H53.2037C53.4622 12.9124 53.8961 12.2476 54.5055 11.7983C55.1149 11.3428 55.8166 11.1151 56.6106 11.1151C56.8076 11.1151 57.02 11.1274 57.2477 11.152C57.4754 11.1766 57.6755 11.2105 57.8478 11.2536V14.7436C57.6632 14.6882 57.4077 14.639 57.0815 14.5959C56.7553 14.5528 56.4567 14.5312 56.1859 14.5312C55.6073 14.5312 55.0903 14.6574 54.6348 14.9098C54.1854 15.156 53.8284 15.5007 53.5638 15.9439C53.3052 16.3871 53.176 16.898 53.176 17.4766V25.5H49.2427ZM64.9043 25.777C63.4455 25.777 62.1898 25.4815 61.1373 24.8906C60.0909 24.2936 59.2845 23.4503 58.7182 22.3608C58.1519 21.2652 57.8688 19.9695 57.8688 18.4737C57.8688 17.0149 58.1519 15.7346 58.7182 14.6328C59.2845 13.531 60.0816 12.6723 61.1096 12.0568C62.1437 11.4413 63.3563 11.1335 64.7474 11.1335C65.683 11.1335 66.5539 11.2843 67.3603 11.5859C68.1728 11.8814 68.8806 12.3277 69.4839 12.9247C70.0932 13.5218 70.5672 14.2727 70.9057 15.1776C71.2443 16.0762 71.4135 17.1288 71.4135 18.3352V19.4155H59.4384V16.978H67.7111C67.7111 16.4117 67.588 15.91 67.3418 15.473C67.0956 15.036 66.754 14.6944 66.317 14.4482C65.8861 14.1958 65.3844 14.0696 64.812 14.0696C64.2149 14.0696 63.6856 14.2081 63.2239 14.4851C62.7684 14.7559 62.4114 15.1222 62.1529 15.5838C61.8944 16.0393 61.762 16.5471 61.7559 17.1072V19.4247C61.7559 20.1264 61.8851 20.7327 62.1437 21.2436C62.4083 21.7545 62.7807 22.1484 63.2608 22.4254C63.741 22.7024 64.3103 22.8409 64.9689 22.8409C65.406 22.8409 65.8061 22.7794 66.1692 22.6562C66.5324 22.5331 66.8432 22.3485 67.1018 22.1023C67.3603 21.8561 67.5572 21.5545 67.6927 21.1974L71.3304 21.4375C71.1458 22.3116 70.7672 23.0748 70.1948 23.7273C69.6285 24.3736 68.896 24.8783 67.9974 25.2415C67.1048 25.5985 66.0738 25.777 64.9043 25.777ZM77.1335 6.59091V25.5H73.2003V6.59091H77.1335ZM79.5043 25.5V11.3182H83.4375V25.5H79.5043ZM81.4801 9.49006C80.8954 9.49006 80.3937 9.29616 79.9752 8.90838C79.5628 8.51444 79.3566 8.04356 79.3566 7.49574C79.3566 6.95407 79.5628 6.48935 79.9752 6.10156C80.3937 5.70762 80.8954 5.51065 81.4801 5.51065C82.0649 5.51065 82.5635 5.70762 82.9759 6.10156C83.3944 6.48935 83.6037 6.95407 83.6037 7.49574C83.6037 8.04356 83.3944 8.51444 82.9759 8.90838C82.5635 9.29616 82.0649 9.49006 81.4801 9.49006ZM89.7415 17.3011V25.5H85.8083V11.3182H89.5569V13.8203H89.723C90.037 12.9955 90.5632 12.343 91.3019 11.8629C92.0405 11.3767 92.9361 11.1335 93.9887 11.1335C94.9735 11.1335 95.8322 11.349 96.5647 11.7798C97.2971 12.2107 97.8665 12.8262 98.2728 13.6264C98.679 14.4205 98.8821 15.3684 98.8821 16.4702V25.5H94.9489V17.1719C94.9551 16.304 94.7335 15.6269 94.2841 15.1406C93.8348 14.6482 93.2162 14.402 92.4283 14.402C91.8989 14.402 91.4311 14.5159 91.0249 14.7436C90.6248 14.9714 90.3109 15.3037 90.0831 15.7408C89.8615 16.1716 89.7477 16.6918 89.7415 17.3011ZM107.665 25.777C106.206 25.777 104.951 25.4815 103.898 24.8906C102.852 24.2936 102.045 23.4503 101.479 22.3608C100.913 21.2652 100.63 19.9695 100.63 18.4737C100.63 17.0149 100.913 15.7346 101.479 14.6328C102.045 13.531 102.842 12.6723 103.87 12.0568C104.905 11.4413 106.117 11.1335 107.508 11.1335C108.444 11.1335 109.315 11.2843 110.121 11.5859C110.934 11.8814 111.641 12.3277 112.245 12.9247C112.854 13.5218 113.328 14.2727 113.667 15.1776C114.005 16.0762 114.174 17.1288 114.174 18.3352V19.4155H102.199V16.978H110.472C110.472 16.4117 110.349 15.91 110.103 15.473C109.856 15.036 109.515 14.6944 109.078 14.4482C108.647 14.1958 108.145 14.0696 107.573 14.0696C106.976 14.0696 106.446 14.2081 105.985 14.4851C105.529 14.7559 105.172 15.1222 104.914 15.5838C104.655 16.0393 104.523 16.5471 104.517 17.1072V19.4247C104.517 20.1264 104.646 20.7327 104.905 21.2436C105.169 21.7545 105.542 22.1484 106.022 22.4254C106.502 22.7024 107.071 22.8409 107.73 22.8409C108.167 22.8409 108.567 22.7794 108.93 22.6562C109.293 22.5331 109.604 22.3485 109.863 22.1023C110.121 21.8561 110.318 21.5545 110.454 21.1974L114.091 21.4375C113.907 22.3116 113.528 23.0748 112.956 23.7273C112.389 24.3736 111.657 24.8783 110.758 25.2415C109.866 25.5985 108.835 25.777 107.665 25.777Z" class="fill-white" fill="currentColor" />
            <path d="M1 29.5V16.5C1 9.87258 6.37258 4.5 13 4.5C19.6274 4.5 25 9.87258 25 16.5C25 23.1274 19.6274 28.5 13 28.5H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <path d="M5 29.5V16.66C5 12.1534 8.58172 8.5 13 8.5C17.4183 8.5 21 12.1534 21 16.66C21 21.1666 17.4183 24.82 13 24.82H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <circle cx="13" cy="16.5214" r="5" class="fill-white" fill="currentColor" />
          </svg>
        </a>
        <!-- End Logo -->
      </div>

      <!-- Button Group -->
      <div class="md:order-3 flex items-center gap-x-3">
        <div class="md:ps-3">
          <a class="group inline-flex items-center gap-x-2 py-2 px-3 bg-[#ff0] font-medium text-sm text-nowrap text-neutral-800 rounded-full focus:outline-hidden" href="#">
            Request demo
          </a>
        </div>

        <div class="md:hidden">
          <button type="button" class="hs-collapse-toggle size-9 flex justify-center items-center text-sm font-semibold rounded-full bg-neutral-800 text-white disabled:opacity-50 disabled:pointer-events-none" id="hs-pro-an-collapse" aria-expanded="false" aria-controls="hs-pro-an" aria-label="Toggle navigation" data-hs-collapse="#hs-pro-an">
            <svg class="hs-collapse-open:hidden shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="3" x2="21" y1="6" y2="6" />
              <line x1="3" x2="21" y1="12" y2="12" />
              <line x1="3" x2="21" y1="18" y2="18" />
            </svg>
            <svg class="hs-collapse-open:block hidden shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>
      </div>
      <!-- End Button Group -->

      <!-- Collapse -->
      <div id="hs-pro-an" class="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow md:block" aria-labelledby="hs-pro-an-collapse">
        <!-- Nav -->
        <div class="overflow-hidden overflow-y-auto max-h-[75vh] [&::-webkit-scrollbar]:w-0">
          <div class="flex flex-col md:flex-row md:items-center md:justify-end gap-y-3 pt-6 pb-2 md:py-0 md:ps-7">
            <a class="md:px-3 md:py-4 text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" href="index.html" aria-current="page">Home</a>

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-ancpd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Company
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-ancpd">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="about.html">About</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="services.html">Services</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers.html">Careers</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers-job-details.html">Careers: Job Details</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="careers-apply.html">Careers: Apply</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anstd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Stories
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anstd">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="case-study.html">Case Study</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="case-study-details.html">Case Study Details</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anbld" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                News
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-50 hidden z-10 top-full rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anbld">
                <div class="p-5 flex flex-col gap-y-3 bg-neutral-900 rounded-xl">
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="news.html">News</a>
                  <a class="text-sm text-white hover:text-[#ff0] focus:outline-hidden focus:text-[#ff0]" href="news-details.html">News Details</a>
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->

            <!-- Dropdown Link -->
            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] [--auto-close:inside] md:inline-block">
              <!-- Link Button -->
              <button id="hs-pro-anpd" type="button" class="hs-dropdown-toggle md:px-3 md:py-4 w-full md:w-auto flex items-center text-sm text-white hover:text-neutral-300 focus:outline-hidden focus:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                Product
                <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 ms-auto md:ms-1 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </button>
              <!-- End Link Button -->

              <!-- Dropdown Menu -->
              <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] lg:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-150 hidden z-10 top-full end-0 rounded-2xl bg-neutral-800 p-1 before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden mt-2 md:mt-0" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-anpd">
                <div class="flex flex-col gap-y-1">
                  <!-- Grid -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-1">
                    <div class="p-5 min-h-50 flex flex-col justify-between bg-neutral-900 rounded-t-xl md:rounded-tr-none md:rounded-tl-xl">
                      <!-- Heading -->
                      <div class="mb-5">
                        <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                          Build
                          <span class="ms-auto size-6 flex shrink-0 justify-center items-center bg-[#ff0] text-black rounded-sm">
                            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M5 12h14"></path>
                              <path d="m12 5 7 7-7 7"></path>
                            </svg>
                          </span>
                        </a>
                      </div>
                      <!-- End Heading -->

                      <!-- List -->
                      <ul class="flex flex-col">
                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Websites
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Mobile apps
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Pages
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>
                      </ul>
                      <!-- End List -->
                    </div>
                    <!-- End Col -->

                    <div class="p-5 min-h-50 flex flex-col justify-between bg-neutral-900 md:rounded-tr-xl">
                      <!-- Heading -->
                      <div class="mb-5">
                        <a class="group flex items-center gap-x-3 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                          Resources
                          <span class="ms-auto size-6 flex shrink-0 justify-center items-center bg-[#ff0] text-black rounded-sm">
                            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M5 12h14"></path>
                              <path d="m12 5 7 7-7 7"></path>
                            </svg>
                          </span>
                        </a>
                      </div>
                      <!-- End Heading -->

                      <!-- List -->
                      <ul class="flex flex-col">
                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Documentation
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>

                        <li class="py-2 first:pt-0 last:pb-0 first:border-t-0 border-t border-neutral-800">
                          <a class="group flex items-center gap-x-2 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                            <span class="size-1 bg-[#ff0] rounded-full"></span>
                            Support
                            <span class="ms-auto size-6 flex shrink-0 justify-center items-center">
                              <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                            </span>
                          </a>
                        </li>
                      </ul>
                      <!-- End List -->
                    </div>
                    <!-- End Col -->
                  </div>
                  <!-- End Grid -->

                  <!-- Footer -->
                  <div class="p-2 bg-neutral-900 rounded-b-xl">
                    <div class="flex flex-wrap justify-between items-center gap-1">
                      <a class="py-1.5 ps-3 pe-2 group flex items-center gap-x-1 font-medium text-sm text-neutral-200 hover:text-[#ff0] focus:text-[#ff0] focus:outline-hidden" href="#">
                        Sessions 2025 &dash; Watch the product keynote live
                        <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M5 12h14"></path>
                          <path d="m12 5 7 7-7 7"></path>
                        </svg>
                      </a>

                      <a class="py-1.5 px-3 font-medium text-sm text-[#ff0] rounded-full hover:bg-neutral-800 focus:outline-hidden focus:bg-neutral-800" href="#">
                        Changelog
                      </a>
                    </div>
                  </div>
                  <!-- End Footer -->
                </div>
              </div>
              <!-- End Dropdown Menu -->
            </div>
            <!-- End Dropdown Link -->
          </div>
        </div>
        <!-- End Nav -->
      </div>
      <!-- End Collapse -->
    </nav>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">
    <!-- Hero -->
    <div class="relative bg-neutral-900 before:absolute before:inset-0 before:size-full before:bg-[radial-gradient([var(--color-neutral-700])_1px,transparent_1px)] before:bg-[size:20px_20px]">
      <div class="relative z-10 min-h-130 lg:min-h-[800px] flex flex-col justify-center">
        <div class="max-w-5xl mx-auto px-4 xl:px-0">
          <!-- Content -->
          <div class="max-w-lg lg:max-w-2xl mx-auto text-center">
            <p class="mb-4 font-semibold text-sm uppercase tracking-wider text-white">
              The Preline Story
            </p>

            <h1 class="font-bold text-neutral-800 text-4xl md:text-5xl lg:text-6xl leading-[1.1] text-white">
              Our mission is to make work <span class="text-[#ff0]">meaningful</span>
            </h1>
          </div>
          <!-- End Content -->

          <div class="block absolute top-[2%] start-[4%] sm:start-[20%] lg:start-[33%] -z-1 scale-50 md:scale-75 lg:scale-100">
            <!-- Card -->
            <div class="w-72">
              <img class="rounded-xl" src="https://images.unsplash.com/photo-1719937206097-ae8528124815?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero image">
            </div>
            <!-- End Card -->
          </div>

          <div class="absolute top-[25%] lg:top-[35%] -start-[5%] xl:start-[5%] -z-1 scale-50 md:scale-75 lg:scale-100">
            <!-- Card -->
            <div class="w-90">
              <img class="rounded-xl" src="https://images.unsplash.com/photo-1744836239470-f1cfcbb9d691?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero image">
            </div>
            <!-- End Card -->
          </div>

          <div class="absolute top-0 lg:top-[12%] end-[15%] xl:end-[30%] -z-1 scale-50 md:scale-75 lg:scale-100">
            <!-- Card -->
            <div class="w-40">
              <img class="rounded-xl" src="https://images.unsplash.com/photo-1744380623181-a675718f120c?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero image">
            </div>
            <!-- End Card -->
          </div>

          <div class="block absolute top-[40%] end-0 xl:end-[10%] -z-1 scale-50 md:scale-75 xl:scale-100">
            <!-- Card -->
            <div class="w-50">
              <img class="rounded-xl" src="https://images.unsplash.com/photo-1720048171256-38c59a19fd37?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero image">
            </div>
          </div>

          <div class="block absolute -bottom-[10%] lg:-bottom-[5%] end-[35%] xl:end-[40%] -z-1 scale-50 md:scale-75 xl:scale-100">
            <!-- Card -->
            <div class="w-96">
              <img class="rounded-xl" src="https://images.unsplash.com/photo-1744922679571-e6d7e9a8804c?q=80&w=480&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero image">
            </div>
            <!-- End Card -->
          </div>
        </div>
      </div>
    </div>
    <!-- End Hero -->

    <div class="pt-40 pb-14">
      <!-- Stats -->
      <div class="bg-neutral-900">
        <div class="max-w-3xl mx-auto px-4 xl:px-0">
          <div class="mb-20 text-center">
            <h2 class="text-3xl sm:text-4xl leading-[1.2] text-neutral-200">
              We cut through complexity, empowering businesses to challenge the status quo, create unlimited opportunities – and change the world.
            </h2>
          </div>

          <!-- Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-3 items-center gap-y-20 gap-x-12">
            <!-- Stats -->
            <div class="relative text-center first:before:hidden before:absolute before:-top-full sm:before:top-1/2 before:start-1/2 sm:before:-start-6 before:w-px before:h-20 before:bg-neutral-700 before:rotate-[60deg] sm:before:rotate-12 before:transform sm:before:-translate-y-1/2 before:-translate-x-1/2 sm:before:-translate-x-0 before:mt-3.5 sm:before:mt-0">
              <h3 class="text-[#ff0] text-4xl md:text-5xl">2,000+</h3>
              <p class="mt-5 text-sm sm:text-base text-neutral-200">Preline partners</p>
            </div>
            <!-- End Stats -->

            <!-- Stats -->
            <div class="relative text-center first:before:hidden before:absolute before:-top-full sm:before:top-1/2 before:start-1/2 sm:before:-start-6 before:w-px before:h-20 before:bg-neutral-700 before:rotate-[60deg] sm:before:rotate-12 before:transform sm:before:-translate-y-1/2 before:-translate-x-1/2 sm:before:-translate-x-0 before:mt-3.5 sm:before:mt-0">
              <h3 class="text-[#ff0] text-4xl md:text-5xl">85%</h3>
              <p class="mt-5 text-sm sm:text-base text-neutral-200">Happy customers</p>
            </div>
            <!-- End Stats -->

            <!-- Stats -->
            <div class="relative text-center first:before:hidden before:absolute before:-top-full sm:before:top-1/2 before:start-1/2 sm:before:-start-6 before:w-px before:h-20 before:bg-neutral-700 before:rotate-[60deg] sm:before:rotate-12 before:transform sm:before:-translate-y-1/2 before:-translate-x-1/2 sm:before:-translate-x-0 before:mt-3.5 sm:before:mt-0">
              <h3 class="text-[#ff0] text-4xl md:text-5xl">$55M+</h3>
              <p class="mt-5 text-sm sm:text-base text-neutral-200">Ads managed yearly</p>
            </div>
            <!-- End Stats -->
          </div>
          <!-- End Stats -->
        </div>
      </div>
      <!-- End Stats -->
    </div>

    <!-- Timeline -->
    <div class="py-10 lg:py-20">
      <div class="max-w-5xl px-4 xl:px-0 mx-auto">
        <!-- Title -->
        <div class="max-w-3xl mb-10 lg:mb-14">
          <h2 class="text-white font-semibold text-2xl md:text-4xl md:leading-tight">A brief history</h2>
          <p class="mt-1 text-neutral-400">Discover our journey through key milestones that highlight our growth, achievements, and commitment to excellence in delivering transformative solutions.</p>
        </div>
        <!-- End Title -->
      </div>

      <!-- Slider -->
      <div class="overflow-hidden bg-neutral-900">
        <div class="max-w-5xl px-4 xl:px-0 mx-auto">
          <!-- Slider -->
          <div data-hs-carousel='{
            "loadingClasses": "opacity-0",
            "slidesQty": {
              "xs": 1,
              "sm": 2,
              "lg": 2
            },
            "isDraggable": true
          }' class="relative">

            <div class="hs-carousel w-full min-h-57">
              <div class="hs-carousel-body flex flex-nowrap opacity-0 transition-transform duration-700 cursor-grab hs-carousel-dragging:transition-none hs-carousel-dragging:cursor-grabbing">
                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2020
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      Preline is born
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Initial concept for Preline begins, inspired by the need for a streamlined UI component library built&nbsp;on Tailwind CSS.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->

                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2021
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      First major release
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Launched our first comprehensive component library with over 100 pre-built components and templates.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->

                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2022
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      Enterprise expansion
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Expanded our services to enterprise clients, introducing advanced customization options and dedicated support.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->

                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2023
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      Global recognition
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Received industry recognition for innovation in UI/UX design and expanded our team to support growing demand.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->

                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2024
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      AI integration
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Launched AI-powered features to enhance user experience and streamline development workflows.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->

                <!-- Slide -->
                <div class="hs-carousel-slide">
                  <div class="pt-10 pe-10 relative before:absolute before:top-5 before:start-0 before:w-full before:border before:border-neutral-700 after:absolute after:top-4 after:start-0 after:size-2.5 after:bg-[#ff0] after:rounded-full">
                    <span class="block text-[#ff0] text-4xl">
                      2025
                    </span>
                    <h4 class="mt-2 font-semibold text-xl text-white">
                      Future vision
                    </h4>
                    <p class="mt-5 text-neutral-400">
                      Planning next-generation features and expanding our global presence to serve more clients worldwide.
                    </p>
                  </div>
                </div>
                <!-- End Slide -->
              </div>
            </div>

            <!-- Arrows -->
            <div class="flex justify-end items-center gap-2">
              <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                <span class="text-2xl" aria-hidden="true">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m12 19-7-7 7-7" />
                    <path d="M19 12H5" />
                  </svg>
                </span>
                <span class="sr-only">Previous</span>
              </button>
              <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:pointer-events-none inline-flex justify-center items-center w-12 h-8 border border-neutral-800 text-neutral-400 rounded-full hover:border-neutral-700 focus:outline-hidden focus:border-neutral-700">
                <span class="sr-only">Next</span>
                <span class="text-2xl" aria-hidden="true">
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14" />
                    <path d="m12 5 7 7-7 7" />
                  </svg>
                </span>
              </button>
            </div>
            <!-- End Arrows -->
          </div>
          <!-- End Slider -->
        </div>
      </div>
      <!-- End Slider -->
    </div>
    <!-- End Timeline -->

    <div class="w-full h-64 md:h-96 bg-neutral-800">
      <img class="size-full object-cover" src="https://images.unsplash.com/photo-1543269866-487350d6fa5e?q=80&w=1920&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero Image">
    </div>

    <!-- Icon Block -->
    <div class="bg-neutral-900">
      <div class="max-w-5xl px-4 xl:px-0 py-10 lg:py-20 mx-auto">
        <!-- Title -->
        <div class="max-w-3xl mb-10 lg:mb-14">
          <h2 class="text-white font-semibold text-2xl md:text-4xl md:leading-tight">Our values</h2>
          <p class="mt-1 text-neutral-400">Discover the core principles that drive our work and shape our culture, guiding us in delivering exceptional results for our clients.</p>
        </div>
        <!-- End Title -->

        <!-- Card Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-12 gap-x-6">
          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
              <path d="M20 3v4" />
              <path d="M22 5h-4" />
              <path d="M4 17v2" />
              <path d="M5 18H3" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Innovation
            </h4>

            <p class="mt-2 text-neutral-400">
              We embrace cutting-edge technology and creative solutions to deliver exceptional results that push boundaries and drive progress.
            </p>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 3h4v4" />
              <path d="M18.575 11.082a13 13 0 0 1 1.048 9.027 1.17 1.17 0 0 1-1.914.597L14 17" />
              <path d="M7 10 3.29 6.29a1.17 1.17 0 0 1 .6-1.91 13 13 0 0 1 9.03 1.05" />
              <path d="M7 14a1.7 1.7 0 0 0-1.207.5l-2.646 2.646A.5.5 0 0 0 3.5 18H5a1 1 0 0 1 1 1v1.5a.5.5 0 0 0 .854.354L9.5 18.207A1.7 1.7 0 0 0 10 17v-2a1 1 0 0 0-1-1z" />
              <path d="M9.707 14.293 21 3" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Excellence
            </h4>

            <p class="mt-2 text-neutral-400">
              We strive for excellence in everything we do, delivering high-quality solutions that exceed expectations and set new standards.
            </p>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15" />
              <path d="M11 12 5.12 2.2" />
              <path d="m13 12 5.88-9.8" />
              <path d="M8 7h8" />
              <circle cx="12" cy="17" r="5" />
              <path d="M12 18v-2h-.5" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Integrity
            </h4>

            <p class="mt-2 text-neutral-400">
              We conduct our business with honesty, transparency, and ethical practices, building trust with our clients and partners.
            </p>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15" />
              <path d="M11 12 5.12 2.2" />
              <path d="m13 12 5.88-9.8" />
              <path d="M8 7h8" />
              <circle cx="12" cy="17" r="5" />
              <path d="M12 18v-2h-.5" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Collaboration
            </h4>

            <p class="mt-2 text-neutral-400">
              We foster a culture of teamwork and partnership, working closely with clients and colleagues to achieve shared goals and create lasting impact.
            </p>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12.034 12.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z" />
              <path d="M5 3a2 2 0 0 0-2 2" />
              <path d="M19 3a2 2 0 0 1 2 2" />
              <path d="M5 21a2 2 0 0 1-2-2" />
              <path d="M9 3h1" />
              <path d="M9 21h2" />
              <path d="M14 3h1" />
              <path d="M3 9v1" />
              <path d="M21 9v2" />
              <path d="M3 14v1" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Sustainability
            </h4>

            <p class="mt-2 text-neutral-400">
              We are committed to sustainable practices and environmental responsibility, ensuring our solutions contribute positively to a better future.
            </p>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="flex flex-col">
            <svg class="size-8 mb-5 text-[#ff0] " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
              <path d="M6.376 18.91a6 6 0 0 1 11.249.003" />
              <circle cx="12" cy="11" r="4" />
            </svg>

            <h4 class="mt-5 font-semibold text-xl text-white">
              Customer Focus
            </h4>

            <p class="mt-2 text-neutral-400">
              We prioritize understanding and meeting our clients' needs, delivering personalized solutions that drive their success and growth.
            </p>
          </div>
          <!-- End Card -->
        </div>
        <!-- End Card Grid -->
      </div>
    </div>
    <!-- End Icon Block -->

    <!-- News -->
    <div class="bg-neutral-900 bg-linear-to-b from-black to-transparent">
      <div class="max-w-5xl px-4 xl:px-0 py-10 lg:py-20 mx-auto">
        <!-- Title -->
        <div class="max-w-3xl mb-10 lg:mb-14">
          <h2 class="text-white font-semibold text-2xl md:text-4xl md:leading-tight">Insights</h2>
          <p class="mt-1 text-neutral-400">Stay in the know with insights from industry experts.</p>
        </div>
        <!-- End Title -->

        <!-- Card Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-10 gap-x-5 lg:gap-y-16 lg:gap-x-10">
          <!-- Card -->
          <div class="relative group flex flex-col focus:outline-hidden">
            <div class="relative pt-[50%] sm:pt-[70%] rounded-xl overflow-hidden bg-neutral-800">
              <img class="size-full absolute inset-0 object-cover group-hover:scale-105 group-focus:scale-105 transition-transform duration-300 ease-in-out rounded-xl" src="https://images.unsplash.com/photo-1747285726356-535557675eda?q=80&w=768&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Blog Image">
            </div>

            <div class="mt-7">
              <h3 class="font-semibold text-xl text-white">
                Authentication trends
              </h3>
              <p class="mt-3 text-neutral-400">
                Discover the latest innovations in user authentication and identity management shaping the future of digital security.
              </p>
            </div>

            <a class="after:absolute after:inset-0" href="news-details.html"></a>
          </div>
          <!-- End Card -->

          <!-- Card -->
          <div class="relative group flex flex-col focus:outline-hidden">
            <div class="relative pt-[50%] sm:pt-[70%] rounded-xl overflow-hidden bg-neutral-800">
              <img class="size-full absolute inset-0 object-cover group-hover:scale-105 group-focus:scale-105 transition-transform duration-300 ease-in-out rounded-xl" src="https://images.unsplash.com/photo-1747515203898-2df8f083f417?q=80&w=768&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Blog Image">
            </div>

            <div class="mt-7">
              <h3 class="font-semibold text-xl text-white">
                Cloud infrastructure
              </h3>
              <p class="mt-3 text-neutral-400">
                How modern cloud platforms are revolutionizing web development and enabling faster, more scalable applications.
              </p>
            </div>

            <a class="after:absolute after:inset-0" href="news-details.html"></a>
          </div>
          <!-- End Card -->
        </div>
        <!-- End Card Grid -->

        <div class="mt-10 sm:mt-20 text-center">
          <a class="group inline-flex items-center gap-x-2 py-2 px-3 bg-[#ff0] font-medium text-sm text-nowrap text-neutral-800 rounded-full focus:outline-hidden" href="news.html">
            See all insights
            <svg class="shrink-0 size-4 transition group-hover:translate-x-0.5 group-focus:translate-x-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
    <!-- End News -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== FOOTER ========== -->
  <footer class="relative overflow-hidden bg-neutral-900">
    <svg class="absolute -bottom-20 start-1/2 w-[1900px] transform -translate-x-1/2" width="2745" height="488" viewBox="0 0 2745 488" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0.5 330.864C232.505 403.801 853.749 527.683 1482.69 439.719C2111.63 351.756 2585.54 434.588 2743.87 487" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 308.873C232.505 381.81 853.749 505.692 1482.69 417.728C2111.63 329.765 2585.54 412.597 2743.87 465.009" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 286.882C232.505 359.819 853.749 483.701 1482.69 395.738C2111.63 307.774 2585.54 390.606 2743.87 443.018" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 264.891C232.505 337.828 853.749 461.71 1482.69 373.747C2111.63 285.783 2585.54 368.615 2743.87 421.027" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 242.9C232.505 315.837 853.749 439.719 1482.69 351.756C2111.63 263.792 2585.54 346.624 2743.87 399.036" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 220.909C232.505 293.846 853.749 417.728 1482.69 329.765C2111.63 241.801 2585.54 324.633 2743.87 377.045" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 198.918C232.505 271.855 853.749 395.737 1482.69 307.774C2111.63 219.81 2585.54 302.642 2743.87 355.054" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 176.927C232.505 249.864 853.749 373.746 1482.69 285.783C2111.63 197.819 2585.54 280.651 2743.87 333.063" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 154.937C232.505 227.873 853.749 351.756 1482.69 263.792C2111.63 175.828 2585.54 258.661 2743.87 311.072" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 132.946C232.505 205.882 853.749 329.765 1482.69 241.801C2111.63 153.837 2585.54 236.67 2743.87 289.082" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 110.955C232.505 183.891 853.749 307.774 1482.69 219.81C2111.63 131.846 2585.54 214.679 2743.87 267.091" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 88.9639C232.505 161.901 853.749 285.783 1482.69 197.819C2111.63 109.855 2585.54 192.688 2743.87 245.1" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 66.9729C232.505 139.91 853.749 263.792 1482.69 175.828C2111.63 87.8643 2585.54 170.697 2743.87 223.109" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 44.9819C232.505 117.919 853.749 241.801 1482.69 153.837C2111.63 65.8733 2585.54 148.706 2743.87 201.118" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 22.991C232.505 95.9276 853.749 219.81 1482.69 131.846C2111.63 43.8824 2585.54 126.715 2743.87 179.127" class="stroke-neutral-700/50" stroke="currentColor" />
      <path d="M0.5 1C232.505 73.9367 853.749 197.819 1482.69 109.855C2111.63 21.8914 2585.54 104.724 2743.87 157.136" class="stroke-neutral-700/50" stroke="currentColor" />
    </svg>

    <div class="relative z-10">
      <div class="w-full max-w-5xl px-4 xl:px-0 py-10 lg:pt-16 mx-auto">
        <div class="inline-flex items-center">
          <!-- Logo -->
          <svg class="w-24 h-auto" width="116" height="32" viewBox="0 0 116 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M33.5696 30.8182V11.3182H37.4474V13.7003H37.6229C37.7952 13.3187 38.0445 12.9309 38.3707 12.5369C38.7031 12.1368 39.134 11.8045 39.6634 11.5398C40.1989 11.2689 40.8636 11.1335 41.6577 11.1335C42.6918 11.1335 43.6458 11.4044 44.5199 11.946C45.3939 12.4815 46.0926 13.291 46.6158 14.3743C47.139 15.4515 47.4006 16.8026 47.4006 18.4276C47.4006 20.0095 47.1451 21.3452 46.6342 22.4347C46.1295 23.518 45.4401 24.3397 44.5661 24.8999C43.6982 25.4538 42.7256 25.7308 41.6484 25.7308C40.8852 25.7308 40.2358 25.6046 39.7003 25.3523C39.1709 25.0999 38.737 24.7829 38.3984 24.4013C38.0599 24.0135 37.8014 23.6226 37.6229 23.2287H37.5028V30.8182H33.5696ZM37.4197 18.4091C37.4197 19.2524 37.5367 19.9879 37.7706 20.6158C38.0045 21.2436 38.343 21.733 38.7862 22.0838C39.2294 22.4285 39.768 22.6009 40.402 22.6009C41.0421 22.6009 41.5838 22.4254 42.027 22.0746C42.4702 21.7176 42.8056 21.2251 43.0334 20.5973C43.2673 19.9633 43.3842 19.2339 43.3842 18.4091C43.3842 17.5904 43.2704 16.8703 43.0426 16.2486C42.8149 15.6269 42.4794 15.1406 42.0362 14.7898C41.593 14.4389 41.0483 14.2635 40.402 14.2635C39.7618 14.2635 39.2202 14.4328 38.777 14.7713C38.34 15.1098 38.0045 15.59 37.7706 16.2116C37.5367 16.8333 37.4197 17.5658 37.4197 18.4091ZM49.2427 25.5V11.3182H53.0559V13.7926H53.2037C53.4622 12.9124 53.8961 12.2476 54.5055 11.7983C55.1149 11.3428 55.8166 11.1151 56.6106 11.1151C56.8076 11.1151 57.02 11.1274 57.2477 11.152C57.4754 11.1766 57.6755 11.2105 57.8478 11.2536V14.7436C57.6632 14.6882 57.4077 14.639 57.0815 14.5959C56.7553 14.5528 56.4567 14.5312 56.1859 14.5312C55.6073 14.5312 55.0903 14.6574 54.6348 14.9098C54.1854 15.156 53.8284 15.5007 53.5638 15.9439C53.3052 16.3871 53.176 16.898 53.176 17.4766V25.5H49.2427ZM64.9043 25.777C63.4455 25.777 62.1898 25.4815 61.1373 24.8906C60.0909 24.2936 59.2845 23.4503 58.7182 22.3608C58.1519 21.2652 57.8688 19.9695 57.8688 18.4737C57.8688 17.0149 58.1519 15.7346 58.7182 14.6328C59.2845 13.531 60.0816 12.6723 61.1096 12.0568C62.1437 11.4413 63.3563 11.1335 64.7474 11.1335C65.683 11.1335 66.5539 11.2843 67.3603 11.5859C68.1728 11.8814 68.8806 12.3277 69.4839 12.9247C70.0932 13.5218 70.5672 14.2727 70.9057 15.1776C71.2443 16.0762 71.4135 17.1288 71.4135 18.3352V19.4155H59.4384V16.978H67.7111C67.7111 16.4117 67.588 15.91 67.3418 15.473C67.0956 15.036 66.754 14.6944 66.317 14.4482C65.8861 14.1958 65.3844 14.0696 64.812 14.0696C64.2149 14.0696 63.6856 14.2081 63.2239 14.4851C62.7684 14.7559 62.4114 15.1222 62.1529 15.5838C61.8944 16.0393 61.762 16.5471 61.7559 17.1072V19.4247C61.7559 20.1264 61.8851 20.7327 62.1437 21.2436C62.4083 21.7545 62.7807 22.1484 63.2608 22.4254C63.741 22.7024 64.3103 22.8409 64.9689 22.8409C65.406 22.8409 65.8061 22.7794 66.1692 22.6562C66.5324 22.5331 66.8432 22.3485 67.1018 22.1023C67.3603 21.8561 67.5572 21.5545 67.6927 21.1974L71.3304 21.4375C71.1458 22.3116 70.7672 23.0748 70.1948 23.7273C69.6285 24.3736 68.896 24.8783 67.9974 25.2415C67.1048 25.5985 66.0738 25.777 64.9043 25.777ZM77.1335 6.59091V25.5H73.2003V6.59091H77.1335ZM79.5043 25.5V11.3182H83.4375V25.5H79.5043ZM81.4801 9.49006C80.8954 9.49006 80.3937 9.29616 79.9752 8.90838C79.5628 8.51444 79.3566 8.04356 79.3566 7.49574C79.3566 6.95407 79.5628 6.48935 79.9752 6.10156C80.3937 5.70762 80.8954 5.51065 81.4801 5.51065C82.0649 5.51065 82.5635 5.70762 82.9759 6.10156C83.3944 6.48935 83.6037 6.95407 83.6037 7.49574C83.6037 8.04356 83.3944 8.51444 82.9759 8.90838C82.5635 9.29616 82.0649 9.49006 81.4801 9.49006ZM89.7415 17.3011V25.5H85.8083V11.3182H89.5569V13.8203H89.723C90.037 12.9955 90.5632 12.343 91.3019 11.8629C92.0405 11.3767 92.9361 11.1335 93.9887 11.1335C94.9735 11.1335 95.8322 11.349 96.5647 11.7798C97.2971 12.2107 97.8665 12.8262 98.2728 13.6264C98.679 14.4205 98.8821 15.3684 98.8821 16.4702V25.5H94.9489V17.1719C94.9551 16.304 94.7335 15.6269 94.2841 15.1406C93.8348 14.6482 93.2162 14.402 92.4283 14.402C91.8989 14.402 91.4311 14.5159 91.0249 14.7436C90.6248 14.9714 90.3109 15.3037 90.0831 15.7408C89.8615 16.1716 89.7477 16.6918 89.7415 17.3011ZM107.665 25.777C106.206 25.777 104.951 25.4815 103.898 24.8906C102.852 24.2936 102.045 23.4503 101.479 22.3608C100.913 21.2652 100.63 19.9695 100.63 18.4737C100.63 17.0149 100.913 15.7346 101.479 14.6328C102.045 13.531 102.842 12.6723 103.87 12.0568C104.905 11.4413 106.117 11.1335 107.508 11.1335C108.444 11.1335 109.315 11.2843 110.121 11.5859C110.934 11.8814 111.641 12.3277 112.245 12.9247C112.854 13.5218 113.328 14.2727 113.667 15.1776C114.005 16.0762 114.174 17.1288 114.174 18.3352V19.4155H102.199V16.978H110.472C110.472 16.4117 110.349 15.91 110.103 15.473C109.856 15.036 109.515 14.6944 109.078 14.4482C108.647 14.1958 108.145 14.0696 107.573 14.0696C106.976 14.0696 106.446 14.2081 105.985 14.4851C105.529 14.7559 105.172 15.1222 104.914 15.5838C104.655 16.0393 104.523 16.5471 104.517 17.1072V19.4247C104.517 20.1264 104.646 20.7327 104.905 21.2436C105.169 21.7545 105.542 22.1484 106.022 22.4254C106.502 22.7024 107.071 22.8409 107.73 22.8409C108.167 22.8409 108.567 22.7794 108.93 22.6562C109.293 22.5331 109.604 22.3485 109.863 22.1023C110.121 21.8561 110.318 21.5545 110.454 21.1974L114.091 21.4375C113.907 22.3116 113.528 23.0748 112.956 23.7273C112.389 24.3736 111.657 24.8783 110.758 25.2415C109.866 25.5985 108.835 25.777 107.665 25.777Z" class="fill-white" fill="currentColor" />
            <path d="M1 29.5V16.5C1 9.87258 6.37258 4.5 13 4.5C19.6274 4.5 25 9.87258 25 16.5C25 23.1274 19.6274 28.5 13 28.5H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <path d="M5 29.5V16.66C5 12.1534 8.58172 8.5 13 8.5C17.4183 8.5 21 12.1534 21 16.66C21 21.1666 17.4183 24.82 13 24.82H12" class="stroke-white" stroke="currentColor" stroke-width="2" />
            <circle cx="13" cy="16.5214" r="5" class="fill-white" fill="currentColor" />
          </svg>
          <!-- End Logo -->

          <div class="border-s border-neutral-700 ps-5 ms-5">
            <p class="text-sm text-neutral-400">
              © 2025 Preline Labs.
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!-- ========== END FOOTER ========== -->

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>

</body>
</html>