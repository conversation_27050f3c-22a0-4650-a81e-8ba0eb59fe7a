import "../selection/each";
import "transition";

d3_transitionPrototype.each = function(type, listener) {
  var id = this.id, ns = this.namespace;
  if (arguments.length < 2) {
    var inherit = d3_transitionInherit,
        inheritId = d3_transitionInheritId;
    try {
      d3_transitionInheritId = id;
      d3_selection_each(this, function(node, i, j) {
        d3_transitionInherit = node[ns][id];
        type.call(node, node.__data__, i, j);
      });
    } finally {
      d3_transitionInherit = inherit;
      d3_transitionInheritId = inheritId;
    }
  } else {
    d3_selection_each(this, function(node) {
      var transition = node[ns][id];
      (transition.event || (transition.event = d3.dispatch("start", "end", "interrupt"))).on(type, listener);
    });
  }
  return this;
};
